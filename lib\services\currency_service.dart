import '../database/currency_dao.dart';
import '../database/exchange_rate_dao.dart';
import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/currency.dart';
import '../models/exchange_rate.dart';
import '../utils/result.dart';

/// خدمة إدارة العملات
class CurrencyService {
  final CurrencyDao _currencyDao = CurrencyDao();
  final ExchangeRateDao _exchangeRateDao = ExchangeRateDao();
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إضافة عملة جديدة
  Future<Result<Currency>> addCurrency(Currency currency) async {
    try {
      // التحقق من عدم وجود العملة مسبقاً
      bool exists = await _currencyDao.currencyExists(currency.code);
      if (exists) {
        return Result.error('العملة ${currency.code} موجودة مسبقاً');
      }

      final result = await _currencyDao.insertCurrency(currency);
      if (result.isSuccess) {
        final newCurrency = currency.copyWith(id: result.data);
        return Result.success(newCurrency);
      } else {
        return Result.error(result.error!);
      }
    } catch (e) {
      return Result.error('خطأ في إضافة العملة: ${e.toString()}');
    }
  }

  /// تحديث عملة
  Future<Result<Currency>> updateCurrency(Currency currency) async {
    try {
      final result = await _currencyDao.updateCurrency(currency);
      if (result.isSuccess) {
        return Result.success(currency);
      } else {
        return Result.error(result.error!);
      }
    } catch (e) {
      return Result.error('خطأ في تحديث العملة: ${e.toString()}');
    }
  }

  /// حذف عملة
  Future<Result<bool>> deleteCurrency(int id) async {
    try {
      // الحصول على معلومات العملة أولاً
      final currency = await _currencyDao.getCurrencyById(id);
      if (currency == null) {
        return Result.error('العملة غير موجودة');
      }

      // التحقق من عدم كون العملة هي العملة الأساسية
      if (currency.isBaseCurrency) {
        return Result.error('لا يمكن حذف العملة الأساسية');
      }

      // التحقق من عدم استخدام العملة في المعاملات المالية
      final usageCheck = await _checkCurrencyUsage(currency.code);
      if (!usageCheck.isSuccess) {
        return usageCheck;
      }

      return await _currencyDao.deleteCurrency(id);
    } catch (e) {
      return Result.error('خطأ في حذف العملة: ${e.toString()}');
    }
  }

  /// فحص استخدام العملة في المعاملات المالية
  Future<Result<bool>> _checkCurrencyUsage(String currencyCode) async {
    try {
      final db = await _dbHelper.database;

      // فحص استخدام العملة في إعدادات الشركة
      final companySettingsResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableCompanySettings} WHERE currency_code = ?',
        [currencyCode],
      );

      if ((companySettingsResult.first['count'] as int) > 0) {
        return Result.error(
          'لا يمكن حذف العملة لأنها مستخدمة في إعدادات الشركة',
        );
      }

      // فحص استخدام العملة في أسعار الصرف
      final exchangeRatesResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableExchangeRates} WHERE from_currency_code = ? OR to_currency_code = ?',
        [currencyCode, currencyCode],
      );

      if ((exchangeRatesResult.first['count'] as int) > 0) {
        return Result.error('لا يمكن حذف العملة لأنها مستخدمة في أسعار الصرف');
      }

      // فحص استخدام العملة في تاريخ أسعار الصرف
      final exchangeRateHistoryResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableExchangeRateHistory} WHERE from_currency_code = ? OR to_currency_code = ?',
        [currencyCode, currencyCode],
      );

      if ((exchangeRateHistoryResult.first['count'] as int) > 0) {
        return Result.error(
          'لا يمكن حذف العملة لأنها مستخدمة في تاريخ أسعار الصرف',
        );
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في فحص استخدام العملة: ${e.toString()}');
    }
  }

  /// الحصول على عملة بالمعرف
  Future<Currency?> getCurrencyById(int id) async {
    return await _currencyDao.getCurrencyById(id);
  }

  /// الحصول على عملة بالرمز
  Future<Currency?> getCurrencyByCode(String code) async {
    return await _currencyDao.getCurrencyByCode(code);
  }

  /// الحصول على جميع العملات
  Future<List<Currency>> getAllCurrencies() async {
    return await _currencyDao.getAllCurrencies();
  }

  /// الحصول على العملات النشطة
  Future<List<Currency>> getActiveCurrencies() async {
    return await _currencyDao.getActiveCurrencies();
  }

  /// الحصول على العملة الأساسية
  Future<Currency?> getBaseCurrency() async {
    return await _currencyDao.getBaseCurrency();
  }

  /// تعيين عملة كعملة أساسية
  Future<Result<bool>> setBaseCurrency(String currencyCode) async {
    try {
      // التحقق من وجود العملة
      final currency = await _currencyDao.getCurrencyByCode(currencyCode);
      if (currency == null) {
        return Result.error('العملة غير موجودة');
      }

      if (!currency.isActive) {
        return Result.error('لا يمكن تعيين عملة غير نشطة كعملة أساسية');
      }

      return await _currencyDao.setBaseCurrency(currencyCode);
    } catch (e) {
      return Result.error('خطأ في تعيين العملة الأساسية: ${e.toString()}');
    }
  }

  /// البحث في العملات
  Future<List<Currency>> searchCurrencies(String query) async {
    return await _currencyDao.searchCurrencies(query);
  }

  /// تفعيل/إلغاء تفعيل عملة
  Future<Result<bool>> toggleCurrencyStatus(int id, bool isActive) async {
    try {
      // إذا كانت العملة أساسية، لا يمكن إلغاء تفعيلها
      if (!isActive) {
        final currency = await _currencyDao.getCurrencyById(id);
        if (currency?.isBaseCurrency == true) {
          return Result.error('لا يمكن إلغاء تفعيل العملة الأساسية');
        }
      }

      return await _currencyDao.toggleCurrencyStatus(id, isActive);
    } catch (e) {
      return Result.error('خطأ في تغيير حالة العملة: ${e.toString()}');
    }
  }

  /// إعداد العملات الافتراضية
  Future<Result<bool>> setupDefaultCurrencies() async {
    try {
      return await _currencyDao.insertDefaultCurrencies();
    } catch (e) {
      return Result.error('خطأ في إعداد العملات الافتراضية: ${e.toString()}');
    }
  }

  /// تحويل مبلغ من عملة إلى أخرى
  Future<Result<CurrencyConversion>> convertCurrency({
    required String fromCurrency,
    required String toCurrency,
    required double amount,
    DateTime? asOfDate,
  }) async {
    try {
      // إذا كانت العملتان متشابهتان، لا حاجة للتحويل
      if (fromCurrency == toCurrency) {
        return Result.success(
          CurrencyConversion(
            fromCurrencyCode: fromCurrency,
            toCurrencyCode: toCurrency,
            fromAmount: amount,
            toAmount: amount,
            exchangeRate: 1.0,
            conversionDate: asOfDate ?? DateTime.now(),
            source: ExchangeRateSource.manual,
          ),
        );
      }

      // البحث عن سعر الصرف المباشر
      ExchangeRate? exchangeRate = await _exchangeRateDao.getLatestExchangeRate(
        fromCurrency,
        toCurrency,
        asOfDate: asOfDate,
      );

      if (exchangeRate != null) {
        final convertedAmount = exchangeRate.convertAmount(amount);
        return Result.success(
          CurrencyConversion(
            fromCurrencyCode: fromCurrency,
            toCurrencyCode: toCurrency,
            fromAmount: amount,
            toAmount: convertedAmount,
            exchangeRate: exchangeRate.rate,
            conversionDate: asOfDate ?? DateTime.now(),
            source: exchangeRate.source,
          ),
        );
      }

      // البحث عن سعر الصرف العكسي
      exchangeRate = await _exchangeRateDao.getLatestExchangeRate(
        toCurrency,
        fromCurrency,
        asOfDate: asOfDate,
      );

      if (exchangeRate != null) {
        final convertedAmount = exchangeRate.convertAmountReverse(amount);
        return Result.success(
          CurrencyConversion(
            fromCurrencyCode: fromCurrency,
            toCurrencyCode: toCurrency,
            fromAmount: amount,
            toAmount: convertedAmount,
            exchangeRate: 1.0 / exchangeRate.rate,
            conversionDate: asOfDate ?? DateTime.now(),
            source: exchangeRate.source,
          ),
        );
      }

      // محاولة التحويل عبر العملة الأساسية
      final baseCurrency = await getBaseCurrency();
      if (baseCurrency != null &&
          baseCurrency.code != fromCurrency &&
          baseCurrency.code != toCurrency) {
        // تحويل من العملة المصدر إلى العملة الأساسية
        final toBaseResult = await convertCurrency(
          fromCurrency: fromCurrency,
          toCurrency: baseCurrency.code,
          amount: amount,
          asOfDate: asOfDate,
        );

        if (toBaseResult.isSuccess) {
          // تحويل من العملة الأساسية إلى العملة المستهدفة
          final toTargetResult = await convertCurrency(
            fromCurrency: baseCurrency.code,
            toCurrency: toCurrency,
            amount: toBaseResult.data!.toAmount,
            asOfDate: asOfDate,
          );

          if (toTargetResult.isSuccess) {
            return Result.success(
              CurrencyConversion(
                fromCurrencyCode: fromCurrency,
                toCurrencyCode: toCurrency,
                fromAmount: amount,
                toAmount: toTargetResult.data!.toAmount,
                exchangeRate: toTargetResult.data!.toAmount / amount,
                conversionDate: asOfDate ?? DateTime.now(),
                source: ExchangeRateSource.manual,
              ),
            );
          }
        }
      }

      return Result.error(
        'لا يوجد سعر صرف متاح للتحويل من $fromCurrency إلى $toCurrency',
      );
    } catch (e) {
      return Result.error('خطأ في تحويل العملة: ${e.toString()}');
    }
  }

  /// تنسيق مبلغ بالعملة
  String formatAmount(double amount, String currencyCode) {
    final currency = Currency.findByCode(currencyCode);
    if (currency != null) {
      return currency.formatAmount(amount);
    }
    return '${amount.toStringAsFixed(2)} $currencyCode';
  }

  /// الحصول على إحصائيات العملات
  Future<Map<String, dynamic>> getCurrencyStats() async {
    try {
      final currencyStats = await _currencyDao.getCurrencyStats();
      final exchangeRateStats = await _exchangeRateDao.getExchangeRateStats();

      return {...currencyStats, ...exchangeRateStats};
    } catch (e) {
      return {};
    }
  }

  /// التحقق من صحة رمز العملة
  bool isValidCurrencyCode(String code) {
    return code.length == 3 && RegExp(r'^[A-Z]{3}$').hasMatch(code);
  }

  /// الحصول على العملات المدعومة مسبقاً
  List<Currency> getSupportedCurrencies() {
    return Currency.predefinedCurrencies;
  }

  /// التحقق من إمكانية حذف العملة
  Future<Result<bool>> canDeleteCurrency(int id) async {
    try {
      // الحصول على معلومات العملة أولاً
      final currency = await _currencyDao.getCurrencyById(id);
      if (currency == null) {
        return Result.error('العملة غير موجودة');
      }

      // التحقق من عدم كون العملة هي العملة الأساسية
      if (currency.isBaseCurrency) {
        return Result.error('لا يمكن حذف العملة الأساسية');
      }

      // التحقق من عدم استخدام العملة في المعاملات المالية
      return await _checkCurrencyUsage(currency.code);
    } catch (e) {
      return Result.error('خطأ في فحص إمكانية حذف العملة: ${e.toString()}');
    }
  }
}
