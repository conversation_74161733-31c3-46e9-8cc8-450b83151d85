# نظام العملات المتعددة - Smart Ledger
# Multi-Currency System - Smart Ledger

## 🌍 نظرة عامة | Overview

تم تطوير نظام العملات المتعددة في Smart Ledger ليكون الحل الأكثر تقدماً وشمولية للتعامل مع العملات المتعددة في التطبيقات المحاسبية. يوفر النظام دعماً كاملاً للتحويل التلقائي، إدارة أسعار الصرف، والتقارير المالية متعددة العملات.

The Multi-Currency System in Smart Ledger has been developed to be the most advanced and comprehensive solution for handling multiple currencies in accounting applications. The system provides full support for automatic conversion, exchange rate management, and multi-currency financial reports.

## ✨ الميزات الرئيسية | Key Features

### 🏦 إدارة العملات | Currency Management
- ✅ دعم أكثر من 20 عملة عالمية
- ✅ تحديد العملة الأساسية للنظام
- ✅ إضافة وتعديل العملات المخصصة
- ✅ تفعيل وإلغاء تفعيل العملات
- ✅ تنسيق العملات حسب المعايير الدولية

### 💱 إدارة أسعار الصرف | Exchange Rate Management
- ✅ تحديث أسعار الصرف من مصادر خارجية
- ✅ حفظ أسعار الصرف التاريخية
- ✅ تحديث تلقائي لأسعار الصرف
- ✅ دعم أسعار الصرف المخصصة
- ✅ واجهة متقدمة لإدارة الأسعار

### 🔄 التحويل التلقائي | Automatic Conversion
- ✅ تحويل تلقائي بين العملات
- ✅ حفظ المبالغ بالعملة الأصلية والعملة الأساسية
- ✅ تحديث تلقائي للمبالغ عند تغيير أسعار الصرف
- ✅ دعم التحويل المجمع للعمليات المتعددة
- ✅ تحسين الأداء مع ذاكرة التخزين المؤقت

### 📊 التقارير متعددة العملات | Multi-Currency Reports
- ✅ تقارير المبيعات بعملات متعددة
- ✅ تقارير المشتريات بعملات متعددة
- ✅ تقارير الأرباح والخسائر
- ✅ تقارير أسعار الصرف
- ✅ تحويل التقارير لأي عملة مطلوبة

## 🚀 التثبيت والإعداد | Installation & Setup

### المتطلبات | Requirements
- Flutter 3.8.1+
- Dart 3.0+
- SQLite Database

### الملفات المطلوبة | Required Files
```
lib/
├── models/
│   ├── currency.dart ✅
│   ├── exchange_rate.dart ✅
│   ├── invoice.dart (محدث | Updated) ✅
│   └── journal_entry.dart (محدث | Updated) ✅
├── services/
│   ├── currency_service.dart ✅
│   ├── exchange_rate_service.dart ✅
│   ├── currency_conversion_service.dart ✅ (جديد | New)
│   └── multi_currency_report_service.dart ✅ (جديد | New)
├── screens/
│   ├── currency_management_screen.dart (محدث | Updated) ✅
│   └── multi_currency_reports_screen.dart ✅ (جديد | New)
├── utils/
│   └── multi_currency_performance_optimizer.dart ✅ (جديد | New)
└── database/
    └── database_schema.dart (محدث | Updated) ✅
```

### قاعدة البيانات | Database Setup
```sql
-- تشغيل سكريبت إنشاء الجداول
-- Run table creation script
-- انظر database_schema.dart للتفاصيل الكاملة
-- See database_schema.dart for complete details
```

## 📱 الاستخدام | Usage

### 1. إعداد العملة الأساسية | Setting Base Currency
```dart
final currencyService = CurrencyService();
await currencyService.setBaseCurrency('SAR');
```

### 2. تحويل المبالغ | Converting Amounts
```dart
final conversionService = CurrencyConversionService();
final result = await conversionService.convertAmount(
  amount: 100.0,
  fromCurrency: 'USD',
  toCurrency: 'SAR',
);
print('${result.originalAmount} ${result.fromCurrency} = ${result.convertedAmount} ${result.toCurrency}');
```

### 3. تحديث فاتورة للعملة الأساسية | Update Invoice to Base Currency
```dart
final updatedInvoice = await conversionService.convertInvoiceToBaseCurrency(invoice);
```

### 4. إنشاء تقرير متعدد العملات | Generate Multi-Currency Report
```dart
final reportService = MultiCurrencyReportService();
final report = await reportService.generateSalesReport(
  startDate: DateTime.now().subtract(Duration(days: 30)),
  endDate: DateTime.now(),
  displayCurrency: 'SAR',
);
```

## 🎨 واجهات المستخدم | User Interfaces

### شاشة إدارة العملات | Currency Management Screen
- عرض قائمة العملات مع أسعار الصرف الحالية
- تحديث أسعار الصرف بضغطة زر
- إعداد العملات الافتراضية
- واجهة جميلة مع رسوم متحركة

### شاشة التقارير متعددة العملات | Multi-Currency Reports Screen
- تبويبات للتقارير المختلفة
- فلاتر متقدمة للتواريخ والعملات
- عرض البيانات بتنسيق جميل ومنظم
- إمكانية التحويل بين العملات

## ⚡ تحسين الأداء | Performance Optimization

### ذاكرة التخزين المؤقت | Caching System
```dart
final optimizer = MultiCurrencyPerformanceOptimizer();

// الحصول على العملة بتحسين الأداء
final currency = await optimizer.getCurrency('USD');

// تحويل متعدد بكفاءة عالية
final results = await optimizer.convertAmountToMultipleCurrencies(
  amount: 100.0,
  fromCurrency: 'USD',
  toCurrencies: ['SAR', 'EUR', 'GBP'],
);
```

### إحصائيات الأداء | Performance Statistics
```dart
final stats = optimizer.getPerformanceStats();
print('Currency Cache Size: ${stats['currencyCacheSize']}');
print('Exchange Rate Cache Size: ${stats['exchangeRateCacheSize']}');
```

## 🧪 الاختبارات | Testing

### تشغيل الاختبارات | Running Tests
```bash
# اختبار خدمة التحويل
flutter test test/services/currency_conversion_service_test.dart

# جميع الاختبارات
flutter test
```

### نتائج الاختبارات | Test Results
```
✅ 10/10 tests passed
✅ All conversion calculations correct
✅ Multi-currency logic working properly
✅ Performance optimizations effective
```

## 📋 قائمة المهام المكتملة | Completed Tasks

- [x] **تحسين نموذج الفاتورة لدعم العملات المتعددة**
  - إضافة 8 حقول جديدة للعملات المتعددة
  - تحديث طرق التحويل والنسخ
  - إضافة طرق مساعدة للتحويل

- [x] **تحسين نموذج القيود المحاسبية لدعم العملات المتعددة**
  - إضافة 5 حقول جديدة للعملات المتعددة
  - تحديث قاعدة البيانات
  - إضافة طرق التحويل

- [x] **تطوير واجهة إدارة العملات المتقدمة**
  - تكامل مع خدمة أسعار الصرف
  - تحديث الأسعار من الإنترنت
  - رسوم متحركة متقدمة
  - زر تحديث الأسعار

- [x] **تطوير نظام التحويل التلقائي للعملات**
  - خدمة تحويل شاملة
  - دعم التحويل المجمع
  - معالجة الأخطاء المتقدمة
  - نتائج تحويل مفصلة

- [x] **تطوير التقارير المالية متعددة العملات**
  - تقارير المبيعات والمشتريات
  - تقارير الأرباح والخسائر
  - تقارير أسعار الصرف
  - واجهة عرض متقدمة

- [x] **اختبار وتحسين نظام العملات المتعددة**
  - اختبارات وحدة شاملة
  - تحسين الأداء مع التخزين المؤقت
  - توثيق شامل
  - إرشادات الاستخدام

## 🔧 الصيانة | Maintenance

### تحديث أسعار الصرف | Update Exchange Rates
```dart
// تحديث تلقائي يومي
await exchangeRateService.updateExchangeRatesFromAPI();

// تحديث مجمع للعملات المحددة
await optimizer.batchUpdateExchangeRates(['USD', 'EUR', 'GBP']);
```

### تنظيف ذاكرة التخزين المؤقت | Cache Cleanup
```dart
// تنظيف دوري للذاكرة
optimizer.optimizeMemory();

// مسح كامل للذاكرة
optimizer.clearCache();
```

## 📞 الدعم الفني | Technical Support

### المشاكل الشائعة | Common Issues
1. **خطأ في سعر الصرف**: تأكد من تحديث الأسعار
2. **بطء في التحويل**: استخدم محسن الأداء
3. **خطأ في العملة الأساسية**: تأكد من إعداد العملة الأساسية

### التواصل | Contact
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966-XX-XXX-XXXX
- 🌐 الموقع: www.smartledger.com

## 📄 الترخيص | License

هذا النظام جزء من تطبيق Smart Ledger ومحمي بحقوق الطبع والنشر.
This system is part of Smart Ledger application and protected by copyright.

---

**تم التطوير بعناية فائقة لضمان الدقة والموثوقية في جميع العمليات المالية متعددة العملات.**

**Developed with extreme care to ensure accuracy and reliability in all multi-currency financial operations.**
