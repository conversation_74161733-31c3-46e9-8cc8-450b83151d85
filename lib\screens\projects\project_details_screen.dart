/// شاشة تفاصيل المشروع
/// Project Details Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/project.dart';
import '../../services/project_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_text_styles.dart';
import 'add_edit_project_screen.dart';
import 'project_costs_screen.dart';
import 'project_phases_screen.dart';
import 'project_reports_screen.dart';
import 'project_tasks_screen.dart';

class ProjectDetailsScreen extends StatefulWidget {
  final Project project;

  const ProjectDetailsScreen({super.key, required this.project});

  @override
  State<ProjectDetailsScreen> createState() => _ProjectDetailsScreenState();
}

class _ProjectDetailsScreenState extends State<ProjectDetailsScreen> {
  final ProjectService _projectService = ProjectService();

  late Project _project;
  ProjectProfitability? _profitability;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _project = widget.project;
    _loadProjectDetails();
  }

  Future<void> _loadProjectDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث بيانات المشروع
      final projectResult = await _projectService.getProjectById(_project.id!);
      if (projectResult.isSuccess && projectResult.data != null) {
        _project = projectResult.data!;
      }

      // تحميل تحليل الربحية
      final profitabilityResult = await _projectService
          .calculateProjectProfitability(_project.id!);
      if (profitabilityResult.isSuccess && profitabilityResult.data != null) {
        _profitability = profitabilityResult.data!;
      }

      setState(() {});
    } catch (e) {
      _showErrorDialog('خطأ في تحميل تفاصيل المشروع: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  Future<void> _navigateToEdit() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditProjectScreen(project: _project),
      ),
    );

    if (result == true) {
      _loadProjectDetails();
    }
  }

  Future<void> _navigateToCosts() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectCostsScreen(project: _project),
      ),
    );

    if (result == true) {
      _loadProjectDetails();
    }
  }

  Future<void> _navigateToPhases() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectPhasesScreen(project: _project),
      ),
    );

    if (result == true) {
      _loadProjectDetails();
    }
  }

  Future<void> _navigateToReports() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectReportsScreen(project: _project),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_project.name),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _navigateToEdit,
            icon: const Icon(Icons.edit),
            tooltip: 'تعديل المشروع',
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // معلومات المشروع الأساسية
              _buildProjectInfoCard(),

              const SizedBox(height: 16),

              // إحصائيات المشروع
              _buildProjectStatsCard(),

              const SizedBox(height: 16),

              // تحليل الربحية
              if (_profitability != null) _buildProfitabilityCard(),

              const SizedBox(height: 16),

              // المراحل والتكاليف
              _buildActionsCard(),

              const SizedBox(height: 16),

              // التقدم والمراحل
              _buildProgressCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectInfoCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'معلومات المشروع',
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildStatusChip(_project.status),
              ],
            ),

            const SizedBox(height: 16),

            _buildInfoRow('كود المشروع', _project.code),
            _buildInfoRow('النوع', _project.type.nameAr),
            _buildInfoRow('الأولوية', _project.priority.nameAr),

            if (_project.customer != null)
              _buildInfoRow(
                'العميل',
                '${_project.customer!.name} (${_project.customer!.code})',
              ),

            if (_project.description != null) ...[
              const SizedBox(height: 8),
              Text(
                'الوصف:',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(_project.description!, style: AppTextStyles.bodyMedium),
            ],

            if (_project.notes != null) ...[
              const SizedBox(height: 8),
              Text(
                'ملاحظات:',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(_project.notes!, style: AppTextStyles.bodyMedium),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProjectStatsCard() {
    final completionPercentage = _project.completionPercentage;
    final remainingDays = _project.remainingDays;
    final isOverdue =
        _project.endDate != null &&
        _project.endDate!.isBefore(DateTime.now()) &&
        _project.status != ProjectStatus.completed;

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات المشروع',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // شريط التقدم
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'نسبة الإنجاز',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${completionPercentage.toStringAsFixed(1)}%',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getProgressColor(completionPercentage),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: completionPercentage / 100,
                  backgroundColor: AppColors.surface,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProgressColor(completionPercentage),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'تاريخ البداية',
                    '${_project.startDate.day}/${_project.startDate.month}/${_project.startDate.year}',
                    Icons.play_arrow,
                    AppColors.success,
                  ),
                ),
                if (_project.endDate != null)
                  Expanded(
                    child: _buildStatItem(
                      'تاريخ النهاية',
                      '${_project.endDate!.day}/${_project.endDate!.month}/${_project.endDate!.year}',
                      Icons.flag,
                      isOverdue ? AppColors.error : AppColors.info,
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الميزانية',
                    '${_project.budgetAmount.toStringAsFixed(0)} ر.س',
                    Icons.account_balance_wallet,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'التكلفة الفعلية',
                    '${_project.actualCost.toStringAsFixed(0)} ر.س',
                    Icons.receipt_long,
                    _project.actualCost > _project.budgetAmount
                        ? AppColors.error
                        : AppColors.success,
                  ),
                ),
              ],
            ),

            if (remainingDays != null) ...[
              const SizedBox(height: 12),
              _buildStatItem(
                'الأيام المتبقية',
                remainingDays > 0
                    ? '$remainingDays يوم'
                    : remainingDays == 0
                    ? 'ينتهي اليوم'
                    : 'متأخر ${-remainingDays} يوم',
                Icons.schedule,
                remainingDays > 7
                    ? AppColors.success
                    : remainingDays >= 0
                    ? AppColors.warning
                    : AppColors.error,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProfitabilityCard() {
    final profitability = _profitability!;

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحليل الربحية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الإيرادات',
                    '${profitability.totalRevenue.toStringAsFixed(0)} ر.س',
                    Icons.trending_up,
                    AppColors.success,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'إجمالي التكاليف',
                    '${profitability.totalCosts.toStringAsFixed(0)} ر.س',
                    Icons.trending_down,
                    AppColors.error,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الربح الإجمالي',
                    '${profitability.grossProfit.toStringAsFixed(0)} ر.س',
                    Icons.account_balance,
                    profitability.grossProfit >= 0
                        ? AppColors.success
                        : AppColors.error,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'هامش الربح',
                    '${profitability.profitMargin.toStringAsFixed(1)}%',
                    Icons.percent,
                    profitability.profitMargin >= 0
                        ? AppColors.success
                        : AppColors.error,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            _buildStatItem(
              'انحراف الميزانية',
              '${profitability.budgetVariance.toStringAsFixed(0)} ر.س (${profitability.budgetVariancePercentage.toStringAsFixed(1)}%)',
              Icons.compare_arrows,
              profitability.budgetVariance >= 0
                  ? AppColors.success
                  : AppColors.error,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إدارة المشروع',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: QuantumButton(
                    onPressed: _navigateToCosts,
                    text: 'إدارة التكاليف',
                    icon: Icons.receipt_long,
                    variant: QuantumButtonVariant.outline,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: QuantumButton(
                    onPressed: _navigateToPhases,
                    text: 'إدارة المراحل',
                    icon: Icons.timeline,
                    variant: QuantumButtonVariant.outline,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: QuantumButton(
                    onPressed: _navigateToReports,
                    text: 'تقارير المشروع',
                    icon: Icons.assessment,
                    variant: QuantumButtonVariant.outline,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: QuantumButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              ProjectTasksScreen(project: widget.project),
                        ),
                      );
                    },
                    text: 'إدارة المهام',
                    icon: Icons.task_alt,
                    variant: QuantumButtonVariant.outline,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مراحل المشروع',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            if (_project.phases.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.timeline,
                      size: 48,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد مراحل محددة للمشروع',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _project.phases.length,
                itemBuilder: (context, index) {
                  final phase = _project.phases[index];
                  return _buildPhaseItem(phase);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhaseItem(ProjectPhase phase) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  phase.name,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              _buildPhaseStatusChip(phase.status),
            ],
          ),

          if (phase.description != null) ...[
            const SizedBox(height: 4),
            Text(
              phase.description!,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],

          const SizedBox(height: 8),

          Row(
            children: [
              Text(
                'التقدم: ${phase.completionPercentage.toStringAsFixed(1)}%',
                style: AppTextStyles.bodySmall,
              ),
              const SizedBox(width: 16),
              Text(
                'الوزن: ${phase.weight.toStringAsFixed(1)}%',
                style: AppTextStyles.bodySmall,
              ),
            ],
          ),

          const SizedBox(height: 4),

          LinearProgressIndicator(
            value: phase.completionPercentage / 100,
            backgroundColor: AppColors.surface,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getProgressColor(phase.completionPercentage),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(child: Text(value, style: AppTextStyles.bodyMedium)),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(ProjectStatus status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case ProjectStatus.planning:
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        break;
      case ProjectStatus.active:
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        break;
      case ProjectStatus.onHold:
        backgroundColor = AppColors.info.withValues(alpha: 0.1);
        textColor = AppColors.info;
        break;
      case ProjectStatus.completed:
        backgroundColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        break;
      case ProjectStatus.cancelled:
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status.nameAr,
        style: AppTextStyles.bodySmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPhaseStatusChip(ProjectPhaseStatus status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case ProjectPhaseStatus.notStarted:
        backgroundColor = AppColors.textSecondary.withValues(alpha: 0.1);
        textColor = AppColors.textSecondary;
        break;
      case ProjectPhaseStatus.inProgress:
        backgroundColor = AppColors.info.withValues(alpha: 0.1);
        textColor = AppColors.info;
        break;
      case ProjectPhaseStatus.completed:
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        break;
      case ProjectPhaseStatus.delayed:
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.nameAr,
        style: AppTextStyles.bodySmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 80) return AppColors.success;
    if (percentage >= 50) return AppColors.warning;
    return AppColors.error;
  }
}
