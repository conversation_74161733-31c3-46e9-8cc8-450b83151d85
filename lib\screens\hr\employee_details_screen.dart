import 'package:flutter/material.dart';
import '../../models/employee.dart';
import '../../models/department.dart';
import '../../services/employee_service.dart';
import '../../dao/department_dao.dart';
import '../../dao/position_dao.dart';
import '../../widgets/quantum_card.dart';
import '../../theme/app_theme.dart';
import 'add_employee_screen.dart';

/// شاشة تفاصيل الموظف
/// Employee Details Screen
class EmployeeDetailsScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeDetailsScreen({super.key, required this.employee});

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late Employee _currentEmployee;
  final EmployeeService _employeeService = EmployeeService();
  final DepartmentDao _departmentDao = DepartmentDao();
  final PositionDao _positionDao = PositionDao();

  bool _isLoading = false;
  Department? _department;
  Position? _position;
  Employee? _manager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _currentEmployee = widget.employee;
    _loadEmployeeDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployeeDetails() async {
    setState(() => _isLoading = true);

    try {
      // تحميل بيانات القسم
      _department = await _departmentDao.getDepartmentById(
        _currentEmployee.departmentId,
      );

      // تحميل بيانات المنصب
      _position = await _positionDao.getPositionById(
        _currentEmployee.positionId,
      );

      // تحميل بيانات المدير المباشر
      if (_currentEmployee.directManagerId != null) {
        final managerResult = await _employeeService.getEmployeeByCode(
          _currentEmployee.directManagerId!,
        );
        if (managerResult.isSuccess) {
          _manager = managerResult.data;
        }
      }
    } catch (e) {
      debugPrint('Error loading employee details: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(_currentEmployee.fullName),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.of(context).push<bool>(
                MaterialPageRoute(
                  builder: (context) =>
                      AddEmployeeScreen(employee: _currentEmployee),
                ),
              );
              if (result == true) {
                // إعادة تحميل بيانات الموظف
                final updatedResult = await _employeeService.getEmployeeById(
                  _currentEmployee.id!,
                );
                if (updatedResult.isSuccess && mounted) {
                  setState(() {
                    _currentEmployee = updatedResult.data!;
                  });
                  _loadEmployeeDetails();
                }
              }
            },
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadEmployeeDetails,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المعلومات الأساسية', icon: Icon(Icons.person)),
            Tab(text: 'معلومات الوظيفة', icon: Icon(Icons.work)),
            Tab(text: 'الراتب والبدلات', icon: Icon(Icons.attach_money)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildBasicInfoTab(),
                _buildJobInfoTab(),
                _buildSalaryInfoTab(),
              ],
            ),
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPersonalInfoCard(),
          const SizedBox(height: 16),
          _buildContactInfoCard(),
          const SizedBox(height: 16),
          _buildEmergencyContactCard(),
        ],
      ),
    );
  }

  Widget _buildJobInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildJobDetailsCard(),
          const SizedBox(height: 16),
          _buildDepartmentCard(),
          const SizedBox(height: 16),
          _buildManagerCard(),
        ],
      ),
    );
  }

  Widget _buildSalaryInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSalaryCard(),
          const SizedBox(height: 16),
          _buildBankInfoCard(),
          const SizedBox(height: 16),
          _buildAllowancesCard(),
          const SizedBox(height: 16),
          _buildDeductionsCard(),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات الشخصية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow('الاسم الكامل', _currentEmployee.fullName),
            _buildInfoRow('رمز الموظف', _currentEmployee.employeeCode),
            if (_currentEmployee.nationalId != null)
              _buildInfoRow('رقم الهوية', _currentEmployee.nationalId!),
            if (_currentEmployee.passportNumber != null)
              _buildInfoRow('رقم الجواز', _currentEmployee.passportNumber!),
            if (_currentEmployee.birthDate != null)
              _buildInfoRow(
                'تاريخ الميلاد',
                _formatDate(_currentEmployee.birthDate!),
              ),
            if (_currentEmployee.age != null)
              _buildInfoRow('العمر', '${_currentEmployee.age} سنة'),
            _buildInfoRow('الجنس', _currentEmployee.gender.displayName),
            _buildInfoRow(
              'الحالة الاجتماعية',
              _currentEmployee.maritalStatus.displayName,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.contact_phone, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الاتصال',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            if (_currentEmployee.phone != null)
              _buildInfoRow('رقم الهاتف', _currentEmployee.phone!),
            if (_currentEmployee.email != null)
              _buildInfoRow('البريد الإلكتروني', _currentEmployee.email!),
            if (_currentEmployee.address != null)
              _buildInfoRow('العنوان', _currentEmployee.address!),
          ],
        ),
      ),
    );
  }

  Widget _buildEmergencyContactCard() {
    if (_currentEmployee.emergencyContact == null &&
        _currentEmployee.emergencyPhone == null) {
      return const SizedBox.shrink();
    }

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.emergency, color: Colors.red),
                const SizedBox(width: 8),
                const Text(
                  'جهة الاتصال في حالات الطوارئ',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            if (_currentEmployee.emergencyContact != null)
              _buildInfoRow('الاسم', _currentEmployee.emergencyContact!),
            if (_currentEmployee.emergencyPhone != null)
              _buildInfoRow('رقم الهاتف', _currentEmployee.emergencyPhone!),
          ],
        ),
      ),
    );
  }

  Widget _buildJobDetailsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.work, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل الوظيفة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow(
              'تاريخ التوظيف',
              _formatDate(_currentEmployee.hireDate),
            ),
            _buildInfoRow(
              'سنوات الخدمة',
              '${_currentEmployee.yearsOfService} سنة',
            ),
            _buildInfoRow(
              'نوع الموظف',
              _currentEmployee.employeeType.displayName,
            ),
            _buildInfoRow('الحالة', _currentEmployee.status.displayName),
            if (_currentEmployee.terminationDate != null)
              _buildInfoRow(
                'تاريخ انتهاء الخدمة',
                _formatDate(_currentEmployee.terminationDate!),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDepartmentCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'القسم والمنصب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow('القسم', _department?.name ?? 'غير محدد'),
            if (_department?.code != null)
              _buildInfoRow('رمز القسم', _department!.code),
            _buildInfoRow('المنصب', _position?.title ?? 'غير محدد'),
            if (_position?.code != null)
              _buildInfoRow('رمز المنصب', _position!.code),
            if (_position?.level != null)
              _buildInfoRow('مستوى المنصب', _position!.level.displayName),
          ],
        ),
      ),
    );
  }

  Widget _buildManagerCard() {
    if (_manager == null) {
      return const SizedBox.shrink();
    }

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.supervisor_account,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'المدير المباشر',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow('الاسم', _manager!.fullName),
            _buildInfoRow('رمز الموظف', _manager!.employeeCode),
            if (_manager!.phone != null)
              _buildInfoRow('رقم الهاتف', _manager!.phone!),
            if (_manager!.email != null)
              _buildInfoRow('البريد الإلكتروني', _manager!.email!),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.attach_money, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الراتب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow(
              'الراتب الأساسي',
              '${_currentEmployee.basicSalary.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'إجمالي البدلات',
              '${_currentEmployee.totalAllowances.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'إجمالي الاستقطاعات',
              '${_currentEmployee.totalDeductions.toStringAsFixed(2)} ر.س',
            ),
            const Divider(),
            _buildInfoRow(
              'صافي الراتب',
              '${_currentEmployee.netSalary.toStringAsFixed(2)} ر.س',
              isHighlighted: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankInfoCard() {
    if (_currentEmployee.bankAccount == null &&
        _currentEmployee.bankName == null &&
        _currentEmployee.iban == null) {
      return const SizedBox.shrink();
    }

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.account_balance, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات المصرفية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            if (_currentEmployee.bankName != null)
              _buildInfoRow('اسم البنك', _currentEmployee.bankName!),
            if (_currentEmployee.bankAccount != null)
              _buildInfoRow('رقم الحساب', _currentEmployee.bankAccount!),
            if (_currentEmployee.iban != null)
              _buildInfoRow('رقم الآيبان', _currentEmployee.iban!),
          ],
        ),
      ),
    );
  }

  Widget _buildAllowancesCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.add_circle, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'البدلات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            if (_currentEmployee.allowances.isEmpty)
              const Text('لا توجد بدلات مسجلة')
            else
              ..._currentEmployee.allowances.map(
                (allowance) => _buildInfoRow(
                  'بدل',
                  '${allowance.amount.toStringAsFixed(2)} ر.س',
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeductionsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.remove_circle, color: Colors.red),
                const SizedBox(width: 8),
                const Text(
                  'الاستقطاعات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            if (_currentEmployee.deductions.isEmpty)
              const Text('لا توجد استقطاعات مسجلة')
            else
              ..._currentEmployee.deductions.map(
                (deduction) => _buildInfoRow(
                  'استقطاع',
                  '${deduction.amount.toStringAsFixed(2)} ر.س',
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    bool isHighlighted = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
                color: isHighlighted ? AppTheme.primaryColor : null,
                fontSize: isHighlighted ? 16 : 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
