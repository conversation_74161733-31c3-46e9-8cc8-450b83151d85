/// نموذج المستخدم
/// User Model
class User {
  final int? id;
  final String name;
  final String? email;
  final String? role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    this.id,
    required this.name,
    this.email,
    this.role = 'محاسب',
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] as int?,
      name: map['name'] as String,
      email: map['email'] as String?,
      role: map['role'] as String? ?? 'محاسب',
      isActive: (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Copy with method for updates
  User copyWith({
    int? id,
    String? name,
    String? email,
    String? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Validation method
  List<String> validate() {
    List<String> errors = [];

    if (name.trim().isEmpty) {
      errors.add('اسم المستخدم مطلوب');
    }

    if (name.trim().length < 2) {
      errors.add('اسم المستخدم يجب أن يكون أكثر من حرفين');
    }

    if (email != null && email!.isNotEmpty) {
      if (!_isValidEmail(email!)) {
        errors.add('البريد الإلكتروني غير صحيح');
      }
    }

    return errors;
  }

  /// Email validation helper
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Get display name for UI
  String get displayName => name;

  /// Get role display name
  String get roleDisplayName => role ?? 'محاسب';

  @override
  String toString() {
    return 'User{id: $id, name: $name, email: $email, role: $role, isActive: $isActive}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
