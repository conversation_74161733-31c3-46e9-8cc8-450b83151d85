/// نماذج التقارير المالية المتقدمة
/// Advanced Financial Reports Models for Smart Ledger
library;

/// تعداد أنواع التحليلات المالية
enum AnalysisType {
  trend('trend', 'تحليل الاتجاهات'),
  ratio('ratio', 'تحليل النسب المالية'),
  variance('variance', 'تحليل التباين'),
  comparative('comparative', 'التحليل المقارن'),
  forecast('forecast', 'التنبؤ المالي'),
  performance('performance', 'تحليل الأداء'),
  liquidity('liquidity', 'تحليل السيولة'),
  profitability('profitability', 'تحليل الربحية');

  const AnalysisType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// تعداد فترات التقارير
enum ReportPeriod {
  daily('daily', 'يومي'),
  weekly('weekly', 'أسبوعي'),
  monthly('monthly', 'شهري'),
  quarterly('quarterly', 'ربع سنوي'),
  yearly('yearly', 'سنوي'),
  custom('custom', 'مخصص');

  const ReportPeriod(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// تعداد مستويات التفصيل
enum DetailLevel {
  summary('summary', 'ملخص'),
  detailed('detailed', 'تفصيلي'),
  comprehensive('comprehensive', 'شامل');

  const DetailLevel(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// نموذج التقرير المالي المتقدم
class AdvancedFinancialReport {
  final String id;
  final String title;
  final String? subtitle;
  final AnalysisType analysisType;
  final ReportPeriod period;
  final DetailLevel detailLevel;
  final DateTime fromDate;
  final DateTime toDate;
  final DateTime generatedAt;
  final String? generatedBy;
  final Map<String, dynamic> parameters;
  final List<ReportSection> sections;
  final Map<String, dynamic> summary;
  final List<FinancialMetric> metrics;
  final List<ChartData> chartData;
  final List<Insight> insights;
  final String? notes;

  AdvancedFinancialReport({
    String? id,
    required this.title,
    this.subtitle,
    required this.analysisType,
    required this.period,
    required this.detailLevel,
    required this.fromDate,
    required this.toDate,
    DateTime? generatedAt,
    this.generatedBy,
    this.parameters = const {},
    this.sections = const [],
    this.summary = const {},
    this.metrics = const [],
    this.chartData = const [],
    this.insights = const [],
    this.notes,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       generatedAt = generatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'analysis_type': analysisType.value,
      'period': period.value,
      'detail_level': detailLevel.value,
      'from_date': fromDate.toIso8601String(),
      'to_date': toDate.toIso8601String(),
      'generated_at': generatedAt.toIso8601String(),
      'generated_by': generatedBy,
      'parameters': parameters,
      'sections': sections.map((s) => s.toMap()).toList(),
      'summary': summary,
      'metrics': metrics.map((m) => m.toMap()).toList(),
      'chart_data': chartData.map((c) => c.toMap()).toList(),
      'insights': insights.map((i) => i.toMap()).toList(),
      'notes': notes,
    };
  }
}

/// نموذج قسم التقرير المتقدم
class ReportSection {
  final String id;
  final String title;
  final String? subtitle;
  final int order;
  final List<ReportLine> lines;
  final Map<String, dynamic> totals;
  final List<SubSection> subSections;
  final Map<String, dynamic> metadata;

  ReportSection({
    String? id,
    required this.title,
    this.subtitle,
    required this.order,
    this.lines = const [],
    this.totals = const {},
    this.subSections = const [],
    this.metadata = const {},
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'order': order,
      'lines': lines.map((l) => l.toMap()).toList(),
      'totals': totals,
      'sub_sections': subSections.map((s) => s.toMap()).toList(),
      'metadata': metadata,
    };
  }
}

/// نموذج القسم الفرعي
class SubSection {
  final String id;
  final String title;
  final List<ReportLine> lines;
  final Map<String, dynamic> totals;

  SubSection({
    String? id,
    required this.title,
    this.lines = const [],
    this.totals = const {},
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'lines': lines.map((l) => l.toMap()).toList(),
      'totals': totals,
    };
  }
}

/// نموذج سطر التقرير المتقدم
class ReportLine {
  final String id;
  final String accountCode;
  final String accountName;
  final double amount;
  final double? previousAmount;
  final double? budgetAmount;
  final double? percentage;
  final double? variance;
  final double? variancePercentage;
  final int level;
  final bool isTotal;
  final bool isBold;
  final bool isHighlighted;
  final String? notes;
  final Map<String, dynamic> details;
  final List<DrillDownData> drillDown;

  ReportLine({
    String? id,
    required this.accountCode,
    required this.accountName,
    required this.amount,
    this.previousAmount,
    this.budgetAmount,
    this.percentage,
    this.variance,
    this.variancePercentage,
    this.level = 0,
    this.isTotal = false,
    this.isBold = false,
    this.isHighlighted = false,
    this.notes,
    this.details = const {},
    this.drillDown = const [],
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'account_code': accountCode,
      'account_name': accountName,
      'amount': amount,
      'previous_amount': previousAmount,
      'budget_amount': budgetAmount,
      'percentage': percentage,
      'variance': variance,
      'variance_percentage': variancePercentage,
      'level': level,
      'is_total': isTotal,
      'is_bold': isBold,
      'is_highlighted': isHighlighted,
      'notes': notes,
      'details': details,
      'drill_down': drillDown.map((d) => d.toMap()).toList(),
    };
  }
}

/// نموذج بيانات التفصيل
class DrillDownData {
  final String id;
  final String description;
  final double amount;
  final DateTime date;
  final String? reference;

  DrillDownData({
    String? id,
    required this.description,
    required this.amount,
    required this.date,
    this.reference,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'description': description,
      'amount': amount,
      'date': date.toIso8601String(),
      'reference': reference,
    };
  }
}

/// نموذج المؤشر المالي
class FinancialMetric {
  final String id;
  final String name;
  final String category;
  final double value;
  final double? previousValue;
  final double? targetValue;
  final String unit;
  final MetricTrend trend;
  final MetricStatus status;
  final String? description;
  final String? formula;

  FinancialMetric({
    String? id,
    required this.name,
    required this.category,
    required this.value,
    this.previousValue,
    this.targetValue,
    this.unit = '',
    this.trend = MetricTrend.stable,
    this.status = MetricStatus.normal,
    this.description,
    this.formula,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  double get changePercentage {
    if (previousValue == null || previousValue == 0) return 0;
    return ((value - previousValue!) / previousValue!) * 100;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'value': value,
      'previous_value': previousValue,
      'target_value': targetValue,
      'unit': unit,
      'trend': trend.value,
      'status': status.value,
      'description': description,
      'formula': formula,
    };
  }
}

/// تعداد اتجاه المؤشر
enum MetricTrend {
  up('up', 'صاعد'),
  down('down', 'هابط'),
  stable('stable', 'مستقر');

  const MetricTrend(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// تعداد حالة المؤشر
enum MetricStatus {
  excellent('excellent', 'ممتاز'),
  good('good', 'جيد'),
  normal('normal', 'عادي'),
  warning('warning', 'تحذير'),
  critical('critical', 'حرج');

  const MetricStatus(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// نموذج بيانات المخطط
class ChartData {
  final String id;
  final String title;
  final ChartType type;
  final List<DataPoint> dataPoints;
  final Map<String, dynamic> options;

  ChartData({
    String? id,
    required this.title,
    required this.type,
    this.dataPoints = const [],
    this.options = const {},
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'type': type.value,
      'data_points': dataPoints.map((d) => d.toMap()).toList(),
      'options': options,
    };
  }
}

/// تعداد أنواع المخططات
enum ChartType {
  line('line', 'خطي'),
  bar('bar', 'أعمدة'),
  pie('pie', 'دائري'),
  area('area', 'منطقة'),
  scatter('scatter', 'نقطي'),
  combo('combo', 'مختلط');

  const ChartType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// نموذج نقطة البيانات
class DataPoint {
  final String label;
  final double value;
  final DateTime? date;
  final String? category;
  final Map<String, dynamic> metadata;

  DataPoint({
    required this.label,
    required this.value,
    this.date,
    this.category,
    this.metadata = const {},
  });

  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'value': value,
      'date': date?.toIso8601String(),
      'category': category,
      'metadata': metadata,
    };
  }
}

/// نموذج الرؤى والتحليلات
class Insight {
  final String id;
  final String title;
  final String description;
  final InsightType type;
  final InsightPriority priority;
  final double? impact;
  final List<String> recommendations;
  final Map<String, dynamic> data;

  Insight({
    String? id,
    required this.title,
    required this.description,
    required this.type,
    this.priority = InsightPriority.medium,
    this.impact,
    this.recommendations = const [],
    this.data = const {},
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.value,
      'priority': priority.value,
      'impact': impact,
      'recommendations': recommendations,
      'data': data,
    };
  }
}

/// تعداد أنواع الرؤى
enum InsightType {
  opportunity('opportunity', 'فرصة'),
  risk('risk', 'مخاطرة'),
  trend('trend', 'اتجاه'),
  anomaly('anomaly', 'شذوذ'),
  recommendation('recommendation', 'توصية');

  const InsightType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// تعداد أولوية الرؤى
enum InsightPriority {
  low('low', 'منخفضة'),
  medium('medium', 'متوسطة'),
  high('high', 'عالية'),
  critical('critical', 'حرجة');

  const InsightPriority(this.value, this.arabicName);
  final String value;
  final String arabicName;
}
