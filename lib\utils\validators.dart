/// مجموعة من الفاليديتورز المستخدمة في التطبيق
class Validators {
  /// التحقق من أن الحقل مطلوب وغير فارغ
  static String? required(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'هذا الحقل مطلوب';
    }
    return null;
  }

  /// التحقق من صحة البريد الإلكتروني
  static String? email(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }

  /// التحقق من رقم الهاتف
  static String? phone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    final phoneRegex = RegExp(r'^[0-9+\-\s()]+$');
    if (!phoneRegex.hasMatch(value.trim()) || value.trim().length < 10) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  /// التحقق من الأرقام الموجبة
  static String? positiveNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'هذا الحقل مطلوب';
    }

    final number = double.tryParse(value.trim());
    if (number == null) {
      return 'يجب أن يكون رقماً صحيحاً';
    }

    if (number <= 0) {
      return 'يجب أن يكون أكبر من صفر';
    }

    return null;
  }

  /// التحقق من الأرقام غير السالبة
  static String? nonNegativeNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    final number = double.tryParse(value.trim());
    if (number == null) {
      return 'يجب أن يكون رقماً صحيحاً';
    }

    if (number < 0) {
      return 'لا يمكن أن يكون سالباً';
    }

    return null;
  }

  /// التحقق من الأرقام الصحيحة الموجبة
  static String? positiveInteger(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'هذا الحقل مطلوب';
    }

    final number = int.tryParse(value.trim());
    if (number == null) {
      return 'يجب أن يكون رقماً صحيحاً';
    }

    if (number <= 0) {
      return 'يجب أن يكون أكبر من صفر';
    }

    return null;
  }

  /// التحقق من الحد الأدنى لطول النص
  static String? Function(String?) minLength(int minLength) {
    return (String? value) {
      if (value == null || value.trim().isEmpty) {
        return 'هذا الحقل مطلوب';
      }

      if (value.trim().length < minLength) {
        return 'يجب أن يكون على الأقل $minLength أحرف';
      }

      return null;
    };
  }

  /// التحقق من الحد الأقصى لطول النص
  static String? Function(String?) maxLength(int maxLength) {
    return (String? value) {
      if (value != null && value.trim().length > maxLength) {
        return 'يجب أن يكون أقل من $maxLength حرف';
      }

      return null;
    };
  }

  /// التحقق من تطابق كلمة المرور
  static String? Function(String?) confirmPassword(String password) {
    return (String? value) {
      if (value == null || value.trim().isEmpty) {
        return 'تأكيد كلمة المرور مطلوب';
      }

      if (value.trim() != password.trim()) {
        return 'كلمة المرور غير متطابقة';
      }

      return null;
    };
  }

  /// التحقق من قوة كلمة المرور
  static String? strongPassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'كلمة المرور مطلوبة';
    }

    if (value.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }

    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على حرف كبير';
    }

    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على حرف صغير';
    }

    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'كلمة المرور يجب أن تحتوي على رقم';
    }

    return null;
  }

  /// التحقق من الرقم الضريبي السعودي
  static String? saudiTaxNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    // الرقم الضريبي السعودي يجب أن يكون 15 رقم
    if (value.trim().length != 15) {
      return 'الرقم الضريبي يجب أن يكون 15 رقم';
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
      return 'الرقم الضريبي يجب أن يحتوي على أرقام فقط';
    }

    return null;
  }

  /// التحقق من رقم السجل التجاري السعودي
  static String? saudiCommercialRegister(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    // رقم السجل التجاري السعودي يجب أن يكون 10 أرقام
    if (value.trim().length != 10) {
      return 'رقم السجل التجاري يجب أن يكون 10 أرقام';
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
      return 'رقم السجل التجاري يجب أن يحتوي على أرقام فقط';
    }

    return null;
  }

  /// التحقق من رقم الهوية السعودي
  static String? saudiNationalId(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }

    // رقم الهوية السعودي يجب أن يكون 10 أرقام
    if (value.trim().length != 10) {
      return 'رقم الهوية يجب أن يكون 10 أرقام';
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
      return 'رقم الهوية يجب أن يحتوي على أرقام فقط';
    }

    // التحقق من صحة رقم الهوية باستخدام خوارزمية Luhn
    return _validateSaudiId(value.trim()) ? null : 'رقم الهوية غير صحيح';
  }

  /// التحقق من صحة رقم الهوية السعودي باستخدام خوارزمية Luhn
  static bool _validateSaudiId(String id) {
    if (id.length != 10) return false;

    int sum = 0;
    for (int i = 0; i < 9; i++) {
      int digit = int.parse(id[i]);
      if (i % 2 == 0) {
        digit *= 2;
        if (digit > 9) {
          digit = digit ~/ 10 + digit % 10;
        }
      }
      sum += digit;
    }

    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == int.parse(id[9]);
  }

  /// دمج عدة فاليديتورز
  static String? Function(String?) combine(
    List<String? Function(String?)> validators,
  ) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }
}
