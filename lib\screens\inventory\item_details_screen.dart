import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/item.dart';
import '../../services/item_service.dart';
import '../../theme/app_theme.dart';
import 'add_item_screen.dart';

class ItemDetailsScreen extends StatefulWidget {
  final Item item;

  const ItemDetailsScreen({super.key, required this.item});

  @override
  State<ItemDetailsScreen> createState() => _ItemDetailsScreenState();
}

class _ItemDetailsScreenState extends State<ItemDetailsScreen>
    with SingleTickerProviderStateMixin {
  final ItemService _itemService = ItemService();
  late TabController _tabController;

  Item? _currentItem;
  List<ItemMovement> _movements = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _currentItem = widget.item;
    _loadItemMovements();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadItemMovements() async {
    setState(() => _isLoading = true);

    final result = await _itemService.getItemMovements(_currentItem!.id!);
    if (result.isSuccess && result.data != null) {
      setState(() {
        _movements = result.data!;
        _isLoading = false;
      });
    } else {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _refreshItem() async {
    final result = await _itemService.getItemById(_currentItem!.id!);
    if (result.isSuccess && result.data != null) {
      setState(() {
        _currentItem = result.data!;
      });
    }
  }

  void _handleEditComplete() {
    if (mounted) {
      Navigator.pop(context, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(_currentItem!.name),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.push<bool>(
                context,
                MaterialPageRoute(
                  builder: (context) => AddItemScreen(item: _currentItem),
                ),
              );
              if (result == true) {
                await _refreshItem();
                // Use a separate method to handle navigation
                _handleEditComplete();
              }
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'التفاصيل', icon: Icon(Icons.info)),
            Tab(text: 'حركات المخزون', icon: Icon(Icons.history)),
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDetailsTab(),
          _buildMovementsTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          // بطاقة المعلومات الأساسية
          _buildInfoCard('المعلومات الأساسية', Icons.info_outline, [
            _buildInfoRow('الرمز', _currentItem!.code),
            _buildInfoRow('الاسم', _currentItem!.name),
            if (_currentItem!.description != null)
              _buildInfoRow('الوصف', _currentItem!.description!),
            _buildInfoRow('وحدة القياس', _currentItem!.unit),
            if (_currentItem!.category != null)
              _buildInfoRow('الفئة', _currentItem!.category!),
            if (_currentItem!.barcode != null)
              _buildInfoRow('الباركود', _currentItem!.barcode!),
            _buildInfoRow(
              'الحالة',
              _currentItem!.isActive ? 'نشط' : 'غير نشط',
              valueColor: _currentItem!.isActive ? Colors.green : Colors.red,
            ),
          ]),

          const SizedBox(height: AppTheme.spacingMedium),

          // بطاقة الأسعار
          _buildInfoCard('الأسعار', Icons.attach_money, [
            _buildInfoRow(
              'سعر التكلفة',
              '${_currentItem!.costPrice.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'سعر البيع',
              '${_currentItem!.sellingPrice.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'هامش الربح',
              '${_currentItem!.profitMargin.toStringAsFixed(1)}%',
              valueColor: _currentItem!.profitMargin > 0
                  ? Colors.green
                  : Colors.red,
            ),
            _buildInfoRow(
              'الربح المتوقع',
              '${_currentItem!.profitAmount.toStringAsFixed(2)} ر.س',
              valueColor: _currentItem!.profitAmount > 0
                  ? Colors.green
                  : Colors.red,
            ),
          ]),

          const SizedBox(height: AppTheme.spacingMedium),

          // بطاقة المخزون
          _buildInfoCard('المخزون', Icons.warehouse, [
            _buildInfoRow(
              'المخزون الحالي',
              '${_currentItem!.currentStock.toStringAsFixed(0)} ${_currentItem!.unit}',
              valueColor: _currentItem!.isLowStock ? Colors.red : Colors.green,
            ),
            if (_currentItem!.minStockLevel != null)
              _buildInfoRow(
                'الحد الأدنى',
                '${_currentItem!.minStockLevel!.toStringAsFixed(0)} ${_currentItem!.unit}',
              ),
            if (_currentItem!.maxStockLevel != null)
              _buildInfoRow(
                'الحد الأقصى',
                '${_currentItem!.maxStockLevel!.toStringAsFixed(0)} ${_currentItem!.unit}',
              ),
            _buildInfoRow(
              'حالة المخزون',
              _currentItem!.stockStatus.displayName,
              valueColor: _getStockStatusColor(_currentItem!.stockStatus),
            ),
            _buildInfoRow(
              'قيمة المخزون',
              '${_currentItem!.stockValue.toStringAsFixed(2)} ر.س',
            ),
          ]),

          const SizedBox(height: AppTheme.spacingMedium),

          // بطاقة التواريخ
          _buildInfoCard('التواريخ', Icons.schedule, [
            _buildInfoRow(
              'تاريخ الإنشاء',
              _formatDate(_currentItem!.createdAt),
            ),
            _buildInfoRow('آخر تحديث', _formatDate(_currentItem!.updatedAt)),
          ]),
        ],
      ),
    );
  }

  Widget _buildMovementsTab() {
    return Column(
      children: [
        // إحصائيات سريعة
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Row(
            children: [
              Expanded(
                child: _buildQuickStat(
                  'إجمالي الحركات',
                  _movements.length.toString(),
                  Icons.swap_horiz,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: _buildQuickStat(
                  'حركات الإدخال',
                  _movements
                      .where((m) => m.movementType == 'in')
                      .length
                      .toString(),
                  Icons.arrow_downward,
                  Colors.green,
                ),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: _buildQuickStat(
                  'حركات الإخراج',
                  _movements
                      .where((m) => m.movementType == 'out')
                      .length
                      .toString(),
                  Icons.arrow_upward,
                  Colors.red,
                ),
              ),
            ],
          ),
        ),

        // قائمة الحركات
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _movements.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.history, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: AppTheme.spacingMedium),
                      Text(
                        'لا توجد حركات مخزون',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                )
              : AnimationLimiter(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingMedium,
                    ),
                    itemCount: _movements.length,
                    itemBuilder: (context, index) {
                      return AnimationConfiguration.staggeredList(
                        position: index,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: _buildMovementCard(_movements[index]),
                          ),
                        ),
                      );
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          // إحصائيات المخزون
          _buildInfoCard('إحصائيات المخزون', Icons.analytics, [
            _buildInfoRow(
              'قيمة المخزون الحالي',
              '${_currentItem!.stockValue.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'متوسط سعر التكلفة',
              '${_currentItem!.costPrice.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'القيمة المتوقعة للبيع',
              '${(_currentItem!.currentStock * _currentItem!.sellingPrice).toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'الربح المتوقع من المخزون',
              '${((_currentItem!.currentStock * _currentItem!.sellingPrice) - _currentItem!.stockValue).toStringAsFixed(2)} ر.س',
              valueColor: Colors.green,
            ),
          ]),

          const SizedBox(height: AppTheme.spacingMedium),

          // إحصائيات الحركات
          if (_movements.isNotEmpty) ...[
            _buildInfoCard('إحصائيات الحركات', Icons.trending_up, [
              _buildInfoRow(
                'إجمالي الكمية المدخلة',
                '${_movements.where((m) => m.movementType == 'in').fold(0.0, (sum, m) => sum + m.quantity).toStringAsFixed(0)} ${_currentItem!.unit}',
              ),
              _buildInfoRow(
                'إجمالي الكمية المخرجة',
                '${_movements.where((m) => m.movementType == 'out').fold(0.0, (sum, m) => sum + m.quantity).toStringAsFixed(0)} ${_currentItem!.unit}',
              ),
              _buildInfoRow(
                'آخر حركة',
                _movements.isNotEmpty
                    ? _formatDate(_movements.first.date)
                    : 'لا توجد حركات',
              ),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: valueColor ?? Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMovementCard(ItemMovement movement) {
    final isIncoming = movement.movementType == 'in';
    final color = isIncoming ? Colors.green : Colors.red;
    final icon = isIncoming ? Icons.arrow_downward : Icons.arrow_upward;

    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: AppTheme.spacingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    movement.reference,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (movement.description != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      movement.description!,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(movement.date),
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isIncoming ? '+' : '-'}${movement.quantity.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontSize: 16,
                  ),
                ),
                Text(
                  'الرصيد: ${movement.balanceAfter.toStringAsFixed(0)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStockStatusColor(StockStatus status) {
    switch (status) {
      case StockStatus.normal:
        return Colors.green;
      case StockStatus.lowStock:
        return Colors.orange;
      case StockStatus.outOfStock:
        return Colors.red;
      case StockStatus.overStock:
        return Colors.blue;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
