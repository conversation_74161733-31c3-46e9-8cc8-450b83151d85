import 'package:flutter/material.dart';
import '../models/account.dart';
import 'package:intl/intl.dart';

class AccountTreeWidget extends StatefulWidget {
  final List<Account> accounts;
  final Function(Account) onAccountTap;
  final Function(Account)? onAccountEdit;
  final Function(Account)? onAccountDelete;
  final bool showBalance;

  const AccountTreeWidget({
    super.key,
    required this.accounts,
    required this.onAccountTap,
    this.onAccountEdit,
    this.onAccountDelete,
    this.showBalance = true,
  });

  @override
  State<AccountTreeWidget> createState() => _AccountTreeWidgetState();
}

class _AccountTreeWidgetState extends State<AccountTreeWidget> {
  final Set<String> _expandedNodes = <String>{};

  @override
  Widget build(BuildContext context) {
    final accountTree = _buildAccountTree(widget.accounts);

    if (accountTree.isEmpty) {
      return const Center(child: Text('لا توجد حسابات لعرضها'));
    }

    return ListView(
      padding: const EdgeInsets.all(8.0),
      children: accountTree.map((node) => _buildTreeNode(node, 0)).toList(),
    );
  }

  List<AccountTreeNode> _buildAccountTree(List<Account> accounts) {
    // Group accounts by parent
    Map<int?, List<Account>> accountsByParent = {};
    for (Account account in accounts) {
      accountsByParent.putIfAbsent(account.parentId, () => []).add(account);
    }

    // Build tree starting from root accounts (parentId == null)
    List<AccountTreeNode> buildNodes(int? parentId) {
      List<Account> children = accountsByParent[parentId] ?? [];
      children.sort((a, b) => a.code.compareTo(b.code));

      return children.map((account) {
        List<AccountTreeNode> childNodes = buildNodes(account.id);
        return AccountTreeNode(account: account, children: childNodes);
      }).toList();
    }

    return buildNodes(null);
  }

  Widget _buildTreeNode(AccountTreeNode node, int level) {
    final hasChildren = node.children.isNotEmpty;
    final isExpanded = _expandedNodes.contains(node.account.code);
    final currencyFormatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 2,
    );

    return Column(
      children: [
        Card(
          margin: EdgeInsets.only(
            left: level * 16.0,
            right: 8.0,
            top: 4.0,
            bottom: 4.0,
          ),
          child: ListTile(
            leading: hasChildren
                ? IconButton(
                    icon: Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                    ),
                    onPressed: () {
                      setState(() {
                        if (isExpanded) {
                          _expandedNodes.remove(node.account.code);
                        } else {
                          _expandedNodes.add(node.account.code);
                        }
                      });
                    },
                  )
                : const SizedBox(width: 48),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getAccountTypeColor(
                      node.account.accountType,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    node.account.code,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getAccountTypeColor(node.account.accountType),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    node.account.name,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  node.account.accountType.displayName,
                  style: TextStyle(
                    color: _getAccountTypeColor(node.account.accountType),
                    fontSize: 12,
                  ),
                ),
                if (widget.showBalance && node.account.balance != 0)
                  Text(
                    'الرصيد: ${currencyFormatter.format(node.account.balance)}',
                    style: TextStyle(
                      color: node.account.balance >= 0
                          ? Colors.green
                          : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
            trailing:
                widget.onAccountEdit != null || widget.onAccountDelete != null
                ? PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          widget.onAccountEdit?.call(node.account);
                          break;
                        case 'delete':
                          widget.onAccountDelete?.call(node.account);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (widget.onAccountEdit != null)
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                      if (widget.onAccountDelete != null)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  )
                : null,
            onTap: () => widget.onAccountTap(node.account),
          ),
        ),
        if (hasChildren && isExpanded)
          ...node.children.map((child) => _buildTreeNode(child, level + 1)),
      ],
    );
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.asset:
        return Colors.green;
      case AccountType.liability:
        return Colors.red;
      case AccountType.equity:
        return Colors.blue;
      case AccountType.revenue:
        return Colors.orange;
      case AccountType.expense:
        return Colors.purple;
    }
  }
}

class AccountTreeNode {
  final Account account;
  final List<AccountTreeNode> children;

  AccountTreeNode({required this.account, required this.children});
}
