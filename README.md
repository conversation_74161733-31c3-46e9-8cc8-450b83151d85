# 📊 Smart Ledger - نظام المحاسبة الذكي

<div align="center">

![Smart Ledger Logo](assets/images/logo.png)

**نظام محاسبة شامل ومتطور مبني بتقنية Flutter**

[![Flutter](https://img.shields.io/badge/Flutter-3.8.1+-blue.svg)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-3.0+-blue.svg)](https://dart.dev/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Android%20%7C%20Windows-lightgrey.svg)](https://flutter.dev/)

</div>

## 🌟 **نظرة عامة**

Smart Ledger هو نظام محاسبة شامل ومتطور مصمم خصيصاً للشركات الصغيرة والمتوسطة. يوفر النظام جميع الأدوات المحاسبية الأساسية والمتقدمة مع واجهة مستخدم عصرية وسهلة الاستخدام.

### ✨ **المميزات الرئيسية**

- 🏢 **نظام محاسبة شامل** - دليل حسابات مرن وقيود محاسبية تلقائية
- 📦 **إدارة المخزون** - تتبع المخزون مع طرق التكلفة المختلفة (FIFO, LIFO, المتوسط المرجح)
- 👥 **إدارة العملاء والموردين** - نظام CRM مدمج
- 📄 **نظام الفواتير** - فواتير مبيعات ومشتريات احترافية
- 🏗️ **إدارة المشاريع** - تتبع تكاليف وإيرادات المشاريع
- 💰 **الأصول الثابتة** - إدارة الأصول والاستهلاك التلقائي
- 👨‍💼 **الموارد البشرية** - إدارة الموظفين والرواتب
- 💱 **العملات المتعددة** - دعم كامل للعملات المختلفة
- 📊 **التقارير المالية** - تقارير شاملة ومخططات بيانية
- 🎨 **واجهة عصرية** - تصميم Material Design 3 مع حركات متطورة

### 🚀 **التقنيات المستخدمة**

- **Flutter 3.8.1+** - إطار العمل الأساسي
- **SQLite** - قاعدة البيانات المحلية
- **Provider** - إدارة الحالة
- **Material Design 3** - نظام التصميم
- **Google Fonts** - خطوط Cairo العربية
- **FL Chart** - المخططات البيانية
- **PDF Generation** - إنشاء التقارير

## 📱 **المنصات المدعومة**

- ✅ **Android** (API 21+)
- ✅ **Windows** (Windows 10+)
- 🔄 **iOS** (قيد التطوير)
- 🔄 **macOS** (قيد التطوير)
- 🔄 **Linux** (قيد التطوير)

## 🏗️ **هيكل المشروع**

```
lib/
├── 📁 database/           # قاعدة البيانات والمخططات
├── 📁 models/            # نماذج البيانات
├── 📁 dao/               # طبقة الوصول للبيانات
├── 📁 services/          # الخدمات والمنطق التجاري
├── 📁 screens/           # شاشات التطبيق
├── 📁 widgets/           # المكونات المخصصة
├── 📁 theme/             # نظام الألوان والتصميم
├── 📁 utils/             # الأدوات المساعدة
└── 📄 main.dart          # نقطة البداية
```

## 🚀 **البدء السريع**

### المتطلبات الأساسية

- Flutter SDK 3.8.1 أو أحدث
- Dart SDK 3.0 أو أحدث
- Android Studio أو VS Code
- Git

### التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/smart_ledger.git
cd smart_ledger
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

### إعداد قاعدة البيانات

التطبيق يقوم بإنشاء قاعدة البيانات تلقائياً عند التشغيل الأول. لا حاجة لإعداد إضافي.

## 📚 **الوثائق**

### دليل المطور

- [📖 دليل البنية المعمارية](docs/ARCHITECTURE.md)
- [🗄️ دليل قاعدة البيانات](docs/DATABASE.md)
- [🎨 دليل التصميم](docs/UI_GUIDE.md)
- [🔧 دليل المساهمة](docs/CONTRIBUTING.md)

### دليل المستخدم

- [👤 دليل المستخدم الشامل](docs/USER_GUIDE.md)
- [📊 دليل التقارير](docs/REPORTS_GUIDE.md)
- [⚙️ دليل الإعدادات](docs/SETTINGS_GUIDE.md)

## 🧪 **الاختبارات**

```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبارات محددة
flutter test test/unit/
flutter test test/widget/
flutter test test/integration/
```

## 📊 **الأداء**

- ⚡ **سرعة الاستعلامات**: < 100ms
- 💾 **استهلاك الذاكرة**: < 150MB
- 🔋 **استهلاك البطارية**: محسن
- 📱 **حجم التطبيق**: ~25MB

## 🤝 **المساهمة**

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### خطوات المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👥 **الفريق**

- **المطور الرئيسي**: [اسمك هنا]
- **مصمم UI/UX**: [اسم المصمم]
- **مهندس قاعدة البيانات**: [اسم المهندس]

## 📞 **التواصل**

- 📧 **البريد الإلكتروني**: <EMAIL>
- 🌐 **الموقع الإلكتروني**: https://smartledger.com
- 💬 **Discord**: [رابط الخادم]
- 📱 **تليجرام**: [@smartledger_support]

## 🙏 **شكر وتقدير**

- Flutter Team لإطار العمل الرائع
- Material Design Team للتصميم الجميل
- مجتمع Flutter العربي للدعم المستمر

---

<div align="center">

**صُنع بـ ❤️ في العالم العربي**

[⭐ إذا أعجبك المشروع، لا تنس إعطاؤه نجمة!](https://github.com/your-username/smart_ledger)

</div>
