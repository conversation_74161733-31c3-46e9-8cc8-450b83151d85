# نظام العملات المتعددة - Smart Ledger
## Multi-Currency System Documentation

### نظرة عامة (Overview)

نظام العملات المتعددة في Smart Ledger هو نظام شامل ومتقدم يدعم التعامل مع عملات متعددة في جميع العمليات المحاسبية. يوفر النظام تحويل تلقائي للعملات، إدارة أسعار الصرف، وتقارير مالية متعددة العملات.

### الميزات الرئيسية (Key Features)

#### 1. إدارة العملات (Currency Management)
- دعم أكثر من 20 عملة عالمية
- تحديد العملة الأساسية للنظام
- إضافة وتعديل العملات المخصصة
- تفعيل وإلغاء تفعيل العملات

#### 2. إدارة أسعار الصرف (Exchange Rate Management)
- تحديث أسعار الصرف من مصادر خارجية
- حفظ أسعار الصرف التاريخية
- تحديث تلقائي لأسعار الصرف
- دعم أسعار الصرف المخصصة

#### 3. التحويل التلقائي (Automatic Conversion)
- تحويل تلقائي بين العملات
- حفظ المبالغ بالعملة الأصلية والعملة الأساسية
- تحديث تلقائي للمبالغ عند تغيير أسعار الصرف
- دعم التحويل المجمع للعمليات المتعددة

#### 4. التقارير متعددة العملات (Multi-Currency Reports)
- تقارير المبيعات بعملات متعددة
- تقارير المشتريات بعملات متعددة
- تقارير الأرباح والخسائر
- تقارير أسعار الصرف

### البنية التقنية (Technical Architecture)

#### النماذج (Models)

##### 1. Currency Model
```dart
class Currency {
  final int? id;
  final String code;           // رمز العملة (SAR, USD, EUR)
  final String nameEn;         // الاسم بالإنجليزية
  final String nameAr;         // الاسم بالعربية
  final String symbol;         // رمز العملة (﷼, $, €)
  final int decimalPlaces;     // عدد الخانات العشرية
  final bool isBaseCurrency;   // هل هي العملة الأساسية
  final bool isActive;         // هل العملة نشطة
}
```

##### 2. ExchangeRate Model
```dart
class ExchangeRate {
  final int? id;
  final String fromCurrency;   // العملة المصدر
  final String toCurrency;     // العملة الهدف
  final double rate;           // سعر الصرف
  final DateTime effectiveDate; // تاريخ السريان
  final String? source;        // مصدر السعر
}
```

##### 3. Enhanced Invoice Model
```dart
class Invoice {
  // الحقول الأساسية
  final String currencyCode;              // عملة الفاتورة
  final double exchangeRate;              // سعر الصرف
  final String baseCurrencyCode;          // العملة الأساسية
  
  // المبالغ بالعملة الأساسية
  final double baseCurrencySubtotal;      // المجموع الفرعي
  final double baseCurrencyTaxAmount;     // مبلغ الضريبة
  final double baseCurrencyDiscountAmount; // مبلغ الخصم
  final double baseCurrencyTotalAmount;   // المجموع الكلي
  final double baseCurrencyPaidAmount;    // المبلغ المدفوع
  
  // طرق مساعدة
  bool get isMultiCurrency;               // هل الفاتورة بعملة مختلفة
  double convertToBaseCurrency(double amount);
  double convertFromBaseCurrency(double amount);
  Invoice updateBaseCurrencyAmounts();
}
```

#### الخدمات (Services)

##### 1. CurrencyService
- إدارة العملات (CRUD)
- التحقق من صحة العملات
- تنسيق المبالغ حسب العملة

##### 2. ExchangeRateService
- إدارة أسعار الصرف
- تحديث الأسعار من مصادر خارجية
- حساب التحويلات

##### 3. CurrencyConversionService
- التحويل التلقائي للعملات
- تحديث الفواتير والقيود
- إدارة التحويلات المجمعة

##### 4. MultiCurrencyReportService
- إنشاء التقارير متعددة العملات
- تجميع البيانات حسب العملة
- تحويل التقارير للعملة المطلوبة

### قاعدة البيانات (Database Schema)

#### جدول العملات (currencies)
```sql
CREATE TABLE currencies (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  code TEXT UNIQUE NOT NULL,
  name_en TEXT NOT NULL,
  name_ar TEXT NOT NULL,
  symbol TEXT NOT NULL,
  decimal_places INTEGER NOT NULL DEFAULT 2,
  is_base_currency BOOLEAN NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
```

#### جدول أسعار الصرف (exchange_rates)
```sql
CREATE TABLE exchange_rates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  from_currency TEXT NOT NULL,
  to_currency TEXT NOT NULL,
  rate REAL NOT NULL,
  effective_date TEXT NOT NULL,
  source TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
```

#### تحديثات جدول الفواتير (invoices)
```sql
-- إضافة حقول العملات المتعددة
ALTER TABLE invoices ADD COLUMN currency_code TEXT NOT NULL DEFAULT 'SAR';
ALTER TABLE invoices ADD COLUMN exchange_rate REAL NOT NULL DEFAULT 1.0;
ALTER TABLE invoices ADD COLUMN base_currency_code TEXT NOT NULL DEFAULT 'SAR';
ALTER TABLE invoices ADD COLUMN base_currency_subtotal REAL NOT NULL DEFAULT 0.0;
ALTER TABLE invoices ADD COLUMN base_currency_tax_amount REAL NOT NULL DEFAULT 0.0;
ALTER TABLE invoices ADD COLUMN base_currency_discount_amount REAL NOT NULL DEFAULT 0.0;
ALTER TABLE invoices ADD COLUMN base_currency_total_amount REAL NOT NULL DEFAULT 0.0;
ALTER TABLE invoices ADD COLUMN base_currency_paid_amount REAL NOT NULL DEFAULT 0.0;
```

### واجهات المستخدم (User Interfaces)

#### 1. شاشة إدارة العملات
- عرض قائمة العملات
- إضافة وتعديل العملات
- تحديد العملة الأساسية
- تحديث أسعار الصرف

#### 2. شاشة التقارير متعددة العملات
- اختيار عملة العرض
- تقارير المبيعات والمشتريات
- تقارير الأرباح والخسائر
- تقارير أسعار الصرف

### تحسين الأداء (Performance Optimization)

#### ذاكرة التخزين المؤقت (Caching)
- تخزين مؤقت للعملات (1 ساعة)
- تخزين مؤقت لأسعار الصرف (15 دقيقة)
- تخزين مؤقت للتحويلات (5 دقائق)

#### التحميل المجمع (Batch Loading)
- تحديث أسعار الصرف بشكل مجمع
- تحويل متعدد للعملات
- تحسين استعلامات قاعدة البيانات

### الاختبارات (Testing)

#### اختبارات الوحدة (Unit Tests)
- اختبار خدمة التحويل
- اختبار نماذج العملات
- اختبار حسابات أسعار الصرف

#### اختبارات التكامل (Integration Tests)
- اختبار التحويل الكامل
- اختبار التقارير
- اختبار واجهات المستخدم

### الاستخدام (Usage Examples)

#### تحويل مبلغ بين عملتين
```dart
final conversionService = CurrencyConversionService();
final result = await conversionService.convertAmount(
  amount: 100.0,
  fromCurrency: 'USD',
  toCurrency: 'SAR',
);
print('${result.originalAmount} ${result.fromCurrency} = ${result.convertedAmount} ${result.toCurrency}');
```

#### تحديث فاتورة للعملة الأساسية
```dart
final updatedInvoice = await conversionService.convertInvoiceToBaseCurrency(invoice);
```

#### إنشاء تقرير مبيعات متعدد العملات
```dart
final reportService = MultiCurrencyReportService();
final report = await reportService.generateSalesReport(
  startDate: DateTime.now().subtract(Duration(days: 30)),
  endDate: DateTime.now(),
  displayCurrency: 'SAR',
);
```

### الصيانة والتطوير المستقبلي (Maintenance & Future Development)

#### التحديثات المطلوبة
- إضافة مزيد من مصادر أسعار الصرف
- تحسين خوارزميات التحويل
- إضافة تقارير أكثر تفصيلاً

#### المراقبة والتتبع
- مراقبة أداء التحويلات
- تتبع دقة أسعار الصرف
- مراقبة استخدام الذاكرة

### الأمان (Security)

#### حماية البيانات
- تشفير أسعار الصرف الحساسة
- التحقق من صحة مصادر الأسعار
- حماية من التلاعب في أسعار الصرف

#### التدقيق (Auditing)
- تسجيل جميع عمليات التحويل
- تتبع تغييرات أسعار الصرف
- مراجعة العمليات المالية

### الدعم الفني (Technical Support)

#### استكشاف الأخطاء
- رسائل خطأ واضحة ومفيدة
- سجلات مفصلة للعمليات
- أدوات تشخيص الأداء

#### التوثيق
- دليل المستخدم
- دليل المطور
- أمثلة عملية

---

تم تطوير هذا النظام بعناية فائقة لضمان الدقة والموثوقية في جميع العمليات المالية متعددة العملات.
