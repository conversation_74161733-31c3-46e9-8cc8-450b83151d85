import '../models/fixed_asset.dart';
import '../models/asset_depreciation.dart';
import '../database/fixed_asset_dao.dart';
import '../database/asset_depreciation_dao.dart';
import '../database/journal_entry_dao.dart';
import '../models/journal_entry.dart';
import '../utils/result.dart';

/// خدمة إدارة الأصول الثابتة
class FixedAssetService {
  final FixedAssetDao _assetDao = FixedAssetDao();
  final AssetDepreciationDao _depreciationDao = AssetDepreciationDao();
  final JournalEntryDao _journalEntryDao = JournalEntryDao();

  /// الحصول على جميع الأصول الثابتة
  Future<Result<List<FixedAsset>>> getAllFixedAssets() async {
    try {
      final assets = await _assetDao.getAllFixedAssets();
      return Result.success(assets);
    } catch (e) {
      return Result.error('خطأ في جلب الأصول الثابتة: ${e.toString()}');
    }
  }

  /// الحصول على أصل ثابت بالمعرف
  Future<Result<FixedAsset>> getFixedAssetById(int id) async {
    try {
      final asset = await _assetDao.getFixedAssetById(id);
      if (asset != null) {
        return Result.success(asset);
      } else {
        return Result.error('الأصل الثابت غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب الأصل الثابت: ${e.toString()}');
    }
  }

  /// الحصول على أصل ثابت بالرمز
  Future<Result<FixedAsset>> getFixedAssetByCode(String code) async {
    try {
      final asset = await _assetDao.getFixedAssetByCode(code);
      if (asset != null) {
        return Result.success(asset);
      } else {
        return Result.error('الأصل الثابت غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب الأصل الثابت: ${e.toString()}');
    }
  }

  /// إنشاء أصل ثابت جديد
  Future<Result<FixedAsset>> createFixedAsset(FixedAsset asset) async {
    try {
      // التحقق من عدم تكرار الرمز
      final existingAsset = await _assetDao.getFixedAssetByCode(asset.code);
      if (existingAsset != null) {
        return Result.error('رمز الأصل الثابت موجود مسبقاً');
      }

      // التحقق من صحة البيانات
      final validation = _validateFixedAsset(asset);
      if (!validation.isSuccess) {
        return validation;
      }

      final id = await _assetDao.insertFixedAsset(asset);
      final newAsset = asset.copyWith(id: id);

      // إنشاء جدول الاستهلاك
      await _generateDepreciationSchedule(newAsset);

      return Result.success(newAsset);
    } catch (e) {
      return Result.error('خطأ في إنشاء الأصل الثابت: ${e.toString()}');
    }
  }

  /// تحديث أصل ثابت
  Future<Result<FixedAsset>> updateFixedAsset(FixedAsset asset) async {
    try {
      if (asset.id == null) {
        return Result.error('معرف الأصل الثابت مطلوب للتحديث');
      }

      // التحقق من وجود الأصل
      final existingAsset = await _assetDao.getFixedAssetById(asset.id!);
      if (existingAsset == null) {
        return Result.error('الأصل الثابت غير موجود');
      }

      // التحقق من عدم تكرار الرمز (إذا تم تغييره)
      if (existingAsset.code != asset.code) {
        final duplicateAsset = await _assetDao.getFixedAssetByCode(asset.code);
        if (duplicateAsset != null) {
          return Result.error('رمز الأصل الثابت موجود مسبقاً');
        }
      }

      // التحقق من صحة البيانات
      final validation = _validateFixedAsset(asset);
      if (!validation.isSuccess) {
        return validation;
      }

      await _assetDao.updateFixedAsset(asset);

      // إعادة إنشاء جدول الاستهلاك إذا تغيرت المعاملات المهمة
      if (_shouldRegenerateSchedule(existingAsset, asset)) {
        await _depreciationDao.deleteAssetDepreciations(asset.id!);
        await _generateDepreciationSchedule(asset);
      }

      return Result.success(asset);
    } catch (e) {
      return Result.error('خطأ في تحديث الأصل الثابت: ${e.toString()}');
    }
  }

  /// حذف أصل ثابت
  Future<Result<void>> deleteFixedAsset(int id) async {
    try {
      // التحقق من وجود الأصل
      final asset = await _assetDao.getFixedAssetById(id);
      if (asset == null) {
        return Result.error('الأصل الثابت غير موجود');
      }

      // فحص المعاملات المرتبطة بالأصل
      final hasTransactions = await _assetDao.hasTransactions(id);
      if (hasTransactions) {
        return Result.error('لا يمكن حذف الأصل الثابت لوجود معاملات مرتبطة به');
      }

      // حذف جدول الاستهلاك أولاً
      await _depreciationDao.deleteAssetDepreciations(id);

      // حذف الأصل
      await _assetDao.deleteFixedAsset(id);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف الأصل الثابت: ${e.toString()}');
    }
  }

  /// البحث في الأصول الثابتة
  Future<Result<List<FixedAsset>>> searchFixedAssets(String query) async {
    try {
      final assets = await _assetDao.searchFixedAssets(query);
      return Result.success(assets);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  /// الحصول على الأصول الثابتة حسب الفئة
  Future<Result<List<FixedAsset>>> getFixedAssetsByCategory(
    AssetCategory category,
  ) async {
    try {
      final assets = await _assetDao.getFixedAssetsByCategory(category);
      return Result.success(assets);
    } catch (e) {
      return Result.error('خطأ في جلب الأصول الثابتة: ${e.toString()}');
    }
  }

  /// الحصول على الأصول الثابتة النشطة
  Future<Result<List<FixedAsset>>> getActiveFixedAssets() async {
    try {
      final assets = await _assetDao.getActiveFixedAssets();
      return Result.success(assets);
    } catch (e) {
      return Result.error('خطأ في جلب الأصول الثابتة النشطة: ${e.toString()}');
    }
  }

  /// الحصول على رمز الأصل التالي
  Future<Result<String>> getNextAssetCode() async {
    try {
      final code = await _assetDao.getNextAssetCode();
      return Result.success(code);
    } catch (e) {
      return Result.error('خطأ في توليد رمز الأصل: ${e.toString()}');
    }
  }

  /// تغيير حالة الأصل
  Future<Result<void>> changeAssetStatus(int id, AssetStatus status) async {
    try {
      final asset = await _assetDao.getFixedAssetById(id);
      if (asset == null) {
        return Result.error('الأصل الثابت غير موجود');
      }

      final updatedAsset = asset.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );

      await _assetDao.updateFixedAsset(updatedAsset);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تغيير حالة الأصل: ${e.toString()}');
    }
  }

  /// الحصول على ملخص الأصول الثابتة
  Future<Result<FixedAssetsSummary>> getFixedAssetsSummary() async {
    try {
      final summary = await _assetDao.getFixedAssetsSummary();
      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب ملخص الأصول الثابتة: ${e.toString()}');
    }
  }

  /// الحصول على جدول استهلاك أصل معين
  Future<Result<List<AssetDepreciation>>> getAssetDepreciationSchedule(
    int assetId,
  ) async {
    try {
      final depreciations = await _depreciationDao.getAssetDepreciations(
        assetId,
      );
      return Result.success(depreciations);
    } catch (e) {
      return Result.error('خطأ في جلب جدول الاستهلاك: ${e.toString()}');
    }
  }

  /// حساب الاستهلاك الشهري
  Future<Result<void>> calculateMonthlyDepreciation(int year, int month) async {
    try {
      final assets = await _assetDao.getActiveFixedAssets();

      for (final asset in assets) {
        // التحقق من عدم وجود استهلاك لهذا الشهر
        final existingDepreciation = await _depreciationDao
            .getDepreciationByAssetAndMonth(asset.id!, year, month);

        if (existingDepreciation == null) {
          // حساب الاستهلاك
          final depreciationAmount = _calculateMonthlyDepreciationAmount(
            asset,
            year,
            month,
          );

          if (depreciationAmount > 0) {
            final totalDepreciation = await _depreciationDao
                .getTotalDepreciationForAsset(asset.id!);

            final accumulatedDepreciation =
                totalDepreciation + depreciationAmount;
            final bookValue = asset.purchasePrice - accumulatedDepreciation;

            final depreciation = AssetDepreciation(
              assetId: asset.id!,
              year: year,
              month: month,
              depreciationDate: DateTime(year, month, DateTime.now().day),
              depreciationAmount: depreciationAmount,
              accumulatedDepreciation: accumulatedDepreciation,
              bookValue: bookValue,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            await _depreciationDao.insertDepreciation(depreciation);
          }
        }
      }

      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حساب الاستهلاك الشهري: ${e.toString()}');
    }
  }

  /// إنشاء قيد استهلاك شهري
  Future<Result<JournalEntry>> createDepreciationJournalEntry(
    int year,
    int month,
  ) async {
    try {
      final depreciations = await _depreciationDao.getDepreciationsByMonth(
        year,
        month,
      );

      if (depreciations.isEmpty) {
        return Result.error('لا توجد سجلات استهلاك لهذا الشهر');
      }

      final totalDepreciation = depreciations.fold(
        0.0,
        (sum, dep) => sum + dep.depreciationAmount,
      );

      if (totalDepreciation <= 0) {
        return Result.error('لا يوجد استهلاك للترحيل');
      }

      // التحقق من عدم وجود قيد استهلاك لهذا الشهر مسبقاً
      final entryNumber = await _generateDepreciationEntryNumber(year, month);
      final existingEntry = await _journalEntryDao.getJournalEntryByNumber(
        entryNumber,
      );
      if (existingEntry != null) {
        return Result.error('قيد الاستهلاك لهذا الشهر موجود مسبقاً');
      }

      // إنشاء القيد المحاسبي
      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        date: DateTime(year, month, DateTime.now().day),
        description: 'استهلاك الأصول الثابتة - $month/$year',
        totalDebit: totalDepreciation,
        totalCredit: totalDepreciation,
        isBalanced: true,
        isPosted: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        lines: [], // سيتم إضافة السطور لاحقاً في تطوير مستقبلي
      );

      // حفظ القيد في قاعدة البيانات
      final entryId = await _journalEntryDao.insertJournalEntry(journalEntry);
      final savedEntry = journalEntry.copyWith(id: entryId);

      return Result.success(savedEntry);
    } catch (e) {
      return Result.error('خطأ في إنشاء قيد الاستهلاك: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات الأصل الثابت
  Result<FixedAsset> _validateFixedAsset(FixedAsset asset) {
    if (asset.code.trim().isEmpty) {
      return Result.error('رمز الأصل الثابت مطلوب');
    }

    if (asset.name.trim().isEmpty) {
      return Result.error('اسم الأصل الثابت مطلوب');
    }

    if (asset.purchasePrice <= 0) {
      return Result.error('سعر الشراء يجب أن يكون أكبر من صفر');
    }

    if (asset.usefulLifeYears <= 0) {
      return Result.error('العمر الإنتاجي يجب أن يكون أكبر من صفر');
    }

    if (asset.salvageValue != null && asset.salvageValue! < 0) {
      return Result.error('القيمة المتبقية لا يمكن أن تكون سالبة');
    }

    if (asset.salvageValue != null &&
        asset.salvageValue! >= asset.purchasePrice) {
      return Result.error('القيمة المتبقية يجب أن تكون أقل من سعر الشراء');
    }

    return Result.success(asset);
  }

  /// إنشاء جدول الاستهلاك
  Future<void> _generateDepreciationSchedule(FixedAsset asset) async {
    final schedule = await _depreciationDao.generateDepreciationSchedule(asset);

    for (final depreciation in schedule) {
      await _depreciationDao.insertDepreciation(depreciation);
    }
  }

  /// التحقق من ضرورة إعادة إنشاء جدول الاستهلاك
  bool _shouldRegenerateSchedule(FixedAsset oldAsset, FixedAsset newAsset) {
    return oldAsset.purchasePrice != newAsset.purchasePrice ||
        oldAsset.salvageValue != newAsset.salvageValue ||
        oldAsset.usefulLifeYears != newAsset.usefulLifeYears ||
        oldAsset.depreciationMethod != newAsset.depreciationMethod ||
        oldAsset.purchaseDate != newAsset.purchaseDate;
  }

  /// حساب مبلغ الاستهلاك الشهري
  double _calculateMonthlyDepreciationAmount(
    FixedAsset asset,
    int year,
    int month,
  ) {
    final depreciationDate = DateTime(year, month, 1);
    final monthsFromPurchase = _getMonthsBetween(
      asset.purchaseDate,
      depreciationDate,
    );

    if (monthsFromPurchase < 0) return 0.0; // لم يبدأ الاستهلاك بعد

    final yearsElapsed = (monthsFromPurchase + 1) / 12.0;
    final totalDepreciationSoFar = asset.calculateDepreciation(yearsElapsed);
    final previousDepreciation = asset.calculateDepreciation(
      monthsFromPurchase / 12.0,
    );

    return totalDepreciationSoFar - previousDepreciation;
  }

  /// حساب عدد الأشهر بين تاريخين
  int _getMonthsBetween(DateTime start, DateTime end) {
    return (end.year - start.year) * 12 + end.month - start.month;
  }

  /// توليد رقم قيد الاستهلاك
  Future<String> _generateDepreciationEntryNumber(int year, int month) async {
    return 'DEP-$year-${month.toString().padLeft(2, '0')}-${DateTime.now().millisecondsSinceEpoch}';
  }
}
