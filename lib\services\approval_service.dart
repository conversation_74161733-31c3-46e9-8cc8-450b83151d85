/// خدمة نظام الموافقات
/// Approval Service for Smart Ledger
library;

import '../models/approval_workflow.dart';
import '../database/approval_dao.dart';
import '../utils/result.dart';

class ApprovalService {
  static final ApprovalService _instance = ApprovalService._internal();
  factory ApprovalService() => _instance;
  ApprovalService._internal();

  final ApprovalDao _approvalDao = ApprovalDao();

  /// Get all workflows
  Future<Result<List<ApprovalWorkflow>>> getAllWorkflows() async {
    try {
      final workflows = await _approvalDao.getAllWorkflows();
      return Result.success(workflows);
    } catch (e) {
      return Result.error('خطأ في جلب سير العمل: ${e.toString()}');
    }
  }

  /// Get workflow by document type and amount
  Future<Result<ApprovalWorkflow?>> getWorkflowForDocument({
    required DocumentType documentType,
    double? amount,
  }) async {
    try {
      final workflow = await _approvalDao.getWorkflowForDocument(
        documentType: documentType,
        amount: amount,
      );
      return Result.success(workflow);
    } catch (e) {
      return Result.error('خطأ في جلب سير العمل: ${e.toString()}');
    }
  }

  /// Create approval workflow
  Future<Result<int>> createWorkflow(ApprovalWorkflow workflow) async {
    try {
      final validation = _validateWorkflow(workflow);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      final id = await _approvalDao.insertWorkflow(workflow);

      // Insert workflow steps
      for (final step in workflow.steps) {
        final stepWithWorkflowId = ApprovalStep(
          workflowId: id,
          stepOrder: step.stepOrder,
          name: step.name,
          level: step.level,
          approverId: step.approverId,
          roleId: step.roleId,
          isRequired: step.isRequired,
          timeoutHours: step.timeoutHours,
        );
        await _approvalDao.insertWorkflowStep(stepWithWorkflowId);
      }

      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إنشاء سير العمل: ${e.toString()}');
    }
  }

  /// Submit document for approval
  Future<Result<int>> submitForApproval({
    required DocumentType documentType,
    required int documentId,
    required String documentNumber,
    double? amount,
    String? description,
    required int requestedBy,
  }) async {
    try {
      // Find appropriate workflow
      final workflowResult = await getWorkflowForDocument(
        documentType: documentType,
        amount: amount,
      );

      if (!workflowResult.isSuccess) {
        return Result.error(workflowResult.error!);
      }

      final workflow = workflowResult.data;
      if (workflow == null) {
        return Result.error('لا يوجد سير عمل مناسب لهذا المستند');
      }

      // Get workflow steps
      final steps = await _approvalDao.getWorkflowSteps(workflow.id!);
      if (steps.isEmpty) {
        return Result.error('سير العمل لا يحتوي على خطوات');
      }

      // Create approval request
      final request = ApprovalRequest(
        workflowId: workflow.id!,
        documentType: documentType,
        documentId: documentId,
        documentNumber: documentNumber,
        amount: amount,
        description: description,
        requestedBy: requestedBy,
        currentStepId: steps.first.id,
      );

      final requestId = await _approvalDao.insertApprovalRequest(request);
      return Result.success(requestId);
    } catch (e) {
      return Result.error('خطأ في إرسال طلب الموافقة: ${e.toString()}');
    }
  }

  /// Approve request
  Future<Result<bool>> approveRequest({
    required int requestId,
    required int approverId,
    String? comments,
  }) async {
    try {
      final request = await _approvalDao.getApprovalRequestById(requestId);
      if (request == null) {
        return Result.error('طلب الموافقة غير موجود');
      }

      if (request.status != ApprovalStatus.pending) {
        return Result.error('لا يمكن الموافقة على هذا الطلب');
      }

      // Check if user can approve current step
      final canApprove = await _canUserApproveStep(
        approverId,
        request.currentStepId!,
      );

      if (!canApprove) {
        return Result.error('ليس لديك صلاحية الموافقة على هذه الخطوة');
      }

      // Record approval action
      final action = ApprovalAction(
        requestId: requestId,
        stepId: request.currentStepId!,
        action: ApprovalStatus.approved,
        actionBy: approverId,
        comments: comments,
      );

      await _approvalDao.insertApprovalAction(action);

      // Get next step
      final nextStep = await _getNextStep(
        request.workflowId,
        request.currentStepId!,
      );

      if (nextStep != null) {
        // Move to next step
        final updatedRequest = request.copyWith(currentStepId: nextStep.id);
        await _approvalDao.updateApprovalRequest(updatedRequest);
      } else {
        // Final approval - complete the request
        final completedRequest = request.copyWith(
          status: ApprovalStatus.approved,
          completedAt: DateTime.now(),
          currentStepId: null,
        );
        await _approvalDao.updateApprovalRequest(completedRequest);
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في الموافقة: ${e.toString()}');
    }
  }

  /// Reject request
  Future<Result<bool>> rejectRequest({
    required int requestId,
    required int approverId,
    required String rejectionReason,
    String? comments,
  }) async {
    try {
      final request = await _approvalDao.getApprovalRequestById(requestId);
      if (request == null) {
        return Result.error('طلب الموافقة غير موجود');
      }

      if (request.status != ApprovalStatus.pending) {
        return Result.error('لا يمكن رفض هذا الطلب');
      }

      // Check if user can approve current step
      final canApprove = await _canUserApproveStep(
        approverId,
        request.currentStepId!,
      );

      if (!canApprove) {
        return Result.error('ليس لديك صلاحية رفض هذه الخطوة');
      }

      // Record rejection action
      final action = ApprovalAction(
        requestId: requestId,
        stepId: request.currentStepId!,
        action: ApprovalStatus.rejected,
        actionBy: approverId,
        comments: comments,
      );

      await _approvalDao.insertApprovalAction(action);

      // Update request status
      final rejectedRequest = request.copyWith(
        status: ApprovalStatus.rejected,
        rejectionReason: rejectionReason,
        completedAt: DateTime.now(),
        currentStepId: null,
      );

      await _approvalDao.updateApprovalRequest(rejectedRequest);
      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في الرفض: ${e.toString()}');
    }
  }

  /// Get pending approvals for user
  Future<Result<List<ApprovalRequest>>> getPendingApprovalsForUser(
    int userId,
  ) async {
    try {
      final requests = await _approvalDao.getPendingApprovalsForUser(userId);
      return Result.success(requests);
    } catch (e) {
      return Result.error('خطأ في جلب الموافقات المعلقة: ${e.toString()}');
    }
  }

  /// Get approval history for document
  Future<Result<List<ApprovalAction>>> getApprovalHistory({
    required DocumentType documentType,
    required int documentId,
  }) async {
    try {
      final actions = await _approvalDao.getApprovalHistoryForDocument(
        documentType: documentType,
        documentId: documentId,
      );
      return Result.success(actions);
    } catch (e) {
      return Result.error('خطأ في جلب تاريخ الموافقات: ${e.toString()}');
    }
  }

  /// Get approval request by document
  Future<Result<ApprovalRequest?>> getApprovalRequestByDocument({
    required DocumentType documentType,
    required int documentId,
  }) async {
    try {
      final request = await _approvalDao.getApprovalRequestByDocument(
        documentType: documentType,
        documentId: documentId,
      );
      return Result.success(request);
    } catch (e) {
      return Result.error('خطأ في جلب طلب الموافقة: ${e.toString()}');
    }
  }

  /// Cancel approval request
  Future<Result<bool>> cancelApprovalRequest(int requestId) async {
    try {
      final request = await _approvalDao.getApprovalRequestById(requestId);
      if (request == null) {
        return Result.error('طلب الموافقة غير موجود');
      }

      if (request.status != ApprovalStatus.pending) {
        return Result.error('لا يمكن إلغاء هذا الطلب');
      }

      final cancelledRequest = request.copyWith(
        status: ApprovalStatus.cancelled,
        completedAt: DateTime.now(),
        currentStepId: null,
      );

      await _approvalDao.updateApprovalRequest(cancelledRequest);
      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إلغاء الطلب: ${e.toString()}');
    }
  }

  /// Check if document requires approval
  Future<Result<bool>> requiresApproval({
    required DocumentType documentType,
    double? amount,
  }) async {
    try {
      final workflowResult = await getWorkflowForDocument(
        documentType: documentType,
        amount: amount,
      );

      return Result.success(workflowResult.data != null);
    } catch (e) {
      return Result.error('خطأ في فحص متطلبات الموافقة: ${e.toString()}');
    }
  }

  /// Validate workflow
  Result<bool> _validateWorkflow(ApprovalWorkflow workflow) {
    if (workflow.name.trim().isEmpty) {
      return Result.error('اسم سير العمل مطلوب');
    }

    if (workflow.steps.isEmpty) {
      return Result.error('يجب إضافة خطوة واحدة على الأقل');
    }

    // Validate step order
    final orders = workflow.steps.map((s) => s.stepOrder).toList();
    orders.sort();
    for (int i = 0; i < orders.length; i++) {
      if (orders[i] != i + 1) {
        return Result.error('ترتيب الخطوات يجب أن يكون متسلسل');
      }
    }

    return Result.success(true);
  }

  /// Check if user can approve step
  Future<bool> _canUserApproveStep(int userId, int stepId) async {
    final step = await _approvalDao.getWorkflowStepById(stepId);
    if (step == null) return false;

    // Check direct approver assignment
    if (step.approverId == userId) return true;

    // Check role-based approval
    if (step.roleId != null) {
      final userRoles = await _approvalDao.getUserRoles(userId);
      return userRoles.contains(step.roleId);
    }

    return false;
  }

  /// Get next step in workflow
  Future<ApprovalStep?> _getNextStep(int workflowId, int currentStepId) async {
    final currentStep = await _approvalDao.getWorkflowStepById(currentStepId);
    if (currentStep == null) return null;

    final steps = await _approvalDao.getWorkflowSteps(workflowId);
    steps.sort((a, b) => a.stepOrder.compareTo(b.stepOrder));

    final currentIndex = steps.indexWhere((s) => s.id == currentStepId);
    if (currentIndex == -1 || currentIndex == steps.length - 1) {
      return null; // Last step
    }

    return steps[currentIndex + 1];
  }
}
