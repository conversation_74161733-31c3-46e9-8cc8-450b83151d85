/// نموذج الأصول الثابتة
/// Fixed Asset Model for Smart Ledger
library;

enum DepreciationMethod {
  straightLine, // خط مستقيم
  decliningBalance, // متناقص
  unitsOfProduction, // وحدات الإنتاج
  sumOfYears, // مجموع السنوات
}

enum AssetStatus {
  active, // نشط
  inactive, // غير نشط
  disposed, // مستبعد
  underMaintenance, // تحت الصيانة
}

enum AssetCategory {
  building, // مباني
  machinery, // آلات ومعدات
  vehicle, // مركبات
  furniture, // أثاث ومفروشات
  computer, // أجهزة حاسوب
  other, // أخرى
}

class FixedAsset {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final AssetCategory category;
  final String? location;
  final DateTime purchaseDate;
  final double purchasePrice;
  final double? salvageValue;
  final int usefulLifeYears;
  final int? usefulLifeUnits; // للاستهلاك بوحدات الإنتاج
  final DepreciationMethod depreciationMethod;
  final AssetStatus status;
  final int? accountId; // حساب الأصل
  final int? depreciationAccountId; // حساب مجمع الاستهلاك
  final int? expenseAccountId; // حساب مصروف الاستهلاك
  final String? serialNumber;
  final String? model;
  final String? manufacturer;
  final String? warrantyInfo;
  final DateTime? warrantyExpiry;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FixedAsset({
    this.id,
    required this.code,
    required this.name,
    this.description,
    required this.category,
    this.location,
    required this.purchaseDate,
    required this.purchasePrice,
    this.salvageValue,
    required this.usefulLifeYears,
    this.usefulLifeUnits,
    required this.depreciationMethod,
    this.status = AssetStatus.active,
    this.accountId,
    this.depreciationAccountId,
    this.expenseAccountId,
    this.serialNumber,
    this.model,
    this.manufacturer,
    this.warrantyInfo,
    this.warrantyExpiry,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء نسخة من الكائن مع تعديل بعض الخصائص
  FixedAsset copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    AssetCategory? category,
    String? location,
    DateTime? purchaseDate,
    double? purchasePrice,
    double? salvageValue,
    int? usefulLifeYears,
    int? usefulLifeUnits,
    DepreciationMethod? depreciationMethod,
    AssetStatus? status,
    int? accountId,
    int? depreciationAccountId,
    int? expenseAccountId,
    String? serialNumber,
    String? model,
    String? manufacturer,
    String? warrantyInfo,
    DateTime? warrantyExpiry,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FixedAsset(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      location: location ?? this.location,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      salvageValue: salvageValue ?? this.salvageValue,
      usefulLifeYears: usefulLifeYears ?? this.usefulLifeYears,
      usefulLifeUnits: usefulLifeUnits ?? this.usefulLifeUnits,
      depreciationMethod: depreciationMethod ?? this.depreciationMethod,
      status: status ?? this.status,
      accountId: accountId ?? this.accountId,
      depreciationAccountId:
          depreciationAccountId ?? this.depreciationAccountId,
      expenseAccountId: expenseAccountId ?? this.expenseAccountId,
      serialNumber: serialNumber ?? this.serialNumber,
      model: model ?? this.model,
      manufacturer: manufacturer ?? this.manufacturer,
      warrantyInfo: warrantyInfo ?? this.warrantyInfo,
      warrantyExpiry: warrantyExpiry ?? this.warrantyExpiry,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'category': category.name,
      'location': location,
      'purchase_date': purchaseDate.toIso8601String(),
      'purchase_price': purchasePrice,
      'salvage_value': salvageValue,
      'useful_life_years': usefulLifeYears,
      'useful_life_units': usefulLifeUnits,
      'depreciation_method': depreciationMethod.name,
      'status': status.name,
      'account_id': accountId,
      'depreciation_account_id': depreciationAccountId,
      'expense_account_id': expenseAccountId,
      'serial_number': serialNumber,
      'model': model,
      'manufacturer': manufacturer,
      'warranty_info': warrantyInfo,
      'warranty_expiry': warrantyExpiry?.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء كائن من Map
  factory FixedAsset.fromMap(Map<String, dynamic> map) {
    return FixedAsset(
      id: map['id']?.toInt(),
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      category: AssetCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => AssetCategory.other,
      ),
      location: map['location'],
      purchaseDate: DateTime.parse(map['purchase_date']),
      purchasePrice: map['purchase_price']?.toDouble() ?? 0.0,
      salvageValue: map['salvage_value']?.toDouble(),
      usefulLifeYears: map['useful_life_years']?.toInt() ?? 1,
      usefulLifeUnits: map['useful_life_units']?.toInt(),
      depreciationMethod: DepreciationMethod.values.firstWhere(
        (e) => e.name == map['depreciation_method'],
        orElse: () => DepreciationMethod.straightLine,
      ),
      status: AssetStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => AssetStatus.active,
      ),
      accountId: map['account_id']?.toInt(),
      depreciationAccountId: map['depreciation_account_id']?.toInt(),
      expenseAccountId: map['expense_account_id']?.toInt(),
      serialNumber: map['serial_number'],
      model: map['model'],
      manufacturer: map['manufacturer'],
      warrantyInfo: map['warranty_info'],
      warrantyExpiry: map['warranty_expiry'] != null
          ? DateTime.parse(map['warranty_expiry'])
          : null,
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// حساب القيمة الدفترية الحالية
  double getCurrentBookValue() {
    final totalDepreciation = getTotalDepreciation();
    return purchasePrice - totalDepreciation;
  }

  /// حساب إجمالي الاستهلاك حتى تاريخ اليوم
  double getTotalDepreciation() {
    final now = DateTime.now();
    final monthsElapsed = _getMonthsBetween(purchaseDate, now);
    final yearsElapsed = monthsElapsed / 12.0;

    return calculateDepreciation(yearsElapsed);
  }

  /// حساب الاستهلاك لفترة معينة
  double calculateDepreciation(double years) {
    final depreciableAmount = purchasePrice - (salvageValue ?? 0.0);

    switch (depreciationMethod) {
      case DepreciationMethod.straightLine:
        return _calculateStraightLineDepreciation(depreciableAmount, years);
      case DepreciationMethod.decliningBalance:
        return _calculateDecliningBalanceDepreciation(years);
      case DepreciationMethod.sumOfYears:
        return _calculateSumOfYearsDepreciation(depreciableAmount, years);
      case DepreciationMethod.unitsOfProduction:
        // يحتاج إلى وحدات الإنتاج الفعلية
        return _calculateStraightLineDepreciation(depreciableAmount, years);
    }
  }

  /// حساب الاستهلاك بطريقة الخط المستقيم
  double _calculateStraightLineDepreciation(
    double depreciableAmount,
    double years,
  ) {
    final annualDepreciation = depreciableAmount / usefulLifeYears;
    final totalDepreciation = annualDepreciation * years;
    return totalDepreciation > depreciableAmount
        ? depreciableAmount
        : totalDepreciation;
  }

  /// حساب الاستهلاك بطريقة الرصيد المتناقص
  double _calculateDecliningBalanceDepreciation(double years) {
    final rate = 2.0 / usefulLifeYears; // معدل الاستهلاك المضاعف
    double bookValue = purchasePrice;
    double totalDepreciation = 0.0;

    for (int year = 1; year <= years.floor(); year++) {
      final yearlyDepreciation = bookValue * rate;
      totalDepreciation += yearlyDepreciation;
      bookValue -= yearlyDepreciation;
    }

    // إضافة الجزء الكسري من السنة
    if (years % 1 != 0) {
      final partialYearDepreciation = bookValue * rate * (years % 1);
      totalDepreciation += partialYearDepreciation;
    }

    final maxDepreciation = purchasePrice - (salvageValue ?? 0.0);
    return totalDepreciation > maxDepreciation
        ? maxDepreciation
        : totalDepreciation;
  }

  /// حساب الاستهلاك بطريقة مجموع السنوات
  double _calculateSumOfYearsDepreciation(
    double depreciableAmount,
    double years,
  ) {
    final sumOfYears = usefulLifeYears * (usefulLifeYears + 1) / 2;
    double totalDepreciation = 0.0;

    for (int year = 1; year <= years.floor(); year++) {
      final fraction = (usefulLifeYears - year + 1) / sumOfYears;
      totalDepreciation += depreciableAmount * fraction;
    }

    // إضافة الجزء الكسري من السنة
    if (years % 1 != 0) {
      final currentYear = years.floor() + 1;
      if (currentYear <= usefulLifeYears) {
        final fraction = (usefulLifeYears - currentYear + 1) / sumOfYears;
        totalDepreciation += depreciableAmount * fraction * (years % 1);
      }
    }

    return totalDepreciation > depreciableAmount
        ? depreciableAmount
        : totalDepreciation;
  }

  /// حساب عدد الأشهر بين تاريخين
  int _getMonthsBetween(DateTime start, DateTime end) {
    return (end.year - start.year) * 12 + end.month - start.month;
  }

  @override
  String toString() {
    return 'FixedAsset(id: $id, code: $code, name: $name, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FixedAsset && other.id == id && other.code == code;
  }

  @override
  int get hashCode => id.hashCode ^ code.hashCode;
}
