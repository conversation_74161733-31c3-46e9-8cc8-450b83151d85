import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/currency.dart';
import '../services/currency_service.dart';
import '../widgets/quantum_card.dart';
import '../widgets/quantum_button.dart';
import '../widgets/quantum_text_field.dart';

/// شاشة إضافة/تعديل العملة
class AddEditCurrencyScreen extends StatefulWidget {
  final Currency? currency;

  const AddEditCurrencyScreen({super.key, this.currency});

  @override
  State<AddEditCurrencyScreen> createState() => _AddEditCurrencyScreenState();
}

class _AddEditCurrencyScreenState extends State<AddEditCurrencyScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final CurrencyService _currencyService = CurrencyService();

  late TextEditingController _codeController;
  late TextEditingController _symbolController;
  late TextEditingController _nameArController;
  late TextEditingController _nameEnController;
  late TextEditingController _decimalPlacesController;

  bool _isActive = true;
  bool _isBaseCurrency = false;
  bool _isLoading = false;

  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _codeController.dispose();
    _symbolController.dispose();
    _nameArController.dispose();
    _nameEnController.dispose();
    _decimalPlacesController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _codeController = TextEditingController(text: widget.currency?.code ?? '');
    _symbolController = TextEditingController(
      text: widget.currency?.symbol ?? '',
    );
    _nameArController = TextEditingController(
      text: widget.currency?.nameAr ?? '',
    );
    _nameEnController = TextEditingController(
      text: widget.currency?.nameEn ?? '',
    );
    _decimalPlacesController = TextEditingController(
      text: widget.currency?.decimalPlaces.toString() ?? '2',
    );

    if (widget.currency != null) {
      _isActive = widget.currency!.isActive;
      _isBaseCurrency = widget.currency!.isBaseCurrency;
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _saveCurrency() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final currency = Currency(
        id: widget.currency?.id,
        code: _codeController.text.trim().toUpperCase(),
        symbol: _symbolController.text.trim(),
        nameAr: _nameArController.text.trim(),
        nameEn: _nameEnController.text.trim(),
        isActive: _isActive,
        isBaseCurrency: _isBaseCurrency,
        decimalPlaces: int.parse(_decimalPlacesController.text),
        createdAt: widget.currency?.createdAt,
        updatedAt: DateTime.now(),
      );

      final result = widget.currency == null
          ? await _currencyService.addCurrency(currency)
          : await _currencyService.updateCurrency(currency);

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.currency == null
                    ? 'تم إضافة العملة بنجاح'
                    : 'تم تحديث العملة بنجاح',
              ),
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(result.error!)));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: ${e.toString()}')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.currency == null ? 'إضافة عملة' : 'تعديل العملة'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value),
            child: FadeTransition(opacity: _fadeAnimation, child: _buildForm()),
          );
        },
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildBasicInfoCard(),
          const SizedBox(height: 16),
          _buildSettingsCard(),
          const SizedBox(height: 16),
          _buildPreviewCard(),
          const SizedBox(height: 24),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _codeController,
                    labelText: 'رمز العملة',
                    hintText: 'مثل: USD, EUR',
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(3),
                      FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z]')),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'رمز العملة مطلوب';
                      }
                      if (value.length != 3) {
                        return 'رمز العملة يجب أن يكون 3 أحرف';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _symbolController,
                    labelText: 'رمز العملة',
                    hintText: 'مثل: \$, €, ر.س',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'رمز العملة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _nameArController,
              labelText: 'الاسم بالعربية',
              hintText: 'مثل: دولار أمريكي',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الاسم بالعربية مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _nameEnController,
              labelText: 'الاسم بالإنجليزية',
              hintText: 'مثل: US Dollar',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الاسم بالإنجليزية مطلوب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإعدادات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _decimalPlacesController,
              labelText: 'عدد الخانات العشرية',
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(1),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'عدد الخانات العشرية مطلوب';
                }
                final number = int.tryParse(value);
                if (number == null || number < 0 || number > 4) {
                  return 'يجب أن يكون بين 0 و 4';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('عملة نشطة'),
              subtitle: const Text('هل العملة متاحة للاستخدام؟'),
              value: _isActive,
              onChanged: (value) => setState(() => _isActive = value),
            ),
            SwitchListTile(
              title: const Text('عملة أساسية'),
              subtitle: const Text('هل هذه العملة الأساسية للنظام؟'),
              value: _isBaseCurrency,
              onChanged: (value) => setState(() => _isBaseCurrency = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معاينة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${_nameArController.text.isEmpty ? 'اسم العملة' : _nameArController.text} (${_codeController.text.isEmpty ? 'XXX' : _codeController.text.toUpperCase()})',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'مثال: 1,234${_decimalPlacesController.text.isEmpty ? '.00' : '.${'0' * int.parse(_decimalPlacesController.text.isEmpty ? '2' : _decimalPlacesController.text)}'} ${_symbolController.text.isEmpty ? '¤' : _symbolController.text}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: QuantumButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            variant: QuantumButtonVariant.outline,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: QuantumButton(
            text: widget.currency == null ? 'إضافة' : 'تحديث',
            onPressed: _isLoading ? null : _saveCurrency,
            isLoading: _isLoading,
          ),
        ),
      ],
    );
  }
}
