/// مزود اللغة لتطبيق Smart Ledger
/// Language Provider for Smart Ledger Application
library;

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مزود اللغة الذي يدير حالة اللغة في التطبيق
/// Language provider that manages the language state in the application
class LanguageProvider extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _arabicCode = 'ar';
  static const String _englishCode = 'en';
  
  Locale _currentLocale = const Locale(_arabicCode);
  SharedPreferences? _prefs;

  /// الحصول على اللغة الحالية
  /// Get the current locale
  Locale get currentLocale => _currentLocale;

  /// التحقق من كون اللغة الحالية عربية
  /// Check if the current language is Arabic
  bool get isArabic => _currentLocale.languageCode == _arabicCode;

  /// التحقق من كون اللغة الحالية إنجليزية
  /// Check if the current language is English
  bool get isEnglish => _currentLocale.languageCode == _englishCode;

  /// الحصول على رمز اللغة الحالية
  /// Get the current language code
  String get languageCode => _currentLocale.languageCode;

  /// الحصول على اسم اللغة الحالية
  /// Get the current language name
  String get languageName {
    switch (_currentLocale.languageCode) {
      case _arabicCode:
        return 'العربية';
      case _englishCode:
        return 'English';
      default:
        return 'العربية';
    }
  }

  /// تهيئة مزود اللغة
  /// Initialize the language provider
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSavedLanguage();
  }

  /// تحميل اللغة المحفوظة من التخزين المحلي
  /// Load saved language from local storage
  Future<void> _loadSavedLanguage() async {
    final savedLanguage = _prefs?.getString(_languageKey);
    if (savedLanguage != null) {
      _currentLocale = Locale(savedLanguage);
      notifyListeners();
    }
  }

  /// تغيير اللغة إلى العربية
  /// Change language to Arabic
  Future<void> setArabic() async {
    await _changeLanguage(_arabicCode);
  }

  /// تغيير اللغة إلى الإنجليزية
  /// Change language to English
  Future<void> setEnglish() async {
    await _changeLanguage(_englishCode);
  }

  /// تبديل اللغة بين العربية والإنجليزية
  /// Toggle language between Arabic and English
  Future<void> toggleLanguage() async {
    final newLanguageCode = isArabic ? _englishCode : _arabicCode;
    await _changeLanguage(newLanguageCode);
  }

  /// تغيير اللغة
  /// Change language
  Future<void> _changeLanguage(String languageCode) async {
    if (_currentLocale.languageCode != languageCode) {
      _currentLocale = Locale(languageCode);
      await _saveLanguage(languageCode);
      notifyListeners();
    }
  }

  /// حفظ اللغة في التخزين المحلي
  /// Save language to local storage
  Future<void> _saveLanguage(String languageCode) async {
    await _prefs?.setString(_languageKey, languageCode);
  }

  /// تعيين اللغة بناءً على رمز اللغة
  /// Set language based on language code
  Future<void> setLanguage(String languageCode) async {
    if (languageCode == _arabicCode || languageCode == _englishCode) {
      await _changeLanguage(languageCode);
    }
  }

  /// تعيين اللغة بناءً على Locale
  /// Set language based on Locale
  Future<void> setLocale(Locale locale) async {
    await setLanguage(locale.languageCode);
  }

  /// الحصول على قائمة اللغات المدعومة
  /// Get list of supported languages
  static List<Locale> get supportedLocales => const [
    Locale(_arabicCode),
    Locale(_englishCode),
  ];

  /// الحصول على قائمة أسماء اللغات المدعومة
  /// Get list of supported language names
  static Map<String, String> get supportedLanguageNames => const {
    _arabicCode: 'العربية',
    _englishCode: 'English',
  };

  /// التحقق من دعم اللغة
  /// Check if language is supported
  static bool isLanguageSupported(String languageCode) {
    return supportedLanguageNames.containsKey(languageCode);
  }

  /// الحصول على اتجاه النص للغة الحالية
  /// Get text direction for current language
  TextDirection get textDirection {
    return isArabic ? TextDirection.rtl : TextDirection.ltr;
  }

  /// الحصول على محاذاة النص للغة الحالية
  /// Get text alignment for current language
  TextAlign get textAlign {
    return isArabic ? TextAlign.right : TextAlign.left;
  }

  /// الحصول على محاذاة النص المعاكسة للغة الحالية
  /// Get opposite text alignment for current language
  TextAlign get oppositeTextAlign {
    return isArabic ? TextAlign.left : TextAlign.right;
  }

  /// الحصول على محاذاة النص في المنتصف
  /// Get center text alignment
  TextAlign get centerTextAlign => TextAlign.center;

}
