import 'employee.dart';

/// نموذج سجل الحضور
/// Attendance Record Model
class AttendanceRecord {
  final int? id;
  final String employeeCode;
  final DateTime date;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final AttendanceStatus status;
  final double? workingHours;
  final double? overtimeHours;
  final double? lateMinutes;
  final double? earlyLeaveMinutes;
  final String? notes;
  final String? location;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Employee? employee;

  AttendanceRecord({
    this.id,
    required this.employeeCode,
    required this.date,
    this.checkInTime,
    this.checkOutTime,
    this.status = AttendanceStatus.absent,
    this.workingHours,
    this.overtimeHours,
    this.lateMinutes,
    this.earlyLeaveMinutes,
    this.notes,
    this.location,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// حساب ساعات العمل
  double calculateWorkingHours() {
    if (checkInTime == null || checkOutTime == null) return 0.0;
    final duration = checkOutTime!.difference(checkInTime!);
    return duration.inMinutes / 60.0;
  }

  /// حساب دقائق التأخير
  double calculateLateMinutes(DateTime expectedCheckIn) {
    if (checkInTime == null) return 0.0;
    if (checkInTime!.isBefore(expectedCheckIn) || checkInTime!.isAtSameMomentAs(expectedCheckIn)) {
      return 0.0;
    }
    return checkInTime!.difference(expectedCheckIn).inMinutes.toDouble();
  }

  /// حساب دقائق المغادرة المبكرة
  double calculateEarlyLeaveMinutes(DateTime expectedCheckOut) {
    if (checkOutTime == null) return 0.0;
    if (checkOutTime!.isAfter(expectedCheckOut) || checkOutTime!.isAtSameMomentAs(expectedCheckOut)) {
      return 0.0;
    }
    return expectedCheckOut.difference(checkOutTime!).inMinutes.toDouble();
  }

  /// Factory constructor from database map
  factory AttendanceRecord.fromMap(Map<String, dynamic> map) {
    return AttendanceRecord(
      id: map['id'] as int?,
      employeeCode: map['employee_code'] as String,
      date: DateTime.parse(map['date'] as String),
      checkInTime: map['check_in_time'] != null 
          ? DateTime.parse(map['check_in_time'] as String)
          : null,
      checkOutTime: map['check_out_time'] != null 
          ? DateTime.parse(map['check_out_time'] as String)
          : null,
      status: AttendanceStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => AttendanceStatus.absent,
      ),
      workingHours: map['working_hours'] != null ? (map['working_hours'] as num).toDouble() : null,
      overtimeHours: map['overtime_hours'] != null ? (map['overtime_hours'] as num).toDouble() : null,
      lateMinutes: map['late_minutes'] != null ? (map['late_minutes'] as num).toDouble() : null,
      earlyLeaveMinutes: map['early_leave_minutes'] != null ? (map['early_leave_minutes'] as num).toDouble() : null,
      notes: map['notes'] as String?,
      location: map['location'] as String?,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'date': date.toIso8601String(),
      'check_in_time': checkInTime?.toIso8601String(),
      'check_out_time': checkOutTime?.toIso8601String(),
      'status': status.name,
      'working_hours': workingHours,
      'overtime_hours': overtimeHours,
      'late_minutes': lateMinutes,
      'early_leave_minutes': earlyLeaveMinutes,
      'notes': notes,
      'location': location,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  AttendanceRecord copyWith({
    int? id,
    String? employeeCode,
    DateTime? date,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    AttendanceStatus? status,
    double? workingHours,
    double? overtimeHours,
    double? lateMinutes,
    double? earlyLeaveMinutes,
    String? notes,
    String? location,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AttendanceRecord(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      date: date ?? this.date,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      status: status ?? this.status,
      workingHours: workingHours ?? this.workingHours,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      lateMinutes: lateMinutes ?? this.lateMinutes,
      earlyLeaveMinutes: earlyLeaveMinutes ?? this.earlyLeaveMinutes,
      notes: notes ?? this.notes,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج نوع الإجازة
/// Leave Type Model
class LeaveType {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final int maxDaysPerYear;
  final bool isPaid;
  final bool requiresApproval;
  final bool carryForward;
  final int? maxCarryForwardDays;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  LeaveType({
    this.id,
    required this.code,
    required this.name,
    this.description,
    required this.maxDaysPerYear,
    this.isPaid = true,
    this.requiresApproval = true,
    this.carryForward = false,
    this.maxCarryForwardDays,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory LeaveType.fromMap(Map<String, dynamic> map) {
    return LeaveType(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      maxDaysPerYear: map['max_days_per_year'] as int,
      isPaid: (map['is_paid'] as int?) == 1,
      requiresApproval: (map['requires_approval'] as int?) == 1,
      carryForward: (map['carry_forward'] as int?) == 1,
      maxCarryForwardDays: map['max_carry_forward_days'] as int?,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'max_days_per_year': maxDaysPerYear,
      'is_paid': isPaid ? 1 : 0,
      'requires_approval': requiresApproval ? 1 : 0,
      'carry_forward': carryForward ? 1 : 0,
      'max_carry_forward_days': maxCarryForwardDays,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  LeaveType copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    int? maxDaysPerYear,
    bool? isPaid,
    bool? requiresApproval,
    bool? carryForward,
    int? maxCarryForwardDays,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeaveType(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      maxDaysPerYear: maxDaysPerYear ?? this.maxDaysPerYear,
      isPaid: isPaid ?? this.isPaid,
      requiresApproval: requiresApproval ?? this.requiresApproval,
      carryForward: carryForward ?? this.carryForward,
      maxCarryForwardDays: maxCarryForwardDays ?? this.maxCarryForwardDays,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج طلب الإجازة
/// Leave Request Model
class LeaveRequest {
  final int? id;
  final String employeeCode;
  final int leaveTypeId;
  final DateTime startDate;
  final DateTime endDate;
  final int totalDays;
  final String reason;
  final LeaveRequestStatus status;
  final String? approvedBy;
  final DateTime? approvedDate;
  final String? rejectionReason;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Employee? employee;
  LeaveType? leaveType;
  Employee? approver;

  LeaveRequest({
    this.id,
    required this.employeeCode,
    required this.leaveTypeId,
    required this.startDate,
    required this.endDate,
    required this.totalDays,
    required this.reason,
    this.status = LeaveRequestStatus.pending,
    this.approvedBy,
    this.approvedDate,
    this.rejectionReason,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// حساب عدد الأيام
  int calculateTotalDays() {
    return endDate.difference(startDate).inDays + 1;
  }

  /// Factory constructor from database map
  factory LeaveRequest.fromMap(Map<String, dynamic> map) {
    return LeaveRequest(
      id: map['id'] as int?,
      employeeCode: map['employee_code'] as String,
      leaveTypeId: map['leave_type_id'] as int,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      totalDays: map['total_days'] as int,
      reason: map['reason'] as String,
      status: LeaveRequestStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => LeaveRequestStatus.pending,
      ),
      approvedBy: map['approved_by'] as String?,
      approvedDate: map['approved_date'] != null 
          ? DateTime.parse(map['approved_date'] as String)
          : null,
      rejectionReason: map['rejection_reason'] as String?,
      notes: map['notes'] as String?,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'leave_type_id': leaveTypeId,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_days': totalDays,
      'reason': reason,
      'status': status.name,
      'approved_by': approvedBy,
      'approved_date': approvedDate?.toIso8601String(),
      'rejection_reason': rejectionReason,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  LeaveRequest copyWith({
    int? id,
    String? employeeCode,
    int? leaveTypeId,
    DateTime? startDate,
    DateTime? endDate,
    int? totalDays,
    String? reason,
    LeaveRequestStatus? status,
    String? approvedBy,
    DateTime? approvedDate,
    String? rejectionReason,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeaveRequest(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      leaveTypeId: leaveTypeId ?? this.leaveTypeId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalDays: totalDays ?? this.totalDays,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedDate: approvedDate ?? this.approvedDate,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// تعدادات حالة الحضور
enum AttendanceStatus {
  present('حاضر'),
  absent('غائب'),
  late('متأخر'),
  halfDay('نصف يوم'),
  leave('إجازة'),
  holiday('عطلة'),
  sick('مرضي');

  const AttendanceStatus(this.displayName);
  final String displayName;
}

/// تعدادات حالة طلب الإجازة
enum LeaveRequestStatus {
  pending('في الانتظار'),
  approved('معتمد'),
  rejected('مرفوض'),
  cancelled('ملغي');

  const LeaveRequestStatus(this.displayName);
  final String displayName;
}
