import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../models/invoice.dart';
import '../models/company_settings.dart';
import '../services/company_settings_service.dart';
import '../utils/result.dart';

/// خدمة توليد ملفات PDF للفواتير
class PdfService {
  final CompanySettingsService _companySettingsService =
      CompanySettingsService();

  /// توليد PDF للفاتورة
  Future<Result<String>> generateInvoicePdf(Invoice invoice) async {
    try {
      // الحصول على إعدادات الشركة
      final companyResult = await _companySettingsService.getCompanySettings();
      if (!companyResult.isSuccess) {
        return Result.error(
          'خطأ في الحصول على إعدادات الشركة: ${companyResult.error}',
        );
      }

      final companySettings = companyResult.data!;

      // إنشاء مستند PDF
      final pdf = await _createInvoicePdf(invoice, companySettings);

      // حفظ الملف
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/invoice_${invoice.invoiceNumber}.pdf');
      await file.writeAsBytes(await pdf.save());

      return Result.success(file.path);
    } catch (e) {
      return Result.error('خطأ في توليد PDF: ${e.toString()}');
    }
  }

  /// طباعة الفاتورة
  Future<Result<void>> printInvoice(Invoice invoice) async {
    try {
      // الحصول على إعدادات الشركة
      final companyResult = await _companySettingsService.getCompanySettings();
      if (!companyResult.isSuccess) {
        return Result.error(
          'خطأ في الحصول على إعدادات الشركة: ${companyResult.error}',
        );
      }

      final companySettings = companyResult.data!;

      // إنشاء مستند PDF
      final pdf = await _createInvoicePdf(invoice, companySettings);

      // طباعة المستند
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'فاتورة ${invoice.invoiceNumber}',
      );

      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في طباعة الفاتورة: ${e.toString()}');
    }
  }

  /// مشاركة الفاتورة كـ PDF
  Future<Result<void>> shareInvoice(Invoice invoice) async {
    try {
      // الحصول على إعدادات الشركة
      final companyResult = await _companySettingsService.getCompanySettings();
      if (!companyResult.isSuccess) {
        return Result.error(
          'خطأ في الحصول على إعدادات الشركة: ${companyResult.error}',
        );
      }

      final companySettings = companyResult.data!;

      // إنشاء مستند PDF
      final pdf = await _createInvoicePdf(invoice, companySettings);

      // مشاركة المستند
      await Printing.sharePdf(
        bytes: await pdf.save(),
        filename: 'invoice_${invoice.invoiceNumber}.pdf',
      );

      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في مشاركة الفاتورة: ${e.toString()}');
    }
  }

  /// إنشاء مستند PDF للفاتورة
  Future<pw.Document> _createInvoicePdf(
    Invoice invoice,
    CompanySettings companySettings,
  ) async {
    final pdf = pw.Document();

    // تحميل الخط العربي
    final arabicFont = await _loadArabicFont();

    // إضافة صفحة الفاتورة
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(base: arabicFont),
        build: (pw.Context context) {
          return _buildInvoicePage(invoice, companySettings, arabicFont);
        },
      ),
    );

    return pdf;
  }

  /// بناء صفحة الفاتورة
  pw.Widget _buildInvoicePage(
    Invoice invoice,
    CompanySettings companySettings,
    pw.Font arabicFont,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // رأس الفاتورة
        _buildHeader(companySettings, arabicFont),
        pw.SizedBox(height: 20),

        // معلومات الفاتورة
        _buildInvoiceInfo(invoice, arabicFont),
        pw.SizedBox(height: 20),

        // معلومات العميل/المورد
        _buildCustomerSupplierInfo(invoice, arabicFont),
        pw.SizedBox(height: 20),

        // جدول العناصر
        _buildItemsTable(invoice, arabicFont),
        pw.SizedBox(height: 20),

        // ملخص المبالغ
        _buildTotalsSection(invoice, companySettings, arabicFont),

        pw.Spacer(),

        // تذييل الفاتورة
        _buildFooter(companySettings, arabicFont),
      ],
    );
  }

  /// بناء رأس الفاتورة
  pw.Widget _buildHeader(CompanySettings companySettings, pw.Font arabicFont) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              companySettings.companyName,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 24,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            if (companySettings.address != null)
              pw.Text(
                companySettings.address!,
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            if (companySettings.phone != null)
              pw.Text(
                'هاتف: ${companySettings.phone!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            if (companySettings.email != null)
              pw.Text(
                'بريد إلكتروني: ${companySettings.email!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            if (companySettings.taxNumber != null)
              pw.Text(
                'الرقم الضريبي: ${companySettings.taxNumber!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
          ],
        ),
        // يمكن إضافة شعار الشركة هنا إذا كان متوفراً
      ],
    );
  }

  /// بناء معلومات الفاتورة
  pw.Widget _buildInvoiceInfo(Invoice invoice, pw.Font arabicFont) {
    final invoiceTypeText = invoice.invoiceType == InvoiceType.sales
        ? 'فاتورة بيع'
        : 'فاتورة شراء';

    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                invoiceTypeText,
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.Text(
                'رقم الفاتورة: ${invoice.invoiceNumber}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'التاريخ: ${_formatDate(invoice.date)}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
              if (invoice.dueDate != null)
                pw.Text(
                  'تاريخ الاستحقاق: ${_formatDate(invoice.dueDate!)}',
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                ),
              pw.Text(
                'الحالة: ${invoice.status.nameAr}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء معلومات العميل/المورد
  pw.Widget _buildCustomerSupplierInfo(Invoice invoice, pw.Font arabicFont) {
    final isCustomer = invoice.invoiceType == InvoiceType.sales;
    final name = isCustomer
        ? (invoice.customer?.name ?? 'عميل محذوف')
        : (invoice.supplier?.name ?? 'مورد محذوف');
    final title = isCustomer ? 'معلومات العميل' : 'معلومات المورد';

    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'الاسم: $name',
            style: pw.TextStyle(font: arabicFont, fontSize: 12),
          ),
          if (isCustomer && invoice.customer != null) ...[
            if (invoice.customer!.phone?.isNotEmpty == true)
              pw.Text(
                'الهاتف: ${invoice.customer!.phone!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            if (invoice.customer!.email?.isNotEmpty == true)
              pw.Text(
                'البريد الإلكتروني: ${invoice.customer!.email!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            if (invoice.customer!.address?.isNotEmpty == true)
              pw.Text(
                'العنوان: ${invoice.customer!.address!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
          ] else if (!isCustomer && invoice.supplier != null) ...[
            if (invoice.supplier!.phone?.isNotEmpty == true)
              pw.Text(
                'الهاتف: ${invoice.supplier!.phone!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            if (invoice.supplier!.email?.isNotEmpty == true)
              pw.Text(
                'البريد الإلكتروني: ${invoice.supplier!.email!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
            if (invoice.supplier!.address?.isNotEmpty == true)
              pw.Text(
                'العنوان: ${invoice.supplier!.address!}',
                style: pw.TextStyle(font: arabicFont, fontSize: 12),
              ),
          ],
        ],
      ),
    );
  }

  /// بناء جدول العناصر
  pw.Widget _buildItemsTable(Invoice invoice, pw.Font arabicFont) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('م', arabicFont, isHeader: true),
            _buildTableCell('الوصف', arabicFont, isHeader: true),
            _buildTableCell('الكمية', arabicFont, isHeader: true),
            _buildTableCell('سعر الوحدة', arabicFont, isHeader: true),
            _buildTableCell('الإجمالي', arabicFont, isHeader: true),
          ],
        ),
        // صفوف العناصر
        ...invoice.lines.asMap().entries.map((entry) {
          final index = entry.key;
          final line = entry.value;
          return pw.TableRow(
            children: [
              _buildTableCell((index + 1).toString(), arabicFont),
              _buildTableCell(line.description ?? 'عنصر غير محدد', arabicFont),
              _buildTableCell(line.quantity.toString(), arabicFont),
              _buildTableCell(line.unitPrice.toStringAsFixed(2), arabicFont),
              _buildTableCell(line.lineTotal.toStringAsFixed(2), arabicFont),
            ],
          );
        }),
      ],
    );
  }

  /// بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font arabicFont, {
    bool isHeader = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء قسم ملخص المبالغ
  pw.Widget _buildTotalsSection(
    Invoice invoice,
    CompanySettings companySettings,
    pw.Font arabicFont,
  ) {
    return pw.Container(
      width: 200,
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildAmountRow(
            'المجموع الفرعي:',
            invoice.subtotal,
            companySettings,
            arabicFont,
          ),
          _buildAmountRow(
            'الضريبة:',
            invoice.taxAmount,
            companySettings,
            arabicFont,
          ),
          _buildAmountRow(
            'الخصم:',
            invoice.discountAmount,
            companySettings,
            arabicFont,
          ),
          pw.Divider(),
          _buildAmountRow(
            'الإجمالي:',
            invoice.totalAmount,
            companySettings,
            arabicFont,
            isTotal: true,
          ),
          _buildAmountRow(
            'المدفوع:',
            invoice.paidAmount,
            companySettings,
            arabicFont,
          ),
          pw.Divider(),
          _buildAmountRow(
            'المتبقي:',
            invoice.totalAmount - invoice.paidAmount,
            companySettings,
            arabicFont,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// بناء صف المبلغ
  pw.Widget _buildAmountRow(
    String label,
    double amount,
    CompanySettings companySettings,
    pw.Font arabicFont, {
    bool isTotal = false,
  }) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: isTotal ? 12 : 10,
            fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
        pw.Text(
          '${amount.toStringAsFixed(2)} ${companySettings.currencySymbol}',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: isTotal ? 12 : 10,
            fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
      ],
    );
  }

  /// بناء تذييل الفاتورة
  pw.Widget _buildFooter(CompanySettings companySettings, pw.Font arabicFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: PdfColors.grey)),
      ),
      child: pw.Center(
        child: pw.Text(
          'شكراً لتعاملكم معنا',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// تحميل الخط العربي
  Future<pw.Font> _loadArabicFont() async {
    try {
      // محاولة تحميل خط عربي من الأصول
      final fontData = await rootBundle.load(
        'assets/fonts/NotoSansArabic-Regular.ttf',
      );
      return pw.Font.ttf(fontData);
    } catch (e) {
      // في حالة عدم وجود الخط، استخدام الخط الافتراضي
      return pw.Font.helvetica();
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // ==================== وظائف PDF المتقدمة ====================

  /// توليد PDF للفاتورة مع إعدادات متقدمة
  Future<Result<Uint8List>> generateAdvancedInvoicePdf({
    required Invoice invoice,
    bool includeQRCode = true,
    bool includeWatermark = false,
    String? watermarkText,
    bool includeSignature = false,
    String? logoPath,
  }) async {
    try {
      final companyResult = await _companySettingsService.getCompanySettings();
      if (!companyResult.isSuccess) {
        return Result.error(
          'خطأ في الحصول على إعدادات الشركة: ${companyResult.error}',
        );
      }

      final companySettings = companyResult.data!;
      final pdf = pw.Document();

      // إضافة صفحة الفاتورة
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // رأس الفاتورة
              _buildAdvancedInvoiceHeader(invoice, companySettings, logoPath),
              pw.SizedBox(height: 20),

              // معلومات العميل/المورد
              _buildAdvancedCustomerSupplierInfo(invoice),
              pw.SizedBox(height: 20),

              // جدول البنود
              _buildAdvancedItemsTable(invoice),
              pw.SizedBox(height: 20),

              // الإجماليات
              _buildAdvancedTotalsSection(invoice),
              pw.SizedBox(height: 20),

              // QR Code إذا كان مطلوباً
              if (includeQRCode) _buildQRCode(invoice),

              // التوقيع إذا كان مطلوباً
              if (includeSignature) _buildSignatureSection(),

              // العلامة المائية إذا كانت مطلوبة
              if (includeWatermark && watermarkText != null)
                _buildWatermark(watermarkText),
            ];
          },
        ),
      );

      return Result.success(await pdf.save());
    } catch (e) {
      return Result.error('خطأ في توليد PDF المتقدم: ${e.toString()}');
    }
  }

  /// بناء رأس الفاتورة المتقدم
  pw.Widget _buildAdvancedInvoiceHeader(
    Invoice invoice,
    dynamic companySettings,
    String? logoPath,
  ) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        // معلومات الشركة
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                companySettings.companyName ?? 'اسم الشركة',
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              if (companySettings.address != null)
                pw.Text(companySettings.address!),
              if (companySettings.phone != null)
                pw.Text('هاتف: ${companySettings.phone}'),
              if (companySettings.email != null)
                pw.Text('بريد إلكتروني: ${companySettings.email}'),
              if (companySettings.taxNumber != null)
                pw.Text('الرقم الضريبي: ${companySettings.taxNumber}'),
            ],
          ),
        ),

        // شعار الشركة
        if (logoPath != null)
          pw.Container(
            width: 100,
            height: 100,
            child: pw.Image(pw.MemoryImage(File(logoPath).readAsBytesSync())),
          ),

        // معلومات الفاتورة
        pw.Expanded(
          flex: 1,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                invoice.invoiceType == InvoiceType.sales
                    ? 'فاتورة مبيعات'
                    : 'فاتورة مشتريات',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.Text('رقم الفاتورة: ${invoice.invoiceNumber}'),
              pw.Text('التاريخ: ${_formatDate(invoice.date)}'),
              if (invoice.dueDate != null)
                pw.Text('تاريخ الاستحقاق: ${_formatDate(invoice.dueDate!)}'),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء معلومات العميل/المورد المتقدم
  pw.Widget _buildAdvancedCustomerSupplierInfo(Invoice invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            invoice.invoiceType == InvoiceType.sales
                ? 'معلومات العميل'
                : 'معلومات المورد',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 5),
          // يمكن إضافة تفاصيل العميل/المورد هنا
          pw.Text(
            'الاسم: ${invoice.customer?.name ?? invoice.supplier?.name ?? 'غير محدد'}',
          ),
          if (invoice.customer?.phone != null ||
              invoice.supplier?.phone != null)
            pw.Text(
              'الهاتف: ${invoice.customer?.phone ?? invoice.supplier?.phone}',
            ),
          if (invoice.customer?.email != null ||
              invoice.supplier?.email != null)
            pw.Text(
              'البريد الإلكتروني: ${invoice.customer?.email ?? invoice.supplier?.email}',
            ),
        ],
      ),
    );
  }

  /// بناء جدول البنود المتقدم
  pw.Widget _buildAdvancedItemsTable(Invoice invoice) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(3),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1.5),
        4: const pw.FlexColumnWidth(1.5),
        5: const pw.FlexColumnWidth(1.5),
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildAdvancedTableCell('م', isHeader: true),
            _buildAdvancedTableCell('الصنف', isHeader: true),
            _buildAdvancedTableCell('الكمية', isHeader: true),
            _buildAdvancedTableCell('سعر الوحدة', isHeader: true),
            _buildAdvancedTableCell('الضريبة', isHeader: true),
            _buildAdvancedTableCell('الإجمالي', isHeader: true),
          ],
        ),

        // بنود الفاتورة
        ...invoice.lines.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final line = entry.value;
          return pw.TableRow(
            children: [
              _buildAdvancedTableCell(index.toString()),
              _buildAdvancedTableCell(line.item?.name ?? 'صنف غير محدد'),
              _buildAdvancedTableCell(line.quantity.toString()),
              _buildAdvancedTableCell(line.unitPrice.toStringAsFixed(2)),
              _buildAdvancedTableCell('0.00'), // الضريبة - يمكن حسابها لاحقاً
              _buildAdvancedTableCell(line.lineTotal.toStringAsFixed(2)),
            ],
          );
        }),
      ],
    );
  }

  /// بناء قسم الإجماليات المتقدم
  pw.Widget _buildAdvancedTotalsSection(Invoice invoice) {
    final totals = invoice.getCalculatedTotals();

    return pw.Container(
      width: 200,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildTotalRow(
            'المجموع الفرعي:',
            totals['subtotal']!.toStringAsFixed(2),
          ),
          if (invoice.discountAmount > 0)
            _buildTotalRow(
              'الخصم:',
              '-${invoice.discountAmount.toStringAsFixed(2)}',
            ),
          _buildTotalRow('الضريبة:', totals['tax']!.toStringAsFixed(2)),
          pw.Divider(thickness: 2),
          _buildTotalRow(
            'الإجمالي النهائي:',
            totals['total']!.toStringAsFixed(2),
            isTotal: true,
          ),
          if (invoice.paidAmount > 0) ...[
            _buildTotalRow('المدفوع:', invoice.paidAmount.toStringAsFixed(2)),
            _buildTotalRow(
              'المتبقي:',
              (invoice.totalAmount - invoice.paidAmount).toStringAsFixed(2),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء QR Code
  pw.Widget _buildQRCode(Invoice invoice) {
    // يمكن إضافة مكتبة QR Code هنا
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      child: pw.Column(
        children: [
          pw.Text('QR Code'),
          pw.Container(
            width: 100,
            height: 100,
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey),
            ),
            child: pw.Center(child: pw.Text('QR\nCode\nHere')),
          ),
        ],
      ),
    );
  }

  /// بناء قسم التوقيع
  pw.Widget _buildSignatureSection() {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          children: [
            pw.Container(
              width: 150,
              height: 50,
              decoration: pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide(color: PdfColors.black),
                ),
              ),
            ),
            pw.SizedBox(height: 5),
            pw.Text('توقيع المحاسب'),
          ],
        ),
        pw.Column(
          children: [
            pw.Container(
              width: 150,
              height: 50,
              decoration: pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide(color: PdfColors.black),
                ),
              ),
            ),
            pw.SizedBox(height: 5),
            pw.Text('توقيع العميل'),
          ],
        ),
      ],
    );
  }

  /// بناء العلامة المائية
  pw.Widget _buildWatermark(String text) {
    return pw.Positioned.fill(
      child: pw.Center(
        child: pw.Transform.rotate(
          angle: -0.5,
          child: pw.Text(
            text,
            style: pw.TextStyle(
              fontSize: 50,
              color: PdfColors.grey300,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء خلية الجدول المتقدم
  pw.Widget _buildAdvancedTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          fontSize: isHeader ? 10 : 9,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء صف الإجمالي
  pw.Widget _buildTotalRow(String label, String value, {bool isTotal = false}) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            fontSize: isTotal ? 12 : 10,
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            fontSize: isTotal ? 12 : 10,
          ),
        ),
      ],
    );
  }
}
