/// نموذج رصيد المخزون
/// Stock Balance Model for Smart Ledger
library;

import 'warehouse.dart';
import 'item.dart';

/// نموذج رصيد المخزون
/// Stock Balance Model
class StockBalance {
  final int? id;
  final int itemId;
  final int warehouseId;
  final int? locationId;
  final double quantity;
  final double averageCost;
  final double totalValue;
  final String? batchNumber;
  final String? serialNumber;
  final DateTime? expiryDate;
  final DateTime lastMovementDate;
  final DateTime updatedAt;

  // Navigation properties
  Item? item;
  Warehouse? warehouse;
  WarehouseLocation? location;

  StockBalance({
    this.id,
    required this.itemId,
    required this.warehouseId,
    this.locationId,
    this.quantity = 0.0,
    this.averageCost = 0.0,
    this.totalValue = 0.0,
    this.batchNumber,
    this.serialNumber,
    this.expiryDate,
    DateTime? lastMovementDate,
    DateTime? updatedAt,
    this.item,
    this.warehouse,
    this.location,
  }) : lastMovementDate = lastMovementDate ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory StockBalance.fromMap(Map<String, dynamic> map) {
    return StockBalance(
      id: map['id'] as int?,
      itemId: map['item_id'] as int,
      warehouseId: map['warehouse_id'] as int,
      locationId: map['location_id'] as int?,
      quantity: (map['quantity'] as num?)?.toDouble() ?? 0.0,
      averageCost: (map['average_cost'] as num?)?.toDouble() ?? 0.0,
      totalValue: (map['total_value'] as num?)?.toDouble() ?? 0.0,
      batchNumber: map['batch_number'] as String?,
      serialNumber: map['serial_number'] as String?,
      expiryDate: map['expiry_date'] != null 
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      lastMovementDate: DateTime.parse(map['last_movement_date'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'quantity': quantity,
      'average_cost': averageCost,
      'total_value': totalValue,
      'batch_number': batchNumber,
      'serial_number': serialNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      'last_movement_date': lastMovementDate.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  StockBalance copyWith({
    int? id,
    int? itemId,
    int? warehouseId,
    int? locationId,
    double? quantity,
    double? averageCost,
    double? totalValue,
    String? batchNumber,
    String? serialNumber,
    DateTime? expiryDate,
    DateTime? lastMovementDate,
    DateTime? updatedAt,
    Item? item,
    Warehouse? warehouse,
    WarehouseLocation? location,
  }) {
    return StockBalance(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      quantity: quantity ?? this.quantity,
      averageCost: averageCost ?? this.averageCost,
      totalValue: totalValue ?? this.totalValue,
      batchNumber: batchNumber ?? this.batchNumber,
      serialNumber: serialNumber ?? this.serialNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      lastMovementDate: lastMovementDate ?? this.lastMovementDate,
      updatedAt: updatedAt ?? this.updatedAt,
      item: item ?? this.item,
      warehouse: warehouse ?? this.warehouse,
      location: location ?? this.location,
    );
  }

  /// Check if stock is low based on minimum stock level
  bool isLowStock(double? minimumStock) {
    if (minimumStock == null || minimumStock <= 0) return false;
    return quantity <= minimumStock;
  }

  /// Check if stock is expired
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// Check if stock is near expiry (within 30 days)
  bool get isNearExpiry {
    if (expiryDate == null) return false;
    final thirtyDaysFromNow = DateTime.now().add(const Duration(days: 30));
    return expiryDate!.isBefore(thirtyDaysFromNow) && !isExpired;
  }

  /// Get formatted expiry date
  String get formattedExpiryDate {
    if (expiryDate == null) return 'غير محدد';
    return '${expiryDate!.day}/${expiryDate!.month}/${expiryDate!.year}';
  }

  /// Get formatted last movement date
  String get formattedLastMovementDate {
    return '${lastMovementDate.day}/${lastMovementDate.month}/${lastMovementDate.year}';
  }

  @override
  String toString() {
    return 'StockBalance{itemId: $itemId, warehouseId: $warehouseId, quantity: $quantity}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockBalance &&
        other.id == id &&
        other.itemId == itemId &&
        other.warehouseId == warehouseId &&
        other.locationId == locationId &&
        other.batchNumber == batchNumber &&
        other.serialNumber == serialNumber;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      itemId,
      warehouseId,
      locationId,
      batchNumber,
      serialNumber,
    );
  }
}
