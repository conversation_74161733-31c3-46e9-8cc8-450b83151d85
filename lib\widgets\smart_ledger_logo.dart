// ملف لوغو Smart Ledger
// هذا الملف يحتوي على تصميم لوغو احترافي لتطبيق Smart Ledger
// مع إمكانيات تخصيص الحجم واللون والحركة

import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../animations/app_animations.dart';

/// ويدجت لوغو Smart Ledger الرئيسي
/// Main Smart Ledger logo widget
class SmartLedgerLogo extends StatelessWidget {
  final double size;
  final Color? primaryColor;
  final Color? secondaryColor;
  final bool animated;
  final bool showText;
  final TextStyle? textStyle;

  const SmartLedgerLogo({
    super.key,
    this.size = 120,
    this.primaryColor,
    this.secondaryColor,
    this.animated = false,
    this.showText = true,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final primary = primaryColor ?? AppTheme.primaryColor;
    final secondary = secondaryColor ?? AppTheme.secondaryColor;

    Widget logo = _buildLogo(primary, secondary);

    // إضافة الحركة إذا كانت مطلوبة
    if (animated) {
      logo = PulseAnimation(
        duration: const Duration(seconds: 2),
        child: logo,
      );
    }

    if (!showText) {
      return logo;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        logo,
        const SizedBox(height: 12),
        Text(
          'Smart Ledger',
          style: textStyle ??
              Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: primary,
                letterSpacing: 1.2,
              ),
        ),
        Text(
          'دفتر الأستاذ الذكي',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildLogo(Color primary, Color secondary) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size * 0.2),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            primary,
            secondary,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: primary.withValues(alpha: 0.3),
            blurRadius: size * 0.1,
            offset: Offset(0, size * 0.05),
          ),
        ],
      ),
      child: Stack(
        children: [
          // الخلفية المتدرجة
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(size * 0.2),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.1),
                  Colors.transparent,
                ],
              ),
            ),
          ),
          
          // رمز الدفتر
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة الدفتر الذكي
                Icon(
                  Icons.auto_stories_rounded,
                  size: size * 0.4,
                  color: Colors.white,
                ),
                
                // خط تحت الأيقونة
                Container(
                  width: size * 0.3,
                  height: 2,
                  margin: EdgeInsets.only(top: size * 0.05),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
                
                // نقاط تمثل البيانات
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    for (int i = 0; i < 3; i++)
                      Container(
                        width: size * 0.03,
                        height: size * 0.03,
                        margin: EdgeInsets.symmetric(
                          horizontal: size * 0.01,
                          vertical: size * 0.02,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.7),
                          shape: BoxShape.circle,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          
          // تأثير الإضاءة
          Positioned(
            top: size * 0.1,
            right: size * 0.1,
            child: Container(
              width: size * 0.2,
              height: size * 0.2,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// لوغو مصغر للاستخدام في شريط التطبيق
/// Mini logo for app bar usage
class SmartLedgerMiniLogo extends StatelessWidget {
  final double size;
  final Color? color;

  const SmartLedgerMiniLogo({
    super.key,
    this.size = 32,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final logoColor = color ?? AppTheme.primaryColor;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size * 0.2),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            logoColor,
            logoColor.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Icon(
        Icons.auto_stories_rounded,
        size: size * 0.6,
        color: Colors.white,
      ),
    );
  }
}

/// لوغو متحرك للشاشة الترحيبية
/// Animated logo for splash screen
class AnimatedSmartLedgerLogo extends StatefulWidget {
  final double size;
  final VoidCallback? onAnimationComplete;

  const AnimatedSmartLedgerLogo({
    super.key,
    this.size = 150,
    this.onAnimationComplete,
  });

  @override
  State<AnimatedSmartLedgerLogo> createState() => _AnimatedSmartLedgerLogoState();
}

class _AnimatedSmartLedgerLogoState extends State<AnimatedSmartLedgerLogo>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late AnimationController _fadeController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // متحكم حركة التكبير
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // متحكم حركة الدوران
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    // متحكم حركة التلاشي
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // إعداد الحركات
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    // بدء تسلسل الحركات
    _startAnimationSequence();
  }

  void _startAnimationSequence() async {
    // بدء حركة التلاشي
    _fadeController.forward();
    
    // انتظار قليل ثم بدء التكبير
    await Future.delayed(const Duration(milliseconds: 200));
    _scaleController.forward();
    
    // انتظار قليل ثم بدء الدوران
    await Future.delayed(const Duration(milliseconds: 400));
    _rotationController.forward();
    
    // انتظار انتهاء جميع الحركات
    await Future.delayed(const Duration(milliseconds: 1000));
    
    // استدعاء دالة الانتهاء إذا كانت موجودة
    if (widget.onAnimationComplete != null) {
      widget.onAnimationComplete!();
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleController,
        _rotationController,
        _fadeController,
      ]),
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: RotationTransition(
              turns: _rotationAnimation,
              child: SmartLedgerLogo(
                size: widget.size,
                animated: false,
                showText: true,
              ),
            ),
          ),
        );
      },
    );
  }
}
