import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../theme/app_theme.dart';
import '../../models/financial_reports.dart';
import '../../services/financial_reports_service.dart';


/// شاشة ميزان المراجعة
class TrialBalanceScreen extends StatefulWidget {
  const TrialBalanceScreen({super.key});

  @override
  State<TrialBalanceScreen> createState() => _TrialBalanceScreenState();
}

class _TrialBalanceScreenState extends State<TrialBalanceScreen>
    with TickerProviderStateMixin {
  final FinancialReportsService _reportsService = FinancialReportsService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  TrialBalanceReport? _report;
  bool _isLoading = false;
  String? _error;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadTrialBalance();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _loadTrialBalance() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final result = await _reportsService.generateTrialBalance(
        asOfDate: _selectedDate,
      );

      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _report = result.data;
            _isLoading = false;
          });
        } else {
          setState(() {
            _error = result.error;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل ميزان المراجعة: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadTrialBalance();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          '⚖️ ميزان المراجعة',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: 'اختيار التاريخ',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTrialBalance,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(opacity: _fadeAnimation, child: _buildBody());
        },
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // شريط التاريخ والمعلومات
        _buildDateHeader(),

        // المحتوى الرئيسي
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildDateHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue,
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'ميزان المراجعة كما في ${_formatDate(_selectedDate)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (_report != null) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildHeaderStat(
                  'إجمالي المدين',
                  _report!.totalDebits,
                  Icons.add_circle_outline,
                ),
                _buildHeaderStat(
                  'إجمالي الدائن',
                  _report!.totalCredits,
                  Icons.remove_circle_outline,
                ),
                _buildHeaderStat(
                  'الحالة',
                  _report!.isBalanced ? 1 : 0,
                  _report!.isBalanced ? Icons.check_circle : Icons.error,
                  isStatus: true,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeaderStat(
    String label,
    double value,
    IconData icon, {
    bool isStatus = false,
  }) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        Text(
          isStatus
              ? (value == 1 ? 'متوازن' : 'غير متوازن')
              : '${value.toStringAsFixed(2)} ر.س',
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.blue),
            SizedBox(height: 16),
            Text(
              'جاري إنشاء ميزان المراجعة...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTrialBalance,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_report == null || _report!.items.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد بيانات لعرضها', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    return Column(
      children: [
        // جدول ميزان المراجعة
        Expanded(child: _buildTrialBalanceTable()),

        // ملخص الإجماليات
        _buildTotalsSummary(),
      ],
    );
  }

  Widget _buildTrialBalanceTable() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس الجدول
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                  flex: 2,
                  child: Text(
                    'رمز الحساب',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                const Expanded(
                  flex: 4,
                  child: Text(
                    'اسم الحساب',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    'مدين',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    'دائن',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // بيانات الجدول
          Expanded(
            child: AnimationLimiter(
              child: ListView.builder(
                itemCount: _report!.items.length,
                itemBuilder: (context, index) {
                  final item = _report!.items[index];
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 400),
                    child: SlideAnimation(
                      verticalOffset: 30.0,
                      child: FadeInAnimation(
                        child: _buildTableRow(item, index),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(TrialBalanceItem item, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: index.isEven ? Colors.grey[50] : Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              item.accountCode,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(item.accountName, style: const TextStyle(fontSize: 13)),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.debitBalance > 0
                  ? item.debitBalance.toStringAsFixed(2)
                  : '-',
              style: TextStyle(
                fontSize: 13,
                color: item.debitBalance > 0 ? Colors.green[700] : Colors.grey,
                fontWeight: item.debitBalance > 0
                    ? FontWeight.w600
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.creditBalance > 0
                  ? item.creditBalance.toStringAsFixed(2)
                  : '-',
              style: TextStyle(
                fontSize: 13,
                color: item.creditBalance > 0 ? Colors.red[700] : Colors.grey,
                fontWeight: item.creditBalance > 0
                    ? FontWeight.w600
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalsSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _report!.isBalanced ? Colors.green : Colors.red,
            (_report!.isBalanced ? Colors.green : Colors.red).withValues(
              alpha: 0.8,
            ),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (_report!.isBalanced ? Colors.green : Colors.red).withValues(
              alpha: 0.3,
            ),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                _report!.isBalanced ? Icons.check_circle : Icons.error,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                _report!.isBalanced
                    ? 'ميزان المراجعة متوازن'
                    : 'ميزان المراجعة غير متوازن',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTotalCard('إجمالي المدين', _report!.totalDebits),
              _buildTotalCard('إجمالي الدائن', _report!.totalCredits),
              if (!_report!.isBalanced)
                _buildTotalCard('الفرق', _report!.difference.abs()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTotalCard(String label, double amount) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} ر.س',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
