/// شاشة القيود المحاسبية المحسنة
/// Enhanced Journal Entries Screen
library;

import 'package:flutter/material.dart';
import '../../models/journal_entry.dart';
import '../../services/journal_entry_service.dart';
import '../../theme/app_theme.dart';
import 'enhanced_add_journal_entry_screen.dart';
import 'journal_entry_details_screen.dart';

class EnhancedJournalEntriesScreen extends StatefulWidget {
  const EnhancedJournalEntriesScreen({super.key});

  @override
  State<EnhancedJournalEntriesScreen> createState() =>
      _EnhancedJournalEntriesScreenState();
}

class _EnhancedJournalEntriesScreenState
    extends State<EnhancedJournalEntriesScreen>
    with TickerProviderStateMixin {
  final JournalEntryService _journalEntryService = JournalEntryService();
  final TextEditingController _searchController = TextEditingController();

  List<JournalEntry> _allEntries = [];
  List<JournalEntry> _filteredEntries = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final String _statusFilter = 'all';
  DateTime? _startDate;
  DateTime? _endDate;

  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(length: 3, vsync: this);
    _loadJournalEntries();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJournalEntries() async {
    setState(() => _isLoading = true);

    try {
      final result = await _journalEntryService.getAllJournalEntries();
      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _allEntries = result.data!;
            _applyFilters();
          });
        } else {
          _showErrorSnackBar(result.error ?? 'خطأ في تحميل القيود');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل القيود: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _applyFilters() {
    List<JournalEntry> filtered = List.from(_allEntries);

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((entry) {
        return entry.entryNumber.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            entry.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            (entry.reference?.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ??
                false);
      }).toList();
    }

    // تطبيق فلتر الحالة
    if (_statusFilter != 'all') {
      filtered = filtered.where((entry) {
        switch (_statusFilter) {
          case 'posted':
            return entry.isPosted;
          case 'unposted':
            return !entry.isPosted;
          default:
            return true;
        }
      }).toList();
    }

    // تطبيق فلتر التاريخ
    if (_startDate != null) {
      filtered = filtered
          .where(
            (entry) =>
                entry.date.isAfter(_startDate!) ||
                entry.date.isAtSameMomentAs(_startDate!),
          )
          .toList();
    }
    if (_endDate != null) {
      filtered = filtered
          .where(
            (entry) =>
                entry.date.isBefore(_endDate!) ||
                entry.date.isAtSameMomentAs(_endDate!),
          )
          .toList();
    }

    setState(() {
      _filteredEntries = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'القيود المحاسبية',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.list)),
            Tab(text: 'منشورة', icon: Icon(Icons.check_circle)),
            Tab(text: 'مسودة', icon: Icon(Icons.edit)),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildSearchAndFilterSection(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildEntriesList(_filteredEntries),
                  _buildEntriesList(
                    _filteredEntries.where((e) => e.isPosted).toList(),
                  ),
                  _buildEntriesList(
                    _filteredEntries.where((e) => !e.isPosted).toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToAddEntry(),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('قيد جديد'),
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في القيود...',
              prefixIcon: Icon(Icons.search, color: AppTheme.primaryColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
          ),
          const SizedBox(height: 12),
          // فلاتر التاريخ
          Row(
            children: [
              Expanded(
                child: _buildDateFilter('من تاريخ', _startDate, (date) {
                  setState(() {
                    _startDate = date;
                    _applyFilters();
                  });
                }),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDateFilter('إلى تاريخ', _endDate, (date) {
                  setState(() {
                    _endDate = date;
                    _applyFilters();
                  });
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter(
    String label,
    DateTime? selectedDate,
    Function(DateTime?) onDateSelected,
  ) {
    return GestureDetector(
      onTap: () async {
        final DateTime? picked = await showDatePicker(
          context: context,
          initialDate: selectedDate ?? DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime(2030),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(primary: AppTheme.primaryColor),
              ),
              child: child!,
            );
          },
        );
        if (picked != null) {
          onDateSelected(picked);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[50],
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: AppTheme.primaryColor, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  Text(
                    selectedDate != null
                        ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                        : 'اختر التاريخ',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: selectedDate != null
                          ? Colors.black
                          : Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
            if (selectedDate != null)
              GestureDetector(
                onTap: () => onDateSelected(null),
                child: Icon(Icons.clear, color: Colors.grey[400], size: 18),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEntriesList(List<JournalEntry> entries) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (entries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد قيود محاسبية',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة قيد محاسبي جديد',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadJournalEntries,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: entries.length,
        itemBuilder: (context, index) => _buildEntryCard(entries[index], index),
      ),
    );
  }

  Widget _buildEntryCard(JournalEntry entry, int index) {
    final totalAmount = entry.lines.fold(
      0.0,
      (sum, line) => sum + line.debitAmount,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () => _navigateToEntryDetails(entry),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: entry.isPosted
                                ? Colors.green.withValues(alpha: 0.1)
                                : Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            entry.isPosted ? Icons.check_circle : Icons.edit,
                            color: entry.isPosted
                                ? Colors.green
                                : Colors.orange,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'قيد رقم ${entry.entryNumber}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              entry.isPosted ? 'منشور' : 'مسودة',
                              style: TextStyle(
                                color: entry.isPosted
                                    ? Colors.green
                                    : Colors.orange,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${totalAmount.toStringAsFixed(2)} ر.س',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Text(
                          '${entry.date.day}/${entry.date.month}/${entry.date.year}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  entry.description,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (entry.reference != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.link, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        'المرجع: ${entry.reference}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${entry.lines.length} بند',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    Row(
                      children: [
                        if (!entry.isPosted)
                          IconButton(
                            onPressed: () => _navigateToEditEntry(entry),
                            icon: const Icon(Icons.edit, size: 20),
                            tooltip: 'تعديل',
                            color: Colors.blue,
                          ),
                        IconButton(
                          onPressed: () => _showEntryOptions(entry),
                          icon: const Icon(Icons.more_vert, size: 20),
                          tooltip: 'خيارات',
                          color: Colors.grey[600],
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToAddEntry() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EnhancedAddJournalEntryScreen(),
      ),
    );

    if (result == true) {
      _loadJournalEntries();
    }
  }

  void _navigateToEditEntry(JournalEntry entry) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedAddJournalEntryScreen(entry: entry),
      ),
    );

    if (result == true) {
      _loadJournalEntries();
    }
  }

  void _navigateToEntryDetails(JournalEntry entry) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JournalEntryDetailsScreen(entry: entry),
      ),
    );
  }

  void _showEntryOptions(JournalEntry entry) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'قيد رقم ${entry.entryNumber}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            _buildOptionTile(
              icon: Icons.visibility,
              title: 'عرض التفاصيل',
              onTap: () {
                Navigator.pop(context);
                _navigateToEntryDetails(entry);
              },
            ),
            if (!entry.isPosted) ...[
              _buildOptionTile(
                icon: Icons.edit,
                title: 'تعديل',
                onTap: () {
                  Navigator.pop(context);
                  _navigateToEditEntry(entry);
                },
              ),
              _buildOptionTile(
                icon: Icons.publish,
                title: 'نشر القيد',
                onTap: () {
                  Navigator.pop(context);
                  _postEntry(entry);
                },
              ),
            ],
            _buildOptionTile(
              icon: Icons.copy,
              title: 'نسخ القيد',
              onTap: () {
                Navigator.pop(context);
                _duplicateEntry(entry);
              },
            ),
            if (!entry.isPosted)
              _buildOptionTile(
                icon: Icons.delete,
                title: 'حذف',
                color: Colors.red,
                onTap: () {
                  Navigator.pop(context);
                  _confirmDeleteEntry(entry);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? AppTheme.primaryColor),
      title: Text(
        title,
        style: TextStyle(color: color, fontWeight: FontWeight.w500),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }

  Future<void> _postEntry(JournalEntry entry) async {
    try {
      final result = await _journalEntryService.postJournalEntry(entry.id!);
      if (result.isSuccess) {
        _showSuccessSnackBar('تم نشر القيد بنجاح');
        _loadJournalEntries();
      } else {
        _showErrorSnackBar(result.error ?? 'خطأ في نشر القيد');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في نشر القيد: $e');
    }
  }

  Future<void> _duplicateEntry(JournalEntry entry) async {
    final duplicatedEntry = JournalEntry(
      entryNumber: '', // Will be generated
      date: DateTime.now(),
      description: '${entry.description} (نسخة)',
      reference: entry.reference,
      lines: entry.lines
          .map(
            (line) => JournalEntryLine(
              journalEntryId: 0,
              accountId: line.accountId,
              description: line.description,
              debitAmount: line.debitAmount,
              creditAmount: line.creditAmount,
            ),
          )
          .toList(),
      isPosted: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: 'current_user',
    );

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            EnhancedAddJournalEntryScreen(entry: duplicatedEntry),
      ),
    );

    if (result == true) {
      _loadJournalEntries();
    }
  }

  void _confirmDeleteEntry(JournalEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القيد رقم ${entry.entryNumber}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEntry(entry);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteEntry(JournalEntry entry) async {
    try {
      final result = await _journalEntryService.deleteJournalEntry(entry.id!);
      if (result.isSuccess) {
        _showSuccessSnackBar('تم حذف القيد بنجاح');
        _loadJournalEntries();
      } else {
        _showErrorSnackBar(result.error ?? 'خطأ في حذف القيد');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف القيد: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
