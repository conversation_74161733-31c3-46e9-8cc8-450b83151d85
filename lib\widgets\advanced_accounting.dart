import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 💼 نظام المحاسبة المتقدم الشامل
/// Advanced Comprehensive Accounting System

/// 🌟 لوحة المحاسبة المتقدمة الشاملة
class AdvancedAccountingDashboard extends StatefulWidget {
  const AdvancedAccountingDashboard({super.key});

  @override
  State<AdvancedAccountingDashboard> createState() =>
      _AdvancedAccountingDashboardState();
}

class _AdvancedAccountingDashboardState
    extends State<AdvancedAccountingDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _dataController;
  late Animation<double> _mainAnimation;
  late Animation<double> _dataAnimation;

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _dataController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _dataAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _dataController, curve: Curves.easeInOut),
    );

    _mainController.repeat(reverse: true);
    _dataController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _mainController.dispose();
    _dataController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_mainAnimation, _dataAnimation]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.5 + (_mainAnimation.value * 0.5),
          child: QuantumEnergyEffect(
            intensity: 2.0 + (_dataAnimation.value * 0.3),
            primaryColor: const Color(0xFF673AB7),
            secondaryColor: const Color(0xFF9C27B0),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF4A148C).withValues(alpha: 0.9),
                    const Color(0xFF6A1B9A).withValues(alpha: 0.8),
                    const Color(0xFF8E24AA).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF9C27B0).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF9C27B0).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس المحاسبة المتقدمة
                  Row(
                    children: [
                      Transform.scale(
                        scale: _dataAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(18),
                          decoration: BoxDecoration(
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFF9C27B0).withValues(alpha: 0.9),
                                const Color(0xFFBA68C8).withValues(alpha: 0.6),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.5),
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.account_balance_rounded,
                            color: Colors.white,
                            size: 36,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '💼 المحاسبة المتقدمة',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام محاسبي شامل ومتطور',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // الوحدات المحاسبية المتقدمة
                  _buildAdvancedModules(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء الوحدات المحاسبية المتقدمة
  Widget _buildAdvancedModules() {
    final modules = [
      AccountingModule(
        title: 'دليل الحسابات المتقدم',
        description: 'إدارة شاملة لجميع الحسابات مع تصنيف متقدم',
        icon: Icons.account_tree_rounded,
        color: const Color(0xFF2196F3),
        progress: 0.95,
      ),
      AccountingModule(
        title: 'إدارة المخزون الذكي',
        description: 'تتبع المخزون بتقنية FIFO/LIFO/المتوسط المرجح',
        icon: Icons.inventory_2_rounded,
        color: const Color(0xFF4CAF50),
        progress: 0.88,
      ),
      AccountingModule(
        title: 'محاسبة التكاليف',
        description: 'حساب التكاليف المباشرة وغير المباشرة',
        icon: Icons.calculate_rounded,
        color: const Color(0xFFFF9800),
        progress: 0.92,
      ),
      AccountingModule(
        title: 'إدارة الأصول الثابتة',
        description: 'متابعة الأصول والاستهلاك التلقائي',
        icon: Icons.business_rounded,
        color: const Color(0xFFF44336),
        progress: 0.85,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🔧 الوحدات المحاسبية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        ...modules.map((module) => _buildModuleCard(module)),
      ],
    );
  }

  Widget _buildModuleCard(AccountingModule module) {
    return InkWell(
      onTap: () => _handleModuleTap(module),
      borderRadius: BorderRadius.circular(18),
      child: Container(
        margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(18),
          border: Border.all(
            color: module.color.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: module.color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(module.icon, color: module.color, size: 24),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        module.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingXSmall),
                      Text(
                        module.description,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 12,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${(module.progress * 100).toInt()}%',
                  style: TextStyle(
                    color: module.color,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            LinearProgressIndicator(
              value: module.progress,
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(module.color),
            ),
          ],
        ),
      ),
    );
  }

  void _handleModuleTap(AccountingModule module) {
    if (module.title == 'إدارة الأصول الثابتة') {
      Navigator.pushNamed(context, '/fixed-assets-dashboard');
    }
    // يمكن إضافة المزيد من الوحدات هنا
  }
}

/// 🏦 بطاقة البنوك والخزائن المتقدمة
class AdvancedBanksTreasuriesCard extends StatefulWidget {
  const AdvancedBanksTreasuriesCard({super.key});

  @override
  State<AdvancedBanksTreasuriesCard> createState() =>
      _AdvancedBanksTreasuriesCardState();
}

class _AdvancedBanksTreasuriesCardState
    extends State<AdvancedBanksTreasuriesCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  final List<BankAccount> _bankAccounts = [
    BankAccount(
      name: 'البنك الأهلي التجاري',
      accountNumber: '**********',
      balance: 250000.0,
      currency: 'ر.س',
      type: 'جاري',
      color: const Color(0xFF1976D2),
    ),
    BankAccount(
      name: 'بنك الراجحي',
      accountNumber: '**********',
      balance: 180000.0,
      currency: 'ر.س',
      type: 'توفير',
      color: const Color(0xFF388E3C),
    ),
    BankAccount(
      name: 'الخزينة الرئيسية',
      accountNumber: 'CASH001',
      balance: 45000.0,
      currency: 'ر.س',
      type: 'نقدي',
      color: const Color(0xFFE64A19),
    ),
  ];

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return HologramEffect(
          intensity: 1.5 + (_animation.value * 0.5),
          child: Container(
            margin: const EdgeInsets.all(AppTheme.spacingMedium),
            padding: const EdgeInsets.all(AppTheme.spacingLarge),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF0D47A1).withValues(alpha: 0.9),
                  const Color(0xFF1565C0).withValues(alpha: 0.8),
                  const Color(0xFF1976D2).withValues(alpha: 0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: const Color(0xFF1976D2).withValues(alpha: 0.6),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF1976D2).withValues(alpha: 0.4),
                  blurRadius: 25,
                  offset: const Offset(0, 15),
                  spreadRadius: 3,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس البنوك والخزائن
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Icon(
                        Icons.account_balance_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Text(
                      '🏦 البنوك والخزائن',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.spacingLarge),

                // قائمة الحسابات البنكية
                ...List.generate(_bankAccounts.length, (index) {
                  return _buildBankAccountCard(_bankAccounts[index], index);
                }),

                const SizedBox(height: AppTheme.spacingMedium),

                // إجمالي الأرصدة
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'إجمالي الأرصدة:',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_calculateTotalBalance().toStringAsFixed(2)} ر.س',
                        style: const TextStyle(
                          color: Color(0xFF4CAF50),
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBankAccountCard(BankAccount account, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: account.color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: account.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  account.type == 'نقدي'
                      ? Icons.money_rounded
                      : Icons.account_balance_rounded,
                  color: account.color,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      account.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'رقم الحساب: ${account.accountNumber}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: account.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  account.type,
                  style: TextStyle(
                    color: account.color,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'الرصيد الحالي:',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
              Text(
                '${account.balance.toStringAsFixed(2)} ${account.currency}',
                style: TextStyle(
                  color: account.color,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  double _calculateTotalBalance() {
    return _bankAccounts.fold(0.0, (sum, account) => sum + account.balance);
  }
}

/// نموذج الوحدة المحاسبية
class AccountingModule {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final double progress;

  AccountingModule({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.progress,
  });
}

/// نموذج الحساب البنكي
class BankAccount {
  final String name;
  final String accountNumber;
  final double balance;
  final String currency;
  final String type;
  final Color color;

  BankAccount({
    required this.name,
    required this.accountNumber,
    required this.balance,
    required this.currency,
    required this.type,
    required this.color,
  });
}
