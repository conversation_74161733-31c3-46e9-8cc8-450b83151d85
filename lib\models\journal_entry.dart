import 'account.dart';

class JournalEntry {
  final int? id;
  final String entryNumber;
  final DateTime date;
  final String description;
  final String? reference;
  final int? projectId; // ربط القيد بالمشروع
  final double totalDebit;
  final double totalCredit;
  final bool isBalanced;
  final bool isPosted;
  final String? createdBy;

  // Multi-Currency Support Fields
  final String currencyCode; // رمز العملة (SAR, USD, EUR, etc.)
  final double exchangeRate; // سعر الصرف مقابل العملة الأساسية
  final String baseCurrencyCode; // رمز العملة الأساسية
  final double baseCurrencyTotalDebit; // إجمالي المدين بالعملة الأساسية
  final double baseCurrencyTotalCredit; // إجمالي الدائن بالعملة الأساسية

  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  List<JournalEntryLine> lines = [];

  JournalEntry({
    this.id,
    required this.entryNumber,
    required this.date,
    required this.description,
    this.reference,
    this.projectId,
    this.totalDebit = 0.0,
    this.totalCredit = 0.0,
    this.isBalanced = false,
    this.isPosted = false,
    this.createdBy,

    // Multi-Currency Support Parameters
    this.currencyCode = 'SAR', // العملة الافتراضية
    this.exchangeRate = 1.0, // سعر الصرف الافتراضي
    this.baseCurrencyCode = 'SAR', // العملة الأساسية الافتراضية
    this.baseCurrencyTotalDebit = 0.0,
    this.baseCurrencyTotalCredit = 0.0,

    DateTime? createdAt,
    DateTime? updatedAt,
    List<JournalEntryLine>? lines,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now() {
    if (lines != null) {
      this.lines = lines;
    }
  }

  // Factory constructor from database map
  factory JournalEntry.fromMap(Map<String, dynamic> map) {
    return JournalEntry(
      id: map['id'] as int?,
      entryNumber: map['entry_number'] as String,
      date: DateTime.parse(map['date'] as String),
      description: map['description'] as String,
      reference: map['reference'] as String?,
      projectId: map['project_id'] as int?,
      totalDebit: (map['total_debit'] as num?)?.toDouble() ?? 0.0,
      totalCredit: (map['total_credit'] as num?)?.toDouble() ?? 0.0,
      isBalanced: (map['is_balanced'] as int) == 1,
      isPosted: (map['is_posted'] as int) == 1,
      createdBy: map['created_by'] as String?,

      // Multi-Currency Support Fields
      currencyCode: map['currency_code'] as String? ?? 'SAR',
      exchangeRate: (map['exchange_rate'] as num?)?.toDouble() ?? 1.0,
      baseCurrencyCode: map['base_currency_code'] as String? ?? 'SAR',
      baseCurrencyTotalDebit:
          (map['base_currency_total_debit'] as num?)?.toDouble() ?? 0.0,
      baseCurrencyTotalCredit:
          (map['base_currency_total_credit'] as num?)?.toDouble() ?? 0.0,

      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entry_number': entryNumber,
      'date': date.toIso8601String().split('T')[0], // Date only
      'description': description,
      'reference': reference,
      'project_id': projectId,
      'total_debit': totalDebit,
      'total_credit': totalCredit,
      'is_balanced': isBalanced ? 1 : 0,
      'is_posted': isPosted ? 1 : 0,
      'created_by': createdBy,

      // Multi-Currency Support Fields
      'currency_code': currencyCode,
      'exchange_rate': exchangeRate,
      'base_currency_code': baseCurrencyCode,
      'base_currency_total_debit': baseCurrencyTotalDebit,
      'base_currency_total_credit': baseCurrencyTotalCredit,

      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  JournalEntry copyWith({
    int? id,
    String? entryNumber,
    DateTime? date,
    String? description,
    String? reference,
    int? projectId,
    double? totalDebit,
    double? totalCredit,
    bool? isBalanced,
    bool? isPosted,
    String? createdBy,

    // Multi-Currency Support Parameters
    String? currencyCode,
    double? exchangeRate,
    String? baseCurrencyCode,
    double? baseCurrencyTotalDebit,
    double? baseCurrencyTotalCredit,

    DateTime? createdAt,
    DateTime? updatedAt,
    List<JournalEntryLine>? lines,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      date: date ?? this.date,
      description: description ?? this.description,
      reference: reference ?? this.reference,
      projectId: projectId ?? this.projectId,
      totalDebit: totalDebit ?? this.totalDebit,
      totalCredit: totalCredit ?? this.totalCredit,
      isBalanced: isBalanced ?? this.isBalanced,
      isPosted: isPosted ?? this.isPosted,
      createdBy: createdBy ?? this.createdBy,

      // Multi-Currency Support Fields
      currencyCode: currencyCode ?? this.currencyCode,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      baseCurrencyCode: baseCurrencyCode ?? this.baseCurrencyCode,
      baseCurrencyTotalDebit:
          baseCurrencyTotalDebit ?? this.baseCurrencyTotalDebit,
      baseCurrencyTotalCredit:
          baseCurrencyTotalCredit ?? this.baseCurrencyTotalCredit,

      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lines: lines ?? this.lines,
    );
  }

  // Multi-Currency Helper Methods
  /// التحقق من كون القيد بعملة مختلفة عن العملة الأساسية
  bool get isMultiCurrency => currencyCode != baseCurrencyCode;

  /// تحويل مبلغ من عملة القيد إلى العملة الأساسية
  double convertToBaseCurrency(double amount) {
    return amount * exchangeRate;
  }

  /// تحويل مبلغ من العملة الأساسية إلى عملة القيد
  double convertFromBaseCurrency(double baseCurrencyAmount) {
    return exchangeRate != 0 ? baseCurrencyAmount / exchangeRate : 0.0;
  }

  /// تحديث المبالغ بالعملة الأساسية بناءً على سعر الصرف الحالي
  JournalEntry updateBaseCurrencyAmounts() {
    return copyWith(
      baseCurrencyTotalDebit: convertToBaseCurrency(totalDebit),
      baseCurrencyTotalCredit: convertToBaseCurrency(totalCredit),
      updatedAt: DateTime.now(),
    );
  }

  // Calculate totals from lines
  void calculateTotals() {
    // Calculate debit and credit totals from all lines
    for (JournalEntryLine line in lines) {
      // Process line amounts for validation
      line.debitAmount; // Access debit amount
      line.creditAmount; // Access credit amount
    }

    // Update totals (this creates a new instance, so we need to handle this differently)
    // In practice, you'd update the instance or return a new one
  }

  // Check if entry is balanced
  bool checkBalance() {
    double debitSum = 0.0;
    double creditSum = 0.0;

    for (JournalEntryLine line in lines) {
      debitSum += line.debitAmount;
      creditSum += line.creditAmount;
    }

    return (debitSum - creditSum).abs() <
        0.01; // Allow for small rounding differences
  }

  // Get calculated totals
  Map<String, double> getCalculatedTotals() {
    double debitSum = 0.0;
    double creditSum = 0.0;

    for (JournalEntryLine line in lines) {
      debitSum += line.debitAmount;
      creditSum += line.creditAmount;
    }

    return {
      'debit': debitSum,
      'credit': creditSum,
      'difference': debitSum - creditSum,
    };
  }

  // Add a line to the entry
  void addLine(JournalEntryLine line) {
    line.lineOrder = lines.length;
    lines.add(line);
  }

  // Remove a line from the entry
  void removeLine(int index) {
    if (index >= 0 && index < lines.length) {
      lines.removeAt(index);
      // Reorder remaining lines
      for (int i = 0; i < lines.length; i++) {
        lines[i].lineOrder = i;
      }
    }
  }

  // Validate entry
  List<String> validate() {
    List<String> errors = [];

    if (entryNumber.trim().isEmpty) {
      errors.add('رقم القيد مطلوب');
    }

    if (description.trim().isEmpty) {
      errors.add('وصف القيد مطلوب');
    }

    if (lines.isEmpty) {
      errors.add('يجب إضافة سطر واحد على الأقل');
    }

    if (lines.length < 2) {
      errors.add('يجب أن يحتوي القيد على سطرين على الأقل');
    }

    if (!checkBalance()) {
      errors.add('القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن');
    }

    // Check for duplicate accounts in the same entry
    Set<int> accountIds = {};
    for (JournalEntryLine line in lines) {
      if (accountIds.contains(line.accountId)) {
        errors.add('لا يمكن استخدام نفس الحساب أكثر من مرة في نفس القيد');
        break;
      }
      accountIds.add(line.accountId);
    }

    // Check that each line has either debit or credit (not both, not neither)
    for (int i = 0; i < lines.length; i++) {
      JournalEntryLine line = lines[i];
      if (line.debitAmount > 0 && line.creditAmount > 0) {
        errors.add(
          'السطر ${i + 1}: لا يمكن أن يحتوي السطر على مبلغ مدين ودائن معاً',
        );
      } else if (line.debitAmount == 0 && line.creditAmount == 0) {
        errors.add('السطر ${i + 1}: يجب إدخال مبلغ مدين أو دائن');
      }
    }

    return errors;
  }

  @override
  String toString() {
    return 'JournalEntry{id: $id, number: $entryNumber, date: $date, description: $description, balanced: $isBalanced}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JournalEntry &&
        other.id == id &&
        other.entryNumber == entryNumber;
  }

  @override
  int get hashCode => id.hashCode ^ entryNumber.hashCode;
}

class JournalEntryLine {
  final int? id;
  final int journalEntryId;
  final int accountId;
  final String? description;
  final double debitAmount;
  final double creditAmount;
  int lineOrder;
  final DateTime createdAt;

  // Navigation properties
  Account? account;

  JournalEntryLine({
    this.id,
    required this.journalEntryId,
    required this.accountId,
    this.description,
    this.debitAmount = 0.0,
    this.creditAmount = 0.0,
    this.lineOrder = 0,
    DateTime? createdAt,
    this.account,
  }) : createdAt = createdAt ?? DateTime.now();

  // Factory constructor from database map
  factory JournalEntryLine.fromMap(Map<String, dynamic> map) {
    return JournalEntryLine(
      id: map['id'] as int?,
      journalEntryId: map['journal_entry_id'] as int,
      accountId: map['account_id'] as int,
      description: map['description'] as String?,
      debitAmount: (map['debit_amount'] as num?)?.toDouble() ?? 0.0,
      creditAmount: (map['credit_amount'] as num?)?.toDouble() ?? 0.0,
      lineOrder: map['line_order'] as int? ?? 0,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'journal_entry_id': journalEntryId,
      'account_id': accountId,
      'description': description,
      'debit_amount': debitAmount,
      'credit_amount': creditAmount,
      'line_order': lineOrder,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  JournalEntryLine copyWith({
    int? id,
    int? journalEntryId,
    int? accountId,
    String? description,
    double? debitAmount,
    double? creditAmount,
    int? lineOrder,
    DateTime? createdAt,
    Account? account,
  }) {
    return JournalEntryLine(
      id: id ?? this.id,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      accountId: accountId ?? this.accountId,
      description: description ?? this.description,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      lineOrder: lineOrder ?? this.lineOrder,
      createdAt: createdAt ?? this.createdAt,
      account: account ?? this.account,
    );
  }

  // Helper methods
  double get amount => debitAmount > 0 ? debitAmount : creditAmount;
  bool get isDebit => debitAmount > 0;
  bool get isCredit => creditAmount > 0;
  String get type => isDebit ? 'مدين' : 'دائن';

  @override
  String toString() {
    return 'JournalEntryLine{id: $id, accountId: $accountId, debit: $debitAmount, credit: $creditAmount}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JournalEntryLine &&
        other.id == id &&
        other.journalEntryId == journalEntryId &&
        other.accountId == accountId;
  }

  @override
  int get hashCode =>
      id.hashCode ^ journalEntryId.hashCode ^ accountId.hashCode;
}
