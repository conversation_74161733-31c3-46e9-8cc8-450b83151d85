import 'package:flutter/material.dart';
import '../../models/customer.dart';
import '../../models/invoice.dart';
import '../../services/customer_service.dart';
import '../../services/invoice_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import '../../animations/app_animations.dart';
import 'add_customer_screen.dart';

/// شاشة تفاصيل العميل
class CustomerDetailsScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailsScreen({super.key, required this.customer});

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final CustomerService _customerService = CustomerService();
  final InvoiceService _invoiceService = InvoiceService();

  late Customer _customer;
  List<Invoice> _customerInvoices = [];
  bool _isLoadingInvoices = false;

  // إحصائيات العميل
  double _totalSales = 0.0;
  double _totalPaid = 0.0;
  double _totalOutstanding = 0.0;
  int _invoiceCount = 0;

  @override
  void initState() {
    super.initState();
    _customer = widget.customer;
    _tabController = TabController(length: 3, vsync: this);
    _loadCustomerData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomerData() async {
    await Future.wait([_loadCustomerInvoices(), _loadCustomerStatistics()]);
  }

  Future<void> _loadCustomerInvoices() async {
    setState(() {
      _isLoadingInvoices = true;
    });

    try {
      final result = await _invoiceService.getInvoicesByCustomer(_customer.id!);
      if (result.isSuccess) {
        setState(() {
          _customerInvoices = result.data!;
          _isLoadingInvoices = false;
        });
      } else {
        setState(() {
          _isLoadingInvoices = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingInvoices = false;
      });
    }
  }

  Future<void> _loadCustomerStatistics() async {
    try {
      final result = await _customerService.getCustomerStatistics(
        _customer.id!,
      );
      if (result.isSuccess && mounted) {
        final stats = result.data!;
        setState(() {
          _totalSales = stats['totalSales'] ?? 0.0;
          _totalPaid = stats['totalPaid'] ?? 0.0;
          _totalOutstanding = stats['totalOutstanding'] ?? 0.0;
          _invoiceCount = stats['invoiceCount'] ?? 0;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_customer.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => AddCustomerScreen(customer: _customer),
                ),
              );
              if (result == true) {
                // إعادة تحميل بيانات العميل
                final updatedResult = await _customerService.getCustomerById(
                  _customer.id!,
                );
                if (updatedResult.isSuccess && mounted) {
                  setState(() {
                    _customer = updatedResult.data!;
                  });
                }
              }
            },
            tooltip: 'تعديل',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المعلومات', icon: Icon(Icons.info)),
            Tab(text: 'الفواتير', icon: Icon(Icons.receipt)),
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildInfoTab(), _buildInvoicesTab(), _buildStatisticsTab()],
      ),
    );
  }

  Widget _buildInfoTab() {
    return ListView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      children: [
        // بطاقة المعلومات الأساسية
        StaggeredAnimation(
          index: 0,
          child: BeautifulCard(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppTheme.primaryColor,
                        child: Text(
                          _customer.name.isNotEmpty
                              ? _customer.name[0].toUpperCase()
                              : 'ع',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _customer.name,
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'رمز العميل: ${_customer.code}',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                            Container(
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _customer.isActive
                                    ? Colors.green
                                    : Colors.red,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _customer.isActive ? 'نشط' : 'غير نشط',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingMedium),

        // بطاقة معلومات الاتصال
        StaggeredAnimation(
          index: 1,
          child: BeautifulCard(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات الاتصال',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  if (_customer.phone != null && _customer.phone!.isNotEmpty)
                    _buildInfoRow(Icons.phone, 'الهاتف', _customer.phone!),
                  if (_customer.email != null && _customer.email!.isNotEmpty)
                    _buildInfoRow(
                      Icons.email,
                      'البريد الإلكتروني',
                      _customer.email!,
                    ),
                  if (_customer.address != null &&
                      _customer.address!.isNotEmpty)
                    _buildInfoRow(
                      Icons.location_on,
                      'العنوان',
                      _customer.address!,
                    ),
                  if (_customer.taxNumber != null &&
                      _customer.taxNumber!.isNotEmpty)
                    _buildInfoRow(
                      Icons.receipt_long,
                      'الرقم الضريبي',
                      _customer.taxNumber!,
                    ),
                ],
              ),
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingMedium),

        // بطاقة المعلومات المالية
        StaggeredAnimation(
          index: 2,
          child: BeautifulCard(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المعلومات المالية',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  _buildInfoRow(
                    Icons.account_balance_wallet,
                    'الرصيد الحالي',
                    '${_customer.currentBalance.toStringAsFixed(2)} ر.س',
                  ),
                  _buildInfoRow(
                    Icons.credit_card,
                    'الحد الائتماني',
                    '${_customer.creditLimit.toStringAsFixed(2)} ر.س',
                  ),
                ],
              ),
            ),
          ),
        ),

        if (_customer.notes != null && _customer.notes!.isNotEmpty) ...[
          const SizedBox(height: AppTheme.spacingMedium),
          StaggeredAnimation(
            index: 3,
            child: BeautifulCard(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ملاحظات',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingSmall),
                    Text(_customer.notes!),
                  ],
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildInvoicesTab() {
    return _isLoadingInvoices
        ? const Center(child: CircularProgressIndicator())
        : _customerInvoices.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Text(
                  'لا توجد فواتير لهذا العميل',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            itemCount: _customerInvoices.length,
            itemBuilder: (context, index) {
              final invoice = _customerInvoices[index];
              return StaggeredAnimation(
                index: index,
                child: _buildInvoiceCard(invoice),
              );
            },
          );
  }

  Widget _buildStatisticsTab() {
    return ListView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      children: [
        // إحصائيات المبيعات
        StaggeredAnimation(
          index: 0,
          child: BeautifulCard(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إحصائيات المبيعات',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'إجمالي المبيعات',
                          '${_totalSales.toStringAsFixed(2)} ر.س',
                          Icons.trending_up,
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Expanded(
                        child: _buildStatCard(
                          'عدد الفواتير',
                          _invoiceCount.toString(),
                          Icons.receipt,
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.spacingSmall),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'المبلغ المدفوع',
                          '${_totalPaid.toStringAsFixed(2)} ر.س',
                          Icons.payment,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Expanded(
                        child: _buildStatCard(
                          'المبلغ المستحق',
                          '${_totalOutstanding.toStringAsFixed(2)} ر.س',
                          Icons.pending_actions,
                          Colors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: AppTheme.spacingSmall),
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w500)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildInvoiceCard(Invoice invoice) {
    return BeautifulCard(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getInvoiceStatusColor(invoice.status),
          child: Icon(Icons.receipt, color: Colors.white, size: 20),
        ),
        title: Text('فاتورة رقم: ${invoice.number}'),
        subtitle: Text(
          'التاريخ: ${invoice.date.day}/${invoice.date.month}/${invoice.date.year}',
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${invoice.totalAmount.toStringAsFixed(2)} ر.س',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              _getInvoiceStatusText(invoice.status),
              style: TextStyle(
                color: _getInvoiceStatusColor(invoice.status),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 16,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getInvoiceStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.posted:
        return Colors.blue.shade300;
      case InvoiceStatus.sent:
        return Colors.blue;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.partiallyPaid:
        return Colors.orange;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.red;
    }
  }

  String _getInvoiceStatusText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.posted:
        return 'مرحلة';
      case InvoiceStatus.sent:
        return 'مرسلة';
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.partiallyPaid:
        return 'مدفوعة جزئياً';
      case InvoiceStatus.overdue:
        return 'متأخرة';
      case InvoiceStatus.cancelled:
        return 'ملغية';
    }
  }
}
