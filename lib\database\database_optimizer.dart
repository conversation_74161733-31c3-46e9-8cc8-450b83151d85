import 'dart:developer' as developer;
import 'database_helper.dart';

/// 🚀 محسن قاعدة البيانات
/// Database Optimizer for Smart Ledger
class DatabaseOptimizer {
  static final DatabaseOptimizer _instance = DatabaseOptimizer._internal();
  factory DatabaseOptimizer() => _instance;
  DatabaseOptimizer._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إنشاء الفهارس لتحسين الأداء
  Future<void> createIndexes() async {
    final db = await _dbHelper.database;

    try {
      // فهارس جدول الحسابات
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_accounts_code ON accounts(code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_accounts_parent_id ON accounts(parent_id);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(account_type);
      ''');

      // فهارس جدول القيود المحاسبية
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(entry_date);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_journal_entries_number ON journal_entries(entry_number);
      ''');

      // فهارس جدول تفاصيل القيود
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_entry_id ON journal_entry_lines(journal_entry_id);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_account_id ON journal_entry_lines(account_id);
      ''');

      // فهارس جدول العملاء
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);
      ''');

      // فهارس جدول الموردين
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);
      ''');

      // فهارس جدول الأصناف
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_items_code ON items(code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_items_name ON items(name);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_items_category ON items(category);
      ''');

      // فهارس جدول الفواتير
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(date);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_invoices_customer_id ON invoices(customer_id);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_invoices_supplier_id ON invoices(supplier_id);
      ''');

      // فهارس جدول حركات المخزون
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON stock_movements(movement_date);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_stock_movements_item_id ON stock_movements(item_id);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_stock_movements_warehouse_id ON stock_movements(warehouse_id);
      ''');

      // فهارس جدول أرصدة المخزون
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_stock_balances_item_warehouse ON stock_balances(item_id, warehouse_id);
      ''');

      // فهارس جدول المشاريع
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_projects_code ON projects(code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
      ''');

      // فهارس جدول الموظفين
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_employees_code ON employees(employee_code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department_id);
      ''');

      // فهارس جدول الضرائب
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_taxes_code ON taxes(tax_code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_tax_calculations_date ON tax_calculations(calculation_date);
      ''');

      // فهارس جدول العملات
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_currencies_code ON currencies(currency_code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_exchange_rates_date ON exchange_rates(rate_date);
      ''');

      // فهارس جدول الأصول الثابتة
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_fixed_assets_code ON fixed_assets(asset_code);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_fixed_assets_category ON fixed_assets(category);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_asset_depreciation_asset_id ON asset_depreciation(asset_id);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_asset_depreciation_date ON asset_depreciation(depreciation_date);
      ''');

      // فهارس مركبة للاستعلامات المعقدة
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_account_date ON journal_entry_lines(account_id, created_at);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_invoices_customer_date ON invoices(customer_id, invoice_date);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_invoices_supplier_date ON invoices(supplier_id, invoice_date);
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_stock_movements_item_date ON stock_movements(item_id, movement_date);
      ''');

      developer.log(
        'تم إنشاء جميع الفهارس المحسنة بنجاح (${DateTime.now()})',
        name: 'DatabaseOptimizer',
      );
    } catch (e) {
      developer.log(
        'خطأ في إنشاء الفهارس: $e',
        name: 'DatabaseOptimizer',
        level: 1000,
      );
    }
  }

  /// تحليل قاعدة البيانات
  Future<void> analyzeDatabase() async {
    final db = await _dbHelper.database;

    try {
      await db.execute('ANALYZE');
      developer.log('تم تحليل قاعدة البيانات بنجاح', name: 'DatabaseOptimizer');
    } catch (e) {
      developer.log(
        'خطأ في تحليل قاعدة البيانات: $e',
        name: 'DatabaseOptimizer',
        level: 1000,
      );
    }
  }

  /// تنظيف قاعدة البيانات
  Future<void> vacuumDatabase() async {
    final db = await _dbHelper.database;

    try {
      await db.execute('VACUUM');
      developer.log('تم تنظيف قاعدة البيانات بنجاح', name: 'DatabaseOptimizer');
    } catch (e) {
      developer.log(
        'خطأ في تنظيف قاعدة البيانات: $e',
        name: 'DatabaseOptimizer',
        level: 1000,
      );
    }
  }

  /// تحسين شامل لقاعدة البيانات
  Future<void> optimizeDatabase() async {
    developer.log('بدء تحسين قاعدة البيانات...', name: 'DatabaseOptimizer');

    await createIndexes();
    await analyzeDatabase();
    await vacuumDatabase();

    developer.log('تم تحسين قاعدة البيانات بنجاح', name: 'DatabaseOptimizer');
  }

  /// الحصول على إحصائيات قاعدة البيانات
  Future<Map<String, dynamic>> getDatabaseStats() async {
    final db = await _dbHelper.database;
    final stats = <String, dynamic>{};

    try {
      // حجم قاعدة البيانات
      final sizeResult = await db.rawQuery('PRAGMA page_count');
      final pageSize = await db.rawQuery('PRAGMA page_size');

      if (sizeResult.isNotEmpty && pageSize.isNotEmpty) {
        final pageCount = sizeResult.first['page_count'] as int;
        final pageSizeBytes = pageSize.first['page_size'] as int;
        stats['database_size_bytes'] = pageCount * pageSizeBytes;
        stats['database_size_mb'] = (pageCount * pageSizeBytes) / (1024 * 1024);
      }

      // عدد الجداول
      final tablesResult = await db.rawQuery('''
        SELECT COUNT(*) as table_count 
        FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ''');

      if (tablesResult.isNotEmpty) {
        stats['table_count'] = tablesResult.first['table_count'];
      }

      // عدد الفهارس
      final indexesResult = await db.rawQuery('''
        SELECT COUNT(*) as index_count 
        FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      ''');

      if (indexesResult.isNotEmpty) {
        stats['index_count'] = indexesResult.first['index_count'];
      }

      // إحصائيات الجداول الرئيسية
      final tables = [
        'accounts',
        'journal_entries',
        'customers',
        'suppliers',
        'items',
        'invoices',
        'stock_movements',
        'projects',
        'employees',
      ];

      for (final table in tables) {
        try {
          final countResult = await db.rawQuery(
            'SELECT COUNT(*) as count FROM $table',
          );
          if (countResult.isNotEmpty) {
            stats['${table}_count'] = countResult.first['count'];
          }
        } catch (e) {
          stats['${table}_count'] = 0;
        }
      }
    } catch (e) {
      developer.log(
        'خطأ في الحصول على إحصائيات قاعدة البيانات: $e',
        name: 'DatabaseOptimizer',
        level: 1000,
      );
    }

    return stats;
  }

  /// فحص سلامة قاعدة البيانات
  Future<bool> checkDatabaseIntegrity() async {
    final db = await _dbHelper.database;

    try {
      final result = await db.rawQuery('PRAGMA integrity_check');

      if (result.isNotEmpty) {
        final status = result.first.values.first as String;
        return status == 'ok';
      }

      return false;
    } catch (e) {
      developer.log(
        'خطأ في فحص سلامة قاعدة البيانات: $e',
        name: 'DatabaseOptimizer',
        level: 1000,
      );
      return false;
    }
  }

  /// تحسين استعلامات محددة
  Future<void> optimizeQueries() async {
    final db = await _dbHelper.database;

    try {
      // تحسين استعلام الحسابات
      await db.execute('''
        CREATE VIEW IF NOT EXISTS v_accounts_with_balance AS
        SELECT 
          a.*,
          COALESCE(SUM(jel.debit_amount - jel.credit_amount), 0) as balance
        FROM accounts a
        LEFT JOIN journal_entry_lines jel ON a.id = jel.account_id
        GROUP BY a.id
      ''');

      // تحسين استعلام أرصدة المخزون
      await db.execute('''
        CREATE VIEW IF NOT EXISTS v_current_stock_balances AS
        SELECT 
          i.id as item_id,
          i.name as item_name,
          w.id as warehouse_id,
          w.name as warehouse_name,
          COALESCE(sb.quantity, 0) as current_quantity,
          COALESCE(sb.value, 0) as current_value
        FROM items i
        CROSS JOIN warehouses w
        LEFT JOIN stock_balances sb ON i.id = sb.item_id AND w.id = sb.warehouse_id
      ''');

      developer.log('تم تحسين الاستعلامات بنجاح', name: 'DatabaseOptimizer');
    } catch (e) {
      developer.log(
        'خطأ في تحسين الاستعلامات: $e',
        name: 'DatabaseOptimizer',
        level: 1000,
      );
    }
  }
}
