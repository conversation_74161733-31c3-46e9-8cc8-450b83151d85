import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/journal_entry.dart';
import '../../models/account.dart';
import '../../services/journal_entry_service.dart';
import '../../services/account_service.dart';
import '../../services/user_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_text_form_field.dart';

class AddJournalEntryScreen extends StatefulWidget {
  final JournalEntry? entry;

  const AddJournalEntryScreen({super.key, this.entry});

  @override
  State<AddJournalEntryScreen> createState() => _AddJournalEntryScreenState();
}

class _AddJournalEntryScreenState extends State<AddJournalEntryScreen> {
  final _formKey = GlobalKey<FormState>();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();

  // Controllers
  final TextEditingController _entryNumberController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _referenceController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  List<JournalEntryLine> _lines = [];
  List<Account> _accounts = [];
  bool _isLoading = false;
  bool _isLoadingAccounts = true;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
    _initializeForm();
  }

  @override
  void dispose() {
    _entryNumberController.dispose();
    _descriptionController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (widget.entry != null) {
      _entryNumberController.text = widget.entry!.entryNumber;
      _descriptionController.text = widget.entry!.description;
      _referenceController.text = widget.entry!.reference ?? '';
      _selectedDate = widget.entry!.date;
      _lines = List.from(widget.entry!.lines);
    } else {
      _generateEntryNumber();
      _addEmptyLine();
      _addEmptyLine();
    }
  }

  Future<void> _loadAccounts() async {
    setState(() => _isLoadingAccounts = true);

    try {
      final result = await _accountService.getAllAccounts();
      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _accounts = result.data!
                .where((account) => account.isActive)
                .toList();
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الحسابات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingAccounts = false);
      }
    }
  }

  Future<void> _generateEntryNumber() async {
    try {
      final result = await _journalEntryService.getNextEntryNumber();
      if (mounted && result.isSuccess) {
        setState(() {
          _entryNumberController.text = result.data!;
        });
      }
    } catch (e) {
      // Handle error silently or show a message
    }
  }

  void _addEmptyLine() {
    setState(() {
      _lines.add(
        JournalEntryLine(
          journalEntryId: widget.entry?.id ?? 0,
          accountId: 0,
          lineOrder: _lines.length,
        ),
      );
    });
  }

  void _removeLine(int index) {
    if (_lines.length > 2) {
      setState(() {
        _lines.removeAt(index);
        // إعادة ترقيم الأسطر
        for (int i = 0; i < _lines.length; i++) {
          _lines[i] = _lines[i].copyWith(lineOrder: i);
        }
      });
    }
  }

  void _updateLine(int index, JournalEntryLine updatedLine) {
    setState(() {
      _lines[index] = updatedLine;
    });
  }

  Map<String, double> _calculateTotals() {
    double totalDebit = 0.0;
    double totalCredit = 0.0;

    for (var line in _lines) {
      totalDebit += line.debitAmount;
      totalCredit += line.creditAmount;
    }

    return {
      'debit': totalDebit,
      'credit': totalCredit,
      'difference': totalDebit - totalCredit,
    };
  }

  bool _isBalanced() {
    final totals = _calculateTotals();
    return (totals['difference']!).abs() < 0.01;
  }

  /// Get current user name for journal entry
  Future<String> _getCurrentUserName() async {
    return await UserService.instance.getCurrentUserName();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveJournalEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من وجود أسطر صالحة
    List<JournalEntryLine> validLines = _lines
        .where(
          (line) =>
              line.accountId > 0 &&
              (line.debitAmount > 0 || line.creditAmount > 0),
        )
        .toList();

    if (validLines.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة سطرين على الأقل بحسابات ومبالغ صحيحة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (!_isBalanced()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final totals = _calculateTotals();

      final journalEntry = JournalEntry(
        id: widget.entry?.id,
        entryNumber: _entryNumberController.text.trim(),
        date: _selectedDate,
        description: _descriptionController.text.trim(),
        reference: _referenceController.text.trim().isEmpty
            ? null
            : _referenceController.text.trim(),
        totalDebit: totals['debit']!,
        totalCredit: totals['credit']!,
        isBalanced: _isBalanced(),
        isPosted: widget.entry?.isPosted ?? false,
        createdBy: await _getCurrentUserName(),
        lines: validLines,
      );

      final result = widget.entry == null
          ? await _journalEntryService.createJournalEntry(journalEntry)
          : await _journalEntryService.updateJournalEntry(journalEntry);

      if (mounted) {
        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.entry == null
                    ? 'تم إنشاء القيد بنجاح'
                    : 'تم تحديث القيد بنجاح',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'خطأ في حفظ القيد'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ القيد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(widget.entry == null ? 'إضافة قيد يومي' : 'تعديل قيد يومي'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            IconButton(
              onPressed: _saveJournalEntry,
              icon: const Icon(Icons.save),
            ),
        ],
      ),
      body: _isLoadingAccounts
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: Column(
                children: [
                  // معلومات القيد الأساسية
                  _buildBasicInfo(),

                  // أسطر القيد
                  Expanded(child: _buildJournalLines()),

                  // ملخص المبالغ
                  _buildTotalsSummary(),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addEmptyLine,
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // رقم القيد
              Expanded(
                child: BeautifulTextFormField(
                  controller: _entryNumberController,
                  labelText: 'رقم القيد',
                  prefixIcon: Icons.numbers,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'رقم القيد مطلوب';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(width: AppTheme.spacingMedium),

              // تاريخ القيد
              Expanded(
                child: InkWell(
                  onTap: _selectDate,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text(
                          _formatDate(_selectedDate),
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // وصف القيد
          BeautifulTextFormField(
            controller: _descriptionController,
            labelText: 'وصف القيد',
            prefixIcon: Icons.description,
            maxLines: 2,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'وصف القيد مطلوب';
              }
              return null;
            },
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // المرجع (اختياري)
          BeautifulTextFormField(
            controller: _referenceController,
            labelText: 'المرجع (اختياري)',
            prefixIcon: Icons.link,
          ),
        ],
      ),
    );
  }

  Widget _buildJournalLines() {
    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          // عنوان الأسطر
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            child: Row(
              children: [
                const Icon(Icons.list, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'أسطر القيد',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_lines.length} سطر',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // قائمة الأسطر
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              itemCount: _lines.length,
              itemBuilder: (context, index) {
                return _buildJournalLineCard(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJournalLineCard(int index) {
    final line = _lines[index];

    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس السطر
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'سطر ${index + 1}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                const Spacer(),
                if (_lines.length > 2)
                  IconButton(
                    onPressed: () => _removeLine(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    iconSize: 20,
                  ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // اختيار الحساب
            DropdownButtonFormField<int>(
              value: line.accountId > 0 ? line.accountId : null,
              decoration: const InputDecoration(
                labelText: 'الحساب',
                prefixIcon: Icon(Icons.account_balance),
                border: OutlineInputBorder(),
              ),
              items: _accounts.map((account) {
                return DropdownMenuItem<int>(
                  value: account.id,
                  child: Text('${account.code} - ${account.name}'),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  _updateLine(index, line.copyWith(accountId: value));
                }
              },
              validator: (value) {
                if (value == null || value <= 0) {
                  return 'يجب اختيار حساب';
                }
                return null;
              },
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // المبالغ
            Row(
              children: [
                // المبلغ المدين
                Expanded(
                  child: TextFormField(
                    initialValue: line.debitAmount > 0
                        ? line.debitAmount.toString()
                        : '',
                    decoration: const InputDecoration(
                      labelText: 'مدين',
                      prefixIcon: Icon(Icons.add, color: Colors.green),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      double debitAmount = double.tryParse(value) ?? 0.0;
                      _updateLine(
                        index,
                        line.copyWith(
                          debitAmount: debitAmount,
                          creditAmount:
                              0.0, // إذا كان مدين، فلا يمكن أن يكون دائن
                        ),
                      );
                    },
                  ),
                ),

                const SizedBox(width: AppTheme.spacingMedium),

                // المبلغ الدائن
                Expanded(
                  child: TextFormField(
                    initialValue: line.creditAmount > 0
                        ? line.creditAmount.toString()
                        : '',
                    decoration: const InputDecoration(
                      labelText: 'دائن',
                      prefixIcon: Icon(Icons.remove, color: Colors.red),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      double creditAmount = double.tryParse(value) ?? 0.0;
                      _updateLine(
                        index,
                        line.copyWith(
                          creditAmount: creditAmount,
                          debitAmount:
                              0.0, // إذا كان دائن، فلا يمكن أن يكون مدين
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // وصف السطر (اختياري)
            TextFormField(
              initialValue: line.description ?? '',
              decoration: const InputDecoration(
                labelText: 'وصف السطر (اختياري)',
                prefixIcon: Icon(Icons.notes),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                _updateLine(
                  index,
                  line.copyWith(
                    description: value.trim().isEmpty ? null : value.trim(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSummary() {
    final totals = _calculateTotals();
    final isBalanced = _isBalanced();

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildTotalCard(
                'إجمالي المدين',
                totals['debit']!,
                Colors.green,
                Icons.add_circle,
              ),
              _buildTotalCard(
                'إجمالي الدائن',
                totals['credit']!,
                Colors.red,
                Icons.remove_circle,
              ),
              _buildTotalCard(
                'الفرق',
                totals['difference']!,
                isBalanced ? Colors.green : Colors.orange,
                isBalanced ? Icons.check_circle : Icons.warning,
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // حالة التوازن
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isBalanced
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isBalanced ? Colors.green : Colors.orange,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isBalanced ? Icons.check_circle : Icons.warning,
                  color: isBalanced ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  isBalanced ? 'القيد متوازن' : 'القيد غير متوازن',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isBalanced ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalCard(
    String title,
    double amount,
    Color color,
    IconData icon,
  ) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              '${amount.toStringAsFixed(2)} ر.س',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
