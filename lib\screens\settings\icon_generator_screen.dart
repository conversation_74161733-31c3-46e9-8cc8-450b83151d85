import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../../utils/icon_generator.dart';
import '../../widgets/smart_ledger_app_icon.dart';
import '../../theme/app_theme.dart';

/// 🎨 شاشة إنشاء أيقونات التطبيق
/// App Icon Generator Screen
class IconGeneratorScreen extends StatefulWidget {
  const IconGeneratorScreen({super.key});

  @override
  State<IconGeneratorScreen> createState() => _IconGeneratorScreenState();
}

class _IconGeneratorScreenState extends State<IconGeneratorScreen> {
  Color _primaryColor = const Color(0xFF1565C0);
  Color _secondaryColor = const Color(0xFF42A5F5);
  bool _isGenerating = false;
  String _status = '';

  final List<int> _iconSizes = [48, 72, 96, 144, 192, 512];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مولد أيقونات التطبيق'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معاينة الأيقونة
            _buildIconPreview(),
            
            const SizedBox(height: 24),
            
            // إعدادات الألوان
            _buildColorSettings(),
            
            const SizedBox(height: 24),
            
            // أزرار الإنشاء
            _buildGenerationButtons(),
            
            const SizedBox(height: 24),
            
            // حالة العملية
            if (_status.isNotEmpty) _buildStatusCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildIconPreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاينة الأيقونة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    SmartLedgerAppIcon(
                      size: 120,
                      animated: true,
                      primaryColor: _primaryColor,
                      secondaryColor: _secondaryColor,
                    ),
                    const SizedBox(height: 8),
                    const Text('متحركة'),
                  ],
                ),
                Column(
                  children: [
                    SmartLedgerAppIcon(
                      size: 120,
                      animated: false,
                      primaryColor: _primaryColor,
                      secondaryColor: _secondaryColor,
                    ),
                    const SizedBox(height: 8),
                    const Text('ثابتة'),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الألوان',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            // اللون الأساسي
            Row(
              children: [
                const Text('اللون الأساسي: '),
                const SizedBox(width: 16),
                GestureDetector(
                  onTap: () => _showColorPicker(true),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _primaryColor,
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // اللون الثانوي
            Row(
              children: [
                const Text('اللون الثانوي: '),
                const SizedBox(width: 16),
                GestureDetector(
                  onTap: () => _showColorPicker(false),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _secondaryColor,
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerationButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إنشاء الأيقونات',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGenerating ? null : _generateAndroidIcons,
                    icon: const Icon(Icons.android),
                    label: const Text('أيقونات أندرويد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGenerating ? null : _generateAllIcons,
                    icon: const Icon(Icons.apps),
                    label: const Text('جميع الأيقونات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      color: _status.contains('نجح') ? Colors.green[50] : Colors.orange[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _status.contains('نجح') ? Icons.check_circle : Icons.info,
              color: _status.contains('نجح') ? Colors.green : Colors.orange,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _status,
                style: TextStyle(
                  color: _status.contains('نجح') ? Colors.green[800] : Colors.orange[800],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showColorPicker(bool isPrimary) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isPrimary ? 'اختر اللون الأساسي' : 'اختر اللون الثانوي'),
        content: SingleChildScrollView(
          child: BlockPicker(
            pickerColor: isPrimary ? _primaryColor : _secondaryColor,
            onColorChanged: (color) {
              setState(() {
                if (isPrimary) {
                  _primaryColor = color;
                } else {
                  _secondaryColor = color;
                }
              });
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<void> _generateAndroidIcons() async {
    setState(() {
      _isGenerating = true;
      _status = 'جاري إنشاء أيقونات أندرويد...';
    });

    try {
      final directory = await getApplicationDocumentsDirectory();
      final iconsDir = Directory('${directory.path}/icons');
      if (!await iconsDir.exists()) {
        await iconsDir.create(recursive: true);
      }

      for (final size in _iconSizes) {
        final iconData = await IconGenerator.generateAppIcon(
          size: size,
          primaryColor: _primaryColor,
          secondaryColor: _secondaryColor,
        );

        final file = File('${iconsDir.path}/ic_launcher_${size}x$size.png');
        await file.writeAsBytes(iconData);
      }

      setState(() {
        _status = 'نجح إنشاء أيقونات أندرويد في: ${iconsDir.path}';
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ في إنشاء الأيقونات: $e';
        _isGenerating = false;
      });
    }
  }

  Future<void> _generateAllIcons() async {
    setState(() {
      _isGenerating = true;
      _status = 'جاري إنشاء جميع الأيقونات...';
    });

    try {
      final directory = await getApplicationDocumentsDirectory();
      final iconsDir = Directory('${directory.path}/icons');
      if (!await iconsDir.exists()) {
        await iconsDir.create(recursive: true);
      }

      // أيقونات دائرية
      for (final size in _iconSizes) {
        final iconData = await IconGenerator.generateAppIcon(
          size: size,
          primaryColor: _primaryColor,
          secondaryColor: _secondaryColor,
        );

        final file = File('${iconsDir.path}/ic_launcher_round_${size}x$size.png');
        await file.writeAsBytes(iconData);
      }

      // أيقونات مربعة
      for (final size in _iconSizes) {
        final iconData = await IconGenerator.generateSquareIcon(
          size: size,
          primaryColor: _primaryColor,
          secondaryColor: _secondaryColor,
        );

        final file = File('${iconsDir.path}/ic_launcher_square_${size}x$size.png');
        await file.writeAsBytes(iconData);
      }

      setState(() {
        _status = 'نجح إنشاء جميع الأيقونات في: ${iconsDir.path}';
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ في إنشاء الأيقونات: $e';
        _isGenerating = false;
      });
    }
  }
}

/// منتقي الألوان البسيط
class BlockPicker extends StatelessWidget {
  final Color pickerColor;
  final ValueChanged<Color> onColorChanged;

  const BlockPicker({
    super.key,
    required this.pickerColor,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    final colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
    ];

    return Wrap(
      children: colors.map((color) {
        return GestureDetector(
          onTap: () => onColorChanged(color),
          child: Container(
            width: 40,
            height: 40,
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color,
              border: Border.all(
                color: pickerColor == color ? Colors.black : Colors.grey,
                width: pickerColor == color ? 3 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }).toList(),
    );
  }
}
