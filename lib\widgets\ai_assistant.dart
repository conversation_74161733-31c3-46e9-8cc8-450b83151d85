import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';
import 'smart_notifications.dart';

/// 🤖 مساعد الذكاء الاصطناعي المستقبلي
/// Futuristic AI Assistant
///
/// هذا الملف يحتوي على مساعد ذكي لا مثيل له في التاريخ
/// This file contains unprecedented AI assistant in history

/// 🌟 مساعد الذكاء الاصطناعي العائم
/// Floating AI Assistant
class FloatingAIAssistant extends StatefulWidget {
  final VoidCallback? onTap;

  const FloatingAIAssistant({super.key, this.onTap});

  @override
  State<FloatingAIAssistant> createState() => _FloatingAIAssistantState();
}

class _FloatingAIAssistantState extends State<FloatingAIAssistant>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _breathController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _breathAnimation;
  bool _isActive = false;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );

    _breathController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _breathAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _breathController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    _breathController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _breathController.dispose();
    super.dispose();
  }

  void _activateAssistant() {
    setState(() {
      _isActive = !_isActive;
    });

    if (_isActive) {
      SmartNotificationManager.showInfo(
        context,
        title: '🤖 المساعد الذكي',
        message: 'مرحباً! كيف يمكنني مساعدتك اليوم؟',
        onTap: () {
          _showAIDialog();
        },
      );
    }
  }

  void _showAIDialog() {
    showDialog(context: context, builder: (context) => AIAssistantDialog());
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _pulseAnimation,
        _rotationAnimation,
        _breathAnimation,
      ]),
      builder: (context, child) {
        return Positioned(
          bottom: 100,
          right: 20,
          child: GestureDetector(
            onTap: () {
              _activateAssistant();
              if (widget.onTap != null) {
                widget.onTap!();
              }
            },
            child: Transform.scale(
              scale: _pulseAnimation.value,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 0.1,
                child: HologramEffect(
                  intensity: _isActive ? 2.0 : 1.0,
                  child: QuantumEnergyEffect(
                    intensity: 1.0 + (_breathAnimation.value * 0.8),
                    primaryColor: _isActive
                        ? const Color(0xFF00E676)
                        : const Color(0xFF2196F3),
                    secondaryColor: _isActive
                        ? const Color(0xFF76FF03)
                        : const Color(0xFF03A9F4),
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            (_isActive
                                    ? const Color(0xFF00E676)
                                    : const Color(0xFF2196F3))
                                .withValues(alpha: 0.9),
                            (_isActive
                                    ? const Color(0xFF76FF03)
                                    : const Color(0xFF03A9F4))
                                .withValues(alpha: 0.7),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(40),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.4),
                          width: 3,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color:
                                (_isActive
                                        ? const Color(0xFF00E676)
                                        : const Color(0xFF2196F3))
                                    .withValues(alpha: 0.6),
                            blurRadius: 25,
                            offset: const Offset(0, 10),
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // خلفية الجسيمات
                          ...List.generate(8, (index) {
                            final angle = (index / 8) * 2 * math.pi;
                            final radius = 25.0;
                            return Positioned(
                              left:
                                  40 +
                                  radius *
                                      math.cos(
                                        angle + _rotationAnimation.value,
                                      ),
                              top:
                                  40 +
                                  radius *
                                      math.sin(
                                        angle + _rotationAnimation.value,
                                      ),
                              child: Container(
                                width: 3,
                                height: 3,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(
                                    alpha: 0.6 + (_breathAnimation.value * 0.4),
                                  ),
                                  borderRadius: BorderRadius.circular(1.5),
                                ),
                              ),
                            );
                          }),

                          // الأيقونة المركزية
                          Icon(
                            _isActive
                                ? Icons.psychology_rounded
                                : Icons.smart_toy_rounded,
                            color: Colors.white,
                            size: 35,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 🎯 حوار المساعد الذكي
/// AI Assistant Dialog
class AIAssistantDialog extends StatefulWidget {
  const AIAssistantDialog({super.key});

  @override
  State<AIAssistantDialog> createState() => _AIAssistantDialogState();
}

class _AIAssistantDialogState extends State<AIAssistantDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  final TextEditingController _messageController = TextEditingController();
  final List<AIMessage> _messages = [];

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _scaleController.forward();

    // رسالة ترحيب
    _messages.add(
      AIMessage(
        text:
            'مرحباً! أنا مساعدك الذكي في Smart Ledger. كيف يمكنني مساعدتك اليوم؟',
        isUser: false,
        timestamp: DateTime.now(),
      ),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    setState(() {
      _messages.add(
        AIMessage(
          text: _messageController.text,
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
    });

    final userMessage = _messageController.text;
    _messageController.clear();

    // محاكاة رد المساعد الذكي
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _messages.add(
          AIMessage(
            text: _generateAIResponse(userMessage),
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
      });
    });
  }

  String _generateAIResponse(String userMessage) {
    final message = userMessage.toLowerCase();

    if (message.contains('مساعدة') || message.contains('help')) {
      return 'يمكنني مساعدتك في:\n• إنشاء القيود المحاسبية\n• إعداد التقارير المالية\n• إدارة الحسابات\n• تحليل البيانات المالية';
    } else if (message.contains('قيد') || message.contains('entry')) {
      return 'لإنشاء قيد محاسبي جديد، انقر على زر "إضافة قيد" في الشاشة الرئيسية. سأقوم بإرشادك خطوة بخطوة.';
    } else if (message.contains('تقرير') || message.contains('report')) {
      return 'يمكنك الوصول للتقارير المالية من قسم "التقارير". متوفر تقارير الأرباح والخسائر، الميزانية العمومية، وتقارير التدفق النقدي.';
    } else if (message.contains('حساب') || message.contains('account')) {
      return 'لإدارة الحسابات، اذهب إلى قسم "الحسابات" حيث يمكنك إضافة، تعديل، أو حذف الحسابات المحاسبية.';
    } else {
      return 'شكراً لك على سؤالك. يمكنني مساعدتك في جميع أمور المحاسبة والتقارير المالية. هل تريد معرفة المزيد عن ميزة معينة؟';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Dialog(
            backgroundColor: Colors.transparent,
            child: HologramEffect(
              intensity: 1.5,
              child: Container(
                width: 400,
                height: 500,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF1A1A2E),
                      Color(0xFF16213E),
                      Color(0xFF0F3460),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2196F3).withValues(alpha: 0.4),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // رأس الحوار
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingLarge),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF2196F3), Color(0xFF03A9F4)],
                        ),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25),
                          topRight: Radius.circular(25),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.psychology_rounded,
                            color: Colors.white,
                            size: 28,
                          ),
                          const SizedBox(width: AppTheme.spacingMedium),
                          const Text(
                            'المساعد الذكي',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: const Icon(
                              Icons.close_rounded,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // منطقة الرسائل
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(AppTheme.spacingMedium),
                        itemCount: _messages.length,
                        itemBuilder: (context, index) {
                          return _buildMessageBubble(_messages[index]);
                        },
                      ),
                    ),

                    // منطقة الإدخال
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingMedium),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.2),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(25),
                          bottomRight: Radius.circular(25),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _messageController,
                              style: const TextStyle(color: Colors.white),
                              decoration: InputDecoration(
                                hintText: 'اكتب رسالتك هنا...',
                                hintStyle: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.6),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(
                                    color: Colors.white.withValues(alpha: 0.3),
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(
                                    color: Colors.white.withValues(alpha: 0.3),
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: const BorderSide(
                                    color: Color(0xFF2196F3),
                                    width: 2,
                                  ),
                                ),
                              ),
                              onSubmitted: (_) => _sendMessage(),
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacingMedium),
                          GestureDetector(
                            onTap: _sendMessage,
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFF2196F3),
                                    Color(0xFF03A9F4),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: const Icon(
                                Icons.send_rounded,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMessageBubble(AIMessage message) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppTheme.spacingSmall),
      child: Row(
        mainAxisAlignment: message.isUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF2196F3).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.psychology_rounded,
                color: Color(0xFF2196F3),
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingSmall),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingMedium,
                vertical: AppTheme.spacingSmall,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: message.isUser
                      ? [
                          const Color(0xFF4CAF50).withValues(alpha: 0.8),
                          const Color(0xFF8BC34A).withValues(alpha: 0.6),
                        ]
                      : [
                          const Color(0xFF2196F3).withValues(alpha: 0.8),
                          const Color(0xFF03A9F4).withValues(alpha: 0.6),
                        ],
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
              ),
              child: Text(
                message.text,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: AppTheme.spacingSmall),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.person_rounded,
                color: Color(0xFF4CAF50),
                size: 20,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// 📝 نموذج رسالة المساعد الذكي
/// AI Message Model
class AIMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  AIMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}
