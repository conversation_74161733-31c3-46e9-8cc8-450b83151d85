import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 📦 نظام إدارة المخزون والأصناف المتقدم
/// Advanced Inventory and Items Management System
///
/// هذا الملف يحتوي على نظام إدارة المخزون والأصناف المتقدم لا مثيل له في التاريخ
/// This file contains unprecedented advanced inventory and items management system in history

/// 🌟 لوحة إدارة المخزون والأصناف المتقدمة
/// Advanced Inventory and Items Management Dashboard
class AdvancedInventoryDashboard extends StatefulWidget {
  const AdvancedInventoryDashboard({super.key});

  @override
  State<AdvancedInventoryDashboard> createState() =>
      _AdvancedInventoryDashboardState();
}

class _AdvancedInventoryDashboardState extends State<AdvancedInventoryDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _itemController;
  late AnimationController _stockController;
  late Animation<double> _mainAnimation;
  late Animation<double> _itemAnimation;
  late Animation<double> _stockAnimation;

  int _selectedTab = 0; // 0 for items, 1 for stock movements, 2 for categories

  final List<InventoryItem> _inventoryItems = [
    InventoryItem(
      id: 'ITEM-001',
      name: 'جهاز كمبيوتر محمول HP EliteBook',
      category: 'أجهزة كمبيوتر',
      barcode: '1234567890123',
      currentStock: 25,
      minimumStock: 5,
      maximumStock: 100,
      unitPrice: 3500.0,
      costPrice: 2800.0,
      location: 'مستودع الرياض - الرف A1',
      supplier: 'شركة التقنية المتقدمة',
      lastUpdated: DateTime.now().subtract(const Duration(days: 2)),
      valuationMethod: ValuationMethod.fifo,
      status: ItemStatus.active,
    ),
    InventoryItem(
      id: 'ITEM-002',
      name: 'طابعة ليزر Canon LBP6030',
      category: 'طابعات',
      barcode: '2345678901234',
      currentStock: 15,
      minimumStock: 3,
      maximumStock: 50,
      unitPrice: 450.0,
      costPrice: 380.0,
      location: 'مستودع جدة - الرف B2',
      supplier: 'مؤسسة الطباعة الحديثة',
      lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
      valuationMethod: ValuationMethod.weightedAverage,
      status: ItemStatus.active,
    ),
    InventoryItem(
      id: 'ITEM-003',
      name: 'كرسي مكتبي جلد طبيعي',
      category: 'أثاث مكتبي',
      barcode: '3456789012345',
      currentStock: 8,
      minimumStock: 10,
      maximumStock: 30,
      unitPrice: 850.0,
      costPrice: 650.0,
      location: 'مستودع الدمام - الرف C3',
      supplier: 'شركة الأثاث الفاخر',
      lastUpdated: DateTime.now().subtract(const Duration(hours: 6)),
      valuationMethod: ValuationMethod.lifo,
      status: ItemStatus.lowStock,
    ),
    InventoryItem(
      id: 'ITEM-004',
      name: 'ورق طباعة A4 - 500 ورقة',
      category: 'مستلزمات مكتبية',
      barcode: '4567890123456',
      currentStock: 150,
      minimumStock: 50,
      maximumStock: 500,
      unitPrice: 25.0,
      costPrice: 18.0,
      location: 'مستودع الرياض - الرف D1',
      supplier: 'شركة المستلزمات الورقية',
      lastUpdated: DateTime.now().subtract(const Duration(hours: 12)),
      valuationMethod: ValuationMethod.fifo,
      status: ItemStatus.active,
    ),
  ];

  final List<StockMovement> _stockMovements = [
    StockMovement(
      id: 'MOV-001',
      itemId: 'ITEM-001',
      itemName: 'جهاز كمبيوتر محمول HP EliteBook',
      movementType: MovementType.purchase,
      quantity: 10,
      unitCost: 2800.0,
      totalCost: 28000.0,
      reference: 'PO-2024-001',
      date: DateTime.now().subtract(const Duration(days: 3)),
      notes: 'شراء دفعة جديدة من الموردين',
    ),
    StockMovement(
      id: 'MOV-002',
      itemId: 'ITEM-002',
      itemName: 'طابعة ليزر Canon LBP6030',
      movementType: MovementType.sale,
      quantity: -5,
      unitCost: 380.0,
      totalCost: -1900.0,
      reference: 'INV-2024-015',
      date: DateTime.now().subtract(const Duration(days: 1)),
      notes: 'بيع للعميل شركة الأندلس',
    ),
    StockMovement(
      id: 'MOV-003',
      itemId: 'ITEM-003',
      itemName: 'كرسي مكتبي جلد طبيعي',
      movementType: MovementType.adjustment,
      quantity: -2,
      unitCost: 650.0,
      totalCost: -1300.0,
      reference: 'ADJ-2024-003',
      date: DateTime.now().subtract(const Duration(hours: 8)),
      notes: 'تسوية نقص في المخزون',
    ),
    StockMovement(
      id: 'MOV-004',
      itemId: 'ITEM-004',
      itemName: 'ورق طباعة A4 - 500 ورقة',
      movementType: MovementType.transfer,
      quantity: 25,
      unitCost: 18.0,
      totalCost: 450.0,
      reference: 'TRF-2024-007',
      date: DateTime.now().subtract(const Duration(hours: 4)),
      notes: 'نقل من مستودع جدة إلى الرياض',
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _itemController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _stockController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _itemAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _itemController, curve: Curves.easeInOut),
    );

    _stockAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(parent: _stockController, curve: Curves.linear));

    _mainController.repeat(reverse: true);
    _itemController.repeat(reverse: true);
    _stockController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _itemController.dispose();
    _stockController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _itemAnimation,
        _stockAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.4 + (_mainAnimation.value * 0.6),
          child: QuantumEnergyEffect(
            intensity: 2.0 + (_itemAnimation.value * 0.5),
            primaryColor: const Color(0xFF4CAF50),
            secondaryColor: const Color(0xFF66BB6A),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF1B5E20).withValues(alpha: 0.9),
                    const Color(0xFF2E7D32).withValues(alpha: 0.8),
                    const Color(0xFF388E3C).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام إدارة المخزون
                  Row(
                    children: [
                      Transform.scale(
                        scale: _itemAnimation.value,
                        child: Transform.rotate(
                          angle: _stockAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF4CAF50,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFF66BB6A,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.inventory_2_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '📦 إدارة المخزون والأصناف',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام متطور لإدارة المخزون مع تتبع الكميات والحركات',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات المخزون
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  if (_selectedTab == 0) _buildItemsView(),
                  if (_selectedTab == 1) _buildStockMovementsView(),
                  if (_selectedTab == 2) _buildCategoriesView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 0),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 0
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.inventory_rounded,
                      color: _selectedTab == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'الأصناف',
                      style: TextStyle(
                        color: _selectedTab == 0
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 1),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 1
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.swap_horiz_rounded,
                      color: _selectedTab == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'الحركات',
                      style: TextStyle(
                        color: _selectedTab == 1
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 2),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 2
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.category_rounded,
                      color: _selectedTab == 2
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'الفئات',
                      style: TextStyle(
                        color: _selectedTab == 2
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض الأصناف
  Widget _buildItemsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات المخزون
        _buildInventoryStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة الأصناف مع زر الإدارة
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '📦 قائمة الأصناف',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.pushNamed(context, '/items');
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.settings, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'إدارة الأصناف',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_inventoryItems.length, (index) {
          return _buildInventoryItemCard(_inventoryItems[index]);
        }),
      ],
    );
  }

  /// بناء عرض حركات المخزون
  Widget _buildStockMovementsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات الحركات
        _buildMovementStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة حركات المخزون
        Text(
          '🔄 حركات المخزون',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_stockMovements.length, (index) {
          return _buildStockMovementCard(_stockMovements[index]);
        }),
      ],
    );
  }

  /// بناء عرض الفئات
  Widget _buildCategoriesView() {
    final categories = _inventoryItems
        .map((item) => item.category)
        .toSet()
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات الفئات
        _buildCategoryStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة الفئات
        Text(
          '🏷️ فئات الأصناف',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(categories.length, (index) {
          return _buildCategoryCard(categories[index]);
        }),
      ],
    );
  }

  /// بناء إحصائيات المخزون
  Widget _buildInventoryStats() {
    final totalItems = _inventoryItems.length;
    final lowStockItems = _inventoryItems
        .where((item) => item.status == ItemStatus.lowStock)
        .length;
    final totalValue = _inventoryItems.fold(
      0.0,
      (sum, item) => sum + (item.currentStock * item.unitPrice),
    );
    final totalCost = _inventoryItems.fold(
      0.0,
      (sum, item) => sum + (item.currentStock * item.costPrice),
    );

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الأصناف',
            totalItems.toString(),
            Icons.inventory_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'أصناف منخفضة',
            lowStockItems.toString(),
            Icons.warning_rounded,
            const Color(0xFFF44336),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'قيمة المخزون',
            '${totalValue.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'تكلفة المخزون',
            '${totalCost.toStringAsFixed(0)} ر.س',
            Icons.monetization_on_rounded,
            const Color(0xFFFF9800),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات الحركات
  Widget _buildMovementStats() {
    final totalMovements = _stockMovements.length;
    final purchases = _stockMovements
        .where((m) => m.movementType == MovementType.purchase)
        .length;
    final sales = _stockMovements
        .where((m) => m.movementType == MovementType.sale)
        .length;
    final adjustments = _stockMovements
        .where((m) => m.movementType == MovementType.adjustment)
        .length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الحركات',
            totalMovements.toString(),
            Icons.swap_horiz_rounded,
            const Color(0xFF9C27B0),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'حركات الشراء',
            purchases.toString(),
            Icons.add_shopping_cart_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'حركات البيع',
            sales.toString(),
            Icons.point_of_sale_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'التسويات',
            adjustments.toString(),
            Icons.tune_rounded,
            const Color(0xFFFF9800),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات الفئات
  Widget _buildCategoryStats() {
    final categories = _inventoryItems.map((item) => item.category).toSet();
    final totalCategories = categories.length;
    final mostStockedCategory = categories.isNotEmpty
        ? categories.reduce(
            (a, b) =>
                _inventoryItems
                        .where((item) => item.category == a)
                        .fold(0, (sum, item) => sum + item.currentStock) >
                    _inventoryItems
                        .where((item) => item.category == b)
                        .fold(0, (sum, item) => sum + item.currentStock)
                ? a
                : b,
          )
        : 'غير محدد';

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الفئات',
            totalCategories.toString(),
            Icons.category_rounded,
            const Color(0xFF673AB7),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          flex: 2,
          child: _buildStatCard(
            'الفئة الأكثر مخزوناً',
            mostStockedCategory,
            Icons.trending_up_rounded,
            const Color(0xFF00BCD4),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'متوسط الأصناف',
            totalCategories > 0
                ? (_inventoryItems.length / totalCategories).toStringAsFixed(1)
                : '0',
            Icons.analytics_rounded,
            const Color(0xFFE91E63),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 8,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الصنف
  Widget _buildInventoryItemCard(InventoryItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getItemStatusColor(item.status).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getItemStatusColor(
                    item.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.inventory_2_rounded,
                  color: _getItemStatusColor(item.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${item.id} - ${item.category}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getItemStatusColor(
                    item.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getItemStatusText(item.status),
                  style: TextStyle(
                    color: _getItemStatusColor(item.status),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات الصنف
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الكمية الحالية',
                  item.currentStock.toString(),
                  Icons.inventory_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'سعر البيع',
                  '${item.unitPrice.toStringAsFixed(2)} ر.س',
                  Icons.sell_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الحد الأدنى',
                  item.minimumStock.toString(),
                  Icons.trending_down_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'الموقع',
                  item.location,
                  Icons.location_on_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة حركة المخزون
  Widget _buildStockMovementCard(StockMovement movement) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getMovementTypeColor(
            movement.movementType,
          ).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getMovementTypeColor(
                    movement.movementType,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  _getMovementTypeIcon(movement.movementType),
                  color: _getMovementTypeColor(movement.movementType),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      movement.itemName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${movement.id} - ${movement.reference}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getMovementTypeColor(
                    movement.movementType,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getMovementTypeText(movement.movementType),
                  style: TextStyle(
                    color: _getMovementTypeColor(movement.movementType),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات الحركة
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الكمية',
                  movement.quantity.toString(),
                  Icons.numbers_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'التكلفة الإجمالية',
                  '${movement.totalCost.toStringAsFixed(2)} ر.س',
                  Icons.monetization_on_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'التاريخ',
                  _formatDate(movement.date),
                  Icons.calendar_today_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'الملاحظات',
                  movement.notes,
                  Icons.note_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الفئة
  Widget _buildCategoryCard(String category) {
    final categoryItems = _inventoryItems
        .where((item) => item.category == category)
        .toList();
    final totalStock = categoryItems.fold(
      0,
      (sum, item) => sum + item.currentStock,
    );
    final totalValue = categoryItems.fold(
      0.0,
      (sum, item) => sum + (item.currentStock * item.unitPrice),
    );

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: const Color(0xFF673AB7).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF673AB7).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Icon(
                  Icons.category_rounded,
                  color: Color(0xFF673AB7),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${categoryItems.length} صنف',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات الفئة
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'إجمالي الكمية',
                  totalStock.toString(),
                  Icons.inventory_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'إجمالي القيمة',
                  '${totalValue.toStringAsFixed(0)} ر.س',
                  Icons.monetization_on_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.6), size: 12),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 9,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getItemStatusColor(ItemStatus status) {
    switch (status) {
      case ItemStatus.active:
        return const Color(0xFF4CAF50);
      case ItemStatus.lowStock:
        return const Color(0xFFF44336);
      case ItemStatus.outOfStock:
        return const Color(0xFF9E9E9E);
      case ItemStatus.discontinued:
        return const Color(0xFF795548);
    }
  }

  String _getItemStatusText(ItemStatus status) {
    switch (status) {
      case ItemStatus.active:
        return 'نشط';
      case ItemStatus.lowStock:
        return 'منخفض';
      case ItemStatus.outOfStock:
        return 'نفد';
      case ItemStatus.discontinued:
        return 'متوقف';
    }
  }

  Color _getMovementTypeColor(MovementType type) {
    switch (type) {
      case MovementType.purchase:
        return const Color(0xFF4CAF50);
      case MovementType.sale:
        return const Color(0xFF2196F3);
      case MovementType.adjustment:
        return const Color(0xFFFF9800);
      case MovementType.transfer:
        return const Color(0xFF9C27B0);
      case MovementType.return_:
        return const Color(0xFFF44336);
    }
  }

  String _getMovementTypeText(MovementType type) {
    switch (type) {
      case MovementType.purchase:
        return 'شراء';
      case MovementType.sale:
        return 'بيع';
      case MovementType.adjustment:
        return 'تسوية';
      case MovementType.transfer:
        return 'نقل';
      case MovementType.return_:
        return 'مرتجع';
    }
  }

  IconData _getMovementTypeIcon(MovementType type) {
    switch (type) {
      case MovementType.purchase:
        return Icons.add_shopping_cart_rounded;
      case MovementType.sale:
        return Icons.point_of_sale_rounded;
      case MovementType.adjustment:
        return Icons.tune_rounded;
      case MovementType.transfer:
        return Icons.swap_horiz_rounded;
      case MovementType.return_:
        return Icons.keyboard_return_rounded;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// نموذج صنف المخزون
class InventoryItem {
  final String id;
  final String name;
  final String category;
  final String barcode;
  final int currentStock;
  final int minimumStock;
  final int maximumStock;
  final double unitPrice;
  final double costPrice;
  final String location;
  final String supplier;
  final DateTime lastUpdated;
  final ValuationMethod valuationMethod;
  final ItemStatus status;

  InventoryItem({
    required this.id,
    required this.name,
    required this.category,
    required this.barcode,
    required this.currentStock,
    required this.minimumStock,
    required this.maximumStock,
    required this.unitPrice,
    required this.costPrice,
    required this.location,
    required this.supplier,
    required this.lastUpdated,
    required this.valuationMethod,
    required this.status,
  });
}

/// نموذج حركة المخزون
class StockMovement {
  final String id;
  final String itemId;
  final String itemName;
  final MovementType movementType;
  final int quantity;
  final double unitCost;
  final double totalCost;
  final String reference;
  final DateTime date;
  final String notes;

  StockMovement({
    required this.id,
    required this.itemId,
    required this.itemName,
    required this.movementType,
    required this.quantity,
    required this.unitCost,
    required this.totalCost,
    required this.reference,
    required this.date,
    required this.notes,
  });
}

/// طريقة تقييم المخزون
enum ValuationMethod { fifo, lifo, weightedAverage }

/// حالة الصنف
enum ItemStatus { active, lowStock, outOfStock, discontinued }

/// نوع حركة المخزون
enum MovementType { purchase, sale, adjustment, transfer, return_ }
