import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/services.dart';
import '../database/database_optimizer.dart';

/// 🚀 خدمة تحسين الأداء
/// Performance Optimization Service
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  final DatabaseOptimizer _dbOptimizer = DatabaseOptimizer();

  // إحصائيات الأداء
  final Map<String, List<double>> _performanceMetrics = {};
  Timer? _metricsTimer;

  // ذاكرة التخزين المؤقت
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// بدء مراقبة الأداء
  void startPerformanceMonitoring() {
    _metricsTimer?.cancel();
    _metricsTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _collectMetrics(),
    );
  }

  /// إيقاف مراقبة الأداء
  void stopPerformanceMonitoring() {
    _metricsTimer?.cancel();
    _metricsTimer = null;
  }

  /// جمع مقاييس الأداء المحسنة
  Future<void> _collectMetrics() async {
    try {
      // قياس استخدام الذاكرة مع تفاصيل إضافية
      final memoryUsage = await _getMemoryUsage();
      _addMetric('memory_usage_mb', memoryUsage);

      // قياس وقت الاستجابة لقاعدة البيانات
      final dbResponseTime = await _measureDatabaseResponseTime();
      _addMetric('db_response_time_ms', dbResponseTime);

      // قياس حجم ذاكرة التخزين المؤقت
      final cacheSize = _getCacheSize();
      _addMetric('cache_size_mb', cacheSize);

      // قياس معدل نجاح ذاكرة التخزين المؤقت
      final cacheHitRate = _getCacheHitRate();
      _addMetric('cache_hit_rate_percent', cacheHitRate);

      // قياس عدد الاستعلامات النشطة
      final activeQueries = await _getActiveQueriesCount();
      _addMetric('active_queries_count', activeQueries.toDouble());

      // تنظيف البيانات المنتهية الصلاحية
      _cleanupExpiredCache();
    } catch (e) {
      debugPrint('خطأ في جمع مقاييس الأداء: $e');
    }
  }

  /// إضافة مقياس أداء
  void _addMetric(String key, double value) {
    _performanceMetrics.putIfAbsent(key, () => []);
    _performanceMetrics[key]!.add(value);

    // الاحتفاظ بآخر 100 قياس فقط
    if (_performanceMetrics[key]!.length > 100) {
      _performanceMetrics[key]!.removeAt(0);
    }
  }

  /// قياس استخدام الذاكرة
  Future<double> _getMemoryUsage() async {
    try {
      if (Platform.isAndroid) {
        // استخدام معلومات النظام للأندرويد
        final result = await Process.run('cat', ['/proc/meminfo']);
        final lines = result.stdout.toString().split('\n');

        for (final line in lines) {
          if (line.startsWith('MemAvailable:')) {
            final parts = line.split(RegExp(r'\s+'));
            if (parts.length >= 2) {
              final availableKb = int.tryParse(parts[1]) ?? 0;
              return availableKb / 1024; // تحويل إلى ميجابايت
            }
          }
        }
      }

      // قياس تقريبي للذاكرة المستخدمة
      return ProcessInfo.currentRss / (1024 * 1024);
    } catch (e) {
      return 0.0;
    }
  }

  /// قياس وقت الاستجابة لقاعدة البيانات
  Future<double> _measureDatabaseResponseTime() async {
    final stopwatch = Stopwatch()..start();

    try {
      await _dbOptimizer.getDatabaseStats();
      stopwatch.stop();
      return stopwatch.elapsedMilliseconds.toDouble();
    } catch (e) {
      stopwatch.stop();
      return -1.0; // خطأ
    }
  }

  /// حساب حجم ذاكرة التخزين المؤقت
  double _getCacheSize() {
    double totalSize = 0;

    for (final entry in _cache.entries) {
      // تقدير تقريبي لحجم البيانات
      final dataSize = entry.value.toString().length * 2; // تقدير بالبايت
      totalSize += dataSize;
    }

    return totalSize / 1024; // تحويل إلى كيلوبايت
  }

  /// تحسين شامل للأداء
  Future<Map<String, dynamic>> optimizePerformance() async {
    final results = <String, dynamic>{};
    final stopwatch = Stopwatch()..start();

    try {
      // تحسين قاعدة البيانات
      await _dbOptimizer.optimizeDatabase();
      results['database_optimization'] = 'نجح';

      // تنظيف ذاكرة التخزين المؤقت
      _cleanupCache();
      results['cache_cleanup'] = 'نجح';

      // تحسين الذاكرة
      await _optimizeMemory();
      results['memory_optimization'] = 'نجح';

      // جمع القمامة
      _forceGarbageCollection();
      results['garbage_collection'] = 'نجح';

      stopwatch.stop();
      results['total_time_ms'] = stopwatch.elapsedMilliseconds;
      results['status'] = 'نجح التحسين الشامل';
    } catch (e) {
      stopwatch.stop();
      results['error'] = e.toString();
      results['status'] = 'فشل التحسين';
    }

    return results;
  }

  /// تنظيف ذاكرة التخزين المؤقت
  void _cleanupCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  /// تحسين الذاكرة
  Future<void> _optimizeMemory() async {
    try {
      // تنظيف الصور المؤقتة
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // تقليل حجم ذاكرة التخزين المؤقت للصور
      PaintingBinding.instance.imageCache.maximumSize = 50;
      PaintingBinding.instance.imageCache.maximumSizeBytes =
          50 * 1024 * 1024; // 50 MB
    } catch (e) {
      debugPrint('خطأ في تحسين الذاكرة: $e');
    }
  }

  /// إجبار جمع القمامة
  void _forceGarbageCollection() {
    try {
      // تشغيل جامع القمامة
      SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  /// الحصول على تقرير الأداء
  Map<String, dynamic> getPerformanceReport() {
    final report = <String, dynamic>{};

    for (final entry in _performanceMetrics.entries) {
      final values = entry.value;
      if (values.isNotEmpty) {
        report[entry.key] = {
          'current': values.last,
          'average': values.reduce((a, b) => a + b) / values.length,
          'min': values.reduce((a, b) => a < b ? a : b),
          'max': values.reduce((a, b) => a > b ? a : b),
          'count': values.length,
        };
      }
    }

    report['cache_entries'] = _cache.length;
    report['cache_size_kb'] = _getCacheSize();

    return report;
  }

  /// إضافة بيانات إلى ذاكرة التخزين المؤقت
  void cacheData(String key, dynamic data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
  }

  /// الحصول على بيانات من ذاكرة التخزين المؤقت
  T? getCachedData<T>(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;

    if (DateTime.now().difference(timestamp) > _cacheExpiry) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }

    return _cache[key] as T?;
  }

  /// فحص صحة النظام
  Future<Map<String, dynamic>> performHealthCheck() async {
    final health = <String, dynamic>{};

    try {
      // فحص قاعدة البيانات
      final dbIntegrity = await _dbOptimizer.checkDatabaseIntegrity();
      health['database_integrity'] = dbIntegrity ? 'سليم' : 'تالف';

      // فحص الذاكرة
      final memoryUsage = await _getMemoryUsage();
      health['memory_usage_mb'] = memoryUsage;
      health['memory_status'] = memoryUsage < 100
          ? 'جيد'
          : memoryUsage < 200
          ? 'متوسط'
          : 'مرتفع';

      // فحص ذاكرة التخزين المؤقت
      final cacheSize = _getCacheSize();
      health['cache_size_kb'] = cacheSize;
      health['cache_status'] = cacheSize < 1024
          ? 'جيد'
          : cacheSize < 5120
          ? 'متوسط'
          : 'مرتفع';

      // فحص وقت الاستجابة
      final responseTime = await _measureDatabaseResponseTime();
      health['db_response_time_ms'] = responseTime;
      health['response_status'] = responseTime < 100
          ? 'ممتاز'
          : responseTime < 500
          ? 'جيد'
          : responseTime < 1000
          ? 'متوسط'
          : 'بطيء';

      health['overall_status'] = _calculateOverallHealth(health);
    } catch (e) {
      health['error'] = e.toString();
      health['overall_status'] = 'خطأ';
    }

    return health;
  }

  /// حساب الحالة العامة للنظام
  String _calculateOverallHealth(Map<String, dynamic> health) {
    int score = 0;
    int maxScore = 0;

    // تقييم قاعدة البيانات
    if (health['database_integrity'] == 'سليم') score += 25;
    maxScore += 25;

    // تقييم الذاكرة
    switch (health['memory_status']) {
      case 'جيد':
        score += 25;
        break;
      case 'متوسط':
        score += 15;
        break;
      case 'مرتفع':
        score += 5;
        break;
    }
    maxScore += 25;

    // تقييم ذاكرة التخزين المؤقت
    switch (health['cache_status']) {
      case 'جيد':
        score += 25;
        break;
      case 'متوسط':
        score += 15;
        break;
      case 'مرتفع':
        score += 5;
        break;
    }
    maxScore += 25;

    // تقييم وقت الاستجابة
    switch (health['response_status']) {
      case 'ممتاز':
        score += 25;
        break;
      case 'جيد':
        score += 20;
        break;
      case 'متوسط':
        score += 10;
        break;
      case 'بطيء':
        score += 0;
        break;
    }
    maxScore += 25;

    final percentage = (score / maxScore) * 100;

    if (percentage >= 90) return 'ممتاز';
    if (percentage >= 75) return 'جيد';
    if (percentage >= 50) return 'متوسط';
    return 'يحتاج تحسين';
  }

  /// حساب معدل نجاح ذاكرة التخزين المؤقت
  double _getCacheHitRate() {
    if (_cacheHits == 0 && _cacheMisses == 0) return 0.0;
    return (_cacheHits / (_cacheHits + _cacheMisses)) * 100;
  }

  /// الحصول على عدد الاستعلامات النشطة
  Future<int> _getActiveQueriesCount() async {
    try {
      // هذا مثال - يمكن تحسينه حسب نوع قاعدة البيانات
      return 0; // SQLite لا يدعم استعلامات متزامنة متعددة
    } catch (e) {
      return 0;
    }
  }

  /// تنظيف ذاكرة التخزين المؤقت المنتهية الصلاحية
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  // متغيرات لتتبع معدل نجاح ذاكرة التخزين المؤقت
  final int _cacheHits = 0;
  final int _cacheMisses = 0;

  /// تنظيف الموارد
  void dispose() {
    stopPerformanceMonitoring();
    _cache.clear();
    _cacheTimestamps.clear();
    _performanceMetrics.clear();
  }
}
