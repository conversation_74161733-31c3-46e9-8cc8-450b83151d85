/// نموذج المخزن
/// Warehouse Model for Smart Ledger
library;

enum WarehouseType {
  main, // مخزن رئيسي
  branch, // مخزن فرع
  retail, // مخزن تجزئة
  wholesale, // مخزن جملة
  transit, // مخزن عبور
  damaged, // مخزن تالف
  quarantine, // مخزن حجر صحي
}

extension WarehouseTypeExtension on WarehouseType {
  String get arabicName {
    switch (this) {
      case WarehouseType.main:
        return 'مخزن رئيسي';
      case WarehouseType.branch:
        return 'مخزن فرع';
      case WarehouseType.retail:
        return 'مخزن تجزئة';
      case WarehouseType.wholesale:
        return 'مخزن جملة';
      case WarehouseType.transit:
        return 'مخزن عبور';
      case WarehouseType.damaged:
        return 'مخزن تالف';
      case WarehouseType.quarantine:
        return 'مخزن حجر صحي';
    }
  }

  String get englishName {
    switch (this) {
      case WarehouseType.main:
        return 'Main Warehouse';
      case WarehouseType.branch:
        return 'Branch Warehouse';
      case WarehouseType.retail:
        return 'Retail Warehouse';
      case WarehouseType.wholesale:
        return 'Wholesale Warehouse';
      case WarehouseType.transit:
        return 'Transit Warehouse';
      case WarehouseType.damaged:
        return 'Damaged Warehouse';
      case WarehouseType.quarantine:
        return 'Quarantine Warehouse';
    }
  }
}

enum WarehouseStatus {
  active, // نشط
  inactive, // غير نشط
  maintenance, // تحت الصيانة
  closed, // مغلق
}

extension WarehouseStatusExtension on WarehouseStatus {
  String get arabicName {
    switch (this) {
      case WarehouseStatus.active:
        return 'نشط';
      case WarehouseStatus.inactive:
        return 'غير نشط';
      case WarehouseStatus.maintenance:
        return 'تحت الصيانة';
      case WarehouseStatus.closed:
        return 'مغلق';
    }
  }

  String get englishName {
    switch (this) {
      case WarehouseStatus.active:
        return 'Active';
      case WarehouseStatus.inactive:
        return 'Inactive';
      case WarehouseStatus.maintenance:
        return 'Under Maintenance';
      case WarehouseStatus.closed:
        return 'Closed';
    }
  }
}

class Warehouse {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final WarehouseType type;
  final WarehouseStatus status;
  final String? address;
  final String? city;
  final String? country;
  final String? phone;
  final String? email;
  final String? managerName;
  final double? area; // المساحة بالمتر المربع
  final double? capacity; // السعة التخزينية
  final bool isMainWarehouse;
  final int? parentWarehouseId;
  final bool allowNegativeStock;
  final bool requireApproval;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Warehouse? parentWarehouse;
  List<Warehouse>? subWarehouses;
  List<WarehouseLocation>? locations;

  Warehouse({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.type = WarehouseType.main,
    this.status = WarehouseStatus.active,
    this.address,
    this.city,
    this.country,
    this.phone,
    this.email,
    this.managerName,
    this.area,
    this.capacity,
    this.isMainWarehouse = false,
    this.parentWarehouseId,
    this.allowNegativeStock = false,
    this.requireApproval = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.parentWarehouse,
    this.subWarehouses,
    this.locations,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory Warehouse.fromMap(Map<String, dynamic> map) {
    return Warehouse(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      type: WarehouseType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => WarehouseType.main,
      ),
      status: WarehouseStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => WarehouseStatus.active,
      ),
      address: map['address'] as String?,
      city: map['city'] as String?,
      country: map['country'] as String?,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      managerName: map['manager_name'] as String?,
      area: (map['area'] as num?)?.toDouble(),
      capacity: (map['capacity'] as num?)?.toDouble(),
      isMainWarehouse: (map['is_main_warehouse'] as int?) == 1,
      parentWarehouseId: map['parent_warehouse_id'] as int?,
      allowNegativeStock: (map['allow_negative_stock'] as int?) == 1,
      requireApproval: (map['require_approval'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'type': type.name,
      'status': status.name,
      'address': address,
      'city': city,
      'country': country,
      'phone': phone,
      'email': email,
      'manager_name': managerName,
      'area': area,
      'capacity': capacity,
      'is_main_warehouse': isMainWarehouse ? 1 : 0,
      'parent_warehouse_id': parentWarehouseId,
      'allow_negative_stock': allowNegativeStock ? 1 : 0,
      'require_approval': requireApproval ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Copy with method
  Warehouse copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    WarehouseType? type,
    WarehouseStatus? status,
    String? address,
    String? city,
    String? country,
    String? phone,
    String? email,
    String? managerName,
    double? area,
    double? capacity,
    bool? isMainWarehouse,
    int? parentWarehouseId,
    bool? allowNegativeStock,
    bool? requireApproval,
    DateTime? updatedAt,
  }) {
    return Warehouse(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      managerName: managerName ?? this.managerName,
      area: area ?? this.area,
      capacity: capacity ?? this.capacity,
      isMainWarehouse: isMainWarehouse ?? this.isMainWarehouse,
      parentWarehouseId: parentWarehouseId ?? this.parentWarehouseId,
      allowNegativeStock: allowNegativeStock ?? this.allowNegativeStock,
      requireApproval: requireApproval ?? this.requireApproval,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Get warehouse type name in Arabic
  String get typeNameAr {
    switch (type) {
      case WarehouseType.main:
        return 'مخزن رئيسي';
      case WarehouseType.branch:
        return 'مخزن فرع';
      case WarehouseType.retail:
        return 'مخزن تجزئة';
      case WarehouseType.wholesale:
        return 'مخزن جملة';
      case WarehouseType.transit:
        return 'مخزن عبور';
      case WarehouseType.damaged:
        return 'مخزن تالف';
      case WarehouseType.quarantine:
        return 'مخزن حجر صحي';
    }
  }

  /// Get warehouse status name in Arabic
  String get statusNameAr {
    switch (status) {
      case WarehouseStatus.active:
        return 'نشط';
      case WarehouseStatus.inactive:
        return 'غير نشط';
      case WarehouseStatus.maintenance:
        return 'تحت الصيانة';
      case WarehouseStatus.closed:
        return 'مغلق';
    }
  }

  @override
  String toString() {
    return 'Warehouse{id: $id, code: $code, name: $name, type: $type}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Warehouse &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          code == other.code;

  @override
  int get hashCode => id.hashCode ^ code.hashCode;
}

/// نموذج موقع المخزن
/// Warehouse Location Model
class WarehouseLocation {
  final int? id;
  final int warehouseId;
  final String code;
  final String name;
  final String? description;
  final String? zone; // المنطقة
  final String? aisle; // الممر
  final String? rack; // الرف
  final String? shelf; // الرف الفرعي
  final String? bin; // الصندوق
  final int? level; // المستوى
  final double? maxWeight; // الحد الأقصى للوزن
  final double? maxVolume; // الحد الأقصى للحجم
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Warehouse? warehouse;

  WarehouseLocation({
    this.id,
    required this.warehouseId,
    required this.code,
    required this.name,
    this.description,
    this.zone,
    this.aisle,
    this.rack,
    this.shelf,
    this.bin,
    this.level,
    this.maxWeight,
    this.maxVolume,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.warehouse,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory WarehouseLocation.fromMap(Map<String, dynamic> map) {
    return WarehouseLocation(
      id: map['id'] as int?,
      warehouseId: map['warehouse_id'] as int,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      zone: map['zone'] as String?,
      aisle: map['aisle'] as String?,
      rack: map['rack'] as String?,
      shelf: map['shelf'] as String?,
      bin: map['bin'] as String?,
      level: map['level'] as int?,
      maxWeight: (map['max_weight'] as num?)?.toDouble(),
      maxVolume: (map['max_volume'] as num?)?.toDouble(),
      isActive: (map['is_active'] as int?) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'warehouse_id': warehouseId,
      'code': code,
      'name': name,
      'description': description,
      'zone': zone,
      'aisle': aisle,
      'rack': rack,
      'shelf': shelf,
      'bin': bin,
      'level': level,
      'max_weight': maxWeight,
      'max_volume': maxVolume,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Get full location path
  String get fullPath {
    List<String> parts = [];
    if (zone != null) parts.add('منطقة: $zone');
    if (aisle != null) parts.add('ممر: $aisle');
    if (rack != null) parts.add('رف: $rack');
    if (shelf != null) parts.add('رف فرعي: $shelf');
    if (bin != null) parts.add('صندوق: $bin');
    if (level != null) parts.add('مستوى: $level');

    return parts.isEmpty ? name : parts.join(' - ');
  }

  @override
  String toString() {
    return 'WarehouseLocation{id: $id, code: $code, name: $name}';
  }
}
