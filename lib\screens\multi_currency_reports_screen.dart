import 'package:flutter/material.dart';
import '../models/currency.dart';
import '../services/multi_currency_report_service.dart';
import '../services/currency_service.dart';
import '../widgets/quantum_card.dart';
import '../widgets/quantum_dropdown.dart';

/// شاشة التقارير المالية متعددة العملات
class MultiCurrencyReportsScreen extends StatefulWidget {
  const MultiCurrencyReportsScreen({super.key});

  @override
  State<MultiCurrencyReportsScreen> createState() =>
      _MultiCurrencyReportsScreenState();
}

class _MultiCurrencyReportsScreenState extends State<MultiCurrencyReportsScreen>
    with TickerProviderStateMixin {
  final MultiCurrencyReportService _reportService =
      MultiCurrencyReportService();
  final CurrencyService _currencyService = CurrencyService();

  late TabController _tabController;
  List<Currency> _currencies = [];
  String _selectedCurrency = 'SAR';
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  bool _isLoading = false;

  // بيانات التقارير
  MultiCurrencySalesReport? _salesReport;
  MultiCurrencyPurchaseReport? _purchaseReport;
  MultiCurrencyProfitLossReport? _profitLossReport;
  ExchangeRateReport? _exchangeRateReport;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCurrencies();
    _generateReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrencies() async {
    try {
      final currencies = await _currencyService.getAllCurrencies();
      setState(() {
        _currencies = currencies.where((c) => c.isActive).toList();
      });
    } catch (e) {
      debugPrint('خطأ في تحميل العملات: $e');
    }
  }

  Future<void> _generateReports() async {
    setState(() => _isLoading = true);

    try {
      final salesReport = await _reportService.generateSalesReport(
        startDate: _startDate,
        endDate: _endDate,
        displayCurrency: _selectedCurrency,
      );

      final purchaseReport = await _reportService.generatePurchaseReport(
        startDate: _startDate,
        endDate: _endDate,
        displayCurrency: _selectedCurrency,
      );

      final profitLossReport = await _reportService.generateProfitLossReport(
        startDate: _startDate,
        endDate: _endDate,
        displayCurrency: _selectedCurrency,
      );

      final exchangeRateReport = await _reportService
          .generateExchangeRateReport(
            startDate: _startDate,
            endDate: _endDate,
            baseCurrency: _selectedCurrency,
          );

      setState(() {
        _salesReport = salesReport;
        _purchaseReport = purchaseReport;
        _profitLossReport = profitLossReport;
        _exchangeRateReport = exchangeRateReport;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إنشاء التقارير: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير المالية متعددة العملات'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المبيعات', icon: Icon(Icons.trending_up)),
            Tab(text: 'المشتريات', icon: Icon(Icons.trending_down)),
            Tab(text: 'الأرباح والخسائر', icon: Icon(Icons.analytics)),
            Tab(text: 'أسعار الصرف', icon: Icon(Icons.currency_exchange)),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildSalesReport(),
                      _buildPurchaseReport(),
                      _buildProfitLossReport(),
                      _buildExchangeRateReport(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<String>(
                    value: _selectedCurrency,
                    labelText: 'عملة العرض',
                    items: _currencies.map((currency) {
                      return DropdownMenuItem(
                        value: currency.code,
                        child: Text('${currency.nameAr} (${currency.code})'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedCurrency = value);
                        _generateReports();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _generateReports,
                  icon: const Icon(Icons.refresh),
                  label: const Text('تحديث'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ListTile(
                    title: const Text('من تاريخ'),
                    subtitle: Text(_startDate.toString().split(' ')[0]),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _startDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() => _startDate = date);
                        _generateReports();
                      }
                    },
                  ),
                ),
                Expanded(
                  child: ListTile(
                    title: const Text('إلى تاريخ'),
                    subtitle: Text(_endDate.toString().split(' ')[0]),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _endDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() => _endDate = date);
                        _generateReports();
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesReport() {
    if (_salesReport == null) {
      return const Center(child: Text('لا توجد بيانات مبيعات'));
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        QuantumCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي المبيعات',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  _reportService.formatAmount(
                    _salesReport!.totalInTargetCurrency,
                    _selectedCurrency,
                  ),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text('عدد الفواتير: ${_salesReport!.totalInvoices}'),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        QuantumCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تفصيل حسب العملة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ..._salesReport!.currencyData.map(
                  (data) => _buildCurrencyDataTile(data),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPurchaseReport() {
    if (_purchaseReport == null) {
      return const Center(child: Text('لا توجد بيانات مشتريات'));
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        QuantumCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي المشتريات',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  _reportService.formatAmount(
                    _purchaseReport!.totalInTargetCurrency,
                    _selectedCurrency,
                  ),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text('عدد الفواتير: ${_purchaseReport!.totalInvoices}'),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        QuantumCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تفصيل حسب العملة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ..._purchaseReport!.currencyData.map(
                  (data) => _buildCurrencyDataTile(data),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfitLossReport() {
    if (_profitLossReport == null) {
      return const Center(child: Text('لا توجد بيانات أرباح وخسائر'));
    }

    final isProfit = _profitLossReport!.netProfit >= 0;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        QuantumCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildProfitLossItem(
                  'إجمالي الإيرادات',
                  _profitLossReport!.totalRevenue,
                  Colors.green,
                ),
                const Divider(),
                _buildProfitLossItem(
                  'إجمالي المصروفات',
                  _profitLossReport!.totalExpenses,
                  Colors.red,
                ),
                const Divider(),
                _buildProfitLossItem(
                  isProfit ? 'صافي الربح' : 'صافي الخسارة',
                  _profitLossReport!.netProfit.abs(),
                  isProfit ? Colors.green : Colors.red,
                ),
                const SizedBox(height: 8),
                Text(
                  'هامش الربح: ${_profitLossReport!.profitMargin.toStringAsFixed(2)}%',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExchangeRateReport() {
    if (_exchangeRateReport == null) {
      return const Center(child: Text('لا توجد بيانات أسعار صرف'));
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        QuantumCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أسعار الصرف مقابل $_selectedCurrency',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ..._exchangeRateReport!.exchangeRates.map(
                  (rate) => _buildExchangeRateTile(rate),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCurrencyDataTile(CurrencyReportData data) {
    return ListTile(
      title: Text(data.currencyCode),
      subtitle: Text('عدد الفواتير: ${data.invoiceCount}'),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            _reportService.formatAmount(data.totalAmount, data.currencyCode),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(
            _reportService.formatAmount(
              data.totalAmountInTargetCurrency,
              _selectedCurrency,
            ),
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitLossItem(String title, double amount, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        Text(
          _reportService.formatAmount(amount, _selectedCurrency),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildExchangeRateTile(ExchangeRateData rate) {
    return ListTile(
      title: Text('${rate.currencyName} (${rate.currencyCode})'),
      subtitle: Text('آخر تحديث: ${rate.lastUpdated.toString().split(' ')[0]}'),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            rate.currentRate.toStringAsFixed(4),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(
            '${rate.changePercentage >= 0 ? '+' : ''}${rate.changePercentage.toStringAsFixed(2)}%',
            style: TextStyle(
              fontSize: 12,
              color: rate.changePercentage >= 0 ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}
