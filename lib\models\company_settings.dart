class CompanySettings {
  final int? id;
  final String companyName;
  final String? companyNameEn;
  final String? address;
  final String? phone;
  final String? email;
  final String? taxNumber;
  final String currencyCode;
  final String currencySymbol;
  final String fiscalYearStart;
  final int decimalPlaces;
  final String dateFormat;
  final String? logoPath;
  final DateTime createdAt;
  final DateTime updatedAt;

  CompanySettings({
    this.id,
    required this.companyName,
    this.companyNameEn,
    this.address,
    this.phone,
    this.email,
    this.taxNumber,
    this.currencyCode = 'SAR',
    this.currencySymbol = 'ر.س',
    this.fiscalYearStart = '01-01',
    this.decimalPlaces = 2,
    this.dateFormat = 'dd/MM/yyyy',
    this.logoPath,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from database map
  factory CompanySettings.fromMap(Map<String, dynamic> map) {
    return CompanySettings(
      id: map['id'] as int?,
      companyName: map['company_name'] as String,
      companyNameEn: map['company_name_en'] as String?,
      address: map['address'] as String?,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      taxNumber: map['tax_number'] as String?,
      currencyCode: map['currency_code'] as String? ?? 'SAR',
      currencySymbol: map['currency_symbol'] as String? ?? 'ر.س',
      fiscalYearStart: map['fiscal_year_start'] as String? ?? '01-01',
      decimalPlaces: map['decimal_places'] as int? ?? 2,
      dateFormat: map['date_format'] as String? ?? 'dd/MM/yyyy',
      logoPath: map['logo_path'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'company_name': companyName,
      'company_name_en': companyNameEn,
      'address': address,
      'phone': phone,
      'email': email,
      'tax_number': taxNumber,
      'currency_code': currencyCode,
      'currency_symbol': currencySymbol,
      'fiscal_year_start': fiscalYearStart,
      'decimal_places': decimalPlaces,
      'date_format': dateFormat,
      'logo_path': logoPath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  CompanySettings copyWith({
    int? id,
    String? companyName,
    String? companyNameEn,
    String? address,
    String? phone,
    String? email,
    String? taxNumber,
    String? currencyCode,
    String? currencySymbol,
    String? fiscalYearStart,
    int? decimalPlaces,
    String? dateFormat,
    String? logoPath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanySettings(
      id: id ?? this.id,
      companyName: companyName ?? this.companyName,
      companyNameEn: companyNameEn ?? this.companyNameEn,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      taxNumber: taxNumber ?? this.taxNumber,
      currencyCode: currencyCode ?? this.currencyCode,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      fiscalYearStart: fiscalYearStart ?? this.fiscalYearStart,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      dateFormat: dateFormat ?? this.dateFormat,
      logoPath: logoPath ?? this.logoPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  String get displayName => companyName;
  
  String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(decimalPlaces)} $currencySymbol';
  }

  String formatDate(DateTime date) {
    // Simple date formatting - in a real app you'd use intl package
    switch (dateFormat) {
      case 'dd/MM/yyyy':
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      case 'MM/dd/yyyy':
        return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
      case 'yyyy-MM-dd':
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      default:
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    }
  }

  DateTime parseFiscalYearStart(int year) {
    List<String> parts = fiscalYearStart.split('-');
    if (parts.length == 2) {
      int month = int.tryParse(parts[1]) ?? 1;
      int day = int.tryParse(parts[0]) ?? 1;
      return DateTime(year, month, day);
    }
    return DateTime(year, 1, 1);
  }

  DateTime getCurrentFiscalYearStart() {
    DateTime now = DateTime.now();
    DateTime fiscalStart = parseFiscalYearStart(now.year);
    
    // If we're before the fiscal year start date, use previous year
    if (now.isBefore(fiscalStart)) {
      fiscalStart = parseFiscalYearStart(now.year - 1);
    }
    
    return fiscalStart;
  }

  DateTime getCurrentFiscalYearEnd() {
    DateTime fiscalStart = getCurrentFiscalYearStart();
    return DateTime(fiscalStart.year + 1, fiscalStart.month, fiscalStart.day)
        .subtract(const Duration(days: 1));
  }

  // Validation
  List<String> validate() {
    List<String> errors = [];

    if (companyName.trim().isEmpty) {
      errors.add('اسم الشركة مطلوب');
    }

    if (currencyCode.trim().isEmpty) {
      errors.add('رمز العملة مطلوب');
    }

    if (currencySymbol.trim().isEmpty) {
      errors.add('رمز العملة مطلوب');
    }

    if (decimalPlaces < 0 || decimalPlaces > 6) {
      errors.add('عدد الخانات العشرية يجب أن يكون بين 0 و 6');
    }

    if (email != null && email!.isNotEmpty) {
      if (!_isValidEmail(email!)) {
        errors.add('البريد الإلكتروني غير صحيح');
      }
    }

    if (phone != null && phone!.isNotEmpty) {
      if (!_isValidPhone(phone!)) {
        errors.add('رقم الهاتف غير صحيح');
      }
    }

    if (!_isValidFiscalYearStart(fiscalYearStart)) {
      errors.add('تاريخ بداية السنة المالية غير صحيح (يجب أن يكون بصيغة dd-MM)');
    }

    return errors;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPhone(String phone) {
    return RegExp(r'^[\+]?[0-9\-\s\(\)]{7,15}$').hasMatch(phone);
  }

  bool _isValidFiscalYearStart(String fiscalStart) {
    List<String> parts = fiscalStart.split('-');
    if (parts.length != 2) return false;
    
    int? day = int.tryParse(parts[0]);
    int? month = int.tryParse(parts[1]);
    
    if (day == null || month == null) return false;
    if (day < 1 || day > 31) return false;
    if (month < 1 || month > 12) return false;
    
    return true;
  }

  @override
  String toString() {
    return 'CompanySettings{id: $id, name: $companyName, currency: $currencyCode}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompanySettings && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Predefined currency options
class Currency {
  final String code;
  final String symbol;
  final String nameAr;
  final String nameEn;

  const Currency({
    required this.code,
    required this.symbol,
    required this.nameAr,
    required this.nameEn,
  });

  static const List<Currency> predefinedCurrencies = [
    Currency(code: 'SAR', symbol: 'ر.س', nameAr: 'ريال سعودي', nameEn: 'Saudi Riyal'),
    Currency(code: 'USD', symbol: '\$', nameAr: 'دولار أمريكي', nameEn: 'US Dollar'),
    Currency(code: 'EUR', symbol: '€', nameAr: 'يورو', nameEn: 'Euro'),
    Currency(code: 'AED', symbol: 'د.إ', nameAr: 'درهم إماراتي', nameEn: 'UAE Dirham'),
    Currency(code: 'KWD', symbol: 'د.ك', nameAr: 'دينار كويتي', nameEn: 'Kuwaiti Dinar'),
    Currency(code: 'QAR', symbol: 'ر.ق', nameAr: 'ريال قطري', nameEn: 'Qatari Riyal'),
    Currency(code: 'BHD', symbol: 'د.ب', nameAr: 'دينار بحريني', nameEn: 'Bahraini Dinar'),
    Currency(code: 'OMR', symbol: 'ر.ع', nameAr: 'ريال عماني', nameEn: 'Omani Rial'),
    Currency(code: 'JOD', symbol: 'د.أ', nameAr: 'دينار أردني', nameEn: 'Jordanian Dinar'),
    Currency(code: 'EGP', symbol: 'ج.م', nameAr: 'جنيه مصري', nameEn: 'Egyptian Pound'),
  ];

  static Currency? findByCode(String code) {
    try {
      return predefinedCurrencies.firstWhere((currency) => currency.code == code);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() => '$nameAr ($code)';
}

// Date format options
class DateFormatOption {
  final String format;
  final String nameAr;
  final String nameEn;
  final String example;

  const DateFormatOption({
    required this.format,
    required this.nameAr,
    required this.nameEn,
    required this.example,
  });

  static const List<DateFormatOption> predefinedFormats = [
    DateFormatOption(
      format: 'dd/MM/yyyy',
      nameAr: 'يوم/شهر/سنة',
      nameEn: 'DD/MM/YYYY',
      example: '25/12/2023',
    ),
    DateFormatOption(
      format: 'MM/dd/yyyy',
      nameAr: 'شهر/يوم/سنة',
      nameEn: 'MM/DD/YYYY',
      example: '12/25/2023',
    ),
    DateFormatOption(
      format: 'yyyy-MM-dd',
      nameAr: 'سنة-شهر-يوم',
      nameEn: 'YYYY-MM-DD',
      example: '2023-12-25',
    ),
  ];

  static DateFormatOption? findByFormat(String format) {
    try {
      return predefinedFormats.firstWhere((option) => option.format == format);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() => '$nameAr ($example)';
}
