import '../dao/employee_dao.dart';
import '../dao/department_dao.dart';
import '../dao/position_dao.dart';
import '../models/employee.dart';
import '../utils/result.dart';

/// خدمة إدارة الموظفين
/// Employee Management Service
class EmployeeService {
  final EmployeeDao _employeeDao = EmployeeDao();
  final DepartmentDao _departmentDao = DepartmentDao();
  final PositionDao _positionDao = PositionDao();

  /// الحصول على جميع الموظفين
  Future<Result<List<Employee>>> getAllEmployees() async {
    try {
      final employees = await _employeeDao.getAllEmployees();
      return Result.success(employees);
    } catch (e) {
      return Result.error('خطأ في جلب الموظفين: ${e.toString()}');
    }
  }

  /// الحصول على الموظفين النشطين
  Future<Result<List<Employee>>> getActiveEmployees() async {
    try {
      final employees = await _employeeDao.getActiveEmployees();
      return Result.success(employees);
    } catch (e) {
      return Result.error('خطأ في جلب الموظفين النشطين: ${e.toString()}');
    }
  }

  /// الحصول على موظف بالمعرف
  Future<Result<Employee>> getEmployeeById(int id) async {
    try {
      final employee = await _employeeDao.getEmployeeById(id);
      if (employee != null) {
        return Result.success(employee);
      } else {
        return Result.error('الموظف غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب الموظف: ${e.toString()}');
    }
  }

  /// الحصول على موظف بالرمز
  Future<Result<Employee>> getEmployeeByCode(String employeeCode) async {
    try {
      final employee = await _employeeDao.getEmployeeByCode(employeeCode);
      if (employee != null) {
        return Result.success(employee);
      } else {
        return Result.error('الموظف غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب الموظف: ${e.toString()}');
    }
  }

  /// إنشاء موظف جديد
  Future<Result<Employee>> createEmployee(Employee employee) async {
    try {
      // التحقق من صحة البيانات
      final validation = await _validateEmployee(employee);
      if (!validation.isSuccess) {
        return validation;
      }

      // التحقق من تفرد رمز الموظف
      final isCodeUnique = await _employeeDao.isEmployeeCodeUnique(
        employee.employeeCode,
      );
      if (!isCodeUnique) {
        return Result.error('رمز الموظف موجود مسبقاً');
      }

      // التحقق من تفرد الهوية الوطنية
      if (employee.nationalId != null && employee.nationalId!.isNotEmpty) {
        final isNationalIdUnique = await _employeeDao.isNationalIdUnique(
          employee.nationalId!,
        );
        if (!isNationalIdUnique) {
          return Result.error('الهوية الوطنية موجودة مسبقاً');
        }
      }

      final id = await _employeeDao.insertEmployee(employee);
      final newEmployee = employee.copyWith(id: id);
      return Result.success(newEmployee);
    } catch (e) {
      return Result.error('خطأ في إنشاء الموظف: ${e.toString()}');
    }
  }

  /// تحديث موظف
  Future<Result<Employee>> updateEmployee(Employee employee) async {
    try {
      // التحقق من وجود الموظف
      final existingEmployee = await _employeeDao.getEmployeeById(employee.id!);
      if (existingEmployee == null) {
        return Result.error('الموظف غير موجود');
      }

      // التحقق من صحة البيانات
      final validation = await _validateEmployee(employee);
      if (!validation.isSuccess) {
        return validation;
      }

      // التحقق من تفرد رمز الموظف
      final isCodeUnique = await _employeeDao.isEmployeeCodeUnique(
        employee.employeeCode,
        excludeId: employee.id,
      );
      if (!isCodeUnique) {
        return Result.error('رمز الموظف موجود مسبقاً');
      }

      // التحقق من تفرد الهوية الوطنية
      if (employee.nationalId != null && employee.nationalId!.isNotEmpty) {
        final isNationalIdUnique = await _employeeDao.isNationalIdUnique(
          employee.nationalId!,
          excludeId: employee.id,
        );
        if (!isNationalIdUnique) {
          return Result.error('الهوية الوطنية موجودة مسبقاً');
        }
      }

      final updatedEmployee = employee.copyWith(updatedAt: DateTime.now());
      await _employeeDao.updateEmployee(updatedEmployee);
      return Result.success(updatedEmployee);
    } catch (e) {
      return Result.error('خطأ في تحديث الموظف: ${e.toString()}');
    }
  }

  /// حذف موظف
  Future<Result<void>> deleteEmployee(int id) async {
    try {
      final employee = await _employeeDao.getEmployeeById(id);
      if (employee == null) {
        return Result.error('الموظف غير موجود');
      }

      await _employeeDao.deleteEmployee(id);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف الموظف: ${e.toString()}');
    }
  }

  /// البحث في الموظفين
  Future<Result<List<Employee>>> searchEmployees(String query) async {
    try {
      if (query.trim().isEmpty) {
        return getAllEmployees();
      }
      final employees = await _employeeDao.searchEmployees(query.trim());
      return Result.success(employees);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  /// الحصول على الموظفين حسب القسم
  Future<Result<List<Employee>>> getEmployeesByDepartment(
    int departmentId,
  ) async {
    try {
      final employees = await _employeeDao.getEmployeesByDepartment(
        departmentId,
      );
      return Result.success(employees);
    } catch (e) {
      return Result.error('خطأ في جلب موظفي القسم: ${e.toString()}');
    }
  }

  /// الحصول على الموظفين حسب المنصب
  Future<Result<List<Employee>>> getEmployeesByPosition(int positionId) async {
    try {
      final employees = await _employeeDao.getEmployeesByPosition(positionId);
      return Result.success(employees);
    } catch (e) {
      return Result.error('خطأ في جلب موظفي المنصب: ${e.toString()}');
    }
  }

  /// الحصول على الموظفين حسب المدير
  Future<Result<List<Employee>>> getEmployeesByManager(String managerId) async {
    try {
      final employees = await _employeeDao.getEmployeesByManager(managerId);
      return Result.success(employees);
    } catch (e) {
      return Result.error('خطأ في جلب موظفي المدير: ${e.toString()}');
    }
  }

  /// تحديث حالة الموظف
  Future<Result<void>> updateEmployeeStatus(
    String employeeCode,
    EmployeeStatus status,
  ) async {
    try {
      final employee = await _employeeDao.getEmployeeByCode(employeeCode);
      if (employee == null) {
        return Result.error('الموظف غير موجود');
      }

      await _employeeDao.updateEmployeeStatus(employeeCode, status);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث حالة الموظف: ${e.toString()}');
    }
  }

  /// تحديث راتب الموظف
  Future<Result<void>> updateEmployeeSalary(
    String employeeCode,
    double newSalary,
  ) async {
    try {
      if (newSalary < 0) {
        return Result.error('الراتب يجب أن يكون أكبر من أو يساوي صفر');
      }

      final employee = await _employeeDao.getEmployeeByCode(employeeCode);
      if (employee == null) {
        return Result.error('الموظف غير موجود');
      }

      await _employeeDao.updateEmployeeSalary(employeeCode, newSalary);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث راتب الموظف: ${e.toString()}');
    }
  }

  /// إنهاء خدمة الموظف
  Future<Result<void>> terminateEmployee(
    String employeeCode,
    DateTime terminationDate,
  ) async {
    try {
      final employee = await _employeeDao.getEmployeeByCode(employeeCode);
      if (employee == null) {
        return Result.error('الموظف غير موجود');
      }

      if (terminationDate.isBefore(employee.hireDate)) {
        return Result.error(
          'تاريخ إنهاء الخدمة لا يمكن أن يكون قبل تاريخ التوظيف',
        );
      }

      await _employeeDao.terminateEmployee(employeeCode, terminationDate);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في إنهاء خدمة الموظف: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات الموظفين
  Future<Result<Map<String, dynamic>>> getEmployeeStatistics() async {
    try {
      final statistics = await _employeeDao.getEmployeeStatistics();
      return Result.success(statistics);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات الموظفين: ${e.toString()}');
    }
  }

  /// الحصول على الموظفين مع التفاصيل
  Future<Result<List<Map<String, dynamic>>>> getEmployeesWithDetails() async {
    try {
      final employees = await _employeeDao.getEmployeesWithDetails();
      return Result.success(employees);
    } catch (e) {
      return Result.error('خطأ في جلب تفاصيل الموظفين: ${e.toString()}');
    }
  }

  /// الحصول على الموظفين المتقاعدين قريباً
  Future<Result<List<Employee>>> getEmployeesNearRetirement({
    int yearsToRetirement = 5,
  }) async {
    try {
      final employees = await _employeeDao.getEmployeesNearRetirement(
        yearsToRetirement,
      );
      return Result.success(employees);
    } catch (e) {
      return Result.error(
        'خطأ في جلب الموظفين المتقاعدين قريباً: ${e.toString()}',
      );
    }
  }

  /// توليد رمز موظف جديد
  Future<Result<String>> generateEmployeeCode() async {
    try {
      final currentYear = DateTime.now().year;
      final employees = await _employeeDao.getAllEmployees();

      // البحث عن أعلى رقم في السنة الحالية
      int maxNumber = 0;
      final yearPrefix = currentYear.toString().substring(
        2,
      ); // آخر رقمين من السنة

      for (final employee in employees) {
        if (employee.employeeCode.startsWith('EMP$yearPrefix')) {
          final numberPart = employee.employeeCode.substring(5);
          final number = int.tryParse(numberPart) ?? 0;
          if (number > maxNumber) {
            maxNumber = number;
          }
        }
      }

      final newNumber = (maxNumber + 1).toString().padLeft(4, '0');
      final newCode = 'EMP$yearPrefix$newNumber';

      return Result.success(newCode);
    } catch (e) {
      return Result.error('خطأ في توليد رمز الموظف: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات الموظف
  Future<Result<Employee>> _validateEmployee(Employee employee) async {
    // التحقق من الحقول المطلوبة
    if (employee.employeeCode.trim().isEmpty) {
      return Result.error('رمز الموظف مطلوب');
    }

    if (employee.firstName.trim().isEmpty) {
      return Result.error('الاسم الأول مطلوب');
    }

    if (employee.lastName.trim().isEmpty) {
      return Result.error('اسم العائلة مطلوب');
    }

    if (employee.basicSalary < 0) {
      return Result.error('الراتب الأساسي يجب أن يكون أكبر من أو يساوي صفر');
    }

    // التحقق من وجود القسم
    final department = await _departmentDao.getDepartmentById(
      employee.departmentId,
    );
    if (department == null) {
      return Result.error('القسم المحدد غير موجود');
    }

    // التحقق من وجود المنصب
    final position = await _positionDao.getPositionById(employee.positionId);
    if (position == null) {
      return Result.error('المنصب المحدد غير موجود');
    }

    // التحقق من أن المنصب ينتمي للقسم المحدد
    if (position.departmentId != employee.departmentId) {
      return Result.error('المنصب المحدد لا ينتمي للقسم المحدد');
    }

    // التحقق من المدير المباشر إذا تم تحديده
    if (employee.directManagerId != null &&
        employee.directManagerId!.isNotEmpty) {
      final manager = await _employeeDao.getEmployeeByCode(
        employee.directManagerId!,
      );
      if (manager == null) {
        return Result.error('المدير المباشر المحدد غير موجود');
      }

      // التأكد من أن الموظف لا يكون مديراً لنفسه
      if (manager.employeeCode == employee.employeeCode) {
        return Result.error('لا يمكن للموظف أن يكون مديراً لنفسه');
      }
    }

    // التحقق من صحة البريد الإلكتروني
    if (employee.email != null && employee.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(employee.email!)) {
        return Result.error('البريد الإلكتروني غير صحيح');
      }
    }

    // التحقق من تاريخ الميلاد
    if (employee.birthDate != null) {
      final now = DateTime.now();
      final age = now.year - employee.birthDate!.year;
      if (age < 16 || age > 80) {
        return Result.error('العمر يجب أن يكون بين 16 و 80 سنة');
      }
    }

    // التحقق من تاريخ التوظيف
    if (employee.hireDate.isAfter(DateTime.now())) {
      return Result.error('تاريخ التوظيف لا يمكن أن يكون في المستقبل');
    }

    // التحقق من تاريخ إنهاء الخدمة
    if (employee.terminationDate != null) {
      if (employee.terminationDate!.isBefore(employee.hireDate)) {
        return Result.error(
          'تاريخ إنهاء الخدمة لا يمكن أن يكون قبل تاريخ التوظيف',
        );
      }
    }

    return Result.success(employee);
  }
}
