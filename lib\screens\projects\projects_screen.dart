/// شاشة إدارة المشاريع
/// Projects Management Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/project.dart';
import '../../services/project_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_text_styles.dart';

import 'project_details_screen.dart';
import 'add_edit_project_screen.dart';

class ProjectsScreen extends StatefulWidget {
  const ProjectsScreen({super.key});

  @override
  State<ProjectsScreen> createState() => _ProjectsScreenState();
}

class _ProjectsScreenState extends State<ProjectsScreen> {
  final ProjectService _projectService = ProjectService();
  final TextEditingController _searchController = TextEditingController();

  List<Project> _projects = [];
  List<Project> _filteredProjects = [];
  bool _isLoading = false;
  ProjectStatus? _selectedStatus;
  String _searchTerm = '';

  @override
  void initState() {
    super.initState();
    _loadProjects();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchTerm = _searchController.text;
      _filterProjects();
    });
  }

  void _filterProjects() {
    _filteredProjects = _projects.where((project) {
      final matchesSearch =
          _searchTerm.isEmpty ||
          project.name.toLowerCase().contains(_searchTerm.toLowerCase()) ||
          project.code.toLowerCase().contains(_searchTerm.toLowerCase()) ||
          (project.description?.toLowerCase().contains(
                _searchTerm.toLowerCase(),
              ) ??
              false);

      final matchesStatus =
          _selectedStatus == null || project.status == _selectedStatus;

      return matchesSearch && matchesStatus;
    }).toList();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _projectService.getAllProjects();
      if (result.isSuccess && result.data != null) {
        setState(() {
          _projects = result.data!;
          _filterProjects();
        });
      } else {
        _showErrorDialog(result.error ?? 'فشل في تحميل المشاريع');
      }
    } catch (e) {
      _showErrorDialog('خطأ في تحميل المشاريع: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  void _onStatusFilterChanged(ProjectStatus? status) {
    setState(() {
      _selectedStatus = status;
      _filterProjects();
    });
  }

  Future<void> _navigateToAddProject() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEditProjectScreen()),
    );

    if (result == true) {
      _loadProjects();
    }
  }

  Future<void> _navigateToProjectDetails(Project project) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectDetailsScreen(project: project),
      ),
    );

    if (result == true) {
      _loadProjects();
    }
  }

  Future<void> _deleteProject(Project project) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المشروع "${project.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final result = await _projectService.deleteProject(project.id!);
        if (result.isSuccess) {
          _loadProjects();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم حذف المشروع بنجاح')),
            );
          }
        } else {
          _showErrorDialog(result.error ?? 'فشل في حذف المشروع');
        }
      } catch (e) {
        _showErrorDialog('خطأ في حذف المشروع: ${e.toString()}');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المشاريع'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Column(
          children: [
            // شريط البحث والفلترة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: QuantumTextField(
                          controller: _searchController,
                          labelText: 'البحث في المشاريع',
                          prefixIcon: Icons.search,
                          textAlign: TextAlign.right,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: QuantumDropdown<ProjectStatus>(
                          value: _selectedStatus,
                          items: ProjectStatus.values
                              .map(
                                (status) => DropdownMenuItem(
                                  value: status,
                                  child: Text(status.nameAr),
                                ),
                              )
                              .toList(),
                          onChanged: _onStatusFilterChanged,
                          hintText: 'فلترة حسب الحالة',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      QuantumButton(
                        onPressed: _navigateToAddProject,
                        text: 'إضافة مشروع جديد',
                        icon: Icons.add,
                        variant: QuantumButtonVariant.primary,
                      ),
                      const Spacer(),
                      Text(
                        'إجمالي المشاريع: ${_filteredProjects.length}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // قائمة المشاريع
            Expanded(
              child: _filteredProjects.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.folder_open,
                            size: 64,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _projects.isEmpty
                                ? 'لا توجد مشاريع'
                                : 'لا توجد مشاريع تطابق البحث',
                            style: AppTextStyles.headlineSmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredProjects.length,
                      itemBuilder: (context, index) {
                        final project = _filteredProjects[index];
                        return _buildProjectCard(project);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectCard(Project project) {
    final completionPercentage = project.completionPercentage;
    final isOverdue =
        project.endDate != null &&
        project.endDate!.isBefore(DateTime.now()) &&
        project.status != ProjectStatus.completed;

    return QuantumCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToProjectDetails(project),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          project.name,
                          style: AppTextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'كود المشروع: ${project.code}',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(project.status),
                  if (isOverdue) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.error.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'متأخر',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.error,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              if (project.description != null) ...[
                const SizedBox(height: 8),
                Text(
                  project.description!,
                  style: AppTextStyles.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 12),

              // شريط التقدم
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'نسبة الإنجاز',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${completionPercentage.toStringAsFixed(1)}%',
                        style: AppTextStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: completionPercentage / 100,
                    backgroundColor: AppColors.surface,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getProgressColor(completionPercentage),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // معلومات إضافية
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'الميزانية',
                      '${project.budgetAmount.toStringAsFixed(0)} ر.س',
                      Icons.account_balance_wallet,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'التكلفة الفعلية',
                      '${project.actualCost.toStringAsFixed(0)} ر.س',
                      Icons.receipt_long,
                    ),
                  ),
                  if (project.endDate != null)
                    Expanded(
                      child: _buildInfoItem(
                        'تاريخ الانتهاء',
                        '${project.endDate!.day}/${project.endDate!.month}/${project.endDate!.year}',
                        Icons.calendar_today,
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // أزرار العمليات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    onPressed: () => _navigateToProjectDetails(project),
                    icon: const Icon(Icons.visibility),
                    tooltip: 'عرض التفاصيل',
                  ),
                  IconButton(
                    onPressed: () async {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              AddEditProjectScreen(project: project),
                        ),
                      );
                      if (result == true) {
                        _loadProjects();
                      }
                    },
                    icon: const Icon(Icons.edit),
                    tooltip: 'تعديل',
                  ),
                  IconButton(
                    onPressed: () => _deleteProject(project),
                    icon: const Icon(Icons.delete),
                    color: AppColors.error,
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(ProjectStatus status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case ProjectStatus.planning:
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        break;
      case ProjectStatus.active:
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        break;
      case ProjectStatus.onHold:
        backgroundColor = AppColors.info.withValues(alpha: 0.1);
        textColor = AppColors.info;
        break;
      case ProjectStatus.completed:
        backgroundColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        break;
      case ProjectStatus.cancelled:
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status.nameAr,
        style: AppTextStyles.bodySmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: AppColors.textSecondary),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: AppTextStyles.bodySmall.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 80) return AppColors.success;
    if (percentage >= 50) return AppColors.warning;
    return AppColors.error;
  }
}
