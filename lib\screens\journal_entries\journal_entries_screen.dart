import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/journal_entry.dart';
import '../../services/journal_entry_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_text_form_field.dart';
import 'add_journal_entry_screen.dart';
import 'journal_entry_details_screen.dart';

class JournalEntriesScreen extends StatefulWidget {
  const JournalEntriesScreen({super.key});

  @override
  State<JournalEntriesScreen> createState() => _JournalEntriesScreenState();
}

class _JournalEntriesScreenState extends State<JournalEntriesScreen>
    with SingleTickerProviderStateMixin {
  final JournalEntryService _journalEntryService = JournalEntryService();
  final TextEditingController _searchController = TextEditingController();
  
  List<JournalEntry> _allEntries = [];
  List<JournalEntry> _filteredEntries = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _statusFilter = 'all';
  DateTime? _startDate;
  DateTime? _endDate;
  
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadJournalEntries();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJournalEntries() async {
    setState(() => _isLoading = true);
    
    try {
      final result = await _journalEntryService.getAllJournalEntries();
      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _allEntries = result.data!;
            _applyFilters();
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'خطأ في تحميل القيود'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل القيود: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _applyFilters() {
    List<JournalEntry> filtered = List.from(_allEntries);

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((entry) {
        return entry.entryNumber.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               entry.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (entry.reference?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }

    // تطبيق فلتر الحالة
    if (_statusFilter != 'all') {
      filtered = filtered.where((entry) {
        switch (_statusFilter) {
          case 'posted':
            return entry.isPosted;
          case 'unposted':
            return !entry.isPosted;
          case 'balanced':
            return entry.isBalanced;
          case 'unbalanced':
            return !entry.isBalanced;
          default:
            return true;
        }
      }).toList();
    }

    // تطبيق فلتر التاريخ
    if (_startDate != null && _endDate != null) {
      filtered = filtered.where((entry) {
        return entry.date.isAfter(_startDate!.subtract(const Duration(days: 1))) &&
               entry.date.isBefore(_endDate!.add(const Duration(days: 1)));
      }).toList();
    }

    setState(() {
      _filteredEntries = filtered;
    });
  }

  Future<void> _deleteJournalEntry(JournalEntry entry) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القيد "${entry.entryNumber}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await _journalEntryService.deleteJournalEntry(entry.id!);
      if (mounted) {
        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف القيد بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          _loadJournalEntries();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'خطأ في حذف القيد'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _postJournalEntry(JournalEntry entry) async {
    final result = await _journalEntryService.postJournalEntry(entry.id!);
    if (mounted) {
      if (result.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم ترحيل القيد بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadJournalEntries();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.error ?? 'خطأ في ترحيل القيد'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _applyFilters();
    }
  }

  void _clearDateFilter() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('القيود اليومية'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadJournalEntries,
            icon: const Icon(Icons.refresh),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'جميع القيود', icon: Icon(Icons.list)),
            Tab(text: 'غير مرحلة', icon: Icon(Icons.pending)),
            Tab(text: 'مرحلة', icon: Icon(Icons.check_circle)),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildEntriesList(_filteredEntries),
                _buildEntriesList(_filteredEntries.where((e) => !e.isPosted).toList()),
                _buildEntriesList(_filteredEntries.where((e) => e.isPosted).toList()),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) => const AddJournalEntryScreen(),
            ),
          );
          if (result == true) {
            _loadJournalEntries();
          }
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          BeautifulTextFormField(
            controller: _searchController,
            labelText: 'البحث في القيود...',
            prefixIcon: Icons.search,
            onChanged: (value) {
              setState(() => _searchQuery = value);
              _applyFilters();
            },
          ),
          
          const SizedBox(height: AppTheme.spacingMedium),
          
          // فلاتر إضافية
          Row(
            children: [
              // فلتر الحالة
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _statusFilter,
                  decoration: const InputDecoration(
                    labelText: 'فلتر الحالة',
                    prefixIcon: Icon(Icons.filter_list),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع القيود')),
                    DropdownMenuItem(value: 'posted', child: Text('مرحلة')),
                    DropdownMenuItem(value: 'unposted', child: Text('غير مرحلة')),
                    DropdownMenuItem(value: 'balanced', child: Text('متوازنة')),
                    DropdownMenuItem(value: 'unbalanced', child: Text('غير متوازنة')),
                  ],
                  onChanged: (value) {
                    setState(() => _statusFilter = value!);
                    _applyFilters();
                  },
                ),
              ),
              
              const SizedBox(width: AppTheme.spacingMedium),
              
              // فلتر التاريخ
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectDateRange,
                        icon: const Icon(Icons.date_range),
                        label: Text(
                          _startDate != null && _endDate != null
                              ? '${_formatDate(_startDate!)} - ${_formatDate(_endDate!)}'
                              : 'فترة زمنية',
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _startDate != null ? AppTheme.primaryColor : Colors.grey[300],
                          foregroundColor: _startDate != null ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                    if (_startDate != null)
                      IconButton(
                        onPressed: _clearDateFilter,
                        icon: const Icon(Icons.clear),
                        color: Colors.red,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEntriesList(List<JournalEntry> entries) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (entries.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد قيود',
          style: TextStyle(fontSize: 18, color: Colors.grey),
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        itemCount: entries.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildEntryCard(entries[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEntryCard(JournalEntry entry) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      elevation: 2,
      child: InkWell(
        onTap: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) => JournalEntryDetailsScreen(entry: entry),
            ),
          );
          if (result == true) {
            _loadJournalEntries();
          }
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  // أيقونة القيد
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getStatusColor(entry).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      entry.isPosted ? Icons.check_circle : Icons.pending,
                      color: _getStatusColor(entry),
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(width: AppTheme.spacingMedium),
                  
                  // معلومات القيد
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'قيد رقم: ${entry.entryNumber}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          entry.description,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  // قائمة الإجراءات
                  PopupMenuButton<String>(
                    onSelected: (value) async {
                      switch (value) {
                        case 'edit':
                          if (!entry.isPosted) {
                            final result = await Navigator.push<bool>(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddJournalEntryScreen(entry: entry),
                              ),
                            );
                            if (result == true) {
                              _loadJournalEntries();
                            }
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('لا يمكن تعديل قيد مرحل'),
                                backgroundColor: Colors.orange,
                              ),
                            );
                          }
                          break;
                        case 'post':
                          if (!entry.isPosted) {
                            _postJournalEntry(entry);
                          }
                          break;
                        case 'delete':
                          if (!entry.isPosted) {
                            _deleteJournalEntry(entry);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('لا يمكن حذف قيد مرحل'),
                                backgroundColor: Colors.orange,
                              ),
                            );
                          }
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (!entry.isPosted) ...[
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 20),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'post',
                          child: Row(
                            children: [
                              Icon(Icons.check, size: 20, color: Colors.green),
                              SizedBox(width: 8),
                              Text('ترحيل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 20, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ] else ...[
                        const PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              Icon(Icons.visibility, size: 20),
                              SizedBox(width: 8),
                              Text('عرض'),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: AppTheme.spacingMedium),
              
              // تفاصيل إضافية
              Row(
                children: [
                  _buildInfoChip(
                    Icons.calendar_today,
                    _formatDate(entry.date),
                    Colors.blue,
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  _buildInfoChip(
                    Icons.list,
                    '${entry.lines.length} سطر',
                    Colors.orange,
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  _buildStatusChip(entry),
                ],
              ),
              
              const SizedBox(height: AppTheme.spacingSmall),
              
              // المبالغ
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المدين: ${entry.totalDebit.toStringAsFixed(2)} ر.س',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    'الدائن: ${entry.totalCredit.toStringAsFixed(2)} ر.س',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              
              if (entry.reference != null && entry.reference!.isNotEmpty) ...[
                const SizedBox(height: AppTheme.spacingSmall),
                Text(
                  'المرجع: ${entry.reference}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(JournalEntry entry) {
    String status;
    Color color;
    
    if (entry.isPosted) {
      status = 'مرحل';
      color = Colors.green;
    } else if (entry.isBalanced) {
      status = 'متوازن';
      color = Colors.blue;
    } else {
      status = 'غير متوازن';
      color = Colors.red;
    }
    
    return _buildInfoChip(
      entry.isPosted ? Icons.check_circle : 
      (entry.isBalanced ? Icons.balance : Icons.warning),
      status,
      color,
    );
  }

  Color _getStatusColor(JournalEntry entry) {
    if (entry.isPosted) {
      return Colors.green;
    } else if (entry.isBalanced) {
      return Colors.blue;
    } else {
      return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
