import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🔮 نظام التنبؤات المالية بالذكاء الاصطناعي
/// AI Financial Predictions System
///
/// هذا الملف يحتوي على نظام تنبؤات مالية بالذكاء الاصطناعي لا مثيل له في التاريخ
/// This file contains unprecedented AI financial predictions system in history

/// 🌟 لوحة التنبؤات المالية الذكية
/// Smart Financial Predictions Dashboard
class AIFinancialPredictionsDashboard extends StatefulWidget {
  const AIFinancialPredictionsDashboard({super.key});

  @override
  State<AIFinancialPredictionsDashboard> createState() =>
      _AIFinancialPredictionsDashboardState();
}

class _AIFinancialPredictionsDashboardState
    extends State<AIFinancialPredictionsDashboard>
    with TickerProviderStateMixin {
  late AnimationController _brainController;
  late AnimationController _dataController;
  late AnimationController _predictionController;
  late Animation<double> _brainAnimation;
  late Animation<double> _dataAnimation;
  late Animation<double> _predictionAnimation;

  @override
  void initState() {
    super.initState();

    _brainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _dataController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _predictionController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _brainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _brainController, curve: Curves.easeInOut),
    );

    _dataAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _dataController, curve: Curves.easeInOut),
    );

    _predictionAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _predictionController, curve: Curves.easeInOut),
    );

    _brainController.repeat();
    _dataController.repeat();
    _predictionController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _brainController.dispose();
    _dataController.dispose();
    _predictionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _brainAnimation,
        _dataAnimation,
        _predictionAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.0 + (_predictionAnimation.value * 0.5),
          child: QuantumEnergyEffect(
            intensity: 1.8 + (_brainAnimation.value * 0.7),
            primaryColor: const Color(0xFFFF6B35),
            secondaryColor: const Color(0xFFFF8C42),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFBF360C).withValues(alpha: 0.9),
                    const Color(0xFFD84315).withValues(alpha: 0.8),
                    const Color(0xFFFF5722).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFFFF6B35).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFF6B35).withValues(alpha: 0.5),
                    blurRadius: 35,
                    offset: const Offset(0, 18),
                    spreadRadius: 6,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس التنبؤات الذكية
                  Row(
                    children: [
                      Transform.scale(
                        scale: _predictionAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(18),
                          decoration: BoxDecoration(
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFFFF6B35).withValues(alpha: 0.9),
                                const Color(0xFFFF8C42).withValues(alpha: 0.6),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.5),
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.psychology_rounded,
                            color: Colors.white,
                            size: 36,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🔮 التنبؤات الذكية',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'تحليل مستقبلي بالذكاء الاصطناعي',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      // مؤشر دقة التنبؤ
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: const Color(
                              0xFF4CAF50,
                            ).withValues(alpha: 0.5),
                          ),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'دقة التنبؤ',
                              style: TextStyle(
                                color: Color(0xFF4CAF50),
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '96.8%',
                              style: TextStyle(
                                color: const Color(0xFF4CAF50),
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // عرض الدماغ الاصطناعي
                  _buildAIBrainVisualization(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // التنبؤات المالية
                  _buildFinancialPredictions(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء تصور الدماغ الاصطناعي
  Widget _buildAIBrainVisualization() {
    return Container(
      height: 150,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: const Color(0xFFFF6B35).withValues(alpha: 0.4),
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // الخلفية العصبية
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                colors: [
                  const Color(0xFFFF6B35).withValues(alpha: 0.15),
                  const Color(0xFFFF8C42).withValues(alpha: 0.08),
                  Colors.transparent,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
            ),
          ),

          // الشبكة العصبية
          ...List.generate(20, (index) {
            final x = (index % 5) * 60.0 + 50;
            final y = (index ~/ 5) * 30.0 + 30;
            final pulsePhase =
                (index * 0.3) + (_brainAnimation.value * 2 * math.pi);
            final intensity = 0.5 + (math.sin(pulsePhase) * 0.5);

            return Positioned(
              left: x,
              top: y,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF6B35).withValues(alpha: intensity),
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(
                        0xFFFF6B35,
                      ).withValues(alpha: intensity * 0.5),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
            );
          }),

          // الاتصالات العصبية
          ...List.generate(15, (index) {
            final startX = 50.0 + (index % 4) * 60;
            final startY = 30.0 + (index ~/ 4) * 30;
            final endX = startX + 60;
            final endY = startY + (index.isEven ? 30 : -30);
            final opacity = 0.3 + (_dataAnimation.value * 0.4);

            return Positioned(
              left: startX,
              top: startY,
              child: CustomPaint(
                size: Size(endX - startX, (endY - startY).abs()),
                painter: NeuralConnectionPainter(
                  color: const Color(0xFFFF6B35).withValues(alpha: opacity),
                  progress: _dataAnimation.value,
                ),
              ),
            );
          }),

          // مركز المعالجة
          Center(
            child: Transform.scale(
              scale: _predictionAnimation.value,
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    colors: [
                      const Color(0xFFFF6B35).withValues(alpha: 0.9),
                      const Color(0xFFFF8C42).withValues(alpha: 0.6),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.6),
                    width: 2,
                  ),
                ),
                child: const Icon(
                  Icons.memory_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التنبؤات المالية
  Widget _buildFinancialPredictions() {
    final predictions = [
      FinancialPrediction(
        title: 'الإيرادات المتوقعة',
        currentValue: '₹ 125,000',
        predictedValue: '₹ 142,500',
        change: '+14%',
        confidence: 94.2,
        trend: TrendDirection.up,
        timeframe: 'الشهر القادم',
      ),
      FinancialPrediction(
        title: 'المصروفات المتوقعة',
        currentValue: '₹ 85,000',
        predictedValue: '₹ 78,200',
        change: '-8%',
        confidence: 91.7,
        trend: TrendDirection.down,
        timeframe: 'الشهر القادم',
      ),
      FinancialPrediction(
        title: 'صافي الربح المتوقع',
        currentValue: '₹ 40,000',
        predictedValue: '₹ 64,300',
        change: '+61%',
        confidence: 96.8,
        trend: TrendDirection.up,
        timeframe: 'الشهر القادم',
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📈 التنبؤات المالية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        ...predictions.map((prediction) => _buildPredictionCard(prediction)),
      ],
    );
  }

  Widget _buildPredictionCard(FinancialPrediction prediction) {
    final trendColor = prediction.trend == TrendDirection.up
        ? const Color(0xFF4CAF50)
        : const Color(0xFFF44336);

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: trendColor.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                prediction.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: trendColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  prediction.change,
                  style: TextStyle(
                    color: trendColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الحالي: ${prediction.currentValue}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      'المتوقع: ${prediction.predictedValue}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'الثقة: ${prediction.confidence.toStringAsFixed(1)}%',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 11,
                    ),
                  ),
                  Text(
                    prediction.timeframe,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.6),
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          LinearProgressIndicator(
            value: prediction.confidence / 100,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(trendColor),
          ),
        ],
      ),
    );
  }
}

/// رسام الاتصالات العصبية
class NeuralConnectionPainter extends CustomPainter {
  final Color color;
  final double progress;

  NeuralConnectionPainter({required this.color, required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    path.moveTo(0, 0);
    path.quadraticBezierTo(
      size.width / 2,
      size.height / 2,
      size.width,
      size.height,
    );

    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      final extractPath = pathMetric.extractPath(
        0,
        pathMetric.length * progress,
      );
      canvas.drawPath(extractPath, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// نموذج التنبؤ المالي
class FinancialPrediction {
  final String title;
  final String currentValue;
  final String predictedValue;
  final String change;
  final double confidence;
  final TrendDirection trend;
  final String timeframe;

  FinancialPrediction({
    required this.title,
    required this.currentValue,
    required this.predictedValue,
    required this.change,
    required this.confidence,
    required this.trend,
    required this.timeframe,
  });
}

/// اتجاه الاتجاه
enum TrendDirection { up, down, stable }
