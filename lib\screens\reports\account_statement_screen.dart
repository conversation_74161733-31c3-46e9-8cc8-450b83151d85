import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../theme/app_theme.dart';
import '../../models/financial_reports.dart';
import '../../models/account.dart';
import '../../services/financial_reports_service.dart';
import '../../services/account_service.dart';

/// شاشة كشف الحساب
class AccountStatementScreen extends StatefulWidget {
  const AccountStatementScreen({super.key});

  @override
  State<AccountStatementScreen> createState() => _AccountStatementScreenState();
}

class _AccountStatementScreenState extends State<AccountStatementScreen>
    with TickerProviderStateMixin {
  final FinancialReportsService _reportsService = FinancialReportsService();
  final AccountService _accountService = AccountService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  AccountStatementReport? _report;
  List<Account> _accounts = [];
  Account? _selectedAccount;
  bool _isLoading = false;
  bool _isLoadingAccounts = false;
  String? _error;
  DateTime _startDate = DateTime(DateTime.now().year, 1, 1);
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadAccounts();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _loadAccounts() async {
    try {
      setState(() {
        _isLoadingAccounts = true;
      });

      final result = await _accountService.getAllAccounts();

      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _accounts = result.data ?? [];
            _isLoadingAccounts = false;
          });
        } else {
          setState(() {
            _error = result.error;
            _isLoadingAccounts = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل الحسابات: ${e.toString()}';
          _isLoadingAccounts = false;
        });
      }
    }
  }

  Future<void> _loadAccountStatement() async {
    if (_selectedAccount == null) return;

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final result = await _reportsService.generateAccountStatement(
        _selectedAccount!.id!,
        startDate: _startDate,
        endDate: _endDate,
      );

      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _report = result.data;
            _isLoading = false;
          });
        } else {
          setState(() {
            _error = result.error;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل كشف الحساب: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      if (_selectedAccount != null) {
        _loadAccountStatement();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          '📋 كشف الحساب',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
            tooltip: 'اختيار الفترة',
          ),
          if (_selectedAccount != null)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadAccountStatement,
              tooltip: 'تحديث',
            ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(opacity: _fadeAnimation, child: _buildBody());
        },
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // شريط اختيار الحساب والفترة
        _buildSelectionHeader(),

        // المحتوى الرئيسي
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildSelectionHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple,
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // اختيار الحساب
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<Account>(
                value: _selectedAccount,
                hint: const Text(
                  'اختر الحساب',
                  style: TextStyle(color: Colors.white70),
                ),
                isExpanded: true,
                dropdownColor: Colors.purple[700],
                style: const TextStyle(color: Colors.white),
                items: _accounts.map((account) {
                  return DropdownMenuItem<Account>(
                    value: account,
                    child: Text(
                      '${account.code} - ${account.name}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  );
                }).toList(),
                onChanged: _isLoadingAccounts
                    ? null
                    : (Account? account) {
                        setState(() {
                          _selectedAccount = account;
                          _report = null;
                        });
                        if (account != null) {
                          _loadAccountStatement();
                        }
                      },
              ),
            ),
          ),

          const SizedBox(height: 12),

          // عرض الفترة المحددة
          Text(
            'الفترة: من ${_formatDate(_startDate)} إلى ${_formatDate(_endDate)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),

          // معلومات الحساب المحدد
          if (_selectedAccount != null && _report != null) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildHeaderStat(
                  'الرصيد الافتتاحي',
                  _report!.openingBalance,
                  Icons.play_arrow,
                ),
                _buildHeaderStat(
                  'إجمالي المدين',
                  _report!.totalDebits,
                  Icons.add_circle_outline,
                ),
                _buildHeaderStat(
                  'إجمالي الدائن',
                  _report!.totalCredits,
                  Icons.remove_circle_outline,
                ),
                _buildHeaderStat(
                  'الرصيد الختامي',
                  _report!.closingBalance,
                  Icons.stop,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, double value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 16),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 10),
        ),
        Text(
          value.toStringAsFixed(2),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoadingAccounts) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.purple),
            SizedBox(height: 16),
            Text('جاري تحميل الحسابات...', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    if (_selectedAccount == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_circle_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'يرجى اختيار حساب لعرض كشف الحساب',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.purple),
            SizedBox(height: 16),
            Text('جاري إنشاء كشف الحساب...', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _selectedAccount != null
                  ? _loadAccountStatement
                  : _loadAccounts,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_report == null || _report!.transactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد حركات للحساب في الفترة المحددة',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    return _buildAccountStatementTable();
  }

  Widget _buildAccountStatementTable() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس الجدول
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.purple.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                  flex: 2,
                  child: Text(
                    'التاريخ',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                const Expanded(
                  flex: 3,
                  child: Text(
                    'البيان',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    'مدين',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    'دائن',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    'الرصيد',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // بيانات الجدول
          Expanded(
            child: AnimationLimiter(
              child: ListView.builder(
                itemCount: _report!.transactions.length,
                itemBuilder: (context, index) {
                  final transaction = _report!.transactions[index];
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 400),
                    child: SlideAnimation(
                      verticalOffset: 30.0,
                      child: FadeInAnimation(
                        child: _buildTransactionRow(transaction, index),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionRow(
    AccountStatementTransaction transaction,
    int index,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: index.isEven ? Colors.grey[50] : Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(transaction.date),
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              transaction.description,
              style: const TextStyle(fontSize: 12),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              transaction.debitAmount > 0
                  ? transaction.debitAmount.toStringAsFixed(2)
                  : '-',
              style: TextStyle(
                fontSize: 12,
                color: transaction.debitAmount > 0
                    ? Colors.green[700]
                    : Colors.grey,
                fontWeight: transaction.debitAmount > 0
                    ? FontWeight.w600
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              transaction.creditAmount > 0
                  ? transaction.creditAmount.toStringAsFixed(2)
                  : '-',
              style: TextStyle(
                fontSize: 12,
                color: transaction.creditAmount > 0
                    ? Colors.red[700]
                    : Colors.grey,
                fontWeight: transaction.creditAmount > 0
                    ? FontWeight.w600
                    : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              transaction.balance.toStringAsFixed(2),
              style: TextStyle(
                fontSize: 12,
                color: transaction.balance >= 0
                    ? Colors.green[700]
                    : Colors.red[700],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
