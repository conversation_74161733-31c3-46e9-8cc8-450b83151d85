/// شاشة إدارة مراحل المشروع
/// Project Phases Management Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/project.dart';
import '../../services/project_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/quantum_loading.dart';
import '../../theme/app_theme.dart';

class ProjectPhasesScreen extends StatefulWidget {
  final Project project;

  const ProjectPhasesScreen({super.key, required this.project});

  @override
  State<ProjectPhasesScreen> createState() => _ProjectPhasesScreenState();
}

class _ProjectPhasesScreenState extends State<ProjectPhasesScreen> {
  final ProjectService _projectService = ProjectService();
  List<ProjectPhase> _phases = [];
  List<ProjectPhase> _filteredPhases = [];
  bool _isLoading = false;
  String _searchTerm = '';
  ProjectPhaseStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _loadPhases();
  }

  void _filterPhases() {
    _filteredPhases = _phases.where((phase) {
      final matchesSearch =
          _searchTerm.isEmpty ||
          phase.name.toLowerCase().contains(_searchTerm.toLowerCase()) ||
          (phase.description?.toLowerCase().contains(
                _searchTerm.toLowerCase(),
              ) ??
              false);

      final matchesStatus =
          _selectedStatus == null || phase.status == _selectedStatus;

      return matchesSearch && matchesStatus;
    }).toList();
  }

  Future<void> _loadPhases() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final phases = await _projectService.projectDao.getProjectPhases(
        widget.project.id!,
      );
      setState(() {
        _phases = phases;
        _filterPhases();
      });
    } catch (e) {
      _showErrorDialog('خطأ في تحميل المراحل: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('مراحل المشروع - ${widget.project.name}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: QuantumLoading())
          : Column(
              children: [
                // شريط البحث والفلترة
                QuantumCard(
                  margin: const EdgeInsets.all(16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: QuantumTextField(
                                labelText: 'البحث في المراحل',
                                prefixIcon: Icons.search,
                                onChanged: (value) {
                                  setState(() {
                                    _searchTerm = value;
                                    _filterPhases();
                                  });
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: QuantumDropdown<ProjectPhaseStatus>(
                                labelText: 'الحالة',
                                value: _selectedStatus,
                                items: [
                                  const DropdownMenuItem(
                                    value: null,
                                    child: Text('جميع الحالات'),
                                  ),
                                  ...ProjectPhaseStatus.values.map(
                                    (status) => DropdownMenuItem(
                                      value: status,
                                      child: Text(status.nameAr),
                                    ),
                                  ),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _selectedStatus = value;
                                    _filterPhases();
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            QuantumButton(
                              onPressed: () => _showAddPhaseDialog(),
                              text: 'إضافة مرحلة',
                              icon: Icons.add,
                            ),
                            const Spacer(),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  'إجمالي الميزانية: ${_getTotalBudget().toStringAsFixed(2)} ر.س',
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: AppTheme.primaryColor,
                                      ),
                                ),
                                Text(
                                  'عدد المراحل: ${_filteredPhases.length}',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // قائمة المراحل
                Expanded(
                  child: _filteredPhases.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.timeline,
                                size: 64,
                                color: AppTheme.textSecondaryColor,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _phases.isEmpty
                                    ? 'لا توجد مراحل للمشروع'
                                    : 'لا توجد مراحل تطابق البحث',
                                style: Theme.of(context).textTheme.bodyLarge
                                    ?.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _filteredPhases.length,
                          itemBuilder: (context, index) {
                            final phase = _filteredPhases[index];
                            return _buildPhaseCard(phase);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  double _getTotalBudget() {
    return _filteredPhases.fold(0.0, (sum, phase) => sum + phase.budgetAmount);
  }

  Widget _buildPhaseCard(ProjectPhase phase) {
    return QuantumCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    phase.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildPhaseStatusChip(phase.status),
              ],
            ),

            if (phase.description != null) ...[
              const SizedBox(height: 8),
              Text(
                phase.description!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],

            const SizedBox(height: 12),

            // معلومات المرحلة
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ البداية',
                    _formatDate(phase.startDate),
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ النهاية',
                    phase.endDate != null
                        ? _formatDate(phase.endDate!)
                        : 'غير محدد',
                    Icons.event,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'الميزانية',
                    '${phase.budgetAmount.toStringAsFixed(2)} ر.س',
                    Icons.attach_money,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'التكلفة الفعلية',
                    '${phase.actualCost.toStringAsFixed(2)} ر.س',
                    Icons.receipt,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // شريط التقدم
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      '${phase.completionPercentage.toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: phase.completionPercentage / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProgressColor(phase.completionPercentage),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // أزرار العمليات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () => _showEditPhaseDialog(phase),
                  icon: const Icon(Icons.edit),
                  tooltip: 'تعديل',
                ),
                IconButton(
                  onPressed: () => _deletePhase(phase),
                  icon: const Icon(Icons.delete),
                  color: Colors.red,
                  tooltip: 'حذف',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppTheme.textSecondaryColor),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              Text(
                value,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPhaseStatusChip(ProjectPhaseStatus status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case ProjectPhaseStatus.notStarted:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        break;
      case ProjectPhaseStatus.inProgress:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue;
        break;
      case ProjectPhaseStatus.completed:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        break;
      case ProjectPhaseStatus.delayed:
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.nameAr,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 80) return Colors.green;
    if (percentage >= 50) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _showAddPhaseDialog() async {
    final result = await showDialog<ProjectPhase>(
      context: context,
      builder: (context) => AddPhaseDialog(projectId: widget.project.id!),
    );

    if (result != null) {
      await _addPhase(result);
    }
  }

  Future<void> _showEditPhaseDialog(ProjectPhase phase) async {
    final result = await showDialog<ProjectPhase>(
      context: context,
      builder: (context) =>
          AddPhaseDialog(projectId: widget.project.id!, phase: phase),
    );

    if (result != null) {
      await _updatePhase(result);
    }
  }

  Future<void> _addPhase(ProjectPhase phase) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _projectService.createProjectPhase(phase);
      if (result.isSuccess) {
        await _loadPhases();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة المرحلة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _showErrorDialog(result.error ?? 'فشل في إضافة المرحلة');
      }
    } catch (e) {
      _showErrorDialog('خطأ في إضافة المرحلة: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updatePhase(ProjectPhase phase) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _projectService.updateProjectPhase(phase);
      if (result.isSuccess) {
        await _loadPhases();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث المرحلة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _showErrorDialog(result.error ?? 'فشل في تحديث المرحلة');
      }
    } catch (e) {
      _showErrorDialog('خطأ في تحديث المرحلة: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deletePhase(ProjectPhase phase) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المرحلة "${phase.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final result = await _projectService.deleteProjectPhase(phase.id!);
        if (result.isSuccess) {
          await _loadPhases();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف المرحلة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          _showErrorDialog(result.error ?? 'فشل في حذف المرحلة');
        }
      } catch (e) {
        _showErrorDialog('خطأ في حذف المرحلة: ${e.toString()}');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

/// حوار إضافة/تعديل مرحلة المشروع
class AddPhaseDialog extends StatefulWidget {
  final int projectId;
  final ProjectPhase? phase;

  const AddPhaseDialog({super.key, required this.projectId, this.phase});

  @override
  State<AddPhaseDialog> createState() => _AddPhaseDialogState();
}

class _AddPhaseDialogState extends State<AddPhaseDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _budgetController = TextEditingController();
  final _weightController = TextEditingController();
  final _completionController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  ProjectPhaseStatus _selectedStatus = ProjectPhaseStatus.notStarted;

  bool get _isEditing => widget.phase != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _initializeForEditing();
    } else {
      _budgetController.text = '0.0';
      _weightController.text = '1.0';
      _completionController.text = '0.0';
    }
  }

  void _initializeForEditing() {
    final phase = widget.phase!;
    _nameController.text = phase.name;
    _descriptionController.text = phase.description ?? '';
    _budgetController.text = phase.budgetAmount.toString();
    _weightController.text = phase.weight.toString();
    _completionController.text = phase.completionPercentage.toString();
    _notesController.text = phase.notes ?? '';
    _startDate = phase.startDate;
    _endDate = phase.endDate;
    _selectedStatus = phase.status;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _budgetController.dispose();
    _weightController.dispose();
    _completionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_isEditing ? 'تعديل المرحلة' : 'إضافة مرحلة جديدة'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                QuantumTextField(
                  controller: _nameController,
                  labelText: 'اسم المرحلة *',
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم المرحلة';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                QuantumTextField(
                  controller: _descriptionController,
                  labelText: 'الوصف',
                  maxLines: 3,
                ),

                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: QuantumTextField(
                        controller: _budgetController,
                        labelText: 'الميزانية *',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال الميزانية';
                          }
                          if (double.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: QuantumTextField(
                        controller: _weightController,
                        labelText: 'الوزن *',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال الوزن';
                          }
                          final weight = double.tryParse(value);
                          if (weight == null || weight <= 0) {
                            return 'يرجى إدخال وزن صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: QuantumTextField(
                        controller: _completionController,
                        labelText: 'نسبة الإنجاز (%)',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.trim().isNotEmpty) {
                            final completion = double.tryParse(value);
                            if (completion == null ||
                                completion < 0 ||
                                completion > 100) {
                              return 'يرجى إدخال نسبة بين 0 و 100';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: QuantumDropdown<ProjectPhaseStatus>(
                        labelText: 'الحالة',
                        value: _selectedStatus,
                        items: ProjectPhaseStatus.values
                            .map(
                              (status) => DropdownMenuItem(
                                value: status,
                                child: Text(status.nameAr),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // تواريخ المرحلة
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectStartDate(),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تاريخ البداية *',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectEndDate(),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تاريخ النهاية',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _endDate != null
                                    ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                                    : 'غير محدد',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                QuantumTextField(
                  controller: _notesController,
                  labelText: 'ملاحظات',
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        QuantumButton(
          onPressed: _savePhase,
          text: _isEditing ? 'تحديث' : 'إضافة',
        ),
      ],
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 30)),
      firstDate: _startDate,
      lastDate: DateTime(2030),
    );

    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  void _savePhase() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final phase = ProjectPhase(
        id: _isEditing ? widget.phase!.id : null,
        projectId: widget.projectId,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        startDate: _startDate,
        endDate: _endDate,
        budgetAmount: double.parse(_budgetController.text),
        weight: double.parse(_weightController.text),
        completionPercentage: double.parse(
          _completionController.text.isEmpty ? '0' : _completionController.text,
        ),
        status: _selectedStatus,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      Navigator.of(context).pop(phase);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في البيانات: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
