import '../models/account.dart';
import '../models/journal_entry.dart';
import '../models/financial_reports.dart';
import '../database/account_dao.dart';
import '../database/journal_entry_dao.dart';
import '../database/company_settings_dao.dart';
import 'account_service.dart';

class FinancialReportsService {
  final AccountDao _accountDao = AccountDao();
  final JournalEntryDao _journalEntryDao = JournalEntryDao();
  final CompanySettingsDao _settingsDao = CompanySettingsDao();

  // Generate Trial Balance Report
  Future<Result<TrialBalanceReport>> generateTrialBalance({
    DateTime? asOfDate,
  }) async {
    try {
      DateTime reportDate = asOfDate ?? DateTime.now();

      // Get all accounts with balances
      List<Account> accounts = await _accountDao.getAccountsWithBalance();

      List<TrialBalanceItem> items = [];
      double totalDebits = 0.0;
      double totalCredits = 0.0;

      for (Account account in accounts) {
        double balance = account.balance;
        double debitBalance = 0.0;
        double creditBalance = 0.0;

        // Determine if balance should be shown as debit or credit
        if (account.accountType.isDebitNormal) {
          if (balance >= 0) {
            debitBalance = balance;
          } else {
            creditBalance = -balance;
          }
        } else {
          if (balance >= 0) {
            creditBalance = balance;
          } else {
            debitBalance = -balance;
          }
        }

        if (debitBalance != 0 || creditBalance != 0) {
          items.add(
            TrialBalanceItem(
              accountCode: account.code,
              accountName: account.name,
              debitBalance: debitBalance,
              creditBalance: creditBalance,
            ),
          );

          totalDebits += debitBalance;
          totalCredits += creditBalance;
        }
      }

      // Sort by account code
      items.sort((a, b) => a.accountCode.compareTo(b.accountCode));

      TrialBalanceReport report = TrialBalanceReport(
        reportDate: reportDate,
        items: items,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
        isBalanced: (totalDebits - totalCredits).abs() < 0.01,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء ميزان المراجعة: ${e.toString()}');
    }
  }

  // Generate Balance Sheet Report
  Future<Result<BalanceSheetReport>> generateBalanceSheet({
    DateTime? asOfDate,
  }) async {
    try {
      DateTime reportDate = asOfDate ?? DateTime.now();

      // Get all accounts
      List<Account> allAccounts = await _accountDao.getAllAccounts();

      // Get balance summary by account type
      Map<AccountType, double> balanceSummary = await _accountDao
          .getBalanceSummaryByType();

      // Get account balances (use the balance property from Account model)
      Map<int, double> accountBalances = {};
      for (Account account in allAccounts) {
        accountBalances[account.id!] = account.balance;
      }

      // Filter accounts by type
      List<Account> assetAccounts = allAccounts
          .where((a) => a.accountType == AccountType.asset)
          .toList();
      List<Account> liabilityAccounts = allAccounts
          .where((a) => a.accountType == AccountType.liability)
          .toList();

      // Assets
      double currentAssets = balanceSummary[AccountType.asset] ?? 0.0;
      double totalAssets = currentAssets;

      // Liabilities
      double currentLiabilities = balanceSummary[AccountType.liability] ?? 0.0;
      double totalLiabilities = currentLiabilities;

      // Equity
      double equity = balanceSummary[AccountType.equity] ?? 0.0;

      // Calculate retained earnings (Revenue - Expenses)
      double revenue = balanceSummary[AccountType.revenue] ?? 0.0;
      double expenses = balanceSummary[AccountType.expense] ?? 0.0;
      double netIncome = revenue - expenses;
      double totalEquity = equity + netIncome;

      // Create asset items
      List<BalanceSheetItem> assetItems = [];
      for (Account account in assetAccounts) {
        double balance = accountBalances[account.id] ?? 0.0;
        if (balance != 0) {
          assetItems.add(
            BalanceSheetItem(
              accountCode: account.code,
              accountName: account.name,
              amount: balance,
            ),
          );
        }
      }

      // Create liability items
      List<BalanceSheetItem> liabilityItems = [];
      for (Account account in liabilityAccounts) {
        double balance = accountBalances[account.id] ?? 0.0;
        if (balance != 0) {
          liabilityItems.add(
            BalanceSheetItem(
              accountCode: account.code,
              accountName: account.name,
              amount: balance,
            ),
          );
        }
      }

      BalanceSheetReport report = BalanceSheetReport(
        reportDate: reportDate,
        currentAssets: currentAssets,
        totalAssets: totalAssets,
        currentLiabilities: currentLiabilities,
        totalLiabilities: totalLiabilities,
        equity: equity,
        retainedEarnings: netIncome,
        totalEquity: totalEquity,
        isBalanced:
            (totalAssets - (totalLiabilities + totalEquity)).abs() < 0.01,
        assets: assetItems,
        liabilities: liabilityItems,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء الميزانية العمومية: ${e.toString()}');
    }
  }

  // Generate Income Statement Report
  Future<Result<IncomeStatementReport>> generateIncomeStatement({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Default to current fiscal year if dates not provided
      if (startDate == null || endDate == null) {
        Map<String, DateTime> fiscalYear = await _settingsDao
            .getCurrentFiscalYear();
        startDate ??= fiscalYear['start']!;
        endDate ??= fiscalYear['end']!;
      }

      // Get revenue and expense accounts
      List<Account> revenueAccounts = await _accountDao.getAccountsByType(
        AccountType.revenue,
      );
      List<Account> expenseAccounts = await _accountDao.getAccountsByType(
        AccountType.expense,
      );

      double totalRevenue = 0.0;
      double totalExpenses = 0.0;

      // Calculate total revenue
      for (Account account in revenueAccounts) {
        totalRevenue += account.balance;
      }

      // Calculate total expenses
      for (Account account in expenseAccounts) {
        totalExpenses += account.balance;
      }

      double netIncome = totalRevenue - totalExpenses;

      // Create revenue items
      List<IncomeStatementItem> revenueItems = [];
      for (Account account in revenueAccounts) {
        if (account.balance != 0) {
          revenueItems.add(
            IncomeStatementItem(
              accountCode: account.code,
              accountName: account.name,
              amount: account.balance,
            ),
          );
        }
      }

      // Create expense items
      List<IncomeStatementItem> expenseItems = [];
      for (Account account in expenseAccounts) {
        if (account.balance != 0) {
          expenseItems.add(
            IncomeStatementItem(
              accountCode: account.code,
              accountName: account.name,
              amount: account.balance,
            ),
          );
        }
      }

      IncomeStatementReport report = IncomeStatementReport(
        startDate: startDate,
        endDate: endDate,
        totalRevenue: totalRevenue,
        totalExpenses: totalExpenses,
        netIncome: netIncome,
        revenueAccounts: revenueAccounts,
        expenseAccounts: expenseAccounts,
        revenueItems: revenueItems,
        expenseItems: expenseItems,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء قائمة الدخل: ${e.toString()}');
    }
  }

  // Generate Account Statement
  Future<Result<AccountStatementReport>> generateAccountStatement(
    int accountId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Get account details
      Account? account = await _accountDao.getAccountById(accountId);
      if (account == null) {
        return Result.error('الحساب غير موجود');
      }

      // Default date range to current fiscal year
      if (startDate == null || endDate == null) {
        Map<String, DateTime> fiscalYear = await _settingsDao
            .getCurrentFiscalYear();
        startDate ??= fiscalYear['start']!;
        endDate ??= fiscalYear['end']!;
      }

      // Get journal entries for this account
      // SQL query would be:
      // SELECT je.date, je.entry_number, je.description, je.reference,
      //        jel.debit_amount, jel.credit_amount
      // FROM journal_entries je
      // INNER JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
      // WHERE jel.account_id = ? AND je.is_posted = 1
      //   AND je.date >= ? AND je.date <= ?
      // ORDER BY je.date ASC, je.entry_number ASC

      // This would need to be implemented in the DAO layer
      // For now, return empty list
      List<Map<String, dynamic>> results = [];

      List<AccountStatementItem> items = [];
      List<AccountStatementTransaction> transactions = [];
      double runningBalance = 0.0; // This should start with opening balance
      double totalDebits = 0.0;
      double totalCredits = 0.0;

      for (Map<String, dynamic> row in results) {
        double debitAmount = (row['debit_amount'] as num?)?.toDouble() ?? 0.0;
        double creditAmount = (row['credit_amount'] as num?)?.toDouble() ?? 0.0;

        totalDebits += debitAmount;
        totalCredits += creditAmount;

        // Update running balance based on account type
        if (account.accountType.isDebitNormal) {
          runningBalance += debitAmount - creditAmount;
        } else {
          runningBalance += creditAmount - debitAmount;
        }

        items.add(
          AccountStatementItem(
            date: DateTime.parse(row['date'] as String),
            entryNumber: row['entry_number'] as String,
            description: row['description'] as String,
            reference: row['reference'] as String?,
            debitAmount: debitAmount,
            creditAmount: creditAmount,
            balance: runningBalance,
          ),
        );

        transactions.add(
          AccountStatementTransaction(
            date: DateTime.parse(row['date'] as String),
            description: row['description'] as String,
            debitAmount: debitAmount,
            creditAmount: creditAmount,
            balance: runningBalance,
          ),
        );
      }

      AccountStatementReport report = AccountStatementReport(
        account: account,
        startDate: startDate,
        endDate: endDate,
        openingBalance: 0.0, // Should calculate actual opening balance
        closingBalance: runningBalance,
        items: items,
        transactions: transactions,
        totalDebits: totalDebits,
        totalCredits: totalCredits,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء كشف الحساب: ${e.toString()}');
    }
  }

  // Generate General Ledger Report
  Future<Result<GeneralLedgerReport>> generateGeneralLedger({
    DateTime? startDate,
    DateTime? endDate,
    List<int>? accountIds,
  }) async {
    try {
      // Default date range to current fiscal year
      if (startDate == null || endDate == null) {
        Map<String, DateTime> fiscalYear = await _settingsDao
            .getCurrentFiscalYear();
        startDate ??= fiscalYear['start']!;
        endDate ??= fiscalYear['end']!;
      }

      // Get accounts to include
      List<Account> accounts;
      if (accountIds != null && accountIds.isNotEmpty) {
        accounts = [];
        for (int id in accountIds) {
          Account? account = await _accountDao.getAccountById(id);
          if (account != null) {
            accounts.add(account);
          }
        }
      } else {
        accounts = await _accountDao.getActiveAccounts();
      }

      List<AccountStatementReport> accountStatements = [];

      for (Account account in accounts) {
        Result<AccountStatementReport> statementResult =
            await generateAccountStatement(
              account.id!,
              startDate: startDate,
              endDate: endDate,
            );

        if (statementResult.isSuccess) {
          accountStatements.add(statementResult.data!);
        }
      }

      GeneralLedgerReport report = GeneralLedgerReport(
        startDate: startDate,
        endDate: endDate,
        accountStatements: accountStatements,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء دفتر الأستاذ العام: ${e.toString()}');
    }
  }

  // Get financial summary for dashboard
  Future<Result<FinancialSummary>> getFinancialSummary() async {
    try {
      Map<AccountType, double> balanceSummary = await _accountDao
          .getBalanceSummaryByType();
      Map<String, dynamic> journalStats = await _journalEntryDao
          .getJournalEntryStatistics();

      double totalAssets = balanceSummary[AccountType.asset] ?? 0.0;
      double totalLiabilities = balanceSummary[AccountType.liability] ?? 0.0;
      double totalEquity = balanceSummary[AccountType.equity] ?? 0.0;
      double totalRevenue = balanceSummary[AccountType.revenue] ?? 0.0;
      double totalExpenses = balanceSummary[AccountType.expense] ?? 0.0;
      double netIncome = totalRevenue - totalExpenses;

      FinancialSummary summary = FinancialSummary(
        totalAssets: totalAssets,
        totalLiabilities: totalLiabilities,
        totalEquity: totalEquity + netIncome,
        totalRevenue: totalRevenue,
        totalExpenses: totalExpenses,
        netIncome: netIncome,
        totalJournalEntries: journalStats['total'] as int,
        postedEntries: journalStats['posted'] as int,
        unpostedEntries:
            (journalStats['total'] as int) - (journalStats['posted'] as int),
      );

      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب الملخص المالي: ${e.toString()}');
    }
  }

  // ==================== التقارير المالية المتقدمة ====================

  // Generate Cash Flow Statement
  Future<Result<CashFlowReport>> generateCashFlowStatement({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Default to current fiscal year if dates not provided
      if (startDate == null || endDate == null) {
        Map<String, DateTime> fiscalYear = await _settingsDao
            .getCurrentFiscalYear();
        startDate ??= fiscalYear['start']!;
        endDate ??= fiscalYear['end']!;
      }

      // Get net income from income statement
      Result<IncomeStatementReport> incomeResult =
          await generateIncomeStatement(startDate: startDate, endDate: endDate);

      if (!incomeResult.isSuccess) {
        return Result.error('خطأ في جلب قائمة الدخل: ${incomeResult.error}');
      }

      double netIncome = incomeResult.data!.netIncome;

      // Calculate operating activities
      double depreciation = await _calculateDepreciation(startDate, endDate);
      double arChange = await _calculateAccountsReceivableChange(
        startDate,
        endDate,
      );
      double inventoryChange = await _calculateInventoryChange(
        startDate,
        endDate,
      );
      double apChange = await _calculateAccountsPayableChange(
        startDate,
        endDate,
      );

      List<CashFlowItem> operatingItems = [
        CashFlowItem(
          description: 'صافي الدخل',
          amount: netIncome,
          category: 'operating',
          date: endDate,
        ),
        CashFlowItem(
          description: 'الاستهلاك والإطفاء',
          amount: depreciation,
          category: 'operating',
          date: endDate,
        ),
        CashFlowItem(
          description: 'تغيير في الذمم المدينة',
          amount: -arChange,
          category: 'operating',
          date: endDate,
        ),
        CashFlowItem(
          description: 'تغيير في المخزون',
          amount: -inventoryChange,
          category: 'operating',
          date: endDate,
        ),
        CashFlowItem(
          description: 'تغيير في الذمم الدائنة',
          amount: apChange,
          category: 'operating',
          date: endDate,
        ),
      ];

      CashFlowOperating operatingActivities = CashFlowOperating(
        netIncome: netIncome,
        depreciation: depreciation,
        accountsReceivableChange: arChange,
        inventoryChange: inventoryChange,
        accountsPayableChange: apChange,
        otherOperatingChanges: 0.0,
        items: operatingItems,
      );

      // Calculate investing activities
      double assetPurchases = await _calculateAssetPurchases(
        startDate,
        endDate,
      );
      double assetSales = await _calculateAssetSales(startDate, endDate);

      List<CashFlowItem> investingItems = [
        if (assetPurchases > 0)
          CashFlowItem(
            description: 'شراء أصول ثابتة',
            amount: -assetPurchases,
            category: 'investing',
            date: endDate,
          ),
        if (assetSales > 0)
          CashFlowItem(
            description: 'بيع أصول ثابتة',
            amount: assetSales,
            category: 'investing',
            date: endDate,
          ),
      ];

      CashFlowInvesting investingActivities = CashFlowInvesting(
        assetPurchases: assetPurchases,
        assetSales: assetSales,
        investmentPurchases: 0.0,
        investmentSales: 0.0,
        items: investingItems,
      );

      // Calculate financing activities
      double loanProceeds = await _calculateLoanProceeds(startDate, endDate);
      double loanRepayments = await _calculateLoanRepayments(
        startDate,
        endDate,
      );

      List<CashFlowItem> financingItems = [
        if (loanProceeds > 0)
          CashFlowItem(
            description: 'قروض جديدة',
            amount: loanProceeds,
            category: 'financing',
            date: endDate,
          ),
        if (loanRepayments > 0)
          CashFlowItem(
            description: 'سداد قروض',
            amount: -loanRepayments,
            category: 'financing',
            date: endDate,
          ),
      ];

      CashFlowFinancing financingActivities = CashFlowFinancing(
        loanProceeds: loanProceeds,
        loanRepayments: loanRepayments,
        equityIssuance: 0.0,
        dividendPayments: 0.0,
        items: financingItems,
      );

      // Calculate cash balances
      double openingCashBalance = await _getCashBalance(startDate);
      double closingCashBalance = await _getCashBalance(endDate);
      double netCashFlow =
          operatingActivities.netOperatingCashFlow +
          investingActivities.netInvestingCashFlow +
          financingActivities.netFinancingCashFlow;

      CashFlowReport report = CashFlowReport(
        startDate: startDate,
        endDate: endDate,
        operatingActivities: operatingActivities,
        investingActivities: investingActivities,
        financingActivities: financingActivities,
        openingCashBalance: openingCashBalance,
        closingCashBalance: closingCashBalance,
        netCashFlow: netCashFlow,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير التدفقات النقدية: ${e.toString()}',
      );
    }
  }

  // Generate Profitability Analysis Report
  Future<Result<ProfitabilityReport>> generateProfitabilityReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Default to current fiscal year if dates not provided
      if (startDate == null || endDate == null) {
        Map<String, DateTime> fiscalYear = await _settingsDao
            .getCurrentFiscalYear();
        startDate ??= fiscalYear['start']!;
        endDate ??= fiscalYear['end']!;
      }

      // Get income statement data
      Result<IncomeStatementReport> incomeResult =
          await generateIncomeStatement(startDate: startDate, endDate: endDate);

      if (!incomeResult.isSuccess) {
        return Result.error('خطأ في جلب قائمة الدخل: ${incomeResult.error}');
      }

      IncomeStatementReport incomeStatement = incomeResult.data!;
      double totalRevenue = incomeStatement.totalRevenue;
      double totalExpenses = incomeStatement.totalExpenses;

      // Calculate cost of goods sold (assuming it's part of expenses)
      double costOfGoodsSold = await _calculateCostOfGoodsSold(
        startDate,
        endDate,
      );
      double operatingExpenses = totalExpenses - costOfGoodsSold;

      double grossProfit = totalRevenue - costOfGoodsSold;
      double operatingProfit = grossProfit - operatingExpenses;
      double netProfit = incomeStatement.netIncome;

      // Create profitability items
      List<ProfitabilityItem> items = [
        ProfitabilityItem(
          category: 'الإيرادات',
          description: 'إجمالي الإيرادات',
          amount: totalRevenue,
          percentage: 100.0,
          type: 'revenue',
        ),
        ProfitabilityItem(
          category: 'التكاليف',
          description: 'تكلفة البضاعة المباعة',
          amount: costOfGoodsSold,
          percentage: totalRevenue == 0
              ? 0
              : (costOfGoodsSold / totalRevenue) * 100,
          type: 'cogs',
        ),
        ProfitabilityItem(
          category: 'الأرباح',
          description: 'إجمالي الربح',
          amount: grossProfit,
          percentage: totalRevenue == 0
              ? 0
              : (grossProfit / totalRevenue) * 100,
          type: 'revenue',
        ),
        ProfitabilityItem(
          category: 'المصروفات',
          description: 'المصروفات التشغيلية',
          amount: operatingExpenses,
          percentage: totalRevenue == 0
              ? 0
              : (operatingExpenses / totalRevenue) * 100,
          type: 'expense',
        ),
        ProfitabilityItem(
          category: 'الأرباح',
          description: 'الربح التشغيلي',
          amount: operatingProfit,
          percentage: totalRevenue == 0
              ? 0
              : (operatingProfit / totalRevenue) * 100,
          type: 'revenue',
        ),
        ProfitabilityItem(
          category: 'الأرباح',
          description: 'صافي الربح',
          amount: netProfit,
          percentage: totalRevenue == 0 ? 0 : (netProfit / totalRevenue) * 100,
          type: 'revenue',
        ),
      ];

      ProfitabilityReport report = ProfitabilityReport(
        startDate: startDate,
        endDate: endDate,
        grossProfit: grossProfit,
        operatingProfit: operatingProfit,
        netProfit: netProfit,
        totalRevenue: totalRevenue,
        costOfGoodsSold: costOfGoodsSold,
        operatingExpenses: operatingExpenses,
        items: items,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء تقرير الربحية: ${e.toString()}');
    }
  }

  // Generate Comparative Financial Report
  Future<Result<ComparativeReport>> generateComparativeReport({
    required DateTime currentStartDate,
    required DateTime currentEndDate,
    required DateTime previousStartDate,
    required DateTime previousEndDate,
  }) async {
    try {
      // Get current period income statement
      Result<IncomeStatementReport> currentResult =
          await generateIncomeStatement(
            startDate: currentStartDate,
            endDate: currentEndDate,
          );

      if (!currentResult.isSuccess) {
        return Result.error(
          'خطأ في جلب قائمة الدخل للفترة الحالية: ${currentResult.error}',
        );
      }

      // Get previous period income statement
      Result<IncomeStatementReport> previousResult =
          await generateIncomeStatement(
            startDate: previousStartDate,
            endDate: previousEndDate,
          );

      if (!previousResult.isSuccess) {
        return Result.error(
          'خطأ في جلب قائمة الدخل للفترة السابقة: ${previousResult.error}',
        );
      }

      IncomeStatementReport currentIncome = currentResult.data!;
      IncomeStatementReport previousIncome = previousResult.data!;

      // Create comparative items
      List<ComparativeItem> items = [
        ComparativeItem(
          accountCode: 'REV',
          accountName: 'إجمالي الإيرادات',
          currentAmount: currentIncome.totalRevenue,
          previousAmount: previousIncome.totalRevenue,
          variance: currentIncome.totalRevenue - previousIncome.totalRevenue,
          variancePercentage: previousIncome.totalRevenue == 0
              ? 0
              : ((currentIncome.totalRevenue - previousIncome.totalRevenue) /
                        previousIncome.totalRevenue) *
                    100,
        ),
        ComparativeItem(
          accountCode: 'EXP',
          accountName: 'إجمالي المصروفات',
          currentAmount: currentIncome.totalExpenses,
          previousAmount: previousIncome.totalExpenses,
          variance: currentIncome.totalExpenses - previousIncome.totalExpenses,
          variancePercentage: previousIncome.totalExpenses == 0
              ? 0
              : ((currentIncome.totalExpenses - previousIncome.totalExpenses) /
                        previousIncome.totalExpenses) *
                    100,
        ),
        ComparativeItem(
          accountCode: 'NET',
          accountName: 'صافي الدخل',
          currentAmount: currentIncome.netIncome,
          previousAmount: previousIncome.netIncome,
          variance: currentIncome.netIncome - previousIncome.netIncome,
          variancePercentage: previousIncome.netIncome == 0
              ? 0
              : ((currentIncome.netIncome - previousIncome.netIncome) /
                        previousIncome.netIncome) *
                    100,
        ),
      ];

      // Create summary
      ComparativeSummary summary = ComparativeSummary(
        totalRevenueChange:
            currentIncome.totalRevenue - previousIncome.totalRevenue,
        totalExpenseChange:
            currentIncome.totalExpenses - previousIncome.totalExpenses,
        netIncomeChange: currentIncome.netIncome - previousIncome.netIncome,
        totalAssetChange: 0.0, // Would need balance sheet data
        totalLiabilityChange: 0.0, // Would need balance sheet data
        equityChange: 0.0, // Would need balance sheet data
      );

      ComparativeReport report = ComparativeReport(
        currentPeriodStart: currentStartDate,
        currentPeriodEnd: currentEndDate,
        previousPeriodStart: previousStartDate,
        previousPeriodEnd: previousEndDate,
        items: items,
        summary: summary,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء التقرير المقارن: ${e.toString()}');
    }
  }

  // Generate Trend Analysis Report
  Future<Result<TrendAnalysisReport>> generateTrendAnalysisReport({
    required List<DateTime> periodStartDates,
    required List<DateTime> periodEndDates,
  }) async {
    try {
      if (periodStartDates.length != periodEndDates.length) {
        return Result.error(
          'عدد تواريخ البداية يجب أن يساوي عدد تواريخ النهاية',
        );
      }

      List<double> revenueValues = [];
      List<double> expenseValues = [];
      List<double> netIncomeValues = [];

      for (int i = 0; i < periodStartDates.length; i++) {
        // Get income statement for this period
        Result<IncomeStatementReport> incomeResult =
            await generateIncomeStatement(
              startDate: periodStartDates[i],
              endDate: periodEndDates[i],
            );

        if (incomeResult.isSuccess) {
          IncomeStatementReport income = incomeResult.data!;
          revenueValues.add(income.totalRevenue);
          expenseValues.add(income.totalExpenses);
          netIncomeValues.add(income.netIncome);
        }
      }

      // Calculate growth rates
      double revenueGrowthRate = _calculateGrowthRate(revenueValues);
      double expenseGrowthRate = _calculateGrowthRate(expenseValues);
      double profitGrowthRate = _calculateGrowthRate(netIncomeValues);

      // Create trend items
      List<TrendItem> items = [
        TrendItem(
          accountCode: 'REV',
          accountName: 'إجمالي الإيرادات',
          values: revenueValues,
          averageGrowthRate: revenueGrowthRate,
          trendDirection: _getTrendDirection(revenueGrowthRate),
        ),
        TrendItem(
          accountCode: 'EXP',
          accountName: 'إجمالي المصروفات',
          values: expenseValues,
          averageGrowthRate: expenseGrowthRate,
          trendDirection: _getTrendDirection(expenseGrowthRate),
        ),
        TrendItem(
          accountCode: 'NET',
          accountName: 'صافي الدخل',
          values: netIncomeValues,
          averageGrowthRate: profitGrowthRate,
          trendDirection: _getTrendDirection(profitGrowthRate),
        ),
      ];

      // Create summary
      TrendSummary summary = TrendSummary(
        revenueGrowthRate: revenueGrowthRate,
        expenseGrowthRate: expenseGrowthRate,
        profitGrowthRate: profitGrowthRate,
        assetGrowthRate: 0.0, // Would need balance sheet data
        overallTrend: _getOverallTrend(
          revenueGrowthRate,
          expenseGrowthRate,
          profitGrowthRate,
        ),
      );

      TrendAnalysisReport report = TrendAnalysisReport(
        periods: periodStartDates,
        items: items,
        summary: summary,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير تحليل الاتجاهات: ${e.toString()}',
      );
    }
  }

  // Generate Financial Ratios Report
  Future<Result<FinancialRatiosReport>> generateFinancialRatiosReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // Get income statement and balance sheet data
      Result<IncomeStatementReport> incomeResult =
          await generateIncomeStatement(startDate: startDate, endDate: endDate);

      Result<BalanceSheetReport> balanceResult = await generateBalanceSheet(
        asOfDate: endDate,
      );

      if (!incomeResult.isSuccess) {
        return Result.error('خطأ في جلب قائمة الدخل: ${incomeResult.error}');
      }

      if (!balanceResult.isSuccess) {
        return Result.error(
          'خطأ في جلب الميزانية العمومية: ${balanceResult.error}',
        );
      }

      IncomeStatementReport income = incomeResult.data!;
      BalanceSheetReport balance = balanceResult.data!;

      // Calculate liquidity ratios
      LiquidityRatios liquidityRatios = LiquidityRatios(
        currentRatio: balance.currentLiabilities == 0
            ? 0
            : balance.currentAssets / balance.currentLiabilities,
        quickRatio: balance.currentLiabilities == 0
            ? 0
            : balance.currentAssets /
                  balance.currentLiabilities *
                  0.8, // Approximation
        cashRatio: balance.currentLiabilities == 0
            ? 0
            : balance.currentAssets /
                  balance.currentLiabilities *
                  0.3, // Approximation
        workingCapital: balance.currentAssets - balance.currentLiabilities,
      );

      // Calculate profitability ratios
      double cogs = await _calculateCostOfGoodsSold(startDate, endDate);
      ProfitabilityRatios profitabilityRatios = ProfitabilityRatios(
        grossProfitMargin: income.totalRevenue == 0
            ? 0
            : (income.totalRevenue - cogs) / income.totalRevenue * 100,
        operatingProfitMargin: income.totalRevenue == 0
            ? 0
            : income.netIncome / income.totalRevenue * 100, // Simplified
        netProfitMargin: income.totalRevenue == 0
            ? 0
            : income.netIncome / income.totalRevenue * 100,
        returnOnAssets: balance.totalAssets == 0
            ? 0
            : income.netIncome / balance.totalAssets * 100,
        returnOnEquity: balance.totalEquity == 0
            ? 0
            : income.netIncome / balance.totalEquity * 100,
      );

      // Calculate efficiency ratios
      EfficiencyRatios efficiencyRatios = EfficiencyRatios(
        assetTurnover: balance.totalAssets == 0
            ? 0
            : income.totalRevenue / balance.totalAssets,
        inventoryTurnover: balance.currentAssets == 0
            ? 0
            : cogs /
                  (balance.currentAssets * 0.3), // Approximation for inventory
        receivablesTurnover: balance.currentAssets == 0
            ? 0
            : income.totalRevenue /
                  (balance.currentAssets *
                      0.4), // Approximation for receivables
        payablesTurnover: balance.currentLiabilities == 0
            ? 0
            : cogs / balance.currentLiabilities,
      );

      // Calculate leverage ratios
      LeverageRatios leverageRatios = LeverageRatios(
        debtToAssets: balance.totalAssets == 0
            ? 0
            : balance.totalLiabilities / balance.totalAssets * 100,
        debtToEquity: balance.totalEquity == 0
            ? 0
            : balance.totalLiabilities / balance.totalEquity * 100,
        equityMultiplier: balance.totalEquity == 0
            ? 0
            : balance.totalAssets / balance.totalEquity,
        interestCoverage: 1.0, // Would need interest expense data
      );

      FinancialRatiosReport report = FinancialRatiosReport(
        reportDate: endDate,
        liquidityRatios: liquidityRatios,
        profitabilityRatios: profitabilityRatios,
        efficiencyRatios: efficiencyRatios,
        leverageRatios: leverageRatios,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء تقرير النسب المالية: ${e.toString()}');
    }
  }

  // Generate Budget vs Actual Report
  Future<Result<BudgetVsActualReport>> generateBudgetVsActualReport({
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, double> budgetAmounts, // Account code -> Budget amount
  }) async {
    try {
      // Get actual income statement
      Result<IncomeStatementReport> actualResult =
          await generateIncomeStatement(startDate: startDate, endDate: endDate);

      if (!actualResult.isSuccess) {
        return Result.error(
          'خطأ في جلب البيانات الفعلية: ${actualResult.error}',
        );
      }

      IncomeStatementReport actual = actualResult.data!;

      // Create budget vs actual items
      List<BudgetVsActualItem> items = [];

      // Revenue comparison
      double budgetRevenue = budgetAmounts['REVENUE'] ?? 0.0;
      items.add(
        BudgetVsActualItem(
          accountCode: 'REV',
          accountName: 'إجمالي الإيرادات',
          budgetAmount: budgetRevenue,
          actualAmount: actual.totalRevenue,
          variance: actual.totalRevenue - budgetRevenue,
          variancePercentage: budgetRevenue == 0
              ? 0
              : ((actual.totalRevenue - budgetRevenue) / budgetRevenue) * 100,
        ),
      );

      // Expense comparison
      double budgetExpenses = budgetAmounts['EXPENSES'] ?? 0.0;
      items.add(
        BudgetVsActualItem(
          accountCode: 'EXP',
          accountName: 'إجمالي المصروفات',
          budgetAmount: budgetExpenses,
          actualAmount: actual.totalExpenses,
          variance: actual.totalExpenses - budgetExpenses,
          variancePercentage: budgetExpenses == 0
              ? 0
              : ((actual.totalExpenses - budgetExpenses) / budgetExpenses) *
                    100,
        ),
      );

      // Net income comparison
      double budgetNetIncome = budgetRevenue - budgetExpenses;
      items.add(
        BudgetVsActualItem(
          accountCode: 'NET',
          accountName: 'صافي الدخل',
          budgetAmount: budgetNetIncome,
          actualAmount: actual.netIncome,
          variance: actual.netIncome - budgetNetIncome,
          variancePercentage: budgetNetIncome == 0
              ? 0
              : ((actual.netIncome - budgetNetIncome) / budgetNetIncome) * 100,
        ),
      );

      // Create summary
      BudgetVsActualSummary summary = BudgetVsActualSummary(
        totalBudgetRevenue: budgetRevenue,
        totalActualRevenue: actual.totalRevenue,
        totalBudgetExpenses: budgetExpenses,
        totalActualExpenses: actual.totalExpenses,
        budgetedNetIncome: budgetNetIncome,
        actualNetIncome: actual.netIncome,
      );

      BudgetVsActualReport report = BudgetVsActualReport(
        startDate: startDate,
        endDate: endDate,
        items: items,
        summary: summary,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير الموازنة مقابل الفعلي: ${e.toString()}',
      );
    }
  }

  // ==================== دوال مساعدة للحسابات ====================

  // Calculate depreciation for the period
  Future<double> _calculateDepreciation(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get depreciation accounts (assuming account codes starting with 'DEP')
      List<Account> depreciationAccounts = await _accountDao.getAccountsByType(
        AccountType.expense,
      );
      double totalDepreciation = 0.0;

      for (Account account in depreciationAccounts) {
        // Check if this is a depreciation account by name or code
        if (account.name.contains('استهلاك') ||
            account.name.contains('إطفاء') ||
            account.code.startsWith('DEP')) {
          // Get journal entries for this account in the period
          List<JournalEntryLine> lines = await _journalEntryDao
              .getAccountTransactions(account.id!);

          for (JournalEntryLine line in lines) {
            // Get the journal entry to check date
            JournalEntry? entry = await _journalEntryDao.getJournalEntryById(
              line.journalEntryId,
            );
            if (entry != null &&
                entry.date.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                entry.date.isBefore(endDate.add(const Duration(days: 1))) &&
                entry.isPosted) {
              totalDepreciation +=
                  line.debitAmount; // Depreciation is usually a debit
            }
          }
        }
      }

      return totalDepreciation;
    } catch (e) {
      return 0.0; // Return 0 if calculation fails
    }
  }

  // Calculate accounts receivable change
  Future<double> _calculateAccountsReceivableChange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get asset accounts and filter for receivables
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double change = 0.0;

      for (Account account in allAccounts) {
        // Check if this is an accounts receivable account
        if (account.name.contains('ذمم مدينة') ||
            account.name.contains('عملاء') ||
            account.code.contains('AR') ||
            account.code.contains('112')) {
          // For simplification, use current balance as approximation
          change += account.balance * 0.1; // Assume 10% change as example
        }
      }

      return change;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate inventory change
  Future<double> _calculateInventoryChange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get asset accounts and filter for inventory
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double change = 0.0;

      for (Account account in allAccounts) {
        // Check if this is an inventory account
        if (account.name.contains('مخزون') ||
            account.name.contains('بضاعة') ||
            account.code.contains('INV') ||
            account.code.contains('113')) {
          // For simplification, use current balance as approximation
          change += account.balance * 0.05; // Assume 5% change as example
        }
      }

      return change;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate accounts payable change
  Future<double> _calculateAccountsPayableChange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get liability accounts and filter for payables
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.liability,
      );
      double change = 0.0;

      for (Account account in allAccounts) {
        // Check if this is an accounts payable account
        if (account.name.contains('ذمم دائنة') ||
            account.name.contains('موردين') ||
            account.code.contains('AP') ||
            account.code.contains('211')) {
          // For simplification, use current balance as approximation
          change += account.balance * 0.08; // Assume 8% change as example
        }
      }

      return change;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate asset purchases
  Future<double> _calculateAssetPurchases(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get asset accounts and filter for fixed assets
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double totalPurchases = 0.0;

      for (Account account in allAccounts) {
        // Check if this is a fixed asset account
        if (account.name.contains('أصول ثابتة') ||
            account.name.contains('معدات') ||
            account.name.contains('مباني') ||
            account.code.startsWith('15')) {
          // Get transactions for this account
          List<JournalEntryLine> lines = await _journalEntryDao
              .getAccountTransactions(account.id!);

          for (JournalEntryLine line in lines) {
            // Get the journal entry to check date
            JournalEntry? entry = await _journalEntryDao.getJournalEntryById(
              line.journalEntryId,
            );
            if (entry != null &&
                entry.date.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                entry.date.isBefore(endDate.add(const Duration(days: 1))) &&
                entry.isPosted &&
                line.debitAmount > 0) {
              totalPurchases += line.debitAmount; // Asset increase = purchase
            }
          }
        }
      }

      return totalPurchases;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate asset sales
  Future<double> _calculateAssetSales(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get asset accounts and filter for fixed assets
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double totalSales = 0.0;

      for (Account account in allAccounts) {
        // Check if this is a fixed asset account
        if (account.name.contains('أصول ثابتة') ||
            account.name.contains('معدات') ||
            account.name.contains('مباني') ||
            account.code.startsWith('15')) {
          // Get transactions for this account
          List<JournalEntryLine> lines = await _journalEntryDao
              .getAccountTransactions(account.id!);

          for (JournalEntryLine line in lines) {
            // Get the journal entry to check date
            JournalEntry? entry = await _journalEntryDao.getJournalEntryById(
              line.journalEntryId,
            );
            if (entry != null &&
                entry.date.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                entry.date.isBefore(endDate.add(const Duration(days: 1))) &&
                entry.isPosted &&
                line.creditAmount > 0) {
              totalSales += line.creditAmount; // Asset decrease = sale
            }
          }
        }
      }

      return totalSales;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate loan proceeds
  Future<double> _calculateLoanProceeds(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get liability accounts and filter for loans
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.liability,
      );
      double totalProceeds = 0.0;

      for (Account account in allAccounts) {
        // Check if this is a loan account
        if (account.name.contains('قرض') ||
            account.name.contains('تمويل') ||
            account.code.contains('LOAN') ||
            account.code.startsWith('22')) {
          // Get transactions for this account
          List<JournalEntryLine> lines = await _journalEntryDao
              .getAccountTransactions(account.id!);

          for (JournalEntryLine line in lines) {
            // Get the journal entry to check date
            JournalEntry? entry = await _journalEntryDao.getJournalEntryById(
              line.journalEntryId,
            );
            if (entry != null &&
                entry.date.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                entry.date.isBefore(endDate.add(const Duration(days: 1))) &&
                entry.isPosted &&
                line.creditAmount > 0) {
              totalProceeds +=
                  line.creditAmount; // Liability increase = new loan
            }
          }
        }
      }

      return totalProceeds;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate loan repayments
  Future<double> _calculateLoanRepayments(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get liability accounts and filter for loans
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.liability,
      );
      double totalRepayments = 0.0;

      for (Account account in allAccounts) {
        // Check if this is a loan account
        if (account.name.contains('قرض') ||
            account.name.contains('تمويل') ||
            account.code.contains('LOAN') ||
            account.code.startsWith('22')) {
          // Get transactions for this account
          List<JournalEntryLine> lines = await _journalEntryDao
              .getAccountTransactions(account.id!);

          for (JournalEntryLine line in lines) {
            // Get the journal entry to check date
            JournalEntry? entry = await _journalEntryDao.getJournalEntryById(
              line.journalEntryId,
            );
            if (entry != null &&
                entry.date.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                entry.date.isBefore(endDate.add(const Duration(days: 1))) &&
                entry.isPosted &&
                line.debitAmount > 0) {
              totalRepayments +=
                  line.debitAmount; // Liability decrease = repayment
            }
          }
        }
      }

      return totalRepayments;
    } catch (e) {
      return 0.0;
    }
  }

  // Get cash balance at specific date
  Future<double> _getCashBalance(DateTime date) async {
    try {
      // Get asset accounts and filter for cash
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double totalCash = 0.0;

      for (Account account in allAccounts) {
        // Check if this is a cash account
        if (account.name.contains('نقدية') ||
            account.name.contains('صندوق') ||
            account.name.contains('بنك') ||
            account.code.startsWith('111')) {
          totalCash += account.balance; // Use current balance as approximation
        }
      }

      return totalCash;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate cost of goods sold
  Future<double> _calculateCostOfGoodsSold(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // Get expense accounts and filter for COGS
      List<Account> allAccounts = await _accountDao.getAccountsByType(
        AccountType.expense,
      );
      double totalCOGS = 0.0;

      for (Account account in allAccounts) {
        // Check if this is a COGS account
        if (account.name.contains('تكلفة البضاعة') ||
            account.name.contains('تكلفة المبيعات') ||
            account.code.contains('COGS') ||
            account.code.startsWith('51')) {
          totalCOGS += account.balance;
        }
      }

      return totalCOGS;
    } catch (e) {
      return 0.0;
    }
  }

  // Calculate growth rate from a list of values
  double _calculateGrowthRate(List<double> values) {
    if (values.length < 2) return 0.0;

    double totalGrowth = 0.0;
    int growthPeriods = 0;

    for (int i = 1; i < values.length; i++) {
      if (values[i - 1] != 0) {
        double periodGrowth =
            ((values[i] - values[i - 1]) / values[i - 1]) * 100;
        totalGrowth += periodGrowth;
        growthPeriods++;
      }
    }

    return growthPeriods > 0 ? totalGrowth / growthPeriods : 0.0;
  }

  // Get trend direction based on growth rate
  String _getTrendDirection(double growthRate) {
    if (growthRate > 5) {
      return 'increasing';
    } else if (growthRate < -5) {
      return 'decreasing';
    } else {
      return 'stable';
    }
  }

  // Get overall trend assessment
  String _getOverallTrend(
    double revenueGrowth,
    double expenseGrowth,
    double profitGrowth,
  ) {
    if (profitGrowth > 10) {
      return 'إيجابي قوي';
    } else if (profitGrowth > 0) {
      return 'إيجابي';
    } else if (profitGrowth > -10) {
      return 'مستقر';
    } else {
      return 'سلبي';
    }
  }
}
