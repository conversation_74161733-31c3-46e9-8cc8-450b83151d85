import '../models/fixed_asset.dart';
import '../models/asset_depreciation.dart';
import 'database_helper.dart';
import 'database_schema.dart';

/// Data Access Object للأصول الثابتة
class FixedAssetDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// الحصول على جميع الأصول الثابتة
  Future<List<FixedAsset>> getAllFixedAssets() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      orderBy: 'code ASC',
    );
    return List.generate(maps.length, (i) => FixedAsset.fromMap(maps[i]));
  }

  /// الحصول على أصل ثابت بالمعرف
  Future<FixedAsset?> getFixedAssetById(int id) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return FixedAsset.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على أصل ثابت بالرمز
  Future<FixedAsset?> getFixedAssetByCode(String code) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      where: 'code = ?',
      whereArgs: [code],
    );
    if (maps.isNotEmpty) {
      return FixedAsset.fromMap(maps.first);
    }
    return null;
  }

  /// إدراج أصل ثابت جديد
  Future<int> insertFixedAsset(FixedAsset asset) async {
    final db = await _dbHelper.database;
    return await db.insert(DatabaseSchema.tableFixedAssets, asset.toMap());
  }

  /// تحديث أصل ثابت
  Future<int> updateFixedAsset(FixedAsset asset) async {
    final db = await _dbHelper.database;
    return await db.update(
      DatabaseSchema.tableFixedAssets,
      asset.toMap(),
      where: 'id = ?',
      whereArgs: [asset.id],
    );
  }

  /// حذف أصل ثابت
  Future<int> deleteFixedAsset(int id) async {
    final db = await _dbHelper.database;
    return await db.delete(
      DatabaseSchema.tableFixedAssets,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// البحث في الأصول الثابتة
  Future<List<FixedAsset>> searchFixedAssets(String query) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      where: 'code LIKE ? OR name LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'code ASC',
    );
    return List.generate(maps.length, (i) => FixedAsset.fromMap(maps[i]));
  }

  /// الحصول على الأصول الثابتة حسب الفئة
  Future<List<FixedAsset>> getFixedAssetsByCategory(
    AssetCategory category,
  ) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      where: 'category = ?',
      whereArgs: [category.name],
      orderBy: 'code ASC',
    );
    return List.generate(maps.length, (i) => FixedAsset.fromMap(maps[i]));
  }

  /// الحصول على الأصول الثابتة حسب الحالة
  Future<List<FixedAsset>> getFixedAssetsByStatus(AssetStatus status) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'code ASC',
    );
    return List.generate(maps.length, (i) => FixedAsset.fromMap(maps[i]));
  }

  /// الحصول على الأصول الثابتة النشطة
  Future<List<FixedAsset>> getActiveFixedAssets() async {
    return await getFixedAssetsByStatus(AssetStatus.active);
  }

  /// الحصول على الرمز التالي للأصل الثابت
  Future<String> getNextAssetCode() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT MAX(CAST(SUBSTR(code, 3) AS INTEGER)) as max_num FROM ${DatabaseSchema.tableFixedAssets} WHERE code LIKE "FA%"',
    );

    int nextNumber = 1;
    if (result.isNotEmpty && result.first['max_num'] != null) {
      nextNumber = (result.first['max_num'] as int) + 1;
    }

    return 'FA${nextNumber.toString().padLeft(4, '0')}';
  }

  /// الحصول على عدد الأصول الثابتة
  Future<int> getFixedAssetsCount() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableFixedAssets}',
    );
    return result.first['count'] as int;
  }

  /// الحصول على إجمالي قيمة الأصول الثابتة
  Future<double> getTotalAssetsValue() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT SUM(purchase_price) as total FROM ${DatabaseSchema.tableFixedAssets} WHERE status = "active"',
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// الحصول على ملخص الأصول الثابتة
  Future<FixedAssetsSummary> getFixedAssetsSummary() async {
    final db = await _dbHelper.database;

    // إجمالي الأصول
    final totalResult = await db.rawQuery(
      'SELECT COUNT(*) as count, SUM(purchase_price) as total_value FROM ${DatabaseSchema.tableFixedAssets}',
    );

    final totalAssets = totalResult.first['count'] as int;
    final totalPurchaseValue =
        (totalResult.first['total_value'] as double?) ?? 0.0;

    // الأصول حسب الفئة
    final categoryResult = await db.rawQuery(
      'SELECT category, COUNT(*) as count, SUM(purchase_price) as value FROM ${DatabaseSchema.tableFixedAssets} GROUP BY category',
    );

    final assetsByCategory = <String, int>{};
    final valueByCategory = <String, double>{};

    for (final row in categoryResult) {
      final category = row['category'] as String;
      assetsByCategory[category] = row['count'] as int;
      valueByCategory[category] = (row['value'] as double?) ?? 0.0;
    }

    // حساب إجمالي الاستهلاك والقيمة الدفترية
    final assets = await getAllFixedAssets();
    double totalDepreciation = 0.0;

    for (final asset in assets) {
      totalDepreciation += asset.getTotalDepreciation();
    }

    final totalBookValue = totalPurchaseValue - totalDepreciation;

    return FixedAssetsSummary(
      totalAssets: totalAssets,
      totalPurchaseValue: totalPurchaseValue,
      totalDepreciation: totalDepreciation,
      totalBookValue: totalBookValue,
      assetsByCategory: assetsByCategory,
      valueByCategory: valueByCategory,
    );
  }

  /// التحقق من وجود معاملات مرتبطة بالأصل
  Future<bool> hasTransactions(int assetId) async {
    final db = await _dbHelper.database;

    // فحص استهلاك الأصول
    final depreciationResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableAssetDepreciation} WHERE asset_id = ?',
      [assetId],
    );

    final depreciationCount = depreciationResult.first['count'] as int;
    return depreciationCount > 0;
  }

  /// الحصول على الأصول التي تحتاج إلى صيانة (انتهت الضمانة)
  Future<List<FixedAsset>> getAssetsNeedingMaintenance() async {
    final db = await _dbHelper.database;
    final now = DateTime.now().toIso8601String();

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      where:
          'warranty_expiry IS NOT NULL AND warranty_expiry < ? AND status = "active"',
      whereArgs: [now],
      orderBy: 'warranty_expiry ASC',
    );

    return List.generate(maps.length, (i) => FixedAsset.fromMap(maps[i]));
  }

  /// الحصول على الأصول حسب الموقع
  Future<List<FixedAsset>> getAssetsByLocation(String location) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableFixedAssets,
      where: 'location = ?',
      whereArgs: [location],
      orderBy: 'code ASC',
    );
    return List.generate(maps.length, (i) => FixedAsset.fromMap(maps[i]));
  }

  /// الحصول على جميع المواقع
  Future<List<String>> getAllLocations() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT DISTINCT location FROM ${DatabaseSchema.tableFixedAssets} WHERE location IS NOT NULL ORDER BY location',
    );
    return result.map((row) => row['location'] as String).toList();
  }

  /// الحصول على جميع الفئات المستخدمة
  Future<List<String>> getAllCategories() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT DISTINCT category FROM ${DatabaseSchema.tableFixedAssets} ORDER BY category',
    );
    return result.map((row) => row['category'] as String).toList();
  }
}
