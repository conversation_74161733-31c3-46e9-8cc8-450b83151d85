/// شاشة إضافة/تعديل الضريبة
/// Add/Edit Tax Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/tax.dart';
import '../../services/tax_service.dart';
import '../../utils/result.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/error_dialog.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/quantum_dropdown.dart';

class AddTaxScreen extends StatefulWidget {
  final Tax? tax;

  const AddTaxScreen({super.key, this.tax});

  @override
  State<AddTaxScreen> createState() => _AddTaxScreenState();
}

class _AddTaxScreenState extends State<AddTaxScreen> {
  final TaxService _taxService = TaxService();
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _nameArController = TextEditingController();
  final TextEditingController _nameEnController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _rateController = TextEditingController();
  final TextEditingController _minAmountController = TextEditingController();
  final TextEditingController _maxAmountController = TextEditingController();

  // Form values
  TaxType _selectedType = TaxType.vat;
  TaxCalculationMethod _selectedMethod = TaxCalculationMethod.percentage;
  TaxStatus _selectedStatus = TaxStatus.active;
  bool _isInclusive = false;
  bool _isCompound = false;
  DateTime? _effectiveDate;
  DateTime? _expiryDate;

  bool _isLoading = false;
  bool get _isEditing => widget.tax != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadTaxData();
    }
  }

  void _loadTaxData() {
    final tax = widget.tax!;
    _codeController.text = tax.code;
    _nameArController.text = tax.nameAr;
    _nameEnController.text = tax.nameEn;
    _descriptionController.text = tax.description ?? '';
    _rateController.text = tax.rate.toString();
    // _minAmountController.text = tax.minAmount?.toString() ?? '';
    // _maxAmountController.text = tax.maxAmount?.toString() ?? '';

    _selectedType = tax.type;
    _selectedMethod = tax.calculationMethod;
    _selectedStatus = tax.status;
    _isInclusive = tax.isInclusive;
    _isCompound = tax.isCompound;
    _effectiveDate = tax.effectiveDate;
    _expiryDate = tax.expiryDate;
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameArController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    _rateController.dispose();
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  Future<void> _saveTax() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final tax = Tax(
        id: _isEditing ? widget.tax!.id : null,
        code: _codeController.text.trim(),
        nameAr: _nameArController.text.trim(),
        nameEn: _nameEnController.text.trim(),
        description: _descriptionController.text.trim(),
        type: _selectedType,
        calculationMethod: _selectedMethod,
        rate: double.parse(_rateController.text),
        // minAmount: _minAmountController.text.isNotEmpty
        //     ? double.parse(_minAmountController.text)
        //     : null,
        // maxAmount: _maxAmountController.text.isNotEmpty
        //     ? double.parse(_maxAmountController.text)
        //     : null,
        isInclusive: _isInclusive,
        isCompound: _isCompound,
        status: _selectedStatus,
        effectiveDate: _effectiveDate,
        expiryDate: _expiryDate,
        createdAt: _isEditing ? widget.tax!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final Result<Tax> result;
      if (_isEditing) {
        result = await _taxService.updateTax(tax);
      } else {
        result = await _taxService.createTax(tax);
      }

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isEditing
                    ? 'تم تحديث الضريبة بنجاح'
                    : 'تم إضافة الضريبة بنجاح',
              ),
            ),
          );
          Navigator.pop(context);
        }
      } else {
        _showError(result.error!);
      }
    } catch (e) {
      _showError('خطأ في حفظ الضريبة: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isEffective) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isEffective
          ? (_effectiveDate ?? DateTime.now())
          : (_expiryDate ?? DateTime.now().add(const Duration(days: 365))),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isEffective) {
          _effectiveDate = picked;
        } else {
          _expiryDate = picked;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل الضريبة' : 'إضافة ضريبة جديدة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(onPressed: _saveTax, icon: const Icon(Icons.save)),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildBasicInfoCard(),
                const SizedBox(height: 16),
                _buildCalculationCard(),
                const SizedBox(height: 16),
                _buildSettingsCard(),
                const SizedBox(height: 16),
                _buildDatesCard(),
                const SizedBox(height: 24),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _codeController,
                    labelText: 'رمز الضريبة *',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'رمز الضريبة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<TaxType>(
                    value: _selectedType,
                    items: TaxType.values
                        .map(
                          (type) => DropdownMenuItem(
                            value: type,
                            child: Text(_getTaxTypeName(type)),
                          ),
                        )
                        .toList(),
                    onChanged: (value) =>
                        setState(() => _selectedType = value!),
                    labelText: 'نوع الضريبة *',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _nameArController,
              labelText: 'الاسم بالعربية *',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الاسم بالعربية مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _nameEnController,
              labelText: 'الاسم بالإنجليزية',
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _descriptionController,
              labelText: 'الوصف',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الحساب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<TaxCalculationMethod>(
                    value: _selectedMethod,
                    items: TaxCalculationMethod.values
                        .map(
                          (method) => DropdownMenuItem(
                            value: method,
                            child: Text(_getCalculationMethodName(method)),
                          ),
                        )
                        .toList(),
                    onChanged: (value) =>
                        setState(() => _selectedMethod = value!),
                    labelText: 'طريقة الحساب *',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _rateController,
                    labelText:
                        _selectedMethod == TaxCalculationMethod.percentage
                        ? 'المعدل (%)'
                        : 'المبلغ الثابت',
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'المعدل مطلوب';
                      }
                      final rate = double.tryParse(value);
                      if (rate == null || rate < 0) {
                        return 'المعدل يجب أن يكون رقم موجب';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _minAmountController,
                    labelText: 'الحد الأدنى للمبلغ',
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _maxAmountController,
                    labelText: 'الحد الأقصى للمبلغ',
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإعدادات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            QuantumDropdown<TaxStatus>(
              value: _selectedStatus,
              items: TaxStatus.values
                  .map(
                    (status) => DropdownMenuItem(
                      value: status,
                      child: Text(_getStatusName(status)),
                    ),
                  )
                  .toList(),
              onChanged: (value) => setState(() => _selectedStatus = value!),
              labelText: 'الحالة *',
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text('ضريبة شاملة'),
              subtitle: const Text('الضريبة مشمولة في السعر'),
              value: _isInclusive,
              onChanged: (value) => setState(() => _isInclusive = value!),
            ),
            CheckboxListTile(
              title: const Text('ضريبة مركبة'),
              subtitle: const Text('تحسب على أساس المبلغ + الضرائب الأخرى'),
              value: _isCompound,
              onChanged: (value) => setState(() => _isCompound = value!),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatesCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تواريخ السريان',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ListTile(
                    title: const Text('تاريخ السريان'),
                    subtitle: Text(
                      _effectiveDate?.toString().split(' ')[0] ?? 'غير محدد',
                    ),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () => _selectDate(context, true),
                  ),
                ),
                Expanded(
                  child: ListTile(
                    title: const Text('تاريخ الانتهاء'),
                    subtitle: Text(
                      _expiryDate?.toString().split(' ')[0] ?? 'غير محدد',
                    ),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () => _selectDate(context, false),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: QuantumButton(
            onPressed: () => Navigator.pop(context),
            text: 'إلغاء',
            variant: QuantumButtonVariant.outline,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: QuantumButton(
            onPressed: _saveTax,
            text: _isEditing ? 'تحديث' : 'حفظ',
          ),
        ),
      ],
    );
  }

  String _getTaxTypeName(TaxType type) {
    switch (type) {
      case TaxType.vat:
        return 'ضريبة القيمة المضافة';
      case TaxType.incomeTax:
        return 'ضريبة الدخل';
      case TaxType.withholdingTax:
        return 'ضريبة الاستقطاع';
      case TaxType.corporateTax:
        return 'ضريبة الشركات';
      case TaxType.customsDuty:
        return 'رسوم جمركية';
      case TaxType.exciseTax:
        return 'ضريبة انتقائية';
      case TaxType.other:
        return 'أخرى';
    }
  }

  String _getCalculationMethodName(TaxCalculationMethod method) {
    switch (method) {
      case TaxCalculationMethod.percentage:
        return 'نسبة مئوية';
      case TaxCalculationMethod.fixedAmount:
        return 'مبلغ ثابت';
      case TaxCalculationMethod.tiered:
        return 'متدرج';
      case TaxCalculationMethod.compound:
        return 'مركب';
    }
  }

  String _getStatusName(TaxStatus status) {
    switch (status) {
      case TaxStatus.active:
        return 'نشط';
      case TaxStatus.inactive:
        return 'غير نشط';
      case TaxStatus.suspended:
        return 'معلق';
      case TaxStatus.archived:
        return 'مؤرشف';
      case TaxStatus.expired:
        return 'منتهي الصلاحية';
    }
  }
}
