import '../models/item.dart';
import '../database/item_dao.dart';

/// نتيجة العملية مع إدارة الأخطاء
class Result<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  Result.success(this.data) : error = null, isSuccess = true;

  Result.error(this.error) : data = null, isSuccess = false;
}

/// خدمة إدارة الأصناف والمخزون
class ItemService {
  final ItemDao _itemDao = ItemDao();

  /// الحصول على جميع الأصناف
  Future<Result<List<Item>>> getAllItems() async {
    try {
      final items = await _itemDao.getAllItems();
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في جلب الأصناف: ${e.toString()}');
    }
  }

  /// الحصول على صنف بالمعرف
  Future<Result<Item>> getItemById(int id) async {
    try {
      final item = await _itemDao.getItemById(id);
      if (item != null) {
        return Result.success(item);
      } else {
        return Result.error('الصنف غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب الصنف: ${e.toString()}');
    }
  }

  /// إنشاء صنف جديد
  Future<Result<Item>> createItem(Item item) async {
    try {
      // التحقق من عدم تكرار الرمز
      final existingItem = await _itemDao.getItemByCode(item.code);
      if (existingItem != null) {
        return Result.error('رمز الصنف موجود مسبقاً');
      }

      // التحقق من عدم تكرار الباركود إذا كان موجوداً
      if (item.barcode != null && item.barcode!.isNotEmpty) {
        final existingBarcode = await _itemDao.getItemByBarcode(item.barcode!);
        if (existingBarcode != null) {
          return Result.error('الباركود موجود مسبقاً');
        }
      }

      // التحقق من صحة البيانات
      final validation = _validateItem(item);
      if (!validation.isSuccess) {
        return validation;
      }

      final id = await _itemDao.insertItem(item);
      final newItem = item.copyWith(id: id);
      return Result.success(newItem);
    } catch (e) {
      return Result.error('خطأ في إنشاء الصنف: ${e.toString()}');
    }
  }

  /// تحديث صنف
  Future<Result<Item>> updateItem(Item item) async {
    try {
      if (item.id == null) {
        return Result.error('معرف الصنف مطلوب للتحديث');
      }

      // التحقق من وجود الصنف
      final existingItem = await _itemDao.getItemById(item.id!);
      if (existingItem == null) {
        return Result.error('الصنف غير موجود');
      }

      // التحقق من عدم تكرار الرمز مع صنف آخر
      final itemWithSameCode = await _itemDao.getItemByCode(item.code);
      if (itemWithSameCode != null && itemWithSameCode.id != item.id) {
        return Result.error('رمز الصنف موجود مسبقاً');
      }

      // التحقق من عدم تكرار الباركود مع صنف آخر
      if (item.barcode != null && item.barcode!.isNotEmpty) {
        final itemWithSameBarcode = await _itemDao.getItemByBarcode(
          item.barcode!,
        );
        if (itemWithSameBarcode != null && itemWithSameBarcode.id != item.id) {
          return Result.error('الباركود موجود مسبقاً');
        }
      }

      // التحقق من صحة البيانات
      final validation = _validateItem(item);
      if (!validation.isSuccess) {
        return validation;
      }

      final updatedItem = item.copyWith(updatedAt: DateTime.now());
      await _itemDao.updateItem(updatedItem);
      return Result.success(updatedItem);
    } catch (e) {
      return Result.error('خطأ في تحديث الصنف: ${e.toString()}');
    }
  }

  /// حذف صنف
  Future<Result<void>> deleteItem(int id) async {
    try {
      final item = await _itemDao.getItemById(id);
      if (item == null) {
        return Result.error('الصنف غير موجود');
      }

      // التحقق من عدم وجود حركات مخزون للصنف
      final movements = await _itemDao.getItemMovements(id);
      if (movements.isNotEmpty) {
        return Result.error('لا يمكن حذف الصنف لوجود حركات مخزون مرتبطة به');
      }

      await _itemDao.deleteItem(id);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف الصنف: ${e.toString()}');
    }
  }

  /// البحث في الأصناف
  Future<Result<List<Item>>> searchItems(String query) async {
    try {
      if (query.trim().isEmpty) {
        return getAllItems();
      }
      final items = await _itemDao.searchItems(query.trim());
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  /// الحصول على الأصناف النشطة فقط
  Future<Result<List<Item>>> getActiveItems() async {
    try {
      final items = await _itemDao.getActiveItems();
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في جلب الأصناف النشطة: ${e.toString()}');
    }
  }

  /// الحصول على الأصناف حسب الفئة
  Future<Result<List<Item>>> getItemsByCategory(String category) async {
    try {
      final items = await _itemDao.getItemsByCategory(category);
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في جلب أصناف الفئة: ${e.toString()}');
    }
  }

  /// الحصول على الأصناف منخفضة المخزون
  Future<Result<List<Item>>> getLowStockItems() async {
    try {
      final items = await _itemDao.getLowStockItems();
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في جلب الأصناف منخفضة المخزون: ${e.toString()}');
    }
  }

  /// الحصول على الأصناف المنتهية من المخزون
  Future<Result<List<Item>>> getOutOfStockItems() async {
    try {
      final items = await _itemDao.getOutOfStockItems();
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في جلب الأصناف المنتهية: ${e.toString()}');
    }
  }

  /// الحصول على رمز الصنف التالي
  Future<Result<String>> getNextItemCode() async {
    try {
      final code = await _itemDao.getNextItemCode();
      return Result.success(code);
    } catch (e) {
      return Result.error('خطأ في توليد رمز الصنف: ${e.toString()}');
    }
  }

  /// تفعيل/إلغاء تفعيل صنف
  Future<Result<void>> toggleItemStatus(int id) async {
    try {
      final item = await _itemDao.getItemById(id);
      if (item == null) {
        return Result.error('الصنف غير موجود');
      }

      final updatedItem = item.copyWith(
        isActive: !item.isActive,
        updatedAt: DateTime.now(),
      );

      await _itemDao.updateItem(updatedItem);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تغيير حالة الصنف: ${e.toString()}');
    }
  }

  /// تحديث مخزون صنف
  Future<Result<void>> updateItemStock(
    int itemId,
    double newStock, {
    String? reference,
    String? description,
  }) async {
    try {
      final item = await _itemDao.getItemById(itemId);
      if (item == null) {
        return Result.error('الصنف غير موجود');
      }

      if (newStock < 0) {
        return Result.error('لا يمكن أن يكون المخزون سالباً');
      }

      // تسجيل حركة المخزون
      final movement = ItemMovement(
        itemId: itemId,
        date: DateTime.now(),
        movementType: newStock > item.currentStock ? 'in' : 'out',
        quantity: (newStock - item.currentStock).abs(),
        unitCost: item.costPrice,
        reference: reference ?? 'تعديل مخزون',
        description: description,
        balanceAfter: newStock,
      );

      await _itemDao.recordItemMovement(movement);

      // تحديث المخزون
      final updatedItem = item.copyWith(
        currentStock: newStock,
        updatedAt: DateTime.now(),
      );

      await _itemDao.updateItem(updatedItem);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث المخزون: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات المخزون
  Future<Result<Map<String, dynamic>>> getInventoryStatistics() async {
    try {
      final itemStats = await _itemDao.getItemStatistics();
      final valueStats = await _itemDao.getInventoryValueSummary();
      final categories = await _itemDao.getItemCategories();

      return Result.success({
        'item_statistics': itemStats,
        'value_statistics': valueStats,
        'categories': categories,
      });
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات المخزون: ${e.toString()}');
    }
  }

  /// الحصول على حركات المخزون لصنف
  Future<Result<List<ItemMovement>>> getItemMovements(
    int itemId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final movements = await _itemDao.getItemMovements(
        itemId,
        startDate: startDate,
        endDate: endDate,
      );
      return Result.success(movements);
    } catch (e) {
      return Result.error('خطأ في جلب حركات المخزون: ${e.toString()}');
    }
  }

  /// تسجيل حركة مخزون
  Future<Result<void>> recordStockMovement(ItemMovement movement) async {
    try {
      await _itemDao.recordItemMovement(movement);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تسجيل حركة المخزون: ${e.toString()}');
    }
  }

  /// الحصول على فئات الأصناف
  Future<Result<List<String>>> getItemCategories() async {
    try {
      final categories = await _itemDao.getItemCategories();
      return Result.success(categories);
    } catch (e) {
      return Result.error('خطأ في جلب فئات الأصناف: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات الصنف
  Result<Item> _validateItem(Item item) {
    if (item.code.trim().isEmpty) {
      return Result.error('رمز الصنف مطلوب');
    }

    if (item.name.trim().isEmpty) {
      return Result.error('اسم الصنف مطلوب');
    }

    if (item.unit.trim().isEmpty) {
      return Result.error('وحدة القياس مطلوبة');
    }

    if (item.costPrice < 0) {
      return Result.error('سعر التكلفة لا يمكن أن يكون سالباً');
    }

    if (item.sellingPrice < 0) {
      return Result.error('سعر البيع لا يمكن أن يكون سالباً');
    }

    if (item.currentStock < 0) {
      return Result.error('المخزون الحالي لا يمكن أن يكون سالباً');
    }

    if (item.minStockLevel != null && item.minStockLevel! < 0) {
      return Result.error('الحد الأدنى للمخزون لا يمكن أن يكون سالباً');
    }

    if (item.maxStockLevel != null && item.maxStockLevel! < 0) {
      return Result.error('الحد الأقصى للمخزون لا يمكن أن يكون سالباً');
    }

    if (item.minStockLevel != null &&
        item.maxStockLevel != null &&
        item.minStockLevel! > item.maxStockLevel!) {
      return Result.error('الحد الأدنى لا يمكن أن يكون أكبر من الحد الأقصى');
    }

    return Result.success(item);
  }
}
