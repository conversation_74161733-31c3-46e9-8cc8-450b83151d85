/// طبقة الوصول للبيانات للضرائب
/// Tax Data Access Object for Smart Ledger
library;

import 'package:sqflite/sqflite.dart';
import '../models/tax.dart';
import '../models/tax_calculation.dart';
import '../utils/result.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class TaxDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // ==================== عمليات الضرائب ====================

  /// إضافة ضريبة جديدة
  Future<Result<Tax>> insertTax(Tax tax) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        DatabaseSchema.tableTaxes,
        tax.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return Result.success(tax.copyWith(id: id));
    } catch (e) {
      return Result.error('خطأ في إضافة الضريبة: ${e.toString()}');
    }
  }

  /// تحديث ضريبة
  Future<Result<Tax>> updateTax(Tax tax) async {
    try {
      final db = await _dbHelper.database;
      final updatedTax = tax.copyWith(updatedAt: DateTime.now());
      await db.update(
        DatabaseSchema.tableTaxes,
        updatedTax.toMap(),
        where: 'id = ?',
        whereArgs: [tax.id],
      );
      return Result.success(updatedTax);
    } catch (e) {
      return Result.error('خطأ في تحديث الضريبة: ${e.toString()}');
    }
  }

  /// حذف ضريبة
  Future<Result<void>> deleteTax(int id) async {
    try {
      final db = await _dbHelper.database;
      await db.delete(
        DatabaseSchema.tableTaxes,
        where: 'id = ?',
        whereArgs: [id],
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف الضريبة: ${e.toString()}');
    }
  }

  /// الحصول على ضريبة بالمعرف
  Future<Result<Tax?>> getTaxById(int id) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxes,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Result.success(Tax.fromMap(maps.first));
      }
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في جلب الضريبة: ${e.toString()}');
    }
  }

  /// الحصول على ضريبة بالرمز
  Future<Result<Tax?>> getTaxByCode(String code) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxes,
        where: 'code = ?',
        whereArgs: [code],
      );

      if (maps.isNotEmpty) {
        return Result.success(Tax.fromMap(maps.first));
      }
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في جلب الضريبة: ${e.toString()}');
    }
  }

  /// الحصول على جميع الضرائب
  Future<Result<List<Tax>>> getAllTaxes() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxes,
        orderBy: 'name_ar ASC',
      );

      final taxes = maps.map((map) => Tax.fromMap(map)).toList();
      return Result.success(taxes);
    } catch (e) {
      return Result.error('خطأ في جلب الضرائب: ${e.toString()}');
    }
  }

  /// الحصول على الضرائب النشطة
  Future<Result<List<Tax>>> getActiveTaxes() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxes,
        where: 'status = ?',
        whereArgs: ['active'],
        orderBy: 'name_ar ASC',
      );

      final taxes = maps.map((map) => Tax.fromMap(map)).toList();
      return Result.success(taxes);
    } catch (e) {
      return Result.error('خطأ في جلب الضرائب النشطة: ${e.toString()}');
    }
  }

  /// الحصول على الضرائب حسب النوع
  Future<Result<List<Tax>>> getTaxesByType(TaxType type) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxes,
        where: 'type = ? AND status = ?',
        whereArgs: [type.value, 'active'],
        orderBy: 'name_ar ASC',
      );

      final taxes = maps.map((map) => Tax.fromMap(map)).toList();
      return Result.success(taxes);
    } catch (e) {
      return Result.error('خطأ في جلب الضرائب حسب النوع: ${e.toString()}');
    }
  }

  /// البحث في الضرائب
  Future<Result<List<Tax>>> searchTaxes(String query) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxes,
        where: 'name_ar LIKE ? OR name_en LIKE ? OR code LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'name_ar ASC',
      );

      final taxes = maps.map((map) => Tax.fromMap(map)).toList();
      return Result.success(taxes);
    } catch (e) {
      return Result.error('خطأ في البحث في الضرائب: ${e.toString()}');
    }
  }

  // ==================== عمليات مجموعات الضرائب ====================

  /// إضافة مجموعة ضرائب جديدة
  Future<Result<TaxGroup>> insertTaxGroup(TaxGroup taxGroup) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        DatabaseSchema.tableTaxGroups,
        taxGroup.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return Result.success(taxGroup.copyWith(id: id));
    } catch (e) {
      return Result.error('خطأ في إضافة مجموعة الضرائب: ${e.toString()}');
    }
  }

  /// تحديث مجموعة ضرائب
  Future<Result<TaxGroup>> updateTaxGroup(TaxGroup taxGroup) async {
    try {
      final db = await _dbHelper.database;
      final updatedGroup = taxGroup.copyWith(updatedAt: DateTime.now());
      await db.update(
        DatabaseSchema.tableTaxGroups,
        updatedGroup.toMap(),
        where: 'id = ?',
        whereArgs: [taxGroup.id],
      );
      return Result.success(updatedGroup);
    } catch (e) {
      return Result.error('خطأ في تحديث مجموعة الضرائب: ${e.toString()}');
    }
  }

  /// حذف مجموعة ضرائب
  Future<Result<void>> deleteTaxGroup(int id) async {
    try {
      final db = await _dbHelper.database;
      await db.delete(
        DatabaseSchema.tableTaxGroups,
        where: 'id = ?',
        whereArgs: [id],
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف مجموعة الضرائب: ${e.toString()}');
    }
  }

  /// الحصول على مجموعة ضرائب بالمعرف
  Future<Result<TaxGroup?>> getTaxGroupById(int id) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxGroups,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        final taxGroup = TaxGroup.fromMap(maps.first);

        // جلب الضرائب المرتبطة بالمجموعة
        final taxesResult = await getTaxesByGroupId(id);
        if (taxesResult.isSuccess) {
          taxGroup.taxes = taxesResult.data!;
        }

        return Result.success(taxGroup);
      }
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في جلب مجموعة الضرائب: ${e.toString()}');
    }
  }

  /// الحصول على جميع مجموعات الضرائب
  Future<Result<List<TaxGroup>>> getAllTaxGroups() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxGroups,
        orderBy: 'name_ar ASC',
      );

      final taxGroups = <TaxGroup>[];
      for (final map in maps) {
        final taxGroup = TaxGroup.fromMap(map);

        // جلب الضرائب المرتبطة بكل مجموعة
        final taxesResult = await getTaxesByGroupId(taxGroup.id!);
        if (taxesResult.isSuccess) {
          taxGroup.taxes = taxesResult.data!;
        }

        taxGroups.add(taxGroup);
      }

      return Result.success(taxGroups);
    } catch (e) {
      return Result.error('خطأ في جلب مجموعات الضرائب: ${e.toString()}');
    }
  }

  /// الحصول على الضرائب المرتبطة بمجموعة
  Future<Result<List<Tax>>> getTaxesByGroupId(int groupId) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.rawQuery(
        '''
        SELECT t.* FROM ${DatabaseSchema.tableTaxes} t
        INNER JOIN ${DatabaseSchema.tableTaxGroupTaxes} tgt ON t.id = tgt.tax_id
        WHERE tgt.tax_group_id = ?
        ORDER BY tgt.sort_order ASC, t.name_ar ASC
      ''',
        [groupId],
      );

      final taxes = maps.map((map) => Tax.fromMap(map)).toList();
      return Result.success(taxes);
    } catch (e) {
      return Result.error('خطأ في جلب ضرائب المجموعة: ${e.toString()}');
    }
  }

  /// إضافة ضريبة إلى مجموعة
  Future<Result<void>> addTaxToGroup(
    int groupId,
    int taxId, {
    int sortOrder = 0,
  }) async {
    try {
      final db = await _dbHelper.database;
      await db.insert(
        DatabaseSchema.tableTaxGroupTaxes,
        {
          'tax_group_id': groupId,
          'tax_id': taxId,
          'sort_order': sortOrder,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في إضافة الضريبة للمجموعة: ${e.toString()}');
    }
  }

  /// إزالة ضريبة من مجموعة
  Future<Result<void>> removeTaxFromGroup(int groupId, int taxId) async {
    try {
      final db = await _dbHelper.database;
      await db.delete(
        DatabaseSchema.tableTaxGroupTaxes,
        where: 'tax_group_id = ? AND tax_id = ?',
        whereArgs: [groupId, taxId],
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في إزالة الضريبة من المجموعة: ${e.toString()}');
    }
  }

  // ==================== عمليات حسابات الضرائب ====================

  /// إضافة حساب ضريبة
  Future<Result<TaxCalculation>> insertTaxCalculation(
    TaxCalculation calculation,
  ) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        DatabaseSchema.tableTaxCalculations,
        calculation.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return Result.success(calculation.copyWith(id: id));
    } catch (e) {
      return Result.error('خطأ في إضافة حساب الضريبة: ${e.toString()}');
    }
  }

  /// تحديث حساب ضريبة
  Future<Result<TaxCalculation>> updateTaxCalculation(
    TaxCalculation calculation,
  ) async {
    try {
      final db = await _dbHelper.database;
      final updatedCalculation = calculation.copyWith(
        updatedAt: DateTime.now(),
      );
      await db.update(
        DatabaseSchema.tableTaxCalculations,
        updatedCalculation.toMap(),
        where: 'id = ?',
        whereArgs: [calculation.id],
      );
      return Result.success(updatedCalculation);
    } catch (e) {
      return Result.error('خطأ في تحديث حساب الضريبة: ${e.toString()}');
    }
  }

  /// حذف حساب ضريبة
  Future<Result<void>> deleteTaxCalculation(int id) async {
    try {
      final db = await _dbHelper.database;
      await db.delete(
        DatabaseSchema.tableTaxCalculations,
        where: 'id = ?',
        whereArgs: [id],
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف حساب الضريبة: ${e.toString()}');
    }
  }

  /// حذف حسابات الضرائب للمستند
  Future<Result<void>> deleteTaxCalculationsForDocument(
    String documentType,
    int documentId,
  ) async {
    try {
      final db = await _dbHelper.database;
      await db.delete(
        DatabaseSchema.tableTaxCalculations,
        where: 'document_type = ? AND document_id = ?',
        whereArgs: [documentType, documentId],
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف حسابات الضرائب للمستند: ${e.toString()}');
    }
  }

  /// الحصول على حسابات الضرائب للمستند
  Future<Result<List<TaxCalculation>>> getTaxCalculationsForDocument(
    String documentType,
    int documentId,
  ) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxCalculations,
        where: 'document_type = ? AND document_id = ?',
        whereArgs: [documentType, documentId],
        orderBy: 'created_at ASC',
      );

      final calculations = <TaxCalculation>[];
      for (final map in maps) {
        final calculation = TaxCalculation.fromMap(map);

        // جلب بيانات الضريبة
        final taxResult = await getTaxById(calculation.taxId);
        if (taxResult.isSuccess && taxResult.data != null) {
          calculation.tax = taxResult.data;
        }

        calculations.add(calculation);
      }

      return Result.success(calculations);
    } catch (e) {
      return Result.error('خطأ في جلب حسابات الضرائب للمستند: ${e.toString()}');
    }
  }

  /// الحصول على حسابات الضرائب لفترة
  Future<Result<List<TaxCalculation>>> getTaxCalculationsForPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableTaxCalculations,
        where: 'calculated_at >= ? AND calculated_at <= ?',
        whereArgs: [
          startDate.toIso8601String().split('T')[0],
          '${endDate.toIso8601String().split('T')[0]} 23:59:59',
        ],
        orderBy: 'calculated_at DESC',
      );

      final calculations = <TaxCalculation>[];
      for (final map in maps) {
        final calculation = TaxCalculation.fromMap(map);

        // جلب بيانات الضريبة
        final taxResult = await getTaxById(calculation.taxId);
        if (taxResult.isSuccess && taxResult.data != null) {
          calculation.tax = taxResult.data;
        }

        calculations.add(calculation);
      }

      return Result.success(calculations);
    } catch (e) {
      return Result.error('خطأ في جلب حسابات الضرائب للفترة: ${e.toString()}');
    }
  }

  /// الحصول على ملخص الضرائب لفترة
  Future<Result<Map<String, double>>> getTaxSummaryForPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.rawQuery(
        '''
        SELECT
          t.code,
          t.name_ar,
          SUM(tc.tax_amount) as total_tax,
          COUNT(tc.id) as calculation_count
        FROM ${DatabaseSchema.tableTaxCalculations} tc
        INNER JOIN ${DatabaseSchema.tableTaxes} t ON tc.tax_id = t.id
        WHERE tc.calculated_at >= ? AND tc.calculated_at <= ?
        GROUP BY t.id, t.code, t.name_ar
        ORDER BY total_tax DESC
      ''',
        [
          startDate.toIso8601String().split('T')[0],
          '${endDate.toIso8601String().split('T')[0]} 23:59:59',
        ],
      );

      final summary = <String, double>{};
      for (final map in maps) {
        final taxCode = map['code'] as String;
        final totalTax = (map['total_tax'] as num).toDouble();
        summary[taxCode] = totalTax;
      }

      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب ملخص الضرائب: ${e.toString()}');
    }
  }

  // ==================== عمليات إعدادات الضرائب ====================

  /// حفظ إعدادات الضرائب للشركة
  Future<Result<CompanyTaxSettings>> saveCompanyTaxSettings(
    CompanyTaxSettings settings,
  ) async {
    try {
      final db = await _dbHelper.database;

      // التحقق من وجود إعدادات سابقة
      final existingMaps = await db.query(
        DatabaseSchema.tableCompanyTaxSettings,
      );

      if (existingMaps.isNotEmpty) {
        // تحديث الإعدادات الموجودة
        final updatedSettings = settings.copyWith(
          id: existingMaps.first['id'] as int,
          updatedAt: DateTime.now(),
        );
        await db.update(
          DatabaseSchema.tableCompanyTaxSettings,
          updatedSettings.toMap(),
          where: 'id = ?',
          whereArgs: [updatedSettings.id],
        );
        return Result.success(updatedSettings);
      } else {
        // إضافة إعدادات جديدة
        final id = await db.insert(
          DatabaseSchema.tableCompanyTaxSettings,
          settings.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        return Result.success(settings.copyWith(id: id));
      }
    } catch (e) {
      return Result.error('خطأ في حفظ إعدادات الضرائب: ${e.toString()}');
    }
  }

  /// الحصول على إعدادات الضرائب للشركة
  Future<Result<CompanyTaxSettings?>> getCompanyTaxSettings() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(DatabaseSchema.tableCompanyTaxSettings);

      if (maps.isNotEmpty) {
        return Result.success(CompanyTaxSettings.fromMap(maps.first));
      }
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في جلب إعدادات الضرائب: ${e.toString()}');
    }
  }
}
