import '../models/item.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class ItemDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Create a new item
  Future<int> insertItem(Item item) async {
    Map<String, dynamic> itemMap = item.toMap();
    itemMap.remove('id'); // Remove id for insert
    return await _dbHelper.insert(DatabaseSchema.tableItems, itemMap);
  }

  // Get all items
  Future<List<Item>> getAllItems() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      orderBy: 'name ASC',
    );
    return maps.map((map) => Item.fromMap(map)).toList();
  }

  // Get item by ID
  Future<Item?> getItemById(int id) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Item.fromMap(maps.first);
    }
    return null;
  }

  // Get item by code
  Future<Item?> getItemByCode(String code) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: 'code = ?',
      whereArgs: [code],
    );
    if (maps.isNotEmpty) {
      return Item.fromMap(maps.first);
    }
    return null;
  }

  // Get item by barcode
  Future<Item?> getItemByBarcode(String barcode) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: 'barcode = ?',
      whereArgs: [barcode],
    );
    if (maps.isNotEmpty) {
      return Item.fromMap(maps.first);
    }
    return null;
  }

  // Get active items only
  Future<List<Item>> getActiveItems() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );
    return maps.map((map) => Item.fromMap(map)).toList();
  }

  // Get items by category
  Future<List<Item>> getItemsByCategory(String category) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: 'category = ? AND is_active = 1',
      whereArgs: [category],
      orderBy: 'name ASC',
    );
    return maps.map((map) => Item.fromMap(map)).toList();
  }

  // Search items by name, code, or barcode
  Future<List<Item>> searchItems(String query) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where:
          'name LIKE ? OR code LIKE ? OR barcode LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => Item.fromMap(map)).toList();
  }

  // Get items with low stock
  Future<List<Item>> getLowStockItems() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where:
          'current_stock <= min_stock_level AND min_stock_level IS NOT NULL AND is_active = 1',
      orderBy: 'current_stock ASC',
    );
    return maps.map((map) => Item.fromMap(map)).toList();
  }

  // Get out of stock items
  Future<List<Item>> getOutOfStockItems() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: 'current_stock <= 0 AND is_active = 1',
      orderBy: 'name ASC',
    );
    return maps.map((map) => Item.fromMap(map)).toList();
  }

  // Get over stock items
  Future<List<Item>> getOverStockItems() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where:
          'current_stock >= max_stock_level AND max_stock_level IS NOT NULL AND is_active = 1',
      orderBy: 'current_stock DESC',
    );
    return maps.map((map) => Item.fromMap(map)).toList();
  }

  // Update item
  Future<int> updateItem(Item item) async {
    return await _dbHelper.update(
      DatabaseSchema.tableItems,
      item.toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  // Update item stock
  Future<int> updateItemStock(int itemId, double newStock) async {
    return await _dbHelper.update(
      DatabaseSchema.tableItems,
      {
        'current_stock': newStock,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Update item prices
  Future<int> updateItemPrices(
    int itemId,
    double costPrice,
    double sellingPrice,
  ) async {
    return await _dbHelper.update(
      DatabaseSchema.tableItems,
      {
        'cost_price': costPrice,
        'selling_price': sellingPrice,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Deactivate item (soft delete)
  Future<int> deactivateItem(int itemId) async {
    return await _dbHelper.update(
      DatabaseSchema.tableItems,
      {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Hard delete item (use with caution)
  Future<int> deleteItem(int itemId) async {
    return await _dbHelper.delete(
      DatabaseSchema.tableItems,
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Check if item code exists
  Future<bool> isCodeExists(String code, {int? excludeId}) async {
    String whereClause = 'code = ?';
    List<dynamic> whereArgs = [code];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.isNotEmpty;
  }

  // Check if barcode exists
  Future<bool> isBarcodeExists(String barcode, {int? excludeId}) async {
    String whereClause = 'barcode = ?';
    List<dynamic> whereArgs = [barcode];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.isNotEmpty;
  }

  // Check if item has transactions
  Future<bool> hasTransactions(int itemId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoiceLines,
      where: 'item_id = ?',
      whereArgs: [itemId],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  // Get next item code
  Future<String> getNextItemCode() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      orderBy: 'code DESC',
      limit: 1,
    );

    if (maps.isEmpty) {
      return 'I001';
    }

    String lastCode = maps.first['code'] as String;
    // Extract number from code (assuming format like I001, I002, etc.)
    RegExp regExp = RegExp(r'(\d+)$');
    Match? match = regExp.firstMatch(lastCode);

    if (match != null) {
      int lastNumber = int.parse(match.group(1)!);
      int nextNumber = lastNumber + 1;
      String prefix = lastCode.substring(0, match.start);
      return '$prefix${nextNumber.toString().padLeft(3, '0')}';
    }

    return 'I001';
  }

  // Get all categories
  Future<List<String>> getAllCategories() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableItems,
      columns: ['category'],
      where: 'category IS NOT NULL AND category != ""',
      groupBy: 'category',
      orderBy: 'category ASC',
    );
    return maps.map((map) => map['category'] as String).toList();
  }

  // Get inventory value summary
  Future<Map<String, double>> getInventoryValueSummary() async {
    final result = await _dbHelper.rawQuery(
      'SELECT SUM(current_stock * cost_price) as total_cost_value, SUM(current_stock * selling_price) as total_selling_value FROM ${DatabaseSchema.tableItems} WHERE is_active = 1',
    );

    double totalCostValue = 0.0;
    double totalSellingValue = 0.0;

    if (result.isNotEmpty) {
      totalCostValue =
          (result.first['total_cost_value'] as num?)?.toDouble() ?? 0.0;
      totalSellingValue =
          (result.first['total_selling_value'] as num?)?.toDouble() ?? 0.0;
    }

    return {
      'total_cost_value': totalCostValue,
      'total_selling_value': totalSellingValue,
      'potential_profit': totalSellingValue - totalCostValue,
    };
  }

  // Get item statistics
  Future<Map<String, dynamic>> getItemStatistics() async {
    final totalResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as total FROM ${DatabaseSchema.tableItems}',
    );

    final activeResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as active FROM ${DatabaseSchema.tableItems} WHERE is_active = 1',
    );

    final lowStockResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as low_stock FROM ${DatabaseSchema.tableItems} WHERE current_stock <= min_stock_level AND min_stock_level IS NOT NULL',
    );

    final outOfStockResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as out_of_stock FROM ${DatabaseSchema.tableItems} WHERE current_stock <= 0',
    );

    return {
      'total': totalResult.first['total'] as int,
      'active': activeResult.first['active'] as int,
      'low_stock': lowStockResult.first['low_stock'] as int,
      'out_of_stock': outOfStockResult.first['out_of_stock'] as int,
    };
  }

  // Record item movement
  Future<int> recordItemMovement(ItemMovement movement) async {
    Map<String, dynamic> movementMap = movement.toMap();
    movementMap.remove('id');
    return await _dbHelper.insert('item_movements', movementMap);
  }

  // Get item movements
  Future<List<ItemMovement>> getItemMovements(
    int itemId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    String whereClause = 'item_id = ?';
    List<dynamic> whereArgs = [itemId];

    if (startDate != null) {
      whereClause += ' AND date >= ?';
      whereArgs.add(startDate.toIso8601String().split('T')[0]);
    }

    if (endDate != null) {
      whereClause += ' AND date <= ?';
      whereArgs.add(endDate.toIso8601String().split('T')[0]);
    }

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      'item_movements',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'date DESC, id DESC',
    );

    return maps.map((map) => ItemMovement.fromMap(map)).toList();
  }

  // Get all item categories
  Future<List<String>> getItemCategories() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.rawQuery(
      'SELECT DISTINCT category FROM ${DatabaseSchema.tableItems} WHERE category IS NOT NULL AND category != "" AND is_active = 1 ORDER BY category ASC',
    );
    return maps.map((map) => map['category'] as String).toList();
  }
}
