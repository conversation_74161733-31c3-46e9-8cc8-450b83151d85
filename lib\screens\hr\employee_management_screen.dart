import 'package:flutter/material.dart';
import '../../models/employee.dart';
import '../../services/employee_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../theme/app_theme.dart';
import 'add_employee_screen.dart';
import 'employee_details_screen.dart';

/// شاشة إدارة الموظفين
/// Employee Management Screen
class EmployeeManagementScreen extends StatefulWidget {
  const EmployeeManagementScreen({super.key});

  @override
  State<EmployeeManagementScreen> createState() =>
      _EmployeeManagementScreenState();
}

class _EmployeeManagementScreenState extends State<EmployeeManagementScreen>
    with TickerProviderStateMixin {
  final EmployeeService _employeeService = EmployeeService();
  final TextEditingController _searchController = TextEditingController();

  List<Employee> _employees = [];
  List<Employee> _filteredEmployees = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';
  EmployeeStatus? _selectedStatus;
  EmployeeType? _selectedType;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadEmployees();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployees() async {
    setState(() => _isLoading = true);

    final result = await _employeeService.getAllEmployees();
    if (result.isSuccess) {
      setState(() {
        _employees = result.data!;
        _applyFilters();
        _isLoading = false;
      });
    } else {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _applyFilters() {
    List<Employee> filtered = List.from(_employees);

    // تطبيق فلتر البحث
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      filtered = filtered.where((employee) {
        return employee.fullName.toLowerCase().contains(query) ||
            employee.employeeCode.toLowerCase().contains(query) ||
            (employee.nationalId?.toLowerCase().contains(query) ?? false) ||
            (employee.email?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // تطبيق فلتر الحالة
    if (_selectedStatus != null) {
      filtered = filtered
          .where((employee) => employee.status == _selectedStatus)
          .toList();
    }

    // تطبيق فلتر نوع التوظيف
    if (_selectedType != null) {
      filtered = filtered
          .where((employee) => employee.employeeType == _selectedType)
          .toList();
    }

    // تطبيق فلتر عام
    switch (_selectedFilter) {
      case 'active':
        filtered = filtered
            .where(
              (employee) =>
                  employee.status == EmployeeStatus.active && employee.isActive,
            )
            .toList();
        break;
      case 'inactive':
        filtered = filtered
            .where(
              (employee) =>
                  !employee.isActive ||
                  employee.status != EmployeeStatus.active,
            )
            .toList();
        break;
      case 'terminated':
        filtered = filtered
            .where((employee) => employee.status == EmployeeStatus.terminated)
            .toList();
        break;
    }

    setState(() {
      _filteredEmployees = filtered;
    });
  }

  void _onSearchChanged() {
    _applyFilters();
  }

  void _onFilterChanged(String? value) {
    setState(() {
      _selectedFilter = value ?? 'all';
    });
    _applyFilters();
  }

  void _onStatusFilterChanged(EmployeeStatus? status) {
    setState(() {
      _selectedStatus = status;
    });
    _applyFilters();
  }

  void _onTypeFilterChanged(EmployeeType? type) {
    setState(() {
      _selectedType = type;
    });
    _applyFilters();
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedFilter = 'all';
      _selectedStatus = null;
      _selectedType = null;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'إدارة الموظفين',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadEmployees,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToAddEmployee(),
            tooltip: 'إضافة موظف جديد',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildSearchAndFilters(),
              _buildStatisticsCards(),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildEmployeesList(),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToAddEmployee(),
        backgroundColor: AppTheme.primaryColor,
        icon: const Icon(Icons.person_add, color: Colors.white),
        label: const Text(
          'موظف جديد',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // شريط البحث
            TextField(
              controller: _searchController,
              onChanged: (_) => _onSearchChanged(),
              decoration: InputDecoration(
                hintText: 'البحث في الموظفين...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            const SizedBox(height: 16),

            // فلاتر
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<String>(
                    value: _selectedFilter,
                    hintText: 'الفلتر العام',
                    items: const [
                      DropdownMenuItem(
                        value: 'all',
                        child: Text('جميع الموظفين'),
                      ),
                      DropdownMenuItem(value: 'active', child: Text('النشطين')),
                      DropdownMenuItem(
                        value: 'inactive',
                        child: Text('غير النشطين'),
                      ),
                      DropdownMenuItem(
                        value: 'terminated',
                        child: Text('المنتهية خدمتهم'),
                      ),
                    ],
                    onChanged: _onFilterChanged,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: QuantumDropdown<EmployeeStatus>(
                    value: _selectedStatus,
                    hintText: 'حالة الموظف',
                    items: EmployeeStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(status.displayName),
                      );
                    }).toList(),
                    onChanged: _onStatusFilterChanged,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: QuantumDropdown<EmployeeType>(
                    value: _selectedType,
                    hintText: 'نوع التوظيف',
                    items: EmployeeType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.displayName),
                      );
                    }).toList(),
                    onChanged: _onTypeFilterChanged,
                  ),
                ),
                const SizedBox(width: 12),
                QuantumButton(
                  onPressed: _clearFilters,
                  text: 'مسح الفلاتر',
                  variant: QuantumButtonVariant.outline,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCards() {
    final totalEmployees = _employees.length;
    final activeEmployees = _employees
        .where((e) => e.status == EmployeeStatus.active && e.isActive)
        .length;
    final inactiveEmployees = totalEmployees - activeEmployees;
    final averageSalary = _employees.isNotEmpty
        ? _employees.map((e) => e.basicSalary).reduce((a, b) => a + b) /
              totalEmployees
        : 0.0;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي الموظفين',
              totalEmployees.toString(),
              Icons.people,
              AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'النشطين',
              activeEmployees.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'غير النشطين',
              inactiveEmployees.toString(),
              Icons.cancel,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'متوسط الراتب',
              '${averageSalary.toStringAsFixed(0)} ر.س',
              Icons.attach_money,
              Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeesList() {
    if (_filteredEmployees.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد موظفين',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإضافة موظفين جدد أو تعديل الفلاتر',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _filteredEmployees.length,
      itemBuilder: (context, index) {
        final employee = _filteredEmployees[index];
        return _buildEmployeeCard(employee, index);
      },
    );
  }

  Widget _buildEmployeeCard(Employee employee, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: QuantumCard(
        margin: const EdgeInsets.only(bottom: 12),
        child: ListTile(
          contentPadding: const EdgeInsets.all(16),
          leading: CircleAvatar(
            backgroundColor: _getStatusColor(employee.status),
            child: Text(
              employee.firstName.isNotEmpty ? employee.firstName[0] : 'م',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          title: Text(
            employee.fullName,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text('الرمز: ${employee.employeeCode}'),
              Text('الراتب: ${employee.basicSalary.toStringAsFixed(0)} ر.س'),
              Row(
                children: [
                  _buildStatusChip(employee.status),
                  const SizedBox(width: 8),
                  _buildTypeChip(employee.employeeType),
                ],
              ),
            ],
          ),
          trailing: PopupMenuButton<String>(
            onSelected: (value) => _handleEmployeeAction(value, employee),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'view',
                child: ListTile(
                  leading: Icon(Icons.visibility),
                  title: Text('عرض'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('تعديل'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'salary',
                child: ListTile(
                  leading: Icon(Icons.attach_money),
                  title: Text('تعديل الراتب'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              if (employee.status == EmployeeStatus.active)
                const PopupMenuItem(
                  value: 'terminate',
                  child: ListTile(
                    leading: Icon(Icons.remove_circle, color: Colors.red),
                    title: Text(
                      'إنهاء الخدمة',
                      style: TextStyle(color: Colors.red),
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('حذف', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(EmployeeStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getStatusColor(status)),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: _getStatusColor(status),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTypeChip(EmployeeType type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue),
      ),
      child: Text(
        type.displayName,
        style: const TextStyle(
          color: Colors.blue,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getStatusColor(EmployeeStatus status) {
    switch (status) {
      case EmployeeStatus.active:
        return Colors.green;
      case EmployeeStatus.inactive:
        return Colors.orange;
      case EmployeeStatus.terminated:
        return Colors.red;
      case EmployeeStatus.suspended:
        return Colors.purple;
    }
  }

  void _handleEmployeeAction(String action, Employee employee) {
    switch (action) {
      case 'view':
        _navigateToEmployeeDetails(employee);
        break;
      case 'edit':
        _navigateToEditEmployee(employee);
        break;
      case 'salary':
        _showSalaryUpdateDialog(employee);
        break;
      case 'terminate':
        _showTerminationDialog(employee);
        break;
      case 'delete':
        _showDeleteConfirmation(employee);
        break;
    }
  }

  void _showSalaryUpdateDialog(Employee employee) {
    final TextEditingController salaryController = TextEditingController(
      text: employee.basicSalary.toString(),
    );
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تحديث راتب ${employee.fullName}'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'الراتب الحالي: ${employee.basicSalary.toStringAsFixed(2)} ريال',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: salaryController,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                decoration: const InputDecoration(
                  labelText: 'الراتب الجديد',
                  hintText: 'أدخل الراتب الجديد',
                  prefixIcon: Icon(Icons.attach_money),
                  suffixText: 'ريال',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الراتب مطلوب';
                  }
                  final salary = double.tryParse(value.trim());
                  if (salary == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  if (salary < 0) {
                    return 'الراتب يجب أن يكون أكبر من أو يساوي صفر';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                final newSalary = double.parse(salaryController.text.trim());
                Navigator.pop(context);
                _updateEmployeeSalary(employee, newSalary);
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.green),
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  void _showTerminationDialog(Employee employee) {
    final TextEditingController terminationDateController =
        TextEditingController();
    final TextEditingController reasonController = TextEditingController();
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    DateTime? selectedDate;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إنهاء خدمة ${employee.fullName}'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'تاريخ التوظيف: ${employee.hireDate.toString().split(' ')[0]}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: terminationDateController,
                readOnly: true,
                decoration: const InputDecoration(
                  labelText: 'تاريخ إنهاء الخدمة',
                  hintText: 'اختر تاريخ إنهاء الخدمة',
                  prefixIcon: Icon(Icons.calendar_today),
                  border: OutlineInputBorder(),
                ),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: employee.hireDate,
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    selectedDate = date;
                    terminationDateController.text = date.toString().split(
                      ' ',
                    )[0];
                  }
                },
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'تاريخ إنهاء الخدمة مطلوب';
                  }
                  if (selectedDate == null) {
                    return 'يرجى اختيار تاريخ صحيح';
                  }
                  if (selectedDate!.isBefore(employee.hireDate)) {
                    return 'تاريخ إنهاء الخدمة لا يمكن أن يكون قبل تاريخ التوظيف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: reasonController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'سبب إنهاء الخدمة (اختياري)',
                  hintText: 'أدخل سبب إنهاء الخدمة',
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context);
                _terminateEmployee(
                  employee,
                  selectedDate!,
                  reasonController.text.trim(),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: const Text('إنهاء الخدمة'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف ${employee.fullName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEmployee(employee);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteEmployee(Employee employee) async {
    final result = await _employeeService.deleteEmployee(employee.id!);
    if (!mounted) return;

    if (result.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف الموظف بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      _loadEmployees();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result.error!), backgroundColor: Colors.red),
      );
    }
  }

  Future<void> _updateEmployeeSalary(
    Employee employee,
    double newSalary,
  ) async {
    final result = await _employeeService.updateEmployeeSalary(
      employee.employeeCode,
      newSalary,
    );
    if (!mounted) return;

    if (result.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم تحديث راتب ${employee.fullName} إلى ${newSalary.toStringAsFixed(2)} ريال',
          ),
          backgroundColor: Colors.green,
        ),
      );
      _loadEmployees(); // Reload the employees list to reflect the changes
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result.error!), backgroundColor: Colors.red),
      );
    }
  }

  Future<void> _terminateEmployee(
    Employee employee,
    DateTime terminationDate,
    String reason,
  ) async {
    final result = await _employeeService.terminateEmployee(
      employee.employeeCode,
      terminationDate,
    );
    if (!mounted) return;

    if (result.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنهاء خدمة الموظف ${employee.fullName} بنجاح'),
          backgroundColor: Colors.orange,
        ),
      );
      _loadEmployees();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result.error!), backgroundColor: Colors.red),
      );
    }
  }

  /// Navigate to add employee screen
  Future<void> _navigateToAddEmployee() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => const AddEmployeeScreen()),
    );

    if (result == true) {
      _loadEmployees();
    }
  }

  /// Navigate to employee details screen
  Future<void> _navigateToEmployeeDetails(Employee employee) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EmployeeDetailsScreen(employee: employee),
      ),
    );
  }

  /// Navigate to edit employee screen
  Future<void> _navigateToEditEmployee(Employee employee) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => AddEmployeeScreen(employee: employee),
      ),
    );

    if (result == true) {
      _loadEmployees();
    }
  }
}
