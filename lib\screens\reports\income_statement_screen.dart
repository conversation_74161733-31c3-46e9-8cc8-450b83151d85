import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../theme/app_theme.dart';
import '../../models/financial_reports.dart';
import '../../services/financial_reports_service.dart';

/// شاشة قائمة الدخل
class IncomeStatementScreen extends StatefulWidget {
  const IncomeStatementScreen({super.key});

  @override
  State<IncomeStatementScreen> createState() => _IncomeStatementScreenState();
}

class _IncomeStatementScreenState extends State<IncomeStatementScreen>
    with TickerProviderStateMixin {
  final FinancialReportsService _reportsService = FinancialReportsService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  IncomeStatementReport? _report;
  bool _isLoading = false;
  String? _error;
  DateTime _startDate = DateTime(DateTime.now().year, 1, 1);
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadIncomeStatement();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _loadIncomeStatement() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final result = await _reportsService.generateIncomeStatement(
        startDate: _startDate,
        endDate: _endDate,
      );

      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _report = result.data;
            _isLoading = false;
          });
        } else {
          setState(() {
            _error = result.error;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل قائمة الدخل: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadIncomeStatement();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          '📈 قائمة الدخل',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
            tooltip: 'اختيار الفترة',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadIncomeStatement,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(opacity: _fadeAnimation, child: _buildBody());
        },
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // شريط الفترة والمعلومات
        _buildPeriodHeader(),

        // المحتوى الرئيسي
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildPeriodHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange,
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'قائمة الدخل للفترة من ${_formatDate(_startDate)} إلى ${_formatDate(_endDate)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (_report != null) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildHeaderStat(
                  'إجمالي الإيرادات',
                  _report!.totalRevenue,
                  Icons.trending_up,
                ),
                _buildHeaderStat(
                  'إجمالي المصروفات',
                  _report!.totalExpenses,
                  Icons.trending_down,
                ),
                _buildHeaderStat(
                  'صافي الدخل',
                  _report!.netIncome,
                  _report!.netIncome >= 0 ? Icons.check_circle : Icons.error,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, double value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        Text(
          '${value.toStringAsFixed(2)} ر.س',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.orange),
            SizedBox(height: 16),
            Text('جاري إنشاء قائمة الدخل...', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadIncomeStatement,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_report == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد بيانات لعرضها', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          children: [
            // قسم الإيرادات
            AnimationConfiguration.staggeredList(
              position: 0,
              duration: const Duration(milliseconds: 600),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(child: _buildRevenueSection()),
              ),
            ),

            const SizedBox(height: 20),

            // قسم المصروفات
            AnimationConfiguration.staggeredList(
              position: 1,
              duration: const Duration(milliseconds: 600),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(child: _buildExpensesSection()),
              ),
            ),

            const SizedBox(height: 20),

            // ملخص صافي الدخل
            AnimationConfiguration.staggeredList(
              position: 2,
              duration: const Duration(milliseconds: 600),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(child: _buildNetIncomeSummary()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس قسم الإيرادات
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.trending_up, color: Colors.green[700], size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الإيرادات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_report!.totalRevenue.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),

          // بنود الإيرادات
          ..._report!.revenueItems.map(
            (item) => _buildIncomeStatementItem(item, Colors.green),
          ),
        ],
      ),
    );
  }

  Widget _buildExpensesSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس قسم المصروفات
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.trending_down, color: Colors.red[700], size: 24),
                const SizedBox(width: 8),
                const Text(
                  'المصروفات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_report!.totalExpenses.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
          ),

          // بنود المصروفات
          ..._report!.expenseItems.map(
            (item) => _buildIncomeStatementItem(item, Colors.red),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatementItem(IncomeStatementItem item, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              item.accountName,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
          Text(
            '${item.amount.toStringAsFixed(2)} ر.س',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetIncomeSummary() {
    final isProfit = _report!.netIncome >= 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            isProfit ? Colors.green : Colors.red,
            (isProfit ? Colors.green : Colors.red).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (isProfit ? Colors.green : Colors.red).withValues(
              alpha: 0.3,
            ),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isProfit ? Icons.trending_up : Icons.trending_down,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                isProfit ? 'ربح صافي' : 'خسارة صافية',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${_report!.netIncome.abs().toStringAsFixed(2)} ر.س',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSummaryCard('إجمالي الإيرادات', _report!.totalRevenue),
              _buildSummaryCard('إجمالي المصروفات', _report!.totalExpenses),
              _buildSummaryCard('هامش الربح', _report!.profitMargin),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String label, double value) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        const SizedBox(height: 4),
        Text(
          label == 'هامش الربح'
              ? '${value.toStringAsFixed(1)}%'
              : '${value.toStringAsFixed(2)} ر.س',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
