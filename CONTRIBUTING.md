# 🤝 دليل المساهمة في Smart Ledger
## Contributing Guide

مرحباً بك في مجتمع Smart Ledger! نحن نقدر مساهماتك ونريد أن نجعل عملية المساهمة سهلة وممتعة للجميع.

## 📋 **جدول المحتويات**

- [قواعد السلوك](#قواعد-السلوك)
- [كيفية المساهمة](#كيفية-المساهمة)
- [إرشادات الكود](#إرشادات-الكود)
- [عملية المراجعة](#عملية-المراجعة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [طلب ميزات جديدة](#طلب-ميزات-جديدة)

## 🤝 **قواعد السلوك**

### التزاماتنا

- **الاحترام المتبادل**: نعامل جميع المساهمين باحترام
- **التعاون البناء**: نقدم ملاحظات بناءة ومفيدة
- **الشمولية**: نرحب بالجميع بغض النظر عن الخلفية
- **التعلم المستمر**: نساعد بعضنا البعض على التطور

### السلوكيات المقبولة

✅ استخدام لغة ترحيبية وشاملة  
✅ احترام وجهات النظر المختلفة  
✅ قبول النقد البناء بصدر رحب  
✅ التركيز على ما هو أفضل للمجتمع  
✅ إظهار التعاطف مع أعضاء المجتمع الآخرين  

### السلوكيات غير المقبولة

❌ استخدام لغة أو صور جنسية  
❌ التنمر أو التعليقات المهينة  
❌ المضايقة العامة أو الخاصة  
❌ نشر معلومات خاصة للآخرين دون إذن  
❌ أي سلوك غير مهني أو غير مناسب  

## 🚀 **كيفية المساهمة**

### 1. إعداد البيئة التطويرية

```bash
# استنساخ المشروع
git clone https://github.com/your-username/smart_ledger.git
cd smart_ledger

# تثبيت التبعيات
flutter pub get

# تشغيل الاختبارات للتأكد من سلامة الإعداد
flutter test
```

### 2. إنشاء فرع جديد

```bash
# إنشاء فرع للميزة الجديدة
git checkout -b feature/your-feature-name

# أو للإصلاح
git checkout -b fix/your-fix-name
```

### 3. تطوير التغييرات

- اكتب كود نظيف ومفهوم
- أضف تعليقات باللغة العربية للوضوح
- اتبع إرشادات الكود المذكورة أدناه
- أضف اختبارات للميزات الجديدة

### 4. اختبار التغييرات

```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل تحليل الكود
flutter analyze

# تنسيق الكود
dart format .
```

### 5. إرسال Pull Request

- اكتب عنوان واضح ووصفي
- اشرح التغييرات التي قمت بها
- أرفق لقطات شاشة إذا كانت التغييرات تؤثر على UI
- تأكد من مرور جميع الاختبارات

## 📝 **إرشادات الكود**

### هيكل الملفات

```
lib/
├── models/           # نماذج البيانات
├── services/         # الخدمات والمنطق التجاري
├── dao/             # طبقة الوصول للبيانات
├── screens/         # شاشات التطبيق
├── widgets/         # المكونات المخصصة
├── theme/           # نظام الألوان والتصميم
└── utils/           # الأدوات المساعدة
```

### تسمية الملفات والمتغيرات

```dart
// أسماء الملفات: snake_case
customer_service.dart
invoice_screen.dart

// أسماء الكلاسات: PascalCase
class CustomerService {}
class InvoiceScreen extends StatefulWidget {}

// أسماء المتغيرات والدوال: camelCase
String customerName;
void calculateTotal() {}

// الثوابت: SCREAMING_SNAKE_CASE
const String API_BASE_URL = 'https://api.example.com';
```

### التعليقات والتوثيق

```dart
/// خدمة إدارة العملاء
/// Customer Management Service
class CustomerService {
  /// إضافة عميل جديد
  /// Add a new customer
  Future<Result<Customer>> addCustomer(Customer customer) async {
    // التحقق من صحة البيانات
    // Validate customer data
    if (customer.name.isEmpty) {
      return Result.error('اسم العميل مطلوب');
    }
    
    // حفظ العميل في قاعدة البيانات
    // Save customer to database
    return await _customerDao.insert(customer);
  }
}
```

### معالجة الأخطاء

```dart
// استخدم Result pattern لمعالجة الأخطاء
Future<Result<List<Customer>>> getCustomers() async {
  try {
    final customers = await _customerDao.getAll();
    return Result.success(customers);
  } catch (e) {
    return Result.error('خطأ في جلب بيانات العملاء: ${e.toString()}');
  }
}
```

### تصميم UI

```dart
// استخدم Material Design 3
class CustomerCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: Icon(Icons.person),
        ),
        title: Text(
          customer.name,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        subtitle: Text(customer.email),
        trailing: IconButton(
          icon: Icon(Icons.edit),
          onPressed: () => _editCustomer(customer),
        ),
      ),
    );
  }
}
```

## 🔍 **عملية المراجعة**

### معايير المراجعة

1. **الوظائف**: هل الكود يعمل كما هو متوقع؟
2. **الأداء**: هل الكود محسن للأداء؟
3. **الأمان**: هل هناك مشاكل أمنية؟
4. **القابلية للقراءة**: هل الكود واضح ومفهوم؟
5. **الاختبارات**: هل تم إضافة اختبارات كافية؟

### عملية المراجعة

1. **المراجعة التلقائية**: CI/CD يتحقق من الاختبارات والتنسيق
2. **مراجعة الكود**: مراجع واحد على الأقل يراجع التغييرات
3. **اختبار يدوي**: اختبار الميزات الجديدة يدوياً
4. **الموافقة**: موافقة المراجع قبل الدمج

## 🐛 **الإبلاغ عن الأخطاء**

### قبل الإبلاغ

- تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
- جرب إعادة إنتاج الخطأ
- اجمع معلومات النظام والجهاز

### معلومات مطلوبة

```markdown
**وصف الخطأ**
وصف واضح ومختصر للخطأ.

**خطوات إعادة الإنتاج**
1. اذهب إلى '...'
2. اضغط على '....'
3. مرر إلى أسفل إلى '....'
4. شاهد الخطأ

**السلوك المتوقع**
وصف واضح لما كنت تتوقع حدوثه.

**لقطات الشاشة**
إذا كان ذلك مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

**معلومات النظام:**
- نظام التشغيل: [مثل iOS, Android, Windows]
- إصدار التطبيق: [مثل 1.0.0]
- إصدار Flutter: [مثل 3.8.1]
```

## ✨ **طلب ميزات جديدة**

### قبل الطلب

- تأكد من أن الميزة لم يتم طلبها مسبقاً
- فكر في كيفية تناسب الميزة مع رؤية التطبيق
- اعتبر التأثير على المستخدمين الحاليين

### معلومات مطلوبة

```markdown
**هل طلبك مرتبط بمشكلة؟**
وصف واضح للمشكلة. مثال: أشعر بالإحباط عندما [...]

**وصف الحل المطلوب**
وصف واضح ومختصر لما تريده أن يحدث.

**وصف البدائل المعتبرة**
وصف واضح لأي حلول أو ميزات بديلة فكرت فيها.

**سياق إضافي**
أضف أي سياق أو لقطات شاشة أخرى حول طلب الميزة هنا.
```

## 🏷️ **أنواع المساهمات**

### 🐛 إصلاح الأخطاء
- إصلاح مشاكل في الكود الحالي
- تحسين الأداء
- إصلاح مشاكل UI/UX

### ✨ ميزات جديدة
- إضافة وظائف جديدة
- تحسين الميزات الموجودة
- إضافة تكاملات جديدة

### 📚 التوثيق
- تحسين README
- إضافة أمثلة للكود
- ترجمة التوثيق

### 🧪 الاختبارات
- إضافة اختبارات وحدة
- اختبارات التكامل
- اختبارات الأداء

### 🎨 التصميم
- تحسين UI/UX
- إضافة رسوم متحركة
- تحسين إمكانية الوصول

## 📞 **الحصول على المساعدة**

إذا كنت بحاجة إلى مساعدة:

- 💬 **Discord**: [رابط الخادم]
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **تليجرام**: [@smartledger_dev]
- 🐛 **GitHub Issues**: لطرح الأسئلة التقنية

## 🙏 **شكراً لك!**

مساهمتك تجعل Smart Ledger أفضل للجميع. نقدر وقتك وجهدك! 

---

**ملاحظة**: هذا الدليل قابل للتطوير. إذا كان لديك اقتراحات لتحسينه، لا تتردد في فتح issue أو pull request.
