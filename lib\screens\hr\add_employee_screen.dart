import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/employee.dart';
import '../../models/department.dart';
import '../../services/employee_service.dart';
import '../../dao/department_dao.dart';
import '../../dao/position_dao.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../theme/app_theme.dart';
import '../../utils/result.dart';

/// شاشة إضافة/تعديل الموظفين
/// Add/Edit Employee Screen
class AddEmployeeScreen extends StatefulWidget {
  final Employee? employee;

  const AddEmployeeScreen({super.key, this.employee});

  @override
  State<AddEmployeeScreen> createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends State<AddEmployeeScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final EmployeeService _employeeService = EmployeeService();
  final DepartmentDao _departmentDao = DepartmentDao();
  final PositionDao _positionDao = PositionDao();

  // Controllers
  late TextEditingController _employeeCodeController;
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _middleNameController;
  late TextEditingController _nationalIdController;
  late TextEditingController _passportController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _addressController;
  late TextEditingController _emergencyContactController;
  late TextEditingController _emergencyPhoneController;
  late TextEditingController _basicSalaryController;
  late TextEditingController _bankAccountController;
  late TextEditingController _bankNameController;
  late TextEditingController _ibanController;
  late TextEditingController _notesController;

  // State variables
  bool _isLoading = false;
  bool _isLoadingData = true;
  List<Department> _departments = [];
  List<Position> _positions = [];
  List<Employee> _managers = [];

  // Selected values
  int? _selectedDepartmentId;
  int? _selectedPositionId;
  String? _selectedManagerId;
  EmployeeGender _selectedGender = EmployeeGender.male;
  EmployeeMaritalStatus _selectedMaritalStatus = EmployeeMaritalStatus.single;
  EmployeeStatus _selectedStatus = EmployeeStatus.active;
  EmployeeType _selectedType = EmployeeType.fullTime;
  DateTime? _selectedBirthDate;
  DateTime _selectedHireDate = DateTime.now();
  bool _isActive = true;

  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeControllers();
    _loadData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  void _initializeControllers() {
    final employee = widget.employee;

    _employeeCodeController = TextEditingController(
      text: employee?.employeeCode ?? '',
    );
    _firstNameController = TextEditingController(
      text: employee?.firstName ?? '',
    );
    _lastNameController = TextEditingController(text: employee?.lastName ?? '');
    _middleNameController = TextEditingController(
      text: employee?.middleName ?? '',
    );
    _nationalIdController = TextEditingController(
      text: employee?.nationalId ?? '',
    );
    _passportController = TextEditingController(
      text: employee?.passportNumber ?? '',
    );
    _phoneController = TextEditingController(text: employee?.phone ?? '');
    _emailController = TextEditingController(text: employee?.email ?? '');
    _addressController = TextEditingController(text: employee?.address ?? '');
    _emergencyContactController = TextEditingController(
      text: employee?.emergencyContact ?? '',
    );
    _emergencyPhoneController = TextEditingController(
      text: employee?.emergencyPhone ?? '',
    );
    _basicSalaryController = TextEditingController(
      text: employee?.basicSalary.toString() ?? '',
    );
    _bankAccountController = TextEditingController(
      text: employee?.bankAccount ?? '',
    );
    _bankNameController = TextEditingController(text: employee?.bankName ?? '');
    _ibanController = TextEditingController(text: employee?.iban ?? '');
    _notesController = TextEditingController(text: employee?.notes ?? '');

    if (employee != null) {
      _selectedDepartmentId = employee.departmentId;
      _selectedPositionId = employee.positionId;
      _selectedManagerId = employee.directManagerId;
      _selectedGender = employee.gender;
      _selectedMaritalStatus = employee.maritalStatus;
      _selectedStatus = employee.status;
      _selectedType = employee.employeeType;
      _selectedBirthDate = employee.birthDate;
      _selectedHireDate = employee.hireDate;
      _isActive = employee.isActive;
    } else {
      _generateEmployeeCode();
    }
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoadingData = true);

      // Load departments
      final departments = await _departmentDao.getActiveDepartments();

      // Load all employees for manager selection
      final employeesResult = await _employeeService.getAllEmployees();

      setState(() {
        _departments = departments;
        if (employeesResult.isSuccess) {
          _managers = employeesResult.data!;
        }
        _isLoadingData = false;
      });

      // Load positions for selected department
      if (_selectedDepartmentId != null) {
        await _loadPositionsForDepartment(_selectedDepartmentId!);
      }
    } catch (e) {
      setState(() => _isLoadingData = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: ${e.toString()}');
    }
  }

  Future<void> _loadPositionsForDepartment(int departmentId) async {
    try {
      final positions = await _positionDao.getPositionsByDepartment(
        departmentId,
      );
      setState(() {
        _positions = positions;
        // Reset position selection if current position doesn't belong to new department
        if (_selectedPositionId != null) {
          final positionExists = positions.any(
            (p) => p.id == _selectedPositionId,
          );
          if (!positionExists) {
            _selectedPositionId = null;
          }
        }
      });
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل المناصب: ${e.toString()}');
    }
  }

  Future<void> _generateEmployeeCode() async {
    final result = await _employeeService.generateEmployeeCode();
    if (result.isSuccess) {
      setState(() {
        _employeeCodeController.text = result.data!;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedDepartmentId == null) {
      _showErrorSnackBar('يرجى اختيار القسم');
      return;
    }

    if (_selectedPositionId == null) {
      _showErrorSnackBar('يرجى اختيار المنصب');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final employee = Employee(
        id: widget.employee?.id,
        employeeCode: _employeeCodeController.text.trim(),
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        middleName: _middleNameController.text.trim().isEmpty
            ? null
            : _middleNameController.text.trim(),
        nationalId: _nationalIdController.text.trim().isEmpty
            ? null
            : _nationalIdController.text.trim(),
        passportNumber: _passportController.text.trim().isEmpty
            ? null
            : _passportController.text.trim(),
        birthDate: _selectedBirthDate,
        gender: _selectedGender,
        maritalStatus: _selectedMaritalStatus,
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        emergencyContact: _emergencyContactController.text.trim().isEmpty
            ? null
            : _emergencyContactController.text.trim(),
        emergencyPhone: _emergencyPhoneController.text.trim().isEmpty
            ? null
            : _emergencyPhoneController.text.trim(),
        departmentId: _selectedDepartmentId!,
        positionId: _selectedPositionId!,
        hireDate: _selectedHireDate,
        status: _selectedStatus,
        employeeType: _selectedType,
        directManagerId: _selectedManagerId,
        basicSalary: double.tryParse(_basicSalaryController.text) ?? 0.0,
        bankAccount: _bankAccountController.text.trim().isEmpty
            ? null
            : _bankAccountController.text.trim(),
        bankName: _bankNameController.text.trim().isEmpty
            ? null
            : _bankNameController.text.trim(),
        iban: _ibanController.text.trim().isEmpty
            ? null
            : _ibanController.text.trim(),
        isActive: _isActive,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      Result<Employee> result;
      if (widget.employee == null) {
        result = await _employeeService.createEmployee(employee);
      } else {
        result = await _employeeService.updateEmployee(employee);
      }

      if (result.isSuccess) {
        _showSuccessSnackBar(
          widget.employee == null
              ? 'تم إضافة الموظف بنجاح'
              : 'تم تحديث الموظف بنجاح',
        );
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      } else {
        _showErrorSnackBar(result.error!);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الموظف: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          widget.employee == null ? 'إضافة موظف جديد' : 'تعديل الموظف',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoadingData
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Form(
                  key: _formKey,
                  child: ListView(
                    padding: const EdgeInsets.all(16.0),
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 16),
                      _buildPersonalInfoSection(),
                      const SizedBox(height: 16),
                      _buildJobInfoSection(),
                      const SizedBox(height: 16),
                      _buildSalaryInfoSection(),
                      const SizedBox(height: 16),
                      _buildContactInfoSection(),
                      const SizedBox(height: 16),
                      _buildNotesSection(),
                      const SizedBox(height: 32),
                      _buildActionButtons(),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _employeeCodeController,
                    labelText: 'رمز الموظف',
                    prefixIcon: Icons.badge,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'رمز الموظف مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<EmployeeStatus>(
                    value: _selectedStatus,
                    labelText: 'الحالة',
                    prefixIcon: Icons.info,
                    items: EmployeeStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusText(status)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedStatus = value!);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _firstNameController,
                    labelText: 'الاسم الأول',
                    prefixIcon: Icons.person,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'الاسم الأول مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _middleNameController,
                    labelText: 'الاسم الأوسط',
                    prefixIcon: Icons.person_outline,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _lastNameController,
              labelText: 'اسم العائلة',
              prefixIcon: Icons.family_restroom,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم العائلة مطلوب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الشخصية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _nationalIdController,
                    labelText: 'رقم الهوية الوطنية',
                    prefixIcon: Icons.credit_card,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _passportController,
                    labelText: 'رقم جواز السفر',
                    prefixIcon: Icons.flight,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<EmployeeGender>(
                    value: _selectedGender,
                    labelText: 'الجنس',
                    prefixIcon: Icons.wc,
                    items: EmployeeGender.values.map((gender) {
                      return DropdownMenuItem(
                        value: gender,
                        child: Text(_getGenderText(gender)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedGender = value!);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<EmployeeMaritalStatus>(
                    value: _selectedMaritalStatus,
                    labelText: 'الحالة الاجتماعية',
                    prefixIcon: Icons.favorite,
                    items: EmployeeMaritalStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(_getMaritalStatusText(status)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedMaritalStatus = value!);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectBirthDate(),
              child: QuantumTextField(
                controller: TextEditingController(
                  text: _selectedBirthDate != null
                      ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                      : '',
                ),
                labelText: 'تاريخ الميلاد',
                prefixIcon: Icons.cake,
                readOnly: true,
                onTap: _selectBirthDate,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobInfoSection() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الوظيفة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<int>(
                    value: _selectedDepartmentId,
                    labelText: 'القسم',
                    prefixIcon: Icons.business,
                    items: _departments.map((department) {
                      return DropdownMenuItem(
                        value: department.id,
                        child: Text(department.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedDepartmentId = value;
                        _selectedPositionId = null;
                        _positions.clear();
                      });
                      if (value != null) {
                        _loadPositionsForDepartment(value);
                      }
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'القسم مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<int>(
                    value: _selectedPositionId,
                    labelText: 'المنصب',
                    prefixIcon: Icons.work,
                    items: _positions.map((position) {
                      return DropdownMenuItem(
                        value: position.id,
                        child: Text(position.title),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedPositionId = value);
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'المنصب مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<EmployeeType>(
                    value: _selectedType,
                    labelText: 'نوع التوظيف',
                    prefixIcon: Icons.schedule,
                    items: EmployeeType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(_getEmployeeTypeText(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedType = value!);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<String>(
                    value: _selectedManagerId,
                    labelText: 'المدير المباشر',
                    prefixIcon: Icons.supervisor_account,
                    items: _managers.map((manager) {
                      return DropdownMenuItem(
                        value: manager.employeeCode,
                        child: Text('${manager.firstName} ${manager.lastName}'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedManagerId = value);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () => _selectHireDate(),
              child: QuantumTextField(
                controller: TextEditingController(
                  text:
                      '${_selectedHireDate.day}/${_selectedHireDate.month}/${_selectedHireDate.year}',
                ),
                labelText: 'تاريخ التوظيف',
                prefixIcon: Icons.date_range,
                readOnly: true,
                onTap: _selectHireDate,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryInfoSection() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الراتب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _basicSalaryController,
              labelText: 'الراتب الأساسي',
              prefixIcon: Icons.attach_money,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الراتب الأساسي مطلوب';
                }
                final salary = double.tryParse(value);
                if (salary == null || salary < 0) {
                  return 'يرجى إدخال راتب صحيح';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoSection() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الاتصال',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _phoneController,
                    labelText: 'رقم الهاتف',
                    prefixIcon: Icons.phone,
                    keyboardType: TextInputType.phone,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _emailController,
                    labelText: 'البريد الإلكتروني',
                    prefixIcon: Icons.email,
                    keyboardType: TextInputType.emailAddress,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _addressController,
              labelText: 'العنوان',
              prefixIcon: Icons.location_on,
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _emergencyContactController,
                    labelText: 'جهة الاتصال في الطوارئ',
                    prefixIcon: Icons.emergency,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _emergencyPhoneController,
                    labelText: 'رقم الطوارئ',
                    prefixIcon: Icons.phone_in_talk,
                    keyboardType: TextInputType.phone,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _bankNameController,
                    labelText: 'اسم البنك',
                    prefixIcon: Icons.account_balance,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _bankAccountController,
                    labelText: 'رقم الحساب البنكي',
                    prefixIcon: Icons.credit_card,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _ibanController,
              labelText: 'رقم الآيبان (IBAN)',
              prefixIcon: Icons.account_balance_wallet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _notesController,
              labelText: 'ملاحظات إضافية',
              prefixIcon: Icons.note,
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Switch(
                  value: _isActive,
                  onChanged: (value) {
                    setState(() => _isActive = value);
                  },
                  activeColor: AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'الموظف نشط',
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: QuantumButton(
            text: 'إلغاء',
            variant: QuantumButtonVariant.secondary,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: QuantumButton(
            text: widget.employee == null ? 'إضافة الموظف' : 'تحديث الموظف',
            variant: QuantumButtonVariant.primary,
            isLoading: _isLoading,
            onPressed: _isLoading ? null : _saveEmployee,
          ),
        ),
      ],
    );
  }

  // Helper methods for text conversion
  String _getStatusText(EmployeeStatus status) {
    switch (status) {
      case EmployeeStatus.active:
        return 'نشط';
      case EmployeeStatus.inactive:
        return 'غير نشط';
      case EmployeeStatus.terminated:
        return 'منتهي الخدمة';
      case EmployeeStatus.suspended:
        return 'موقوف';
    }
  }

  String _getGenderText(EmployeeGender gender) {
    switch (gender) {
      case EmployeeGender.male:
        return 'ذكر';
      case EmployeeGender.female:
        return 'أنثى';
    }
  }

  String _getMaritalStatusText(EmployeeMaritalStatus status) {
    switch (status) {
      case EmployeeMaritalStatus.single:
        return 'أعزب';
      case EmployeeMaritalStatus.married:
        return 'متزوج';
      case EmployeeMaritalStatus.divorced:
        return 'مطلق';
      case EmployeeMaritalStatus.widowed:
        return 'أرمل';
    }
  }

  String _getEmployeeTypeText(EmployeeType type) {
    switch (type) {
      case EmployeeType.fullTime:
        return 'دوام كامل';
      case EmployeeType.partTime:
        return 'دوام جزئي';
      case EmployeeType.contract:
        return 'تعاقد';
      case EmployeeType.intern:
        return 'متدرب';
      case EmployeeType.consultant:
        return 'استشاري';
    }
  }

  // Date selection methods
  Future<void> _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedBirthDate ??
          DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 80)),
      lastDate: DateTime.now().subtract(const Duration(days: 365 * 16)),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
    }
  }

  Future<void> _selectHireDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedHireDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _selectedHireDate) {
      setState(() {
        _selectedHireDate = picked;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _employeeCodeController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _middleNameController.dispose();
    _nationalIdController.dispose();
    _passportController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _emergencyContactController.dispose();
    _emergencyPhoneController.dispose();
    _basicSalaryController.dispose();
    _bankAccountController.dispose();
    _bankNameController.dispose();
    _ibanController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
