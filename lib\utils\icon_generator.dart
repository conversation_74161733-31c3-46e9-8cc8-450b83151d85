import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 🎨 مولد أيقونات التطبيق
/// App Icon Generator
class IconGenerator {
  /// إنشاء أيقونة التطبيق
  static Future<Uint8List> generateAppIcon({
    required int size,
    Color primaryColor = const Color(0xFF1565C0),
    Color secondaryColor = const Color(0xFF42A5F5),
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint();

    // رسم الخلفية المتدرجة
    final gradient = ui.Gradient.radial(
      Offset(size / 2, size / 2),
      size / 2,
      [
        secondaryColor,
        primaryColor,
        primaryColor.withValues(alpha: 0.8),
      ],
      [0.0, 0.7, 1.0],
    );

    paint.shader = gradient;
    canvas.drawCircle(
      Offset(size / 2, size / 2),
      size / 2,
      paint,
    );

    // رسم الحدود
    paint.shader = null;
    paint.color = Colors.white.withValues(alpha: 0.3);
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = size * 0.02;
    canvas.drawCircle(
      Offset(size / 2, size / 2),
      size / 2 - paint.strokeWidth / 2,
      paint,
    );

    // رسم خطوط الطاقة
    paint.style = PaintingStyle.fill;
    paint.color = Colors.white.withValues(alpha: 0.6);
    
    for (int i = 0; i < 8; i++) {
      final angle = (i * math.pi * 2) / 8;
      final startX = size / 2 + math.cos(angle) * (size * 0.25);
      final startY = size / 2 + math.sin(angle) * (size * 0.25);
      final endX = size / 2 + math.cos(angle) * (size * 0.4);
      final endY = size / 2 + math.sin(angle) * (size * 0.4);
      
      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        Paint()
          ..color = Colors.white.withValues(alpha: 0.6)
          ..strokeWidth = size * 0.01
          ..strokeCap = StrokeCap.round,
      );
    }

    // رسم الرمز الرئيسي (محفظة)
    _drawWalletIcon(canvas, size, Colors.white);

    // رسم رمز الذكاء
    final aiIconSize = size * 0.2;
    final aiX = size * 0.7;
    final aiY = size * 0.3;
    
    paint.color = const Color(0xFFFFD700);
    canvas.drawCircle(
      Offset(aiX, aiY),
      aiIconSize / 2,
      paint,
    );

    paint.color = Colors.white;
    paint.strokeWidth = size * 0.005;
    paint.style = PaintingStyle.stroke;
    canvas.drawCircle(
      Offset(aiX, aiY),
      aiIconSize / 2,
      paint,
    );

    // رسم نجمة الذكاء
    _drawStarIcon(canvas, aiX, aiY, aiIconSize * 0.4, primaryColor);

    final picture = recorder.endRecording();
    final img = await picture.toImage(size, size);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }

  /// رسم أيقونة المحفظة
  static void _drawWalletIcon(Canvas canvas, int size, Color color) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size / 2, size / 2);
    final walletSize = size * 0.4;

    // رسم جسم المحفظة
    final walletRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: center,
        width: walletSize,
        height: walletSize * 0.7,
      ),
      Radius.circular(walletSize * 0.1),
    );
    
    canvas.drawRRect(walletRect, paint);

    // رسم الجيب العلوي
    final pocketRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(center.dx, center.dy - walletSize * 0.15),
        width: walletSize * 0.8,
        height: walletSize * 0.3,
      ),
      Radius.circular(walletSize * 0.05),
    );
    
    paint.color = color.withValues(alpha: 0.7);
    canvas.drawRRect(pocketRect, paint);

    // رسم خطوط التفاصيل
    paint.color = color.withValues(alpha: 0.5);
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = size * 0.005;
    
    for (int i = 0; i < 3; i++) {
      final y = center.dy + (i - 1) * walletSize * 0.1;
      canvas.drawLine(
        Offset(center.dx - walletSize * 0.3, y),
        Offset(center.dx + walletSize * 0.3, y),
        paint,
      );
    }
  }

  /// رسم أيقونة النجمة
  static void _drawStarIcon(Canvas canvas, double x, double y, double size, Color color) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final center = Offset(x, y);
    final outerRadius = size / 2;
    final innerRadius = outerRadius * 0.4;

    for (int i = 0; i < 10; i++) {
      final angle = (i * math.pi) / 5;
      final radius = i.isEven ? outerRadius : innerRadius;
      final pointX = center.dx + math.cos(angle - math.pi / 2) * radius;
      final pointY = center.dy + math.sin(angle - math.pi / 2) * radius;

      if (i == 0) {
        path.moveTo(pointX, pointY);
      } else {
        path.lineTo(pointX, pointY);
      }
    }
    
    path.close();
    canvas.drawPath(path, paint);
  }

  /// إنشاء أيقونة مربعة للأندرويد
  static Future<Uint8List> generateSquareIcon({
    required int size,
    Color primaryColor = const Color(0xFF1565C0),
    Color secondaryColor = const Color(0xFF42A5F5),
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint();

    // رسم الخلفية المتدرجة
    final gradient = ui.Gradient.linear(
      const Offset(0, 0),
      Offset(size.toDouble(), size.toDouble()),
      [
        primaryColor,
        secondaryColor,
      ],
    );

    paint.shader = gradient;
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
      Radius.circular(size * 0.2),
    );
    canvas.drawRRect(rect, paint);

    // رسم الرمز الرئيسي
    paint.shader = null;
    _drawWalletIcon(canvas, size, Colors.white);

    // رسم رمز الذكاء في الزاوية
    final aiSize = size * 0.25;
    final aiX = size * 0.8;
    final aiY = size * 0.2;
    
    paint.color = const Color(0xFFFFD700);
    canvas.drawCircle(
      Offset(aiX, aiY),
      aiSize / 2,
      paint,
    );

    _drawStarIcon(canvas, aiX, aiY, aiSize * 0.6, primaryColor);

    final picture = recorder.endRecording();
    final img = await picture.toImage(size, size);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }

  /// إنشاء أيقونة للإشعارات
  static Future<Uint8List> generateNotificationIcon({
    required int size,
    Color color = Colors.white,
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // رسم خلفية شفافة
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
      Paint()..color = Colors.transparent,
    );

    // رسم الرمز البسيط
    _drawWalletIcon(canvas, size, color);

    final picture = recorder.endRecording();
    final img = await picture.toImage(size, size);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
}

/// 🎨 معاينة الأيقونة
/// Icon Preview Widget
class IconPreview extends StatelessWidget {
  final int size;
  final Color primaryColor;
  final Color secondaryColor;

  const IconPreview({
    super.key,
    this.size = 120,
    this.primaryColor = const Color(0xFF1565C0),
    this.secondaryColor = const Color(0xFF42A5F5),
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List>(
      future: IconGenerator.generateAppIcon(
        size: size,
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
      ),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Image.memory(
            snapshot.data!,
            width: size.toDouble(),
            height: size.toDouble(),
          );
        }
        return Container(
          width: size.toDouble(),
          height: size.toDouble(),
          decoration: BoxDecoration(
            color: Colors.grey[300],
            shape: BoxShape.circle,
          ),
          child: const Icon(Icons.image),
        );
      },
    );
  }
}
