import '../models/invoice.dart';
import '../models/journal_entry.dart';
import '../models/stock_movement.dart';
import '../models/cost_calculation.dart';
import '../utils/result.dart';
import '../database/invoice_dao.dart';
import 'journal_entry_service.dart';
import 'warehouse_service.dart';
import 'tax_service.dart';
import 'account_service.dart' as account_service;
import 'item_service.dart' as item_service;
import 'cost_calculation_service.dart';

/// خدمة الفواتير المتقدمة مع التكامل التلقائي
/// Advanced Invoice Service with Automatic Integration
class AdvancedInvoiceService {
  final InvoiceDao _invoiceDao = InvoiceDao();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final WarehouseService _warehouseService = WarehouseService();
  final TaxService _taxService = TaxService();
  final account_service.AccountService _accountService =
      account_service.AccountService();
  final item_service.ItemService _itemService = item_service.ItemService();
  final CostCalculationService _costCalculationService =
      CostCalculationService();

  /// إنشاء فاتورة مع التكامل التلقائي الكامل
  Future<Result<Invoice>> createInvoiceWithIntegration(Invoice invoice) async {
    try {
      // 1. التحقق من صحة البيانات
      final validation = await _validateInvoice(invoice);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      // 2. حساب الضرائب التلقائي
      final taxCalculation = await _calculateTaxes(invoice);
      if (!taxCalculation.isSuccess) {
        return Result.error(taxCalculation.error!);
      }

      // تحديث الفاتورة بالضرائب المحسوبة
      invoice = invoice.copyWith(
        taxAmount: taxCalculation.data!['totalTax'],
        totalAmount: taxCalculation.data!['grandTotal'],
      );

      // 3. إنشاء الفاتورة في قاعدة البيانات
      final invoiceResult = await _createInvoiceRecord(invoice);
      if (!invoiceResult.isSuccess) {
        return Result.error(invoiceResult.error!);
      }

      final createdInvoice = invoiceResult.data!;

      // 4. إنشاء القيد المحاسبي التلقائي
      final journalEntryResult = await _createJournalEntryForInvoice(
        createdInvoice,
      );
      if (!journalEntryResult.isSuccess) {
        // إذا فشل إنشاء القيد، احذف الفاتورة
        await _invoiceDao.deleteInvoice(createdInvoice.id!);
        return Result.error(
          'فشل في إنشاء القيد المحاسبي: ${journalEntryResult.error}',
        );
      }

      // 5. تحديث المخزون (للمبيعات فقط)
      if (invoice.invoiceType == InvoiceType.sales) {
        final stockResult = await _updateStockForSalesInvoice(createdInvoice);
        if (!stockResult.isSuccess) {
          // إذا فشل تحديث المخزون، احذف الفاتورة والقيد
          await _invoiceDao.deleteInvoice(createdInvoice.id!);
          await _journalEntryService.deleteJournalEntry(
            journalEntryResult.data!.id!,
          );
          return Result.error('فشل في تحديث المخزون: ${stockResult.error}');
        }
      }

      // 6. تحديث الفاتورة بمعرف القيد المحاسبي
      final updatedInvoice = createdInvoice.copyWith(
        journalEntryId: journalEntryResult.data!.id,
      );
      await _invoiceDao.updateInvoice(updatedInvoice);

      return Result.success(updatedInvoice);
    } catch (e) {
      return Result.error('خطأ في إنشاء الفاتورة: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات الفاتورة
  Future<Result<bool>> _validateInvoice(Invoice invoice) async {
    try {
      // التحقق من وجود العميل أو المورد
      if (invoice.invoiceType == InvoiceType.sales &&
          invoice.customerId == null) {
        return Result.error('يجب تحديد العميل لفاتورة المبيعات');
      }
      if (invoice.invoiceType == InvoiceType.purchase &&
          invoice.supplierId == null) {
        return Result.error('يجب تحديد المورد لفاتورة المشتريات');
      }

      // التحقق من وجود بنود الفاتورة
      if (invoice.lines.isEmpty) {
        return Result.error('يجب إضافة بند واحد على الأقل للفاتورة');
      }

      // التحقق من صحة بنود الفاتورة
      for (final line in invoice.lines) {
        if (line.quantity <= 0) {
          return Result.error('الكمية يجب أن تكون أكبر من صفر');
        }
        if (line.unitPrice < 0) {
          return Result.error('سعر الوحدة لا يمكن أن يكون سالباً');
        }

        // التحقق من وجود الصنف
        final itemResult = await _itemService.getItemById(line.itemId);
        if (!itemResult.isSuccess) {
          return Result.error('الصنف ${line.itemId} غير موجود');
        }
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في التحقق من صحة الفاتورة: ${e.toString()}');
    }
  }

  /// حساب الضرائب التلقائي
  Future<Result<Map<String, double>>> _calculateTaxes(Invoice invoice) async {
    try {
      double subtotal = 0.0;

      // حساب المجموع الفرعي
      for (final line in invoice.lines) {
        final lineTotal = line.quantity * line.unitPrice;
        subtotal += lineTotal;
      }

      // إنشاء فاتورة مؤقتة مع المجموع الفرعي لحساب الضرائب
      final tempInvoice = invoice.copyWith(subtotal: subtotal);

      // استخدام خدمة الضرائب لحساب الضرائب
      final taxCalculationResult = await _taxService.calculateInvoiceTaxes(
        tempInvoice,
      );

      if (!taxCalculationResult.isSuccess) {
        // في حالة فشل حساب الضرائب، استخدم الحساب الافتراضي
        final totalTax = subtotal * 0.15; // ضريبة القيمة المضافة 15%
        final grandTotal = subtotal + totalTax - invoice.discountAmount;

        return Result.success({
          'subtotal': subtotal,
          'totalTax': totalTax,
          'grandTotal': grandTotal,
        });
      }

      final taxData = taxCalculationResult.data!;
      final totalTax = taxData['totalTax'] as double;
      final grandTotal = subtotal + totalTax - invoice.discountAmount;

      return Result.success({
        'subtotal': subtotal,
        'totalTax': totalTax,
        'grandTotal': grandTotal,
      });
    } catch (e) {
      return Result.error('خطأ في حساب الضرائب: ${e.toString()}');
    }
  }

  /// إنشاء سجل الفاتورة في قاعدة البيانات
  Future<Result<Invoice>> _createInvoiceRecord(Invoice invoice) async {
    try {
      final id = await _invoiceDao.insertInvoice(invoice);
      final createdInvoice = invoice.copyWith(id: id);
      return Result.success(createdInvoice);
    } catch (e) {
      return Result.error('خطأ في حفظ الفاتورة: ${e.toString()}');
    }
  }

  /// إنشاء القيد المحاسبي للفاتورة
  Future<Result<JournalEntry>> _createJournalEntryForInvoice(
    Invoice invoice,
  ) async {
    try {
      final lines = <JournalEntryLine>[];

      if (invoice.invoiceType == InvoiceType.sales) {
        // فاتورة مبيعات
        // مدين: العملاء
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: await _getCustomersAccountId(),
            description: 'فاتورة مبيعات ${invoice.invoiceNumber}',
            debitAmount: invoice.totalAmount,
            creditAmount: 0.0,
            lineOrder: 1,
          ),
        );

        // دائن: المبيعات
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: await _getSalesAccountId(),
            description: 'فاتورة مبيعات ${invoice.invoiceNumber}',
            debitAmount: 0.0,
            creditAmount: invoice.subtotal,
            lineOrder: 2,
          ),
        );

        // دائن: ضريبة القيمة المضافة (إن وجدت)
        if (invoice.taxAmount > 0) {
          lines.add(
            JournalEntryLine(
              journalEntryId: 0,
              accountId: await _getVATAccountId(),
              description:
                  'ضريبة القيمة المضافة - فاتورة ${invoice.invoiceNumber}',
              debitAmount: 0.0,
              creditAmount: invoice.taxAmount,
              lineOrder: 3,
            ),
          );
        }
      } else {
        // فاتورة مشتريات
        // مدين: المشتريات
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: await _getPurchasesAccountId(),
            description: 'فاتورة مشتريات ${invoice.invoiceNumber}',
            debitAmount: invoice.subtotal,
            creditAmount: 0.0,
            lineOrder: 1,
          ),
        );

        // مدين: ضريبة القيمة المضافة (إن وجدت)
        if (invoice.taxAmount > 0) {
          lines.add(
            JournalEntryLine(
              journalEntryId: 0,
              accountId: await _getVATAccountId(),
              description:
                  'ضريبة القيمة المضافة - فاتورة ${invoice.invoiceNumber}',
              debitAmount: invoice.taxAmount,
              creditAmount: 0.0,
              lineOrder: 2,
            ),
          );
        }

        // دائن: الموردين
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: await _getSuppliersAccountId(),
            description: 'فاتورة مشتريات ${invoice.invoiceNumber}',
            debitAmount: 0.0,
            creditAmount: invoice.totalAmount,
            lineOrder: 3,
          ),
        );
      }

      final journalEntry = JournalEntry(
        entryNumber: await _generateJournalEntryNumber(),
        date: invoice.date,
        description: 'قيد فاتورة ${invoice.invoiceNumber}',
        reference: invoice.invoiceNumber,
        lines: lines,
      );

      final result = await _journalEntryService.createJournalEntry(
        journalEntry,
      );
      return Result.success(result.data!);
    } catch (e) {
      return Result.error('خطأ في إنشاء القيد المحاسبي: ${e.toString()}');
    }
  }

  /// تحديث المخزون لفاتورة المبيعات
  Future<Result<bool>> _updateStockForSalesInvoice(Invoice invoice) async {
    try {
      for (final line in invoice.lines) {
        // حساب تكلفة الصنف باستخدام خدمة حساب التكلفة
        final costResult = await _costCalculationService.calculateCost(
          itemId: line.itemId,
          warehouseId: 1, // المخزن الافتراضي - يمكن تحسينه لاحقاً
          quantity: line.quantity,
          method: CostingMethod.weightedAverage, // يمكن جعلها قابلة للتخصيص
        );

        double unitCost = 0.0;
        double totalCost = 0.0;

        if (costResult.isSuccess) {
          unitCost = costResult.data!.averageCost;
          totalCost = costResult.data!.totalCost;
        } else {
          // في حالة فشل حساب التكلفة، استخدم سعر البيع كتكلفة تقديرية
          unitCost = line.unitPrice * 0.7; // تقدير 70% من سعر البيع
          totalCost = unitCost * line.quantity;
        }

        // إنشاء حركة مخزون للبيع
        final stockMovement = StockMovement(
          documentNumber: 'SALE-${invoice.invoiceNumber}',
          type: MovementType.issue,
          reason: MovementReason.sale,
          itemId: line.itemId,
          warehouseId: 1, // المخزن الافتراضي - يمكن تحسينه لاحقاً
          quantity: line.quantity,
          unitCost: unitCost,
          totalCost: totalCost,
          movementDate: invoice.date,
          referenceDocument: invoice.invoiceNumber,
          notes: 'بيع - فاتورة ${invoice.invoiceNumber}',
        );

        final result = await _warehouseService.addStockMovement(stockMovement);
        if (!result.isSuccess) {
          return Result.error(
            'فشل في تحديث مخزون الصنف ${line.itemId}: ${result.error}',
          );
        }
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في تحديث المخزون: ${e.toString()}');
    }
  }

  /// الحصول على معرف حساب العملاء
  Future<int> _getCustomersAccountId() async {
    try {
      final result = await _accountService.getAccountByCode('1201');
      if (result.isSuccess) {
        return result.data!.id!;
      }
      // في حالة عدم وجود الحساب، استخدم القيمة الافتراضية
      return 1201;
    } catch (e) {
      // في حالة حدوث خطأ، استخدم القيمة الافتراضية
      return 1201;
    }
  }

  /// الحصول على معرف حساب المبيعات
  Future<int> _getSalesAccountId() async {
    try {
      final result = await _accountService.getAccountByCode('4001');
      if (result.isSuccess) {
        return result.data!.id!;
      }
      // في حالة عدم وجود الحساب، استخدم القيمة الافتراضية
      return 4001;
    } catch (e) {
      // في حالة حدوث خطأ، استخدم القيمة الافتراضية
      return 4001;
    }
  }

  /// الحصول على معرف حساب ضريبة القيمة المضافة
  Future<int> _getVATAccountId() async {
    try {
      final result = await _accountService.getAccountByCode('2301');
      if (result.isSuccess) {
        return result.data!.id!;
      }
      // في حالة عدم وجود الحساب، استخدم القيمة الافتراضية
      return 2301;
    } catch (e) {
      // في حالة حدوث خطأ، استخدم القيمة الافتراضية
      return 2301;
    }
  }

  /// الحصول على معرف حساب الموردين
  Future<int> _getSuppliersAccountId() async {
    try {
      final result = await _accountService.getAccountByCode('2101');
      if (result.isSuccess) {
        return result.data!.id!;
      }
      // في حالة عدم وجود الحساب، استخدم القيمة الافتراضية
      return 2101;
    } catch (e) {
      // في حالة حدوث خطأ، استخدم القيمة الافتراضية
      return 2101;
    }
  }

  /// الحصول على معرف حساب المشتريات
  Future<int> _getPurchasesAccountId() async {
    try {
      final result = await _accountService.getAccountByCode('5001');
      if (result.isSuccess) {
        return result.data!.id!;
      }
      // في حالة عدم وجود الحساب، استخدم القيمة الافتراضية
      return 5001;
    } catch (e) {
      // في حالة حدوث خطأ، استخدم القيمة الافتراضية
      return 5001;
    }
  }

  /// توليد رقم قيد محاسبي
  Future<String> _generateJournalEntryNumber() async {
    // يمكن تحسين هذا بجعله أكثر ذكاءً
    final now = DateTime.now();
    return 'JE${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch % 10000}';
  }

  /// إلغاء فاتورة مع عكس جميع العمليات
  Future<Result<bool>> cancelInvoiceWithReversal(
    int invoiceId,
    String reason,
  ) async {
    try {
      // الحصول على الفاتورة
      final invoice = await _invoiceDao.getInvoiceById(invoiceId);
      if (invoice == null) {
        return Result.error('الفاتورة غير موجودة');
      }

      if (invoice.status == InvoiceStatus.cancelled) {
        return Result.error('الفاتورة ملغاة مسبقاً');
      }

      // عكس القيد المحاسبي
      if (invoice.journalEntryId != null) {
        final reversalResult = await _journalEntryService.unpostJournalEntry(
          invoice.journalEntryId!,
        );
        if (!reversalResult.isSuccess) {
          return Result.error(
            'فشل في عكس القيد المحاسبي: ${reversalResult.error}',
          );
        }
      }

      // عكس حركات المخزون (للمبيعات فقط)
      if (invoice.invoiceType == InvoiceType.sales) {
        final stockReversalResult = await _reverseStockMovements(invoice);
        if (!stockReversalResult.isSuccess) {
          return Result.error(
            'فشل في عكس حركات المخزون: ${stockReversalResult.error}',
          );
        }
      }

      // إلغاء الفاتورة
      await _invoiceDao.cancelInvoice(invoiceId);

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إلغاء الفاتورة: ${e.toString()}');
    }
  }

  /// عكس حركات المخزون
  Future<Result<bool>> _reverseStockMovements(Invoice invoice) async {
    try {
      for (final line in invoice.lines) {
        // إنشاء حركة مخزون عكسية
        final reversalMovement = StockMovement(
          documentNumber: 'REV-${invoice.invoiceNumber}',
          type: MovementType.adjustment,
          reason: MovementReason.adjustment,
          itemId: line.itemId,
          warehouseId: 1, // المخزن الافتراضي
          quantity: line.quantity, // موجب لإرجاع الكمية
          unitCost: 0.0,
          totalCost: 0.0,
          movementDate: DateTime.now(),
          referenceDocument: 'REV-${invoice.invoiceNumber}',
          notes: 'عكس بيع - إلغاء فاتورة ${invoice.invoiceNumber}',
        );

        final result = await _warehouseService.addStockMovement(
          reversalMovement,
        );
        if (!result.isSuccess) {
          return Result.error(
            'فشل في عكس حركة مخزون الصنف ${line.itemId}: ${result.error}',
          );
        }
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في عكس حركات المخزون: ${e.toString()}');
    }
  }
}
