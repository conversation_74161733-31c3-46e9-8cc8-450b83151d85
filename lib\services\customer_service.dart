import '../models/customer.dart';
import '../database/customer_dao.dart';

/// نتيجة العملية
class Result<T> {
  final bool isSuccess;
  final T? data;
  final String? error;

  Result.success(this.data) : isSuccess = true, error = null;
  Result.error(this.error) : isSuccess = false, data = null;
}

/// خدمة إدارة العملاء
class CustomerService {
  final CustomerDao _customerDao = CustomerDao();

  /// الحصول على جميع العملاء
  Future<Result<List<Customer>>> getAllCustomers() async {
    try {
      final customers = await _customerDao.getAllCustomers();
      return Result.success(customers);
    } catch (e) {
      return Result.error('خطأ في جلب العملاء: ${e.toString()}');
    }
  }

  /// الحصول على عميل بالمعرف
  Future<Result<Customer>> getCustomerById(int id) async {
    try {
      final customer = await _customerDao.getCustomerById(id);
      if (customer != null) {
        return Result.success(customer);
      } else {
        return Result.error('العميل غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب العميل: ${e.toString()}');
    }
  }

  /// الحصول على عميل بالرمز
  Future<Result<Customer>> getCustomerByCode(String code) async {
    try {
      final customer = await _customerDao.getCustomerByCode(code);
      if (customer != null) {
        return Result.success(customer);
      } else {
        return Result.error('العميل غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب العميل: ${e.toString()}');
    }
  }

  /// إنشاء عميل جديد
  Future<Result<Customer>> createCustomer(Customer customer) async {
    try {
      // التحقق من عدم تكرار الرمز
      final existingCustomer = await _customerDao.getCustomerByCode(customer.code);
      if (existingCustomer != null) {
        return Result.error('رمز العميل موجود مسبقاً');
      }

      final id = await _customerDao.insertCustomer(customer);
      final newCustomer = customer.copyWith(id: id);
      return Result.success(newCustomer);
    } catch (e) {
      return Result.error('خطأ في إنشاء العميل: ${e.toString()}');
    }
  }

  /// تحديث عميل
  Future<Result<Customer>> updateCustomer(Customer customer) async {
    try {
      if (customer.id == null) {
        return Result.error('معرف العميل مطلوب للتحديث');
      }

      // التحقق من عدم تكرار الرمز مع عملاء آخرين
      final existingCustomer = await _customerDao.getCustomerByCode(customer.code);
      if (existingCustomer != null && existingCustomer.id != customer.id) {
        return Result.error('رمز العميل موجود مسبقاً');
      }

      await _customerDao.updateCustomer(customer);
      return Result.success(customer);
    } catch (e) {
      return Result.error('خطأ في تحديث العميل: ${e.toString()}');
    }
  }

  /// حذف عميل
  Future<Result<void>> deleteCustomer(int id) async {
    try {
      // التحقق من وجود فواتير للعميل
      final hasInvoices = await _customerDao.hasInvoices(id);
      if (hasInvoices) {
        return Result.error('لا يمكن حذف العميل لوجود فواتير مرتبطة به');
      }

      await _customerDao.deleteCustomer(id);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف العميل: ${e.toString()}');
    }
  }

  /// البحث في العملاء
  Future<Result<List<Customer>>> searchCustomers(String query) async {
    try {
      final customers = await _customerDao.searchCustomers(query);
      return Result.success(customers);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  /// الحصول على العملاء النشطين
  Future<Result<List<Customer>>> getActiveCustomers() async {
    try {
      final customers = await _customerDao.getActiveCustomers();
      return Result.success(customers);
    } catch (e) {
      return Result.error('خطأ في جلب العملاء النشطين: ${e.toString()}');
    }
  }

  /// الحصول على رمز العميل التالي
  Future<Result<String>> getNextCustomerCode() async {
    try {
      final code = await _customerDao.getNextCustomerCode();
      return Result.success(code);
    } catch (e) {
      return Result.error('خطأ في توليد رمز العميل: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات العميل
  Future<Result<Map<String, dynamic>>> getCustomerStatistics(int customerId) async {
    try {
      final stats = await _customerDao.getCustomerStatistics(customerId);
      return Result.success(stats);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات العميل: ${e.toString()}');
    }
  }

  /// الحصول على رصيد العميل
  Future<Result<double>> getCustomerBalance(int customerId) async {
    try {
      final balance = await _customerDao.getCustomerBalance(customerId);
      return Result.success(balance);
    } catch (e) {
      return Result.error('خطأ في جلب رصيد العميل: ${e.toString()}');
    }
  }

  /// تحديث رصيد العميل
  Future<Result<void>> updateCustomerBalance(int customerId, double amount) async {
    try {
      await _customerDao.updateCustomerBalance(customerId, amount);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث رصيد العميل: ${e.toString()}');
    }
  }

  /// الحصول على العملاء مع أرصدة مستحقة
  Future<Result<List<Customer>>> getCustomersWithOutstandingBalance() async {
    try {
      final customers = await _customerDao.getCustomersWithOutstandingBalance();
      return Result.success(customers);
    } catch (e) {
      return Result.error('خطأ في جلب العملاء مع الأرصدة المستحقة: ${e.toString()}');
    }
  }

  /// الحصول على أفضل العملاء (حسب المبيعات)
  Future<Result<List<Customer>>> getTopCustomers({int limit = 10}) async {
    try {
      final customers = await _customerDao.getTopCustomers(limit: limit);
      return Result.success(customers);
    } catch (e) {
      return Result.error('خطأ في جلب أفضل العملاء: ${e.toString()}');
    }
  }

  /// تفعيل/إلغاء تفعيل العميل
  Future<Result<void>> toggleCustomerStatus(int customerId) async {
    try {
      await _customerDao.toggleCustomerStatus(customerId);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تغيير حالة العميل: ${e.toString()}');
    }
  }

  /// الحصول على عدد العملاء
  Future<Result<int>> getCustomersCount() async {
    try {
      final count = await _customerDao.getCustomersCount();
      return Result.success(count);
    } catch (e) {
      return Result.error('خطأ في جلب عدد العملاء: ${e.toString()}');
    }
  }

  /// الحصول على إجمالي مبيعات العملاء
  Future<Result<double>> getTotalCustomerSales() async {
    try {
      final total = await _customerDao.getTotalCustomerSales();
      return Result.success(total);
    } catch (e) {
      return Result.error('خطأ في جلب إجمالي المبيعات: ${e.toString()}');
    }
  }
}
