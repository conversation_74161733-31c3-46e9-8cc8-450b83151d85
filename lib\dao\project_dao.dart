/// طبقة الوصول للبيانات للمشاريع
/// Project Data Access Object for Smart Ledger
library;

import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/project.dart';
import '../models/project_cost.dart';
import 'customer_dao.dart';

class ProjectDao {
  static final ProjectDao _instance = ProjectDao._internal();
  factory ProjectDao() => _instance;
  ProjectDao._internal();

  /// إنشاء مشروع جديد
  Future<int> createProject(Project project) async {
    final db = await DatabaseHelper().database;
    return await db.insert(
      DatabaseSchema.tableProjects,
      project.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث مشروع
  Future<int> updateProject(Project project) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      DatabaseSchema.tableProjects,
      project.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [project.id],
    );
  }

  /// حذف مشروع
  Future<int> deleteProject(int id) async {
    final db = await DatabaseHelper().database;
    return await db.delete(
      DatabaseSchema.tableProjects,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على مشروع بالمعرف
  Future<Project?> getProjectById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjects,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final project = Project.fromMap(maps.first);
      await _loadProjectRelations(project);
      return project;
    }
    return null;
  }

  /// الحصول على مشروع بالكود
  Future<Project?> getProjectByCode(String code) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjects,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      final project = Project.fromMap(maps.first);
      await _loadProjectRelations(project);
      return project;
    }
    return null;
  }

  /// الحصول على جميع المشاريع
  Future<List<Project>> getAllProjects({
    bool activeOnly = false,
    ProjectStatus? status,
    int? customerId,
    String? searchTerm,
    int? limit,
    int? offset,
  }) async {
    final db = await DatabaseHelper().database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (activeOnly) {
      whereClause += 'is_active = 1';
    }

    if (status != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'status = ?';
      whereArgs.add(status.value);
    }

    if (customerId != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'customer_id = ?';
      whereArgs.add(customerId);
    }

    if (searchTerm != null && searchTerm.isNotEmpty) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += '(name LIKE ? OR code LIKE ? OR description LIKE ?)';
      final searchPattern = '%$searchTerm%';
      whereArgs.addAll([searchPattern, searchPattern, searchPattern]);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjects,
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    final projects = <Project>[];
    for (final map in maps) {
      final project = Project.fromMap(map);
      await _loadProjectRelations(project);
      projects.add(project);
    }

    return projects;
  }

  /// الحصول على المشاريع النشطة
  Future<List<Project>> getActiveProjects() async {
    return await getAllProjects(activeOnly: true, status: ProjectStatus.active);
  }

  /// الحصول على المشاريع المكتملة
  Future<List<Project>> getCompletedProjects() async {
    return await getAllProjects(status: ProjectStatus.completed);
  }

  /// الحصول على المشاريع المتأخرة
  Future<List<Project>> getOverdueProjects() async {
    final db = await DatabaseHelper().database;
    final now = DateTime.now().toIso8601String().split('T')[0];

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjects,
      where: 'end_date < ? AND status NOT IN (?, ?)',
      whereArgs: [
        now,
        ProjectStatus.completed.value,
        ProjectStatus.cancelled.value,
      ],
      orderBy: 'end_date ASC',
    );

    final projects = <Project>[];
    for (final map in maps) {
      final project = Project.fromMap(map);
      await _loadProjectRelations(project);
      projects.add(project);
    }

    return projects;
  }

  /// إنشاء مرحلة مشروع
  Future<int> createProjectPhase(ProjectPhase phase) async {
    final db = await DatabaseHelper().database;
    return await db.insert(
      DatabaseSchema.tableProjectPhases,
      phase.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث مرحلة مشروع
  Future<int> updateProjectPhase(ProjectPhase phase) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      DatabaseSchema.tableProjectPhases,
      phase.toMap(),
      where: 'id = ?',
      whereArgs: [phase.id],
    );
  }

  /// حذف مرحلة مشروع
  Future<int> deleteProjectPhase(int id) async {
    final db = await DatabaseHelper().database;
    return await db.delete(
      DatabaseSchema.tableProjectPhases,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على مراحل المشروع
  Future<List<ProjectPhase>> getProjectPhases(int projectId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjectPhases,
      where: 'project_id = ?',
      whereArgs: [projectId],
      orderBy: 'start_date ASC',
    );

    return maps.map((map) => ProjectPhase.fromMap(map)).toList();
  }

  /// إنشاء تكلفة مشروع
  Future<int> createProjectCost(ProjectCost cost) async {
    final db = await DatabaseHelper().database;
    return await db.insert(
      DatabaseSchema.tableProjectCosts,
      cost.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث تكلفة مشروع
  Future<int> updateProjectCost(ProjectCost cost) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      DatabaseSchema.tableProjectCosts,
      cost.toMap(),
      where: 'id = ?',
      whereArgs: [cost.id],
    );
  }

  /// حذف تكلفة مشروع
  Future<int> deleteProjectCost(int id) async {
    final db = await DatabaseHelper().database;
    return await db.delete(
      DatabaseSchema.tableProjectCosts,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على تكاليف المشروع
  Future<List<ProjectCost>> getProjectCosts(
    int projectId, {
    int? phaseId,
  }) async {
    final db = await DatabaseHelper().database;

    String whereClause = 'project_id = ?';
    List<dynamic> whereArgs = [projectId];

    if (phaseId != null) {
      whereClause += ' AND phase_id = ?';
      whereArgs.add(phaseId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjectCosts,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'date DESC',
    );

    return maps.map((map) => ProjectCost.fromMap(map)).toList();
  }

  /// حساب إجمالي تكاليف المشروع
  Future<double> calculateProjectTotalCost(int projectId) async {
    final db = await DatabaseHelper().database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM ${DatabaseSchema.tableProjectCosts} WHERE project_id = ?',
      [projectId],
    );

    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  /// حساب تكاليف المشروع حسب النوع
  Future<Map<String, double>> calculateProjectCostsByType(int projectId) async {
    final db = await DatabaseHelper().database;
    final result = await db.rawQuery(
      'SELECT cost_type, SUM(amount) as total FROM ${DatabaseSchema.tableProjectCosts} WHERE project_id = ? GROUP BY cost_type',
      [projectId],
    );

    final costsByType = <String, double>{};
    for (final row in result) {
      costsByType[row['cost_type'] as String] = (row['total'] as num)
          .toDouble();
    }

    return costsByType;
  }

  /// تحميل العلاقات للمشروع
  Future<void> _loadProjectRelations(Project project) async {
    if (project.id == null) return;

    // تحميل العميل
    if (project.customerId != null) {
      final customerDao = CustomerDao();
      final result = await customerDao.getCustomerById(project.customerId!);
      if (result.isSuccess && result.data != null) {
        project.customer = result.data;
      }
    }

    // تحميل المراحل
    project.phases = await getProjectPhases(project.id!);

    // تحميل التكاليف
    project.costs = await getProjectCosts(project.id!);
  }

  /// البحث في المشاريع
  Future<List<Project>> searchProjects(String searchTerm) async {
    return await getAllProjects(searchTerm: searchTerm);
  }

  // ==================== إدارة مهام المشروع ====================

  /// إنشاء مهمة جديدة
  Future<int> createProjectTask(ProjectTask task) async {
    final db = await DatabaseHelper().database;
    return await db.insert(
      DatabaseSchema.tableProjectTasks,
      task.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث مهمة
  Future<int> updateProjectTask(ProjectTask task) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      DatabaseSchema.tableProjectTasks,
      task.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [task.id],
    );
  }

  /// حذف مهمة
  Future<int> deleteProjectTask(int id) async {
    final db = await DatabaseHelper().database;
    return await db.delete(
      DatabaseSchema.tableProjectTasks,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على مهمة بالمعرف
  Future<ProjectTask?> getProjectTaskById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjectTasks,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return ProjectTask.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على مهام المشروع
  Future<List<ProjectTask>> getProjectTasks(
    int projectId, {
    int? phaseId,
    TaskStatus? status,
    TaskPriority? priority,
    int? assignedTo,
    String? searchTerm,
  }) async {
    final db = await DatabaseHelper().database;

    String whereClause = 'project_id = ?';
    List<dynamic> whereArgs = [projectId];

    if (phaseId != null) {
      whereClause += ' AND phase_id = ?';
      whereArgs.add(phaseId);
    }

    if (status != null) {
      whereClause += ' AND status = ?';
      whereArgs.add(status.value);
    }

    if (priority != null) {
      whereClause += ' AND priority = ?';
      whereArgs.add(priority.value);
    }

    if (assignedTo != null) {
      whereClause += ' AND assigned_to = ?';
      whereArgs.add(assignedTo);
    }

    if (searchTerm != null && searchTerm.isNotEmpty) {
      whereClause += ' AND (name LIKE ? OR description LIKE ?)';
      whereArgs.add('%$searchTerm%');
      whereArgs.add('%$searchTerm%');
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjectTasks,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => ProjectTask.fromMap(map)).toList();
  }

  /// الحصول على مهام المرحلة
  Future<List<ProjectTask>> getPhaseTasks(int phaseId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableProjectTasks,
      where: 'phase_id = ?',
      whereArgs: [phaseId],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => ProjectTask.fromMap(map)).toList();
  }

  /// تحديث حالة المهمة
  Future<int> updateTaskStatus(int taskId, TaskStatus status) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      DatabaseSchema.tableProjectTasks,
      {
        'status': status.value,
        'updated_at': DateTime.now().toIso8601String(),
        if (status == TaskStatus.completed)
          'actual_end_date': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [taskId],
    );
  }

  /// تحديث الساعات الفعلية للمهمة
  Future<int> updateTaskActualHours(int taskId, double actualHours) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      DatabaseSchema.tableProjectTasks,
      {
        'actual_hours': actualHours,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [taskId],
    );
  }

  /// الحصول على إحصائيات مهام المشروع
  Future<Map<String, dynamic>> getProjectTasksStatistics(int projectId) async {
    final db = await DatabaseHelper().database;

    final result = await db.rawQuery(
      '''
      SELECT
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status = 'not_started' THEN 1 ELSE 0 END) as not_started,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'on_hold' THEN 1 ELSE 0 END) as on_hold,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
        SUM(estimated_hours) as total_estimated_hours,
        SUM(actual_hours) as total_actual_hours,
        AVG(CASE WHEN status = 'completed' THEN
          CASE WHEN estimated_hours > 0 THEN (actual_hours / estimated_hours) * 100 ELSE 0 END
        ELSE NULL END) as avg_completion_efficiency
      FROM ${DatabaseSchema.tableProjectTasks}
      WHERE project_id = ?
    ''',
      [projectId],
    );

    if (result.isNotEmpty) {
      return result.first;
    }

    return {
      'total_tasks': 0,
      'not_started': 0,
      'in_progress': 0,
      'completed': 0,
      'on_hold': 0,
      'cancelled': 0,
      'total_estimated_hours': 0.0,
      'total_actual_hours': 0.0,
      'avg_completion_efficiency': 0.0,
    };
  }

  /// الحصول على إحصائيات المشاريع
  Future<Map<String, dynamic>> getProjectStatistics() async {
    final db = await DatabaseHelper().database;

    final totalProjects = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableProjects}',
    );

    final activeProjects = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableProjects} WHERE status = ?',
      [ProjectStatus.active.value],
    );

    final completedProjects = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableProjects} WHERE status = ?',
      [ProjectStatus.completed.value],
    );

    final totalBudget = await db.rawQuery(
      'SELECT SUM(budget_amount) as total FROM ${DatabaseSchema.tableProjects}',
    );

    final totalActualCost = await db.rawQuery(
      'SELECT SUM(actual_cost) as total FROM ${DatabaseSchema.tableProjects}',
    );

    return {
      'totalProjects': (totalProjects.first['count'] as int),
      'activeProjects': (activeProjects.first['count'] as int),
      'completedProjects': (completedProjects.first['count'] as int),
      'totalBudget': (totalBudget.first['total'] as num?)?.toDouble() ?? 0.0,
      'totalActualCost':
          (totalActualCost.first['total'] as num?)?.toDouble() ?? 0.0,
    };
  }
}
