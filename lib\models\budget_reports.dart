/// نماذج تقارير الموازنات والتخطيط المالي
/// Budget Reports Models for Smart Ledger
library;


import 'account.dart';

/// تقرير الموازنة مقابل الفعلي
class BudgetVsActualReport {
  final DateTime startDate;
  final DateTime endDate;
  final List<BudgetVsActualItem> items;
  final BudgetVsActualSummary summary;

  BudgetVsActualReport({
    required this.startDate,
    required this.endDate,
    required this.items,
    required this.summary,
  });

  /// تجميع البنود حسب نوع الحساب
  Map<AccountType, List<BudgetVsActualItem>> get itemsByAccountType {
    Map<AccountType, List<BudgetVsActualItem>> grouped = {};
    for (var item in items) {
      if (item.account != null) {
        grouped.putIfAbsent(item.account!.accountType, () => []).add(item);
      }
    }
    return grouped;
  }

  /// حساب إجمالي الانحراف حسب نوع الحساب
  Map<AccountType, double> get varianceByAccountType {
    Map<AccountType, double> variances = {};
    for (var entry in itemsByAccountType.entries) {
      variances[entry.key] = entry.value.fold(0.0, (sum, item) => sum + item.variance);
    }
    return variances;
  }
}

/// بند تقرير الموازنة مقابل الفعلي
class BudgetVsActualItem {
  final String accountCode;
  final String accountName;
  final AccountType accountType;
  final double budgetAmount;
  final double actualAmount;
  final double variance;
  final double variancePercentage;
  final Account? account;

  BudgetVsActualItem({
    required this.accountCode,
    required this.accountName,
    required this.accountType,
    required this.budgetAmount,
    required this.actualAmount,
    required this.variance,
    required this.variancePercentage,
    this.account,
  });

  /// التحقق من وجود انحراف سلبي
  bool get hasNegativeVariance => variance < 0;

  /// التحقق من وجود انحراف إيجابي
  bool get hasPositiveVariance => variance > 0;

  /// التحقق من عدم وجود انحراف
  bool get hasNoVariance => variance.abs() < 0.01;

  /// حساب نسبة التنفيذ
  double get executionPercentage {
    if (budgetAmount == 0) return 0.0;
    return (actualAmount / budgetAmount) * 100;
  }
}

/// ملخص تقرير الموازنة مقابل الفعلي
class BudgetVsActualSummary {
  final double totalBudgetAmount;
  final double totalActualAmount;
  final double totalVariance;
  final double totalVariancePercentage;
  final int favorableVariances;
  final int unfavorableVariances;
  final int noVariances;

  BudgetVsActualSummary({
    required this.totalBudgetAmount,
    required this.totalActualAmount,
    required this.totalVariance,
    required this.totalVariancePercentage,
    required this.favorableVariances,
    required this.unfavorableVariances,
    required this.noVariances,
  });

  /// حساب نسبة التنفيذ الإجمالية
  double get overallExecutionPercentage {
    if (totalBudgetAmount == 0) return 0.0;
    return (totalActualAmount / totalBudgetAmount) * 100;
  }

  /// التحقق من تجاوز الموازنة
  bool get isOverBudget => totalVariance > 0;

  /// التحقق من عدم تجاوز الموازنة
  bool get isUnderBudget => totalVariance < 0;

  /// التحقق من التوافق مع الموازنة
  bool get isOnBudget => totalVariance.abs() < 0.01;
}

/// تقرير تحليل الانحرافات
class VarianceAnalysisReport {
  final DateTime reportDate;
  final List<VarianceAnalysisItem> items;
  final VarianceAnalysisSummary summary;

  VarianceAnalysisReport({
    required this.reportDate,
    required this.items,
    required this.summary,
  });

  /// تجميع الانحرافات حسب النوع
  Map<VarianceType, List<VarianceAnalysisItem>> get itemsByVarianceType {
    Map<VarianceType, List<VarianceAnalysisItem>> grouped = {};
    for (var item in items) {
      grouped.putIfAbsent(item.varianceType, () => []).add(item);
    }
    return grouped;
  }

  /// الحصول على أكبر الانحرافات
  List<VarianceAnalysisItem> get topVariances {
    var sortedItems = List<VarianceAnalysisItem>.from(items);
    sortedItems.sort((a, b) => b.varianceAmount.abs().compareTo(a.varianceAmount.abs()));
    return sortedItems.take(10).toList();
  }
}

/// بند تحليل الانحرافات
class VarianceAnalysisItem {
  final String accountCode;
  final String accountName;
  final AccountType accountType;
  final double budgetAmount;
  final double actualAmount;
  final double varianceAmount;
  final double variancePercentage;
  final VarianceType varianceType;
  final String? explanation;
  final String? recommendation;

  VarianceAnalysisItem({
    required this.accountCode,
    required this.accountName,
    required this.accountType,
    required this.budgetAmount,
    required this.actualAmount,
    required this.varianceAmount,
    required this.variancePercentage,
    required this.varianceType,
    this.explanation,
    this.recommendation,
  });

  /// التحقق من كون الانحراف مهماً
  bool get isSignificant => variancePercentage.abs() > 10.0;

  /// التحقق من كون الانحراف حرجاً
  bool get isCritical => variancePercentage.abs() > 25.0;
}

/// ملخص تحليل الانحرافات
class VarianceAnalysisSummary {
  final int totalVariances;
  final int favorableVariances;
  final int unfavorableVariances;
  final int significantVariances;
  final int criticalVariances;
  final double totalVarianceAmount;
  final double averageVariancePercentage;

  VarianceAnalysisSummary({
    required this.totalVariances,
    required this.favorableVariances,
    required this.unfavorableVariances,
    required this.significantVariances,
    required this.criticalVariances,
    required this.totalVarianceAmount,
    required this.averageVariancePercentage,
  });

  /// حساب نسبة الانحرافات المواتية
  double get favorableVariancePercentage {
    if (totalVariances == 0) return 0.0;
    return (favorableVariances / totalVariances) * 100;
  }

  /// حساب نسبة الانحرافات غير المواتية
  double get unfavorableVariancePercentage {
    if (totalVariances == 0) return 0.0;
    return (unfavorableVariances / totalVariances) * 100;
  }

  /// حساب نسبة الانحرافات المهمة
  double get significantVariancePercentage {
    if (totalVariances == 0) return 0.0;
    return (significantVariances / totalVariances) * 100;
  }
}

/// تقرير التنبؤات المالية
class FinancialForecastReport {
  final DateTime forecastDate;
  final ForecastPeriod period;
  final List<ForecastItem> items;
  final ForecastSummary summary;

  FinancialForecastReport({
    required this.forecastDate,
    required this.period,
    required this.items,
    required this.summary,
  });

  /// تجميع التنبؤات حسب نوع الحساب
  Map<AccountType, List<ForecastItem>> get itemsByAccountType {
    Map<AccountType, List<ForecastItem>> grouped = {};
    for (var item in items) {
      grouped.putIfAbsent(item.accountType, () => []).add(item);
    }
    return grouped;
  }

  /// حساب إجمالي التنبؤات حسب نوع الحساب
  Map<AccountType, double> get totalsByAccountType {
    Map<AccountType, double> totals = {};
    for (var entry in itemsByAccountType.entries) {
      totals[entry.key] = entry.value.fold(0.0, (sum, item) => sum + item.forecastAmount);
    }
    return totals;
  }
}

/// بند التنبؤ المالي
class ForecastItem {
  final String accountCode;
  final String accountName;
  final AccountType accountType;
  final double historicalAmount;
  final double forecastAmount;
  final double growthRate;
  final ForecastMethod method;
  final double confidence;

  ForecastItem({
    required this.accountCode,
    required this.accountName,
    required this.accountType,
    required this.historicalAmount,
    required this.forecastAmount,
    required this.growthRate,
    required this.method,
    required this.confidence,
  });

  /// حساب التغيير المطلق
  double get absoluteChange => forecastAmount - historicalAmount;

  /// حساب نسبة التغيير
  double get changePercentage {
    if (historicalAmount == 0) return 0.0;
    return (absoluteChange / historicalAmount) * 100;
  }

  /// التحقق من كون التنبؤ موثوقاً
  bool get isReliable => confidence >= 0.8;

  /// التحقق من كون التنبؤ متحفظاً
  bool get isConservative => confidence >= 0.9;
}

/// ملخص التنبؤات المالية
class ForecastSummary {
  final double totalRevenueForecast;
  final double totalExpenseForecast;
  final double netIncomeForecast;
  final double revenueGrowthRate;
  final double expenseGrowthRate;
  final double averageConfidence;

  ForecastSummary({
    required this.totalRevenueForecast,
    required this.totalExpenseForecast,
    required this.netIncomeForecast,
    required this.revenueGrowthRate,
    required this.expenseGrowthRate,
    required this.averageConfidence,
  });

  /// حساب هامش الربح المتوقع
  double get forecastProfitMargin {
    if (totalRevenueForecast == 0) return 0.0;
    return (netIncomeForecast / totalRevenueForecast) * 100;
  }

  /// التحقق من التوقعات الإيجابية
  bool get isPositiveForecast => netIncomeForecast > 0;

  /// التحقق من موثوقية التوقعات
  bool get isReliableForecast => averageConfidence >= 0.8;
}

/// أنواع الانحرافات
enum VarianceType {
  favorable('favorable', 'مواتي', 'Favorable'),
  unfavorable('unfavorable', 'غير مواتي', 'Unfavorable'),
  neutral('neutral', 'محايد', 'Neutral');

  const VarianceType(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}

/// فترات التنبؤ
enum ForecastPeriod {
  monthly('monthly', 'شهري', 'Monthly'),
  quarterly('quarterly', 'ربع سنوي', 'Quarterly'),
  annual('annual', 'سنوي', 'Annual');

  const ForecastPeriod(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}

/// طرق التنبؤ
enum ForecastMethod {
  historical('historical', 'تاريخي', 'Historical'),
  trend('trend', 'اتجاه', 'Trend'),
  regression('regression', 'انحدار', 'Regression'),
  seasonal('seasonal', 'موسمي', 'Seasonal'),
  expert('expert', 'خبير', 'Expert');

  const ForecastMethod(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}
