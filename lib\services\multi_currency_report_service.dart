
import '../models/invoice.dart';
import 'currency_conversion_service.dart';
import 'currency_service.dart';
import 'invoice_service.dart';
import 'journal_entry_service.dart';
import 'account_service.dart';

/// خدمة التقارير المالية متعددة العملات
class MultiCurrencyReportService {
  final CurrencyConversionService _conversionService =
      CurrencyConversionService();
  final CurrencyService _currencyService = CurrencyService();
  final InvoiceService _invoiceService = InvoiceService();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();

  /// تقرير المبيعات متعدد العملات
  Future<MultiCurrencySalesReport> generateSalesReport({
    required DateTime startDate,
    required DateTime endDate,
    String? displayCurrency,
    List<String>? includeCurrencies,
  }) async {
    try {
      // الحصول على العملة المطلوب العرض بها
      final baseCurrency = await _conversionService.getBaseCurrency();
      final targetCurrency = displayCurrency ?? baseCurrency?.code ?? 'SAR';

      // الحصول على فواتير المبيعات
      final invoicesResult = await _invoiceService.getInvoicesByDateRange(
        startDate,
        endDate,
      );

      if (!invoicesResult.isSuccess) {
        throw Exception('خطأ في جلب الفواتير: ${invoicesResult.error}');
      }

      // تصفية فواتير المبيعات فقط
      final invoices = invoicesResult.data!
          .where((invoice) => invoice.invoiceType == InvoiceType.sales)
          .toList();

      // تجميع البيانات حسب العملة
      Map<String, CurrencyReportData> currencyData = {};
      double totalInTargetCurrency = 0.0;

      for (final invoice in invoices) {
        // تصفية العملات إذا تم تحديدها
        if (includeCurrencies != null &&
            !includeCurrencies.contains(invoice.currencyCode)) {
          continue;
        }

        // إضافة بيانات العملة
        if (!currencyData.containsKey(invoice.currencyCode)) {
          currencyData[invoice.currencyCode] = CurrencyReportData(
            currencyCode: invoice.currencyCode,
            totalAmount: 0.0,
            totalAmountInTargetCurrency: 0.0,
            invoiceCount: 0,
            averageAmount: 0.0,
          );
        }

        final data = currencyData[invoice.currencyCode]!;
        data.totalAmount += invoice.totalAmount;
        data.invoiceCount++;

        // تحويل إلى العملة المستهدفة
        if (invoice.currencyCode != targetCurrency) {
          final conversion = await _conversionService.convertAmount(
            amount: invoice.totalAmount,
            fromCurrency: invoice.currencyCode,
            toCurrency: targetCurrency,
            asOfDate: invoice.date,
          );
          data.totalAmountInTargetCurrency += conversion.convertedAmount;
          totalInTargetCurrency += conversion.convertedAmount;
        } else {
          data.totalAmountInTargetCurrency += invoice.totalAmount;
          totalInTargetCurrency += invoice.totalAmount;
        }
      }

      // حساب المتوسطات
      for (final data in currencyData.values) {
        data.averageAmount = data.invoiceCount > 0
            ? data.totalAmount / data.invoiceCount
            : 0.0;
      }

      return MultiCurrencySalesReport(
        startDate: startDate,
        endDate: endDate,
        targetCurrency: targetCurrency,
        currencyData: currencyData.values.toList(),
        totalInTargetCurrency: totalInTargetCurrency,
        totalInvoices: invoices.length,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير المبيعات: ${e.toString()}');
    }
  }

  /// تقرير المشتريات متعدد العملات
  Future<MultiCurrencyPurchaseReport> generatePurchaseReport({
    required DateTime startDate,
    required DateTime endDate,
    String? displayCurrency,
    List<String>? includeCurrencies,
  }) async {
    try {
      final baseCurrency = await _conversionService.getBaseCurrency();
      final targetCurrency = displayCurrency ?? baseCurrency?.code ?? 'SAR';

      final invoicesResult = await _invoiceService.getInvoicesByDateRange(
        startDate,
        endDate,
      );

      if (!invoicesResult.isSuccess) {
        throw Exception('خطأ في جلب الفواتير: ${invoicesResult.error}');
      }

      // تصفية فواتير المشتريات فقط
      final invoices = invoicesResult.data!
          .where((invoice) => invoice.invoiceType == InvoiceType.purchase)
          .toList();

      Map<String, CurrencyReportData> currencyData = {};
      double totalInTargetCurrency = 0.0;

      for (final invoice in invoices) {
        if (includeCurrencies != null &&
            !includeCurrencies.contains(invoice.currencyCode)) {
          continue;
        }

        if (!currencyData.containsKey(invoice.currencyCode)) {
          currencyData[invoice.currencyCode] = CurrencyReportData(
            currencyCode: invoice.currencyCode,
            totalAmount: 0.0,
            totalAmountInTargetCurrency: 0.0,
            invoiceCount: 0,
            averageAmount: 0.0,
          );
        }

        final data = currencyData[invoice.currencyCode]!;
        data.totalAmount += invoice.totalAmount;
        data.invoiceCount++;

        if (invoice.currencyCode != targetCurrency) {
          final conversion = await _conversionService.convertAmount(
            amount: invoice.totalAmount,
            fromCurrency: invoice.currencyCode,
            toCurrency: targetCurrency,
            asOfDate: invoice.date,
          );
          data.totalAmountInTargetCurrency += conversion.convertedAmount;
          totalInTargetCurrency += conversion.convertedAmount;
        } else {
          data.totalAmountInTargetCurrency += invoice.totalAmount;
          totalInTargetCurrency += invoice.totalAmount;
        }
      }

      for (final data in currencyData.values) {
        data.averageAmount = data.invoiceCount > 0
            ? data.totalAmount / data.invoiceCount
            : 0.0;
      }

      return MultiCurrencyPurchaseReport(
        startDate: startDate,
        endDate: endDate,
        targetCurrency: targetCurrency,
        currencyData: currencyData.values.toList(),
        totalInTargetCurrency: totalInTargetCurrency,
        totalInvoices: invoices.length,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير المشتريات: ${e.toString()}');
    }
  }

  /// تقرير الأرباح والخسائر متعدد العملات
  Future<MultiCurrencyProfitLossReport> generateProfitLossReport({
    required DateTime startDate,
    required DateTime endDate,
    String? displayCurrency,
  }) async {
    try {
      final baseCurrency = await _conversionService.getBaseCurrency();
      final targetCurrency = displayCurrency ?? baseCurrency?.code ?? 'SAR';

      // الحصول على تقارير المبيعات والمشتريات
      final salesReport = await generateSalesReport(
        startDate: startDate,
        endDate: endDate,
        displayCurrency: targetCurrency,
      );

      final purchaseReport = await generatePurchaseReport(
        startDate: startDate,
        endDate: endDate,
        displayCurrency: targetCurrency,
      );

      // حساب الربح/الخسارة
      final totalRevenue = salesReport.totalInTargetCurrency;
      final totalExpenses = purchaseReport.totalInTargetCurrency;
      final netProfit = totalRevenue - totalExpenses;
      final profitMargin = totalRevenue > 0
          ? (netProfit / totalRevenue) * 100
          : 0.0;

      return MultiCurrencyProfitLossReport(
        startDate: startDate,
        endDate: endDate,
        targetCurrency: targetCurrency,
        totalRevenue: totalRevenue,
        totalExpenses: totalExpenses,
        netProfit: netProfit,
        profitMargin: profitMargin,
        salesData: salesReport.currencyData,
        purchaseData: purchaseReport.currencyData,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير الأرباح والخسائر: ${e.toString()}');
    }
  }

  /// تقرير أسعار الصرف
  Future<ExchangeRateReport> generateExchangeRateReport({
    required DateTime startDate,
    required DateTime endDate,
    String? baseCurrency,
  }) async {
    try {
      final base =
          baseCurrency ??
          (await _conversionService.getBaseCurrency())?.code ??
          'SAR';
      final currencies = await _conversionService.getActiveCurrencies();

      List<ExchangeRateData> rateData = [];

      for (final currency in currencies) {
        if (currency.code != base) {
          // يمكن إضافة منطق للحصول على أسعار الصرف التاريخية هنا
          rateData.add(
            ExchangeRateData(
              currencyCode: currency.code,
              currencyName: currency.nameAr,
              currentRate: 1.0, // يجب الحصول على السعر الفعلي
              previousRate: 1.0, // يجب الحصول على السعر السابق
              changePercentage: 0.0,
              lastUpdated: DateTime.now(),
            ),
          );
        }
      }

      return ExchangeRateReport(
        reportDate: DateTime.now(),
        baseCurrency: base,
        exchangeRates: rateData,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير أسعار الصرف: ${e.toString()}');
    }
  }

  /// تقرير القيود اليومية متعدد العملات
  Future<MultiCurrencyJournalReport> generateJournalEntriesReport({
    required DateTime startDate,
    required DateTime endDate,
    String? displayCurrency,
    List<String>? includeCurrencies,
    String? accountId,
  }) async {
    try {
      final baseCurrency = await _conversionService.getBaseCurrency();
      final targetCurrency = displayCurrency ?? baseCurrency?.code ?? 'SAR';

      // الحصول على القيود اليومية
      final entriesResult = await _journalEntryService
          .getJournalEntriesByDateRange(startDate, endDate);

      if (!entriesResult.isSuccess) {
        throw Exception('خطأ في جلب القيود اليومية: ${entriesResult.error}');
      }

      var entries = entriesResult.data!;

      // تصفية حسب الحساب إذا تم تحديده
      if (accountId != null) {
        final accountIdInt = int.tryParse(accountId);
        if (accountIdInt != null) {
          // التحقق من وجود الحساب
          final accountResult = await _accountService.getAccountById(
            accountIdInt,
          );
          if (!accountResult.isSuccess) {
            throw Exception('الحساب المحدد غير موجود: ${accountResult.error}');
          }

          entries = entries
              .where(
                (entry) =>
                    entry.lines.any((line) => line.accountId == accountIdInt),
              )
              .toList();
        }
      }

      Map<String, CurrencyJournalData> currencyData = {};
      double totalDebitInTargetCurrency = 0.0;
      double totalCreditInTargetCurrency = 0.0;

      for (final entry in entries) {
        // تصفية العملات إذا تم تحديدها
        if (includeCurrencies != null &&
            !includeCurrencies.contains(entry.currencyCode)) {
          continue;
        }

        if (!currencyData.containsKey(entry.currencyCode)) {
          currencyData[entry.currencyCode] = CurrencyJournalData(
            currencyCode: entry.currencyCode,
            totalDebit: 0.0,
            totalCredit: 0.0,
            totalDebitInTargetCurrency: 0.0,
            totalCreditInTargetCurrency: 0.0,
            entryCount: 0,
          );
        }

        final data = currencyData[entry.currencyCode]!;
        data.totalDebit += entry.totalDebit;
        data.totalCredit += entry.totalCredit;
        data.entryCount++;

        // تحويل إلى العملة المستهدفة
        if (entry.currencyCode != targetCurrency) {
          final debitConversion = await _conversionService.convertAmount(
            amount: entry.totalDebit,
            fromCurrency: entry.currencyCode,
            toCurrency: targetCurrency,
            asOfDate: entry.date,
          );
          final creditConversion = await _conversionService.convertAmount(
            amount: entry.totalCredit,
            fromCurrency: entry.currencyCode,
            toCurrency: targetCurrency,
            asOfDate: entry.date,
          );

          data.totalDebitInTargetCurrency += debitConversion.convertedAmount;
          data.totalCreditInTargetCurrency += creditConversion.convertedAmount;
          totalDebitInTargetCurrency += debitConversion.convertedAmount;
          totalCreditInTargetCurrency += creditConversion.convertedAmount;
        } else {
          data.totalDebitInTargetCurrency += entry.totalDebit;
          data.totalCreditInTargetCurrency += entry.totalCredit;
          totalDebitInTargetCurrency += entry.totalDebit;
          totalCreditInTargetCurrency += entry.totalCredit;
        }
      }

      return MultiCurrencyJournalReport(
        startDate: startDate,
        endDate: endDate,
        targetCurrency: targetCurrency,
        currencyData: currencyData.values.toList(),
        totalDebitInTargetCurrency: totalDebitInTargetCurrency,
        totalCreditInTargetCurrency: totalCreditInTargetCurrency,
        totalEntries: entries.length,
        accountId: accountId,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير القيود اليومية: ${e.toString()}');
    }
  }

  /// تنسيق المبلغ حسب العملة
  String formatAmount(double amount, String currencyCode) {
    return _currencyService.formatAmount(amount, currencyCode);
  }
}

/// بيانات العملة في التقرير
class CurrencyReportData {
  final String currencyCode;
  double totalAmount;
  double totalAmountInTargetCurrency;
  int invoiceCount;
  double averageAmount;

  CurrencyReportData({
    required this.currencyCode,
    required this.totalAmount,
    required this.totalAmountInTargetCurrency,
    required this.invoiceCount,
    required this.averageAmount,
  });
}

/// تقرير المبيعات متعدد العملات
class MultiCurrencySalesReport {
  final DateTime startDate;
  final DateTime endDate;
  final String targetCurrency;
  final List<CurrencyReportData> currencyData;
  final double totalInTargetCurrency;
  final int totalInvoices;

  MultiCurrencySalesReport({
    required this.startDate,
    required this.endDate,
    required this.targetCurrency,
    required this.currencyData,
    required this.totalInTargetCurrency,
    required this.totalInvoices,
  });
}

/// تقرير المشتريات متعدد العملات
class MultiCurrencyPurchaseReport {
  final DateTime startDate;
  final DateTime endDate;
  final String targetCurrency;
  final List<CurrencyReportData> currencyData;
  final double totalInTargetCurrency;
  final int totalInvoices;

  MultiCurrencyPurchaseReport({
    required this.startDate,
    required this.endDate,
    required this.targetCurrency,
    required this.currencyData,
    required this.totalInTargetCurrency,
    required this.totalInvoices,
  });
}

/// تقرير الأرباح والخسائر متعدد العملات
class MultiCurrencyProfitLossReport {
  final DateTime startDate;
  final DateTime endDate;
  final String targetCurrency;
  final double totalRevenue;
  final double totalExpenses;
  final double netProfit;
  final double profitMargin;
  final List<CurrencyReportData> salesData;
  final List<CurrencyReportData> purchaseData;

  MultiCurrencyProfitLossReport({
    required this.startDate,
    required this.endDate,
    required this.targetCurrency,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    required this.profitMargin,
    required this.salesData,
    required this.purchaseData,
  });
}

/// بيانات سعر الصرف
class ExchangeRateData {
  final String currencyCode;
  final String currencyName;
  final double currentRate;
  final double previousRate;
  final double changePercentage;
  final DateTime lastUpdated;

  ExchangeRateData({
    required this.currencyCode,
    required this.currencyName,
    required this.currentRate,
    required this.previousRate,
    required this.changePercentage,
    required this.lastUpdated,
  });
}

/// تقرير أسعار الصرف
class ExchangeRateReport {
  final DateTime reportDate;
  final String baseCurrency;
  final List<ExchangeRateData> exchangeRates;

  ExchangeRateReport({
    required this.reportDate,
    required this.baseCurrency,
    required this.exchangeRates,
  });
}

/// بيانات العملة في تقرير القيود اليومية
class CurrencyJournalData {
  final String currencyCode;
  double totalDebit;
  double totalCredit;
  double totalDebitInTargetCurrency;
  double totalCreditInTargetCurrency;
  int entryCount;

  CurrencyJournalData({
    required this.currencyCode,
    required this.totalDebit,
    required this.totalCredit,
    required this.totalDebitInTargetCurrency,
    required this.totalCreditInTargetCurrency,
    required this.entryCount,
  });

  /// الرصيد الصافي (المدين - الدائن)
  double get netBalance => totalDebit - totalCredit;

  /// الرصيد الصافي بالعملة المستهدفة
  double get netBalanceInTargetCurrency =>
      totalDebitInTargetCurrency - totalCreditInTargetCurrency;
}

/// تقرير القيود اليومية متعدد العملات
class MultiCurrencyJournalReport {
  final DateTime startDate;
  final DateTime endDate;
  final String targetCurrency;
  final List<CurrencyJournalData> currencyData;
  final double totalDebitInTargetCurrency;
  final double totalCreditInTargetCurrency;
  final int totalEntries;
  final String? accountId;

  MultiCurrencyJournalReport({
    required this.startDate,
    required this.endDate,
    required this.targetCurrency,
    required this.currencyData,
    required this.totalDebitInTargetCurrency,
    required this.totalCreditInTargetCurrency,
    required this.totalEntries,
    this.accountId,
  });

  /// الرصيد الصافي الإجمالي بالعملة المستهدفة
  double get totalNetBalanceInTargetCurrency =>
      totalDebitInTargetCurrency - totalCreditInTargetCurrency;

  /// التحقق من توازن القيود
  bool get isBalanced =>
      (totalDebitInTargetCurrency - totalCreditInTargetCurrency).abs() < 0.01;
}
