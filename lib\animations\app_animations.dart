// ملف الحركات والتأثيرات البصرية المتقدمة
// هذا الملف يحتوي على جميع الحركات والتأثيرات المستخدمة في التطبيق
// لجعله يتفوق على برامج المحاسبة التقليدية بمظهر عصري وجذاب

import 'package:flutter/material.dart';

/// فئة تحتوي على جميع مدد الحركات المستخدمة في التطبيق
/// Duration class contains all animation durations used in the app
class AppAnimationDurations {
  // مدد الحركات السريعة للتفاعلات الفورية
  static const Duration fast = Duration(milliseconds: 200);

  // مدد الحركات المتوسطة للانتقالات العادية
  static const Duration medium = Duration(milliseconds: 300);

  // مدد الحركات البطيئة للتأثيرات الدرامية
  static const Duration slow = Duration(milliseconds: 500);

  // مدة حركة الصفحات
  static const Duration pageTransition = Duration(milliseconds: 350);

  // مدة حركة البطاقات
  static const Duration cardAnimation = Duration(milliseconds: 400);

  // مدة حركة الأزرار
  static const Duration buttonAnimation = Duration(milliseconds: 150);
}

/// فئة تحتوي على منحنيات الحركة المختلفة
/// Curves class contains different animation curves
class AppAnimationCurves {
  // منحنى سلس للحركات العادية
  static const Curve smooth = Curves.easeInOut;

  // منحنى مرن للحركات المرحة
  static const Curve bouncy = Curves.elasticOut;

  // منحنى سريع للدخول
  static const Curve fastIn = Curves.easeIn;

  // منحنى سريع للخروج
  static const Curve fastOut = Curves.easeOut;

  // منحنى مخصص للبطاقات
  static const Curve cardCurve = Curves.easeOutCubic;
}

/// ويدجت للحركة المتدرجة للعناصر
/// Staggered animation widget for elements
class StaggeredAnimation extends StatefulWidget {
  final Widget child;
  final int index;
  final Duration delay;
  final Duration duration;
  final Curve curve;

  const StaggeredAnimation({
    super.key,
    required this.child,
    this.index = 0,
    this.delay = const Duration(milliseconds: 100),
    this.duration = AppAnimationDurations.medium,
    this.curve = AppAnimationCurves.smooth,
  });

  @override
  State<StaggeredAnimation> createState() => _StaggeredAnimationState();
}

class _StaggeredAnimationState extends State<StaggeredAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الحركة
    _controller = AnimationController(duration: widget.duration, vsync: this);

    // إنشاء حركة التلاشي
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    // إنشاء حركة الانزلاق
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    // بدء الحركة مع تأخير حسب الفهرس
    Future.delayed(
      Duration(milliseconds: widget.index * widget.delay.inMilliseconds),
      () {
        if (mounted) {
          _controller.forward();
        }
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: widget.child,
          ),
        );
      },
    );
  }
}

/// ويدجت للحركة المتموجة
/// Ripple animation widget
class RippleAnimation extends StatefulWidget {
  final Widget child;
  final Color color;
  final Duration duration;

  const RippleAnimation({
    super.key,
    required this.child,
    this.color = Colors.blue,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<RippleAnimation> createState() => _RippleAnimationState();
}

class _RippleAnimationState extends State<RippleAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // الدوائر المتموجة
            for (int i = 0; i < 3; i++)
              Transform.scale(
                scale: _animation.value * (1 + i * 0.3),
                child: Opacity(
                  opacity: (1 - _animation.value) * 0.3,
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: widget.color, width: 2),
                    ),
                  ),
                ),
              ),
            widget.child,
          ],
        );
      },
    );
  }
}

/// ويدجت للحركة النابضة
/// Pulse animation widget
class PulseAnimation extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double minScale;
  final double maxScale;

  const PulseAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1000),
    this.minScale = 0.95,
    this.maxScale = 1.05,
  });

  @override
  State<PulseAnimation> createState() => _PulseAnimationState();
}

class _PulseAnimationState extends State<PulseAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(
      begin: widget.minScale,
      end: widget.maxScale,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(scale: _animation.value, child: widget.child);
      },
    );
  }
}

/// ويدجت للحركة الدوارة
/// Rotation animation widget
class RotationAnimation extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final bool reverse;

  const RotationAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 2),
    this.reverse = false,
  });

  @override
  State<RotationAnimation> createState() => _RotationAnimationState();
}

class _RotationAnimationState extends State<RotationAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _controller.value * 2 * 3.14159 * (widget.reverse ? -1 : 1),
          child: widget.child,
        );
      },
    );
  }
}
