/// Data Access Object for Periodic Inventory
/// Periodic Inventory DAO for Smart Ledger
library;


import '../models/periodic_inventory.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class PeriodicInventoryDao {
  static final PeriodicInventoryDao _instance = PeriodicInventoryDao._internal();
  factory PeriodicInventoryDao() => _instance;
  PeriodicInventoryDao._internal();

  /// Get all inventories
  Future<List<PeriodicInventory>> getAllInventories() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePeriodicInventories,
      orderBy: 'planned_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return PeriodicInventory.fromMap(maps[i]);
    });
  }

  /// Get inventory by ID
  Future<PeriodicInventory?> getInventoryById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePeriodicInventories,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return PeriodicInventory.fromMap(maps.first);
    }
    return null;
  }

  /// Insert inventory
  Future<int> insertInventory(PeriodicInventory inventory) async {
    final db = await DatabaseHelper().database;
    
    final inventoryMap = inventory.toMap();
    inventoryMap.remove('id');
    inventoryMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tablePeriodicInventories,
      inventoryMap,
    );
  }

  /// Update inventory
  Future<int> updateInventory(PeriodicInventory inventory) async {
    final db = await DatabaseHelper().database;
    
    final inventoryMap = inventory.toMap();
    inventoryMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tablePeriodicInventories,
      inventoryMap,
      where: 'id = ?',
      whereArgs: [inventory.id],
    );
  }

  /// Delete inventory
  Future<int> deleteInventory(int id) async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tablePeriodicInventories,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get inventory count
  Future<int> getInventoryCount() async {
    final db = await DatabaseHelper().database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tablePeriodicInventories}'
    );
    return result.first['count'] as int;
  }

  /// Get inventories by status
  Future<List<PeriodicInventory>> getInventoriesByStatus(InventoryStatus status) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePeriodicInventories,
      where: 'status = ?',
      whereArgs: [status.value],
      orderBy: 'planned_date DESC',
    );

    return List.generate(maps.length, (i) {
      return PeriodicInventory.fromMap(maps[i]);
    });
  }

  /// Get inventories by warehouse
  Future<List<PeriodicInventory>> getInventoriesByWarehouse(int warehouseId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePeriodicInventories,
      where: 'warehouse_id = ?',
      whereArgs: [warehouseId],
      orderBy: 'planned_date DESC',
    );

    return List.generate(maps.length, (i) {
      return PeriodicInventory.fromMap(maps[i]);
    });
  }

  /// Search inventories
  Future<List<PeriodicInventory>> searchInventories(String query) async {
    final db = await DatabaseHelper().database;
    final searchQuery = '%$query%';
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePeriodicInventories,
      where: '''
        inventory_number LIKE ? OR 
        name LIKE ? OR 
        description LIKE ?
      ''',
      whereArgs: [searchQuery, searchQuery, searchQuery],
      orderBy: 'planned_date DESC',
    );

    return List.generate(maps.length, (i) {
      return PeriodicInventory.fromMap(maps[i]);
    });
  }

  /// Get inventory items
  Future<List<InventoryItem>> getInventoryItems(int inventoryId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableInventoryItems,
      where: 'inventory_id = ?',
      whereArgs: [inventoryId],
      orderBy: 'item_id ASC',
    );

    return List.generate(maps.length, (i) {
      return InventoryItem.fromMap(maps[i]);
    });
  }

  /// Get inventory item by ID
  Future<InventoryItem?> getInventoryItemById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableInventoryItems,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return InventoryItem.fromMap(maps.first);
    }
    return null;
  }

  /// Insert inventory item
  Future<int> insertInventoryItem(InventoryItem item) async {
    final db = await DatabaseHelper().database;
    
    final itemMap = item.toMap();
    itemMap.remove('id');
    itemMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableInventoryItems,
      itemMap,
    );
  }

  /// Update inventory item
  Future<int> updateInventoryItem(InventoryItem item) async {
    final db = await DatabaseHelper().database;
    
    final itemMap = item.toMap();
    itemMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableInventoryItems,
      itemMap,
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  /// Get uncounted items
  Future<List<InventoryItem>> getUncounteditems(int inventoryId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableInventoryItems,
      where: 'inventory_id = ? AND counted_quantity IS NULL',
      whereArgs: [inventoryId],
      orderBy: 'item_id ASC',
    );

    return List.generate(maps.length, (i) {
      return InventoryItem.fromMap(maps[i]);
    });
  }

  /// Get inventory items with variance
  Future<List<InventoryItem>> getInventoryItemsWithVariance(int inventoryId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableInventoryItems,
      where: 'inventory_id = ? AND counted_quantity IS NOT NULL AND ABS(variance) > 0.001',
      whereArgs: [inventoryId],
      orderBy: 'ABS(variance) DESC',
    );

    return List.generate(maps.length, (i) {
      return InventoryItem.fromMap(maps[i]);
    });
  }

  /// Get inventory summary
  Future<Map<String, dynamic>> getInventorySummary(int inventoryId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN counted_quantity IS NOT NULL THEN 1 END) as counted_items,
        COUNT(CASE WHEN counted_quantity IS NULL THEN 1 END) as uncounted_items,
        COUNT(CASE WHEN ABS(COALESCE(variance, 0)) > 0.001 THEN 1 END) as variance_items,
        SUM(CASE WHEN variance > 0 THEN variance ELSE 0 END) as positive_variance,
        SUM(CASE WHEN variance < 0 THEN ABS(variance) ELSE 0 END) as negative_variance,
        SUM(CASE WHEN variance > 0 THEN total_variance_cost ELSE 0 END) as positive_variance_cost,
        SUM(CASE WHEN variance < 0 THEN ABS(total_variance_cost) ELSE 0 END) as negative_variance_cost,
        SUM(system_quantity * unit_cost) as total_system_value,
        SUM(COALESCE(counted_quantity, system_quantity) * unit_cost) as total_counted_value
      FROM ${DatabaseSchema.tableInventoryItems}
      WHERE inventory_id = ?
    ''', [inventoryId]);

    return result.isNotEmpty ? result.first : {};
  }

  /// Get inventory items with details
  Future<List<Map<String, dynamic>>> getInventoryItemsWithDetails(int inventoryId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        ii.*,
        i.name as item_name,
        i.code as item_code,
        i.unit as item_unit,
        w.name as warehouse_name,
        w.code as warehouse_code,
        wl.name as location_name
      FROM ${DatabaseSchema.tableInventoryItems} ii
      LEFT JOIN items i ON ii.item_id = i.id
      LEFT JOIN warehouses w ON ii.warehouse_id = w.id
      LEFT JOIN warehouse_locations wl ON ii.location_id = wl.id
      WHERE ii.inventory_id = ?
      ORDER BY i.name ASC
    ''', [inventoryId]);

    return result;
  }

  /// Get inventories by date range
  Future<List<PeriodicInventory>> getInventoriesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await DatabaseHelper().database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePeriodicInventories,
      where: 'planned_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'planned_date DESC',
    );

    return List.generate(maps.length, (i) {
      return PeriodicInventory.fromMap(maps[i]);
    });
  }

  /// Get inventory statistics
  Future<Map<String, dynamic>> getInventoryStatistics() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        status,
        COUNT(*) as count,
        AVG(
          CASE 
            WHEN end_date IS NOT NULL AND start_date IS NOT NULL
            THEN (julianday(end_date) - julianday(start_date)) * 24 
            ELSE NULL 
          END
        ) as avg_duration_hours
      FROM ${DatabaseSchema.tablePeriodicInventories}
      GROUP BY status
    ''');

    final stats = <String, dynamic>{};
    for (final row in result) {
      stats[row['status'] as String] = {
        'count': row['count'],
        'avg_duration_hours': row['avg_duration_hours'],
      };
    }

    return stats;
  }

  /// Delete inventory item
  Future<int> deleteInventoryItem(int id) async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tableInventoryItems,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
