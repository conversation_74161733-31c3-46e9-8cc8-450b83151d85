import '../models/journal_entry.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class JournalEntryDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Create a new journal entry with lines
  Future<int> insertJournalEntry(JournalEntry entry) async {
    return await _dbHelper.transaction((txn) async {
      // Insert the journal entry
      Map<String, dynamic> entryMap = entry.toMap();
      entryMap.remove('id');
      int entryId = await txn.insert(
        DatabaseSchema.tableJournalEntries,
        entryMap,
      );

      // Insert the journal entry lines
      for (JournalEntryLine line in entry.lines) {
        Map<String, dynamic> lineMap = line.toMap();
        lineMap.remove('id');
        lineMap['journal_entry_id'] = entryId;
        await txn.insert(DatabaseSchema.tableJournalEntryLines, lineMap);
      }

      return entryId;
    });
  }

  // Get all journal entries
  Future<List<JournalEntry>> getAllJournalEntries() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      orderBy: 'date DESC, entry_number DESC',
    );

    List<JournalEntry> entries = [];
    for (Map<String, dynamic> map in maps) {
      JournalEntry entry = JournalEntry.fromMap(map);
      entry.lines = await getJournalEntryLines(entry.id!);
      entries.add(entry);
    }

    return entries;
  }

  // Get journal entry by ID
  Future<JournalEntry?> getJournalEntryById(int id) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      JournalEntry entry = JournalEntry.fromMap(maps.first);
      entry.lines = await getJournalEntryLines(id);
      return entry;
    }
    return null;
  }

  // Get journal entry by number
  Future<JournalEntry?> getJournalEntryByNumber(String entryNumber) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      where: 'entry_number = ?',
      whereArgs: [entryNumber],
    );

    if (maps.isNotEmpty) {
      JournalEntry entry = JournalEntry.fromMap(maps.first);
      entry.lines = await getJournalEntryLines(entry.id!);
      return entry;
    }
    return null;
  }

  // Get journal entry lines for a specific entry
  Future<List<JournalEntryLine>> getJournalEntryLines(
    int journalEntryId,
  ) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntryLines,
      where: 'journal_entry_id = ?',
      whereArgs: [journalEntryId],
      orderBy: 'line_order ASC',
    );
    return maps.map((map) => JournalEntryLine.fromMap(map)).toList();
  }

  // Get journal entries by date range
  Future<List<JournalEntry>> getJournalEntriesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      where: 'date >= ? AND date <= ?',
      whereArgs: [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'date DESC, entry_number DESC',
    );

    List<JournalEntry> entries = [];
    for (Map<String, dynamic> map in maps) {
      JournalEntry entry = JournalEntry.fromMap(map);
      entry.lines = await getJournalEntryLines(entry.id!);
      entries.add(entry);
    }

    return entries;
  }

  // Get posted journal entries only
  Future<List<JournalEntry>> getPostedJournalEntries() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      where: 'is_posted = 1',
      orderBy: 'date DESC, entry_number DESC',
    );

    List<JournalEntry> entries = [];
    for (Map<String, dynamic> map in maps) {
      JournalEntry entry = JournalEntry.fromMap(map);
      entry.lines = await getJournalEntryLines(entry.id!);
      entries.add(entry);
    }

    return entries;
  }

  // Get unposted journal entries
  Future<List<JournalEntry>> getUnpostedJournalEntries() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      where: 'is_posted = 0',
      orderBy: 'date DESC, entry_number DESC',
    );

    List<JournalEntry> entries = [];
    for (Map<String, dynamic> map in maps) {
      JournalEntry entry = JournalEntry.fromMap(map);
      entry.lines = await getJournalEntryLines(entry.id!);
      entries.add(entry);
    }

    return entries;
  }

  // Search journal entries
  Future<List<JournalEntry>> searchJournalEntries(String query) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      where: 'entry_number LIKE ? OR description LIKE ? OR reference LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'date DESC, entry_number DESC',
    );

    List<JournalEntry> entries = [];
    for (Map<String, dynamic> map in maps) {
      JournalEntry entry = JournalEntry.fromMap(map);
      entry.lines = await getJournalEntryLines(entry.id!);
      entries.add(entry);
    }

    return entries;
  }

  // Update journal entry
  Future<int> updateJournalEntry(JournalEntry entry) async {
    return await _dbHelper.transaction((txn) async {
      // Update the journal entry
      int result = await txn.update(
        DatabaseSchema.tableJournalEntries,
        entry.toMap(),
        where: 'id = ?',
        whereArgs: [entry.id],
      );

      // Delete existing lines
      await txn.delete(
        DatabaseSchema.tableJournalEntryLines,
        where: 'journal_entry_id = ?',
        whereArgs: [entry.id],
      );

      // Insert updated lines
      for (JournalEntryLine line in entry.lines) {
        Map<String, dynamic> lineMap = line.toMap();
        lineMap.remove('id');
        lineMap['journal_entry_id'] = entry.id;
        await txn.insert(DatabaseSchema.tableJournalEntryLines, lineMap);
      }

      return result;
    });
  }

  // Post journal entry (mark as posted and update account balances)
  Future<bool> postJournalEntry(int entryId) async {
    return await _dbHelper.transaction((txn) async {
      // Get the journal entry
      final entryMaps = await txn.query(
        DatabaseSchema.tableJournalEntries,
        where: 'id = ?',
        whereArgs: [entryId],
      );

      if (entryMaps.isEmpty) return false;

      // Get the journal entry lines
      final lineMaps = await txn.query(
        DatabaseSchema.tableJournalEntryLines,
        where: 'journal_entry_id = ?',
        whereArgs: [entryId],
      );

      // Update account balances
      for (Map<String, dynamic> lineMap in lineMaps) {
        int accountId = lineMap['account_id'] as int;
        double debitAmount = (lineMap['debit_amount'] as num).toDouble();
        double creditAmount = (lineMap['credit_amount'] as num).toDouble();

        // Get current account balance
        final accountMaps = await txn.query(
          DatabaseSchema.tableAccounts,
          where: 'id = ?',
          whereArgs: [accountId],
        );

        if (accountMaps.isNotEmpty) {
          double currentBalance = (accountMaps.first['balance'] as num)
              .toDouble();
          double newBalance = currentBalance + debitAmount - creditAmount;

          // Update account balance
          await txn.update(
            DatabaseSchema.tableAccounts,
            {
              'balance': newBalance,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [accountId],
          );
        }
      }

      // Mark journal entry as posted
      await txn.update(
        DatabaseSchema.tableJournalEntries,
        {'is_posted': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [entryId],
      );

      return true;
    });
  }

  // Unpost journal entry (reverse the posting)
  Future<bool> unpostJournalEntry(int entryId) async {
    return await _dbHelper.transaction((txn) async {
      // Get the journal entry lines
      final lineMaps = await txn.query(
        DatabaseSchema.tableJournalEntryLines,
        where: 'journal_entry_id = ?',
        whereArgs: [entryId],
      );

      // Reverse account balance updates
      for (Map<String, dynamic> lineMap in lineMaps) {
        int accountId = lineMap['account_id'] as int;
        double debitAmount = (lineMap['debit_amount'] as num).toDouble();
        double creditAmount = (lineMap['credit_amount'] as num).toDouble();

        // Get current account balance
        final accountMaps = await txn.query(
          DatabaseSchema.tableAccounts,
          where: 'id = ?',
          whereArgs: [accountId],
        );

        if (accountMaps.isNotEmpty) {
          double currentBalance = (accountMaps.first['balance'] as num)
              .toDouble();
          double newBalance = currentBalance - debitAmount + creditAmount;

          // Update account balance
          await txn.update(
            DatabaseSchema.tableAccounts,
            {
              'balance': newBalance,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [accountId],
          );
        }
      }

      // Mark journal entry as unposted
      await txn.update(
        DatabaseSchema.tableJournalEntries,
        {'is_posted': 0, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [entryId],
      );

      return true;
    });
  }

  // Delete journal entry
  Future<int> deleteJournalEntry(int entryId) async {
    return await _dbHelper.transaction((txn) async {
      // Delete journal entry lines first (due to foreign key)
      await txn.delete(
        DatabaseSchema.tableJournalEntryLines,
        where: 'journal_entry_id = ?',
        whereArgs: [entryId],
      );

      // Delete journal entry
      return await txn.delete(
        DatabaseSchema.tableJournalEntries,
        where: 'id = ?',
        whereArgs: [entryId],
      );
    });
  }

  // Check if entry number exists
  Future<bool> isEntryNumberExists(String entryNumber, {int? excludeId}) async {
    String whereClause = 'entry_number = ?';
    List<dynamic> whereArgs = [entryNumber];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.isNotEmpty;
  }

  // Get next entry number
  Future<String> getNextEntryNumber() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntries,
      orderBy: 'entry_number DESC',
      limit: 1,
    );

    if (maps.isEmpty) {
      return '1';
    }

    String lastNumber = maps.first['entry_number'] as String;
    int? lastNum = int.tryParse(lastNumber);
    if (lastNum != null) {
      return (lastNum + 1).toString();
    }

    return '1';
  }

  // Get journal entry statistics
  Future<Map<String, dynamic>> getJournalEntryStatistics() async {
    final totalResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as total FROM ${DatabaseSchema.tableJournalEntries}',
    );

    final postedResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as posted FROM ${DatabaseSchema.tableJournalEntries} WHERE is_posted = 1',
    );

    final balancedResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as balanced FROM ${DatabaseSchema.tableJournalEntries} WHERE is_balanced = 1',
    );

    return {
      'total': totalResult.first['total'] as int,
      'posted': postedResult.first['posted'] as int,
      'balanced': balancedResult.first['balanced'] as int,
    };
  }

  // Get account transactions
  Future<List<JournalEntryLine>> getAccountTransactions(int accountId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntryLines,
      where: 'account_id = ?',
      whereArgs: [accountId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return JournalEntryLine.fromMap(maps[i]);
    });
  }
}
