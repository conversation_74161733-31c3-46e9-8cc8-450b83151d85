import 'package:flutter/material.dart';
import '../../models/supplier.dart';
import '../../models/invoice.dart';
import '../../services/supplier_service.dart';
import '../../services/invoice_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import 'add_supplier_screen.dart';

/// شاشة تفاصيل المورد
class SupplierDetailsScreen extends StatefulWidget {
  final Supplier supplier;

  const SupplierDetailsScreen({super.key, required this.supplier});

  @override
  State<SupplierDetailsScreen> createState() => _SupplierDetailsScreenState();
}

class _SupplierDetailsScreenState extends State<SupplierDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final SupplierService _supplierService = SupplierService();
  final InvoiceService _invoiceService = InvoiceService();

  List<Invoice> _supplierInvoices = [];
  Map<String, dynamic> _supplierStatistics = {};
  bool _isLoadingInvoices = true;
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSupplierData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSupplierData() async {
    await Future.wait([_loadSupplierInvoices(), _loadSupplierStatistics()]);
  }

  Future<void> _loadSupplierInvoices() async {
    setState(() {
      _isLoadingInvoices = true;
    });

    try {
      final result = await _invoiceService.getInvoicesBySupplier(
        widget.supplier.id!,
      );
      if (result.isSuccess) {
        setState(() {
          _supplierInvoices = result.data!;
          _isLoadingInvoices = false;
        });
      } else {
        setState(() {
          _isLoadingInvoices = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingInvoices = false;
      });
    }
  }

  Future<void> _loadSupplierStatistics() async {
    setState(() {
      _isLoadingStats = true;
    });

    try {
      final result = await _supplierService.getSupplierStatistics(
        widget.supplier.id!,
      );
      if (result.isSuccess) {
        setState(() {
          _supplierStatistics = result.data!;
          _isLoadingStats = false;
        });
      } else {
        setState(() {
          _isLoadingStats = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingStats = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.supplier.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      AddSupplierScreen(supplier: widget.supplier),
                ),
              );
              if (result == true) {
                // تحديث البيانات
                _loadSupplierData();
              }
            },
            tooltip: 'تعديل',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المعلومات', icon: Icon(Icons.info)),
            Tab(text: 'الفواتير', icon: Icon(Icons.receipt)),
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildInfoTab(), _buildInvoicesTab(), _buildStatisticsTab()],
      ),
    );
  }

  Widget _buildInfoTab() {
    return ListView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      children: [
        BeautifulCard(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: AppTheme.primaryColor,
                      child: Text(
                        widget.supplier.name.isNotEmpty
                            ? widget.supplier.name[0].toUpperCase()
                            : 'م',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.supplier.name,
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            'رمز المورد: ${widget.supplier.code}',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingMedium),

        BeautifulCard(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات الاتصال',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMedium),

                if (widget.supplier.phone != null &&
                    widget.supplier.phone!.isNotEmpty)
                  _buildInfoRow(Icons.phone, 'الهاتف', widget.supplier.phone!),

                if (widget.supplier.email != null &&
                    widget.supplier.email!.isNotEmpty)
                  _buildInfoRow(
                    Icons.email,
                    'البريد الإلكتروني',
                    widget.supplier.email!,
                  ),

                if (widget.supplier.address != null &&
                    widget.supplier.address!.isNotEmpty)
                  _buildInfoRow(
                    Icons.location_on,
                    'العنوان',
                    widget.supplier.address!,
                  ),
              ],
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingMedium),

        BeautifulCard(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المعلومات التجارية',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMedium),

                if (widget.supplier.category != null &&
                    widget.supplier.category!.isNotEmpty)
                  _buildInfoRow(
                    Icons.category,
                    'الفئة',
                    widget.supplier.category!,
                  ),

                if (widget.supplier.taxNumber != null &&
                    widget.supplier.taxNumber!.isNotEmpty)
                  _buildInfoRow(
                    Icons.receipt_long,
                    'الرقم الضريبي',
                    widget.supplier.taxNumber!,
                  ),

                if (widget.supplier.creditLimit != null)
                  _buildInfoRow(
                    Icons.credit_card,
                    'حد الائتمان',
                    '${widget.supplier.creditLimit!.toStringAsFixed(2)} ر.س',
                  ),

                if (widget.supplier.paymentTerms != null)
                  _buildInfoRow(
                    Icons.schedule,
                    'شروط الدفع',
                    '${widget.supplier.paymentTerms} يوم',
                  ),
              ],
            ),
          ),
        ),

        if (widget.supplier.notes != null &&
            widget.supplier.notes!.isNotEmpty) ...[
          const SizedBox(height: AppTheme.spacingMedium),
          BeautifulCard(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ملاحظات',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  Text(widget.supplier.notes!),
                ],
              ),
            ),
          ),
        ],

        const SizedBox(height: AppTheme.spacingMedium),

        BeautifulCard(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات النظام',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMedium),

                _buildInfoRow(
                  Icons.calendar_today,
                  'تاريخ الإنشاء',
                  _formatDate(widget.supplier.createdAt),
                ),

                _buildInfoRow(
                  Icons.update,
                  'آخر تحديث',
                  _formatDate(widget.supplier.updatedAt),
                ),

                _buildInfoRow(
                  Icons.toggle_on,
                  'الحالة',
                  widget.supplier.isActive ? 'نشط' : 'غير نشط',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInvoicesTab() {
    return _isLoadingInvoices
        ? const Center(child: CircularProgressIndicator())
        : _supplierInvoices.isEmpty
        ? const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_outlined, size: 64, color: Colors.grey),
                SizedBox(height: AppTheme.spacingMedium),
                Text('لا توجد فواتير لهذا المورد'),
              ],
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            itemCount: _supplierInvoices.length,
            itemBuilder: (context, index) {
              final invoice = _supplierInvoices[index];
              return BeautifulCard(
                margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getInvoiceStatusColor(invoice.status),
                    child: Icon(
                      _getInvoiceStatusIcon(invoice.status),
                      color: Colors.white,
                    ),
                  ),
                  title: Text('فاتورة رقم: ${invoice.number}'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('التاريخ: ${_formatDate(invoice.date)}'),
                      Text(
                        'المبلغ: ${invoice.totalAmount.toStringAsFixed(2)} ر.س',
                      ),
                    ],
                  ),
                  trailing: Chip(
                    label: Text(_getInvoiceStatusText(invoice.status)),
                    backgroundColor: _getInvoiceStatusColor(
                      invoice.status,
                    ).withValues(alpha: 0.2),
                  ),
                ),
              );
            },
          );
  }

  Widget _buildStatisticsTab() {
    return _isLoadingStats
        ? const Center(child: CircularProgressIndicator())
        : ListView(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            children: [
              BeautifulCard(
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إحصائيات المورد',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                      ),
                      const SizedBox(height: AppTheme.spacingMedium),

                      _buildStatCard(
                        'إجمالي المشتريات',
                        '${_supplierStatistics['totalPurchases']?.toStringAsFixed(2) ?? '0.00'} ر.س',
                        Icons.shopping_cart,
                        Colors.blue,
                      ),

                      _buildStatCard(
                        'عدد الفواتير',
                        '${_supplierStatistics['invoiceCount'] ?? 0}',
                        Icons.receipt,
                        Colors.green,
                      ),

                      _buildStatCard(
                        'الرصيد الحالي',
                        '${_supplierStatistics['currentBalance']?.toStringAsFixed(2) ?? '0.00'} ر.س',
                        Icons.account_balance,
                        Colors.orange,
                      ),

                      _buildStatCard(
                        'آخر عملية شراء',
                        _supplierStatistics['lastPurchaseDate'] != null
                            ? _formatDate(
                                DateTime.parse(
                                  _supplierStatistics['lastPurchaseDate'],
                                ),
                              )
                            : 'لا توجد',
                        Icons.schedule,
                        Colors.purple,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: AppTheme.spacingSmall),
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w500)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Icon(icon, color: Colors.white, size: 24),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getInvoiceStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.posted:
        return Colors.blue.shade300;
      case InvoiceStatus.sent:
        return Colors.blue;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.partiallyPaid:
        return Colors.orange;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.orange;
    }
  }

  IconData _getInvoiceStatusIcon(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Icons.edit;
      case InvoiceStatus.posted:
        return Icons.post_add;
      case InvoiceStatus.sent:
        return Icons.send;
      case InvoiceStatus.paid:
        return Icons.check_circle;
      case InvoiceStatus.partiallyPaid:
        return Icons.payments;
      case InvoiceStatus.overdue:
        return Icons.warning;
      case InvoiceStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getInvoiceStatusText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.posted:
        return 'مرحلة';
      case InvoiceStatus.sent:
        return 'مرسلة';
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.partiallyPaid:
        return 'مدفوعة جزئياً';
      case InvoiceStatus.overdue:
        return 'متأخرة';
      case InvoiceStatus.cancelled:
        return 'ملغاة';
    }
  }
}
