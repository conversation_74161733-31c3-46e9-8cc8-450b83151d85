/// نماذج تكاليف المشروع والموارد
/// Project Cost and Resource Models for Smart Ledger
library;

import 'account.dart';
import 'item.dart';

/// تكاليف المشروع
class ProjectCost {
  final int? id;
  final int projectId;
  final int? phaseId;
  final String description;
  final CostType costType;
  final CostCategory category;
  final double amount;
  final double quantity;
  final double unitCost;
  final DateTime date;
  final int? accountId;
  final int? itemId;
  final int? supplierId;
  final String? reference;
  final bool isBillable;
  final bool isApproved;
  final String? approvedBy;
  final DateTime? approvedAt;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Account? account;
  Item? item;

  ProjectCost({
    this.id,
    required this.projectId,
    this.phaseId,
    required this.description,
    required this.costType,
    required this.category,
    required this.amount,
    this.quantity = 1.0,
    this.unitCost = 0.0,
    required this.date,
    this.accountId,
    this.itemId,
    this.supplierId,
    this.reference,
    this.isBillable = true,
    this.isApproved = false,
    this.approvedBy,
    this.approvedAt,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.account,
    this.item,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory ProjectCost.fromMap(Map<String, dynamic> map) {
    return ProjectCost(
      id: map['id'] as int?,
      projectId: map['project_id'] as int,
      phaseId: map['phase_id'] as int?,
      description: map['description'] as String,
      costType: CostType.fromString(map['cost_type'] as String),
      category: CostCategory.fromString(map['category'] as String),
      amount: (map['amount'] as num).toDouble(),
      quantity: (map['quantity'] as num?)?.toDouble() ?? 1.0,
      unitCost: (map['unit_cost'] as num?)?.toDouble() ?? 0.0,
      date: DateTime.parse(map['date'] as String),
      accountId: map['account_id'] as int?,
      itemId: map['item_id'] as int?,
      supplierId: map['supplier_id'] as int?,
      reference: map['reference'] as String?,
      isBillable: (map['is_billable'] as int) == 1,
      isApproved: (map['is_approved'] as int) == 1,
      approvedBy: map['approved_by'] as String?,
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'project_id': projectId,
      'phase_id': phaseId,
      'description': description,
      'cost_type': costType.value,
      'category': category.value,
      'amount': amount,
      'quantity': quantity,
      'unit_cost': unitCost,
      'date': date.toIso8601String().split('T')[0],
      'account_id': accountId,
      'item_id': itemId,
      'supplier_id': supplierId,
      'reference': reference,
      'is_billable': isBillable ? 1 : 0,
      'is_approved': isApproved ? 1 : 0,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// موارد المشروع
class ProjectResource {
  final int? id;
  final int projectId;
  final String name;
  final ResourceType resourceType;
  final double costPerHour;
  final double costPerDay;
  final double allocatedHours;
  final double actualHours;
  final DateTime startDate;
  final DateTime? endDate;
  final String? skills;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProjectResource({
    this.id,
    required this.projectId,
    required this.name,
    required this.resourceType,
    this.costPerHour = 0.0,
    this.costPerDay = 0.0,
    this.allocatedHours = 0.0,
    this.actualHours = 0.0,
    required this.startDate,
    this.endDate,
    this.skills,
    this.notes,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory ProjectResource.fromMap(Map<String, dynamic> map) {
    return ProjectResource(
      id: map['id'] as int?,
      projectId: map['project_id'] as int,
      name: map['name'] as String,
      resourceType: ResourceType.fromString(map['resource_type'] as String),
      costPerHour: (map['cost_per_hour'] as num?)?.toDouble() ?? 0.0,
      costPerDay: (map['cost_per_day'] as num?)?.toDouble() ?? 0.0,
      allocatedHours: (map['allocated_hours'] as num?)?.toDouble() ?? 0.0,
      actualHours: (map['actual_hours'] as num?)?.toDouble() ?? 0.0,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      skills: map['skills'] as String?,
      notes: map['notes'] as String?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'project_id': projectId,
      'name': name,
      'resource_type': resourceType.value,
      'cost_per_hour': costPerHour,
      'cost_per_day': costPerDay,
      'allocated_hours': allocatedHours,
      'actual_hours': actualHours,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'skills': skills,
      'notes': notes,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// حساب التكلفة الإجمالية للمورد
  double get totalCost => actualHours * costPerHour;

  /// حساب نسبة الاستخدام
  double get utilizationPercentage {
    if (allocatedHours == 0) return 0.0;
    return (actualHours / allocatedHours) * 100;
  }
}

/// إدخالات الوقت للمشروع
class ProjectTimeEntry {
  final int? id;
  final int projectId;
  final int? phaseId;
  final int? taskId;
  final int resourceId;
  final DateTime date;
  final double hours;
  final String description;
  final TimeEntryType entryType;
  final bool isBillable;
  final double? hourlyRate;
  final String? notes;
  final bool isApproved;
  final String? approvedBy;
  final DateTime? approvedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProjectTimeEntry({
    this.id,
    required this.projectId,
    this.phaseId,
    this.taskId,
    required this.resourceId,
    required this.date,
    required this.hours,
    required this.description,
    this.entryType = TimeEntryType.regular,
    this.isBillable = true,
    this.hourlyRate,
    this.notes,
    this.isApproved = false,
    this.approvedBy,
    this.approvedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory ProjectTimeEntry.fromMap(Map<String, dynamic> map) {
    return ProjectTimeEntry(
      id: map['id'] as int?,
      projectId: map['project_id'] as int,
      phaseId: map['phase_id'] as int?,
      taskId: map['task_id'] as int?,
      resourceId: map['resource_id'] as int,
      date: DateTime.parse(map['date'] as String),
      hours: (map['hours'] as num).toDouble(),
      description: map['description'] as String,
      entryType: TimeEntryType.fromString(map['entry_type'] as String),
      isBillable: (map['is_billable'] as int) == 1,
      hourlyRate: (map['hourly_rate'] as num?)?.toDouble(),
      notes: map['notes'] as String?,
      isApproved: (map['is_approved'] as int) == 1,
      approvedBy: map['approved_by'] as String?,
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'project_id': projectId,
      'phase_id': phaseId,
      'task_id': taskId,
      'resource_id': resourceId,
      'date': date.toIso8601String().split('T')[0],
      'hours': hours,
      'description': description,
      'entry_type': entryType.value,
      'is_billable': isBillable ? 1 : 0,
      'hourly_rate': hourlyRate,
      'notes': notes,
      'is_approved': isApproved ? 1 : 0,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// حساب التكلفة الإجمالية لإدخال الوقت
  double get totalCost => hours * (hourlyRate ?? 0.0);
}

/// مهام المشروع
class ProjectTask {
  final int? id;
  final int projectId;
  final int? phaseId;
  final String name;
  final String? description;
  final TaskStatus status;
  final TaskPriority priority;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime? actualEndDate;
  final double estimatedHours;
  final double actualHours;
  final int? assignedTo;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProjectTask({
    this.id,
    required this.projectId,
    this.phaseId,
    required this.name,
    this.description,
    this.status = TaskStatus.notStarted,
    this.priority = TaskPriority.medium,
    this.startDate,
    this.endDate,
    this.actualEndDate,
    this.estimatedHours = 0.0,
    this.actualHours = 0.0,
    this.assignedTo,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory ProjectTask.fromMap(Map<String, dynamic> map) {
    return ProjectTask(
      id: map['id'] as int?,
      projectId: map['project_id'] as int,
      phaseId: map['phase_id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      status: TaskStatus.fromString(map['status'] as String),
      priority: TaskPriority.fromString(map['priority'] as String),
      startDate: map['start_date'] != null
          ? DateTime.parse(map['start_date'] as String)
          : null,
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      actualEndDate: map['actual_end_date'] != null
          ? DateTime.parse(map['actual_end_date'] as String)
          : null,
      estimatedHours: (map['estimated_hours'] as num?)?.toDouble() ?? 0.0,
      actualHours: (map['actual_hours'] as num?)?.toDouble() ?? 0.0,
      assignedTo: map['assigned_to'] as int?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'project_id': projectId,
      'phase_id': phaseId,
      'name': name,
      'description': description,
      'status': status.value,
      'priority': priority.value,
      'start_date': startDate?.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'actual_end_date': actualEndDate?.toIso8601String().split('T')[0],
      'estimated_hours': estimatedHours,
      'actual_hours': actualHours,
      'assigned_to': assignedTo,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من المهمة
  ProjectTask copyWith({
    int? id,
    int? projectId,
    int? phaseId,
    String? name,
    String? description,
    TaskStatus? status,
    TaskPriority? priority,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? actualEndDate,
    double? estimatedHours,
    double? actualHours,
    int? assignedTo,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProjectTask(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      phaseId: phaseId ?? this.phaseId,
      name: name ?? this.name,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      actualEndDate: actualEndDate ?? this.actualEndDate,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      assignedTo: assignedTo ?? this.assignedTo,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// أنواع التكاليف
enum CostType {
  labor('labor', 'عمالة'),
  material('material', 'مواد'),
  equipment('equipment', 'معدات'),
  overhead('overhead', 'تكاليف عامة'),
  subcontractor('subcontractor', 'مقاولين فرعيين'),
  travel('travel', 'سفر'),
  other('other', 'أخرى');

  const CostType(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static CostType fromString(String value) {
    return CostType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => CostType.other,
    );
  }
}

/// فئات التكاليف
enum CostCategory {
  direct('direct', 'مباشرة'),
  indirect('indirect', 'غير مباشرة'),
  fixed('fixed', 'ثابتة'),
  variable('variable', 'متغيرة');

  const CostCategory(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static CostCategory fromString(String value) {
    return CostCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => CostCategory.direct,
    );
  }
}

/// أنواع الموارد
enum ResourceType {
  human('human', 'بشري'),
  equipment('equipment', 'معدات'),
  material('material', 'مواد'),
  facility('facility', 'مرافق');

  const ResourceType(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static ResourceType fromString(String value) {
    return ResourceType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ResourceType.human,
    );
  }
}

/// أنواع إدخالات الوقت
enum TimeEntryType {
  regular('regular', 'عادي'),
  overtime('overtime', 'إضافي'),
  holiday('holiday', 'عطلة'),
  sick('sick', 'مرضي');

  const TimeEntryType(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static TimeEntryType fromString(String value) {
    return TimeEntryType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TimeEntryType.regular,
    );
  }
}

/// حالات المهام
enum TaskStatus {
  notStarted('not_started', 'لم تبدأ'),
  inProgress('in_progress', 'قيد التنفيذ'),
  completed('completed', 'مكتملة'),
  onHold('on_hold', 'معلقة'),
  cancelled('cancelled', 'ملغية');

  const TaskStatus(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static TaskStatus fromString(String value) {
    return TaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TaskStatus.notStarted,
    );
  }
}

/// أولويات المهام
enum TaskPriority {
  low('low', 'منخفضة'),
  medium('medium', 'متوسطة'),
  high('high', 'عالية'),
  critical('critical', 'حرجة');

  const TaskPriority(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static TaskPriority fromString(String value) {
    return TaskPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => TaskPriority.medium,
    );
  }
}
