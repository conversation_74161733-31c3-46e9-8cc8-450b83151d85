/// شاشة الأصناف المحسنة
/// Enhanced Items Screen
library;

import 'package:flutter/material.dart';
import '../../models/item.dart';
import '../../services/item_service.dart';
import '../../theme/app_theme.dart';
import 'add_item_screen.dart';
import 'item_details_screen.dart';
import '../warehouse/enhanced_stock_movements_screen.dart';

class EnhancedItemsScreen extends StatefulWidget {
  const EnhancedItemsScreen({super.key});

  @override
  State<EnhancedItemsScreen> createState() => _EnhancedItemsScreenState();
}

class _EnhancedItemsScreenState extends State<EnhancedItemsScreen>
    with TickerProviderStateMixin {
  final ItemService _itemService = ItemService();
  final TextEditingController _searchController = TextEditingController();

  List<Item> _allItems = [];
  List<Item> _filteredItems = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  List<String> _categories = ['الكل'];
  String _sortBy = 'name'; // name, code, category, stock
  bool _sortAscending = true;

  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(length: 4, vsync: this);
    _loadItems();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadItems() async {
    setState(() => _isLoading = true);

    try {
      final result = await _itemService.getAllItems();
      if (mounted) {
        if (result.isSuccess && result.data != null) {
          setState(() {
            _allItems = result.data!;
            _categories = ['الكل', ..._getUniqueCategories()];
            _applyFilters();
          });
        } else {
          _showErrorSnackBar(result.error ?? 'خطأ في تحميل الأصناف');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل الأصناف: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  List<String> _getUniqueCategories() {
    final categories = _allItems
        .map((item) => item.category ?? 'غير محدد')
        .toSet()
        .toList();
    categories.sort();
    return categories;
  }

  void _applyFilters() {
    List<Item> filtered = List.from(_allItems);

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        return item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            item.code.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            (item.barcode?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                false);
      }).toList();
    }

    // تطبيق فلتر الفئة
    if (_selectedCategory != 'الكل') {
      filtered = filtered
          .where((item) => (item.category ?? 'غير محدد') == _selectedCategory)
          .toList();
    }

    // تطبيق الترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'code':
          comparison = a.code.compareTo(b.code);
          break;
        case 'category':
          comparison = (a.category ?? '').compareTo(b.category ?? '');
          break;
        case 'stock':
          comparison = a.currentStock.compareTo(b.currentStock);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    setState(() {
      _filteredItems = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'إدارة الأصناف',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showSortOptions,
            icon: const Icon(Icons.sort),
            tooltip: 'ترتيب',
          ),
          IconButton(
            onPressed: _loadItems,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.inventory)),
            Tab(text: 'متوفر', icon: Icon(Icons.check_circle)),
            Tab(text: 'منخفض', icon: Icon(Icons.warning)),
            Tab(text: 'نفد', icon: Icon(Icons.error)),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildSearchAndFilterSection(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildItemsList(_filteredItems),
                  _buildItemsList(
                    _filteredItems
                        .where((item) => item.currentStock > 0)
                        .toList(),
                  ),
                  _buildItemsList(
                    _filteredItems
                        .where(
                          (item) =>
                              item.currentStock > 0 &&
                              item.currentStock <= (item.minStockLevel ?? 0),
                        )
                        .toList(),
                  ),
                  _buildItemsList(
                    _filteredItems
                        .where((item) => item.currentStock <= 0)
                        .toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToAddItem(),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('صنف جديد'),
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الأصناف...',
              prefixIcon: Icon(Icons.search, color: AppTheme.primaryColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
          ),
          const SizedBox(height: 12),
          // فلتر الفئات
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: InputDecoration(
              labelText: 'الفئة',
              prefixIcon: Icon(Icons.category, color: AppTheme.primaryColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            items: _categories.map((category) {
              return DropdownMenuItem<String>(
                value: category,
                child: Text(category),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedCategory = value;
                  _applyFilters();
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList(List<Item> items) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد أصناف',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة صنف جديد',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadItems,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: items.length,
        itemBuilder: (context, index) => _buildItemCard(items[index], index),
      ),
    );
  }

  Widget _buildItemCard(Item item, int index) {
    final stockStatus = _getStockStatus(item);
    final stockColor = _getStockColor(stockStatus);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () => _navigateToItemDetails(item),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // صورة الصنف أو أيقونة افتراضية
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.inventory_2,
                        color: AppTheme.primaryColor,
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // معلومات الصنف
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'كود: ${item.code}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          if (item.category != null) ...[
                            const SizedBox(height: 2),
                            Text(
                              'الفئة: ${item.category}',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    // حالة المخزون
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: stockColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            stockStatus,
                            style: TextStyle(
                              color: stockColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${item.currentStock.toStringAsFixed(0)} ${item.unit}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: stockColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // معلومات إضافية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'سعر البيع: ${item.sellingPrice.toStringAsFixed(2)} ر.س',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    Row(
                      children: [
                        IconButton(
                          onPressed: () => _navigateToEditItem(item),
                          icon: const Icon(Icons.edit, size: 20),
                          tooltip: 'تعديل',
                          color: Colors.blue,
                        ),
                        IconButton(
                          onPressed: () => _showItemOptions(item),
                          icon: const Icon(Icons.more_vert, size: 20),
                          tooltip: 'خيارات',
                          color: Colors.grey[600],
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getStockStatus(Item item) {
    if (item.currentStock <= 0) {
      return 'نفد';
    } else if (item.minStockLevel != null &&
        item.currentStock <= item.minStockLevel!) {
      return 'منخفض';
    } else {
      return 'متوفر';
    }
  }

  Color _getStockColor(String status) {
    switch (status) {
      case 'نفد':
        return Colors.red;
      case 'منخفض':
        return Colors.orange;
      case 'متوفر':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'ترتيب حسب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            _buildSortOption('الاسم', 'name', Icons.text_fields),
            _buildSortOption('الكود', 'code', Icons.qr_code),
            _buildSortOption('الفئة', 'category', Icons.category),
            _buildSortOption('المخزون', 'stock', Icons.inventory),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _sortAscending = true;
                        _applyFilters();
                      });
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.arrow_upward),
                    label: const Text('تصاعدي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _sortAscending
                          ? AppTheme.primaryColor
                          : Colors.grey[300],
                      foregroundColor: _sortAscending
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _sortAscending = false;
                        _applyFilters();
                      });
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.arrow_downward),
                    label: const Text('تنازلي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: !_sortAscending
                          ? AppTheme.primaryColor
                          : Colors.grey[300],
                      foregroundColor: !_sortAscending
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String title, String value, IconData icon) {
    final isSelected = _sortBy == value;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryColor : Colors.grey[600],
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? AppTheme.primaryColor : Colors.black,
        ),
      ),
      trailing: isSelected
          ? Icon(Icons.check, color: AppTheme.primaryColor)
          : null,
      onTap: () {
        setState(() {
          _sortBy = value;
          _applyFilters();
        });
        Navigator.pop(context);
      },
    );
  }

  void _navigateToAddItem() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddItemScreen()),
    );

    if (result == true) {
      _loadItems();
    }
  }

  void _navigateToEditItem(Item item) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => AddItemScreen(item: item)),
    );

    if (result == true) {
      _loadItems();
    }
  }

  void _navigateToItemDetails(Item item) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ItemDetailsScreen(item: item)),
    );
  }

  void _navigateToStockMovements(Item item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedStockMovementsScreen(itemId: item.id),
      ),
    );
  }

  void _showItemOptions(Item item) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              item.name,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 20),
            _buildOptionTile(
              icon: Icons.visibility,
              title: 'عرض التفاصيل',
              onTap: () {
                Navigator.pop(context);
                _navigateToItemDetails(item);
              },
            ),
            _buildOptionTile(
              icon: Icons.edit,
              title: 'تعديل',
              onTap: () {
                Navigator.pop(context);
                _navigateToEditItem(item);
              },
            ),
            _buildOptionTile(
              icon: Icons.inventory,
              title: 'حركات المخزون',
              onTap: () {
                Navigator.pop(context);
                _navigateToStockMovements(item);
              },
            ),
            _buildOptionTile(
              icon: Icons.copy,
              title: 'نسخ الصنف',
              onTap: () {
                Navigator.pop(context);
                _duplicateItem(item);
              },
            ),
            _buildOptionTile(
              icon: Icons.delete,
              title: 'حذف',
              color: Colors.red,
              onTap: () {
                Navigator.pop(context);
                _confirmDeleteItem(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? AppTheme.primaryColor),
      title: Text(
        title,
        style: TextStyle(color: color, fontWeight: FontWeight.w500),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }

  Future<void> _duplicateItem(Item item) async {
    final duplicatedItem = Item(
      code: '${item.code}_copy',
      name: '${item.name} (نسخة)',
      description: item.description,
      category: item.category,
      unit: item.unit,
      costPrice: item.costPrice,
      sellingPrice: item.sellingPrice,
      minStockLevel: item.minStockLevel,
      maxStockLevel: item.maxStockLevel,
      currentStock: 0, // Start with zero stock
      barcode: null, // Clear barcode for duplicate
      isActive: item.isActive,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddItemScreen(item: duplicatedItem),
      ),
    );

    if (result == true) {
      _loadItems();
    }
  }

  void _confirmDeleteItem(Item item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الصنف "${item.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteItem(item);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteItem(Item item) async {
    try {
      final result = await _itemService.deleteItem(item.id!);
      if (result.isSuccess) {
        _showSuccessSnackBar('تم حذف الصنف بنجاح');
        _loadItems();
      } else {
        _showErrorSnackBar(result.error ?? 'خطأ في حذف الصنف');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف الصنف: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
