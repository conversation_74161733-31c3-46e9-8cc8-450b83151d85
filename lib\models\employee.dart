import 'department.dart';
import 'payroll_components.dart';
import 'payroll_record.dart';

/// نموذج الموظف
/// Employee Model
class Employee {
  final int? id;
  final String employeeCode;
  final String firstName;
  final String lastName;
  final String? middleName;
  final String? nationalId;
  final String? passportNumber;
  final DateTime? birthDate;
  final EmployeeGender gender;
  final EmployeeMaritalStatus maritalStatus;
  final String? phone;
  final String? email;
  final String? address;
  final String? emergencyContact;
  final String? emergencyPhone;

  // معلومات الوظيفة
  final int departmentId;
  final int positionId;
  final DateTime hireDate;
  final DateTime? terminationDate;
  final EmployeeStatus status;
  final EmployeeType employeeType;
  final String? directManagerId;

  // معلومات الراتب
  final double basicSalary;
  final String? bankAccount;
  final String? bankName;
  final String? iban;

  // إعدادات أخرى
  final bool isActive;
  final String? notes;
  final String? profileImagePath;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Department? department;
  Position? position;
  Employee? directManager;
  List<EmployeeAllowance> allowances = [];
  List<EmployeeDeduction> deductions = [];
  List<PayrollRecord> payrollRecords = [];

  Employee({
    this.id,
    required this.employeeCode,
    required this.firstName,
    required this.lastName,
    this.middleName,
    this.nationalId,
    this.passportNumber,
    this.birthDate,
    this.gender = EmployeeGender.male,
    this.maritalStatus = EmployeeMaritalStatus.single,
    this.phone,
    this.email,
    this.address,
    this.emergencyContact,
    this.emergencyPhone,
    required this.departmentId,
    required this.positionId,
    required this.hireDate,
    this.terminationDate,
    this.status = EmployeeStatus.active,
    this.employeeType = EmployeeType.fullTime,
    this.directManagerId,
    required this.basicSalary,
    this.bankAccount,
    this.bankName,
    this.iban,
    this.isActive = true,
    this.notes,
    this.profileImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// الاسم الكامل
  String get fullName {
    final parts = [
      firstName,
      middleName,
      lastName,
    ].where((part) => part != null && part.isNotEmpty).toList();
    return parts.join(' ');
  }

  /// العمر
  int? get age {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate!.year;
    if (now.month < birthDate!.month ||
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      age--;
    }
    return age;
  }

  /// سنوات الخدمة
  int get yearsOfService {
    final now = DateTime.now();
    final endDate = terminationDate ?? now;
    int years = endDate.year - hireDate.year;
    if (endDate.month < hireDate.month ||
        (endDate.month == hireDate.month && endDate.day < hireDate.day)) {
      years--;
    }
    return years;
  }

  /// إجمالي البدلات
  double get totalAllowances {
    return allowances.fold(0.0, (sum, allowance) => sum + allowance.amount);
  }

  /// إجمالي الاستقطاعات
  double get totalDeductions {
    return deductions.fold(0.0, (sum, deduction) => sum + deduction.amount);
  }

  /// صافي الراتب
  double get netSalary {
    return basicSalary + totalAllowances - totalDeductions;
  }

  /// Factory constructor from database map
  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'] as int?,
      employeeCode: map['employee_code'] as String,
      firstName: map['first_name'] as String,
      lastName: map['last_name'] as String,
      middleName: map['middle_name'] as String?,
      nationalId: map['national_id'] as String?,
      passportNumber: map['passport_number'] as String?,
      birthDate: map['birth_date'] != null
          ? DateTime.parse(map['birth_date'] as String)
          : null,
      gender: EmployeeGender.values.firstWhere(
        (e) => e.name == map['gender'],
        orElse: () => EmployeeGender.male,
      ),
      maritalStatus: EmployeeMaritalStatus.values.firstWhere(
        (e) => e.name == map['marital_status'],
        orElse: () => EmployeeMaritalStatus.single,
      ),
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      emergencyContact: map['emergency_contact'] as String?,
      emergencyPhone: map['emergency_phone'] as String?,
      departmentId: map['department_id'] as int,
      positionId: map['position_id'] as int,
      hireDate: DateTime.parse(map['hire_date'] as String),
      terminationDate: map['termination_date'] != null
          ? DateTime.parse(map['termination_date'] as String)
          : null,
      status: EmployeeStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => EmployeeStatus.active,
      ),
      employeeType: EmployeeType.values.firstWhere(
        (e) => e.name == map['employee_type'],
        orElse: () => EmployeeType.fullTime,
      ),
      directManagerId: map['direct_manager_id'] as String?,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      bankAccount: map['bank_account'] as String?,
      bankName: map['bank_name'] as String?,
      iban: map['iban'] as String?,
      isActive: (map['is_active'] as int?) == 1,
      notes: map['notes'] as String?,
      profileImagePath: map['profile_image_path'] as String?,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'first_name': firstName,
      'last_name': lastName,
      'middle_name': middleName,
      'national_id': nationalId,
      'passport_number': passportNumber,
      'birth_date': birthDate?.toIso8601String(),
      'gender': gender.name,
      'marital_status': maritalStatus.name,
      'phone': phone,
      'email': email,
      'address': address,
      'emergency_contact': emergencyContact,
      'emergency_phone': emergencyPhone,
      'department_id': departmentId,
      'position_id': positionId,
      'hire_date': hireDate.toIso8601String(),
      'termination_date': terminationDate?.toIso8601String(),
      'status': status.name,
      'employee_type': employeeType.name,
      'direct_manager_id': directManagerId,
      'basic_salary': basicSalary,
      'bank_account': bankAccount,
      'bank_name': bankName,
      'iban': iban,
      'is_active': isActive ? 1 : 0,
      'notes': notes,
      'profile_image_path': profileImagePath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  Employee copyWith({
    int? id,
    String? employeeCode,
    String? firstName,
    String? lastName,
    String? middleName,
    String? nationalId,
    String? passportNumber,
    DateTime? birthDate,
    EmployeeGender? gender,
    EmployeeMaritalStatus? maritalStatus,
    String? phone,
    String? email,
    String? address,
    String? emergencyContact,
    String? emergencyPhone,
    int? departmentId,
    int? positionId,
    DateTime? hireDate,
    DateTime? terminationDate,
    EmployeeStatus? status,
    EmployeeType? employeeType,
    String? directManagerId,
    double? basicSalary,
    String? bankAccount,
    String? bankName,
    String? iban,
    bool? isActive,
    String? notes,
    String? profileImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      middleName: middleName ?? this.middleName,
      nationalId: nationalId ?? this.nationalId,
      passportNumber: passportNumber ?? this.passportNumber,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      departmentId: departmentId ?? this.departmentId,
      positionId: positionId ?? this.positionId,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      status: status ?? this.status,
      employeeType: employeeType ?? this.employeeType,
      directManagerId: directManagerId ?? this.directManagerId,
      basicSalary: basicSalary ?? this.basicSalary,
      bankAccount: bankAccount ?? this.bankAccount,
      bankName: bankName ?? this.bankName,
      iban: iban ?? this.iban,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// تعدادات الموظف
enum EmployeeGender {
  male('ذكر'),
  female('أنثى');

  const EmployeeGender(this.displayName);
  final String displayName;
}

enum EmployeeMaritalStatus {
  single('أعزب'),
  married('متزوج'),
  divorced('مطلق'),
  widowed('أرمل');

  const EmployeeMaritalStatus(this.displayName);
  final String displayName;
}

enum EmployeeStatus {
  active('نشط'),
  inactive('غير نشط'),
  terminated('منتهي الخدمة'),
  suspended('موقوف');

  const EmployeeStatus(this.displayName);
  final String displayName;
}

enum EmployeeType {
  fullTime('دوام كامل'),
  partTime('دوام جزئي'),
  contract('تعاقد'),
  intern('متدرب'),
  consultant('استشاري');

  const EmployeeType(this.displayName);
  final String displayName;
}
