/// نماذج تقارير المخزون
/// Inventory Reports Models for Smart Ledger
library;

import 'stock_movement.dart';

// ==================== تقرير حالة المخزون ====================

/// تقرير حالة المخزون
class InventoryStatusReport {
  final List<InventoryStatusItem> items;
  final int totalItems;
  final double totalValue;
  final int lowStockCount;
  final int outOfStockCount;
  final DateTime generatedAt;

  InventoryStatusReport({
    required this.items,
    required this.totalItems,
    required this.totalValue,
    required this.lowStockCount,
    required this.outOfStockCount,
    required this.generatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'items': items.map((item) => item.toMap()).toList(),
      'total_items': totalItems,
      'total_value': totalValue,
      'low_stock_count': lowStockCount,
      'out_of_stock_count': outOfStockCount,
      'generated_at': generatedAt.toIso8601String(),
    };
  }
}

/// عنصر تقرير حالة المخزون
class InventoryStatusItem {
  final int itemId;
  final String itemCode;
  final String itemName;
  final String category;
  final String unit;
  final double currentStock;
  final double minStockLevel;
  final double maxStockLevel;
  final double averageCost;
  final double totalValue;
  final String warehouseName;
  final DateTime lastMovementDate;
  StockStatus status = StockStatus.normal;

  InventoryStatusItem({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.category,
    required this.unit,
    required this.currentStock,
    required this.minStockLevel,
    required this.maxStockLevel,
    required this.averageCost,
    required this.totalValue,
    required this.warehouseName,
    required this.lastMovementDate,
  });

  Map<String, dynamic> toMap() {
    return {
      'item_id': itemId,
      'item_code': itemCode,
      'item_name': itemName,
      'category': category,
      'unit': unit,
      'current_stock': currentStock,
      'min_stock_level': minStockLevel,
      'max_stock_level': maxStockLevel,
      'average_cost': averageCost,
      'total_value': totalValue,
      'warehouse_name': warehouseName,
      'last_movement_date': lastMovementDate.toIso8601String(),
      'status': status.toString(),
    };
  }
}

/// حالة المخزون
enum StockStatus { normal, lowStock, outOfStock, overStock }

// ==================== تقرير حركات المخزون ====================

/// تقرير حركات المخزون
class StockMovementReport {
  final DateTime fromDate;
  final DateTime toDate;
  final List<StockMovement> movements;
  final List<MovementSummary> summary;
  final double totalInValue;
  final double totalOutValue;
  final DateTime generatedAt;

  StockMovementReport({
    required this.fromDate,
    required this.toDate,
    required this.movements,
    required this.summary,
    required this.totalInValue,
    required this.totalOutValue,
    required this.generatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'from_date': fromDate.toIso8601String(),
      'to_date': toDate.toIso8601String(),
      'movements': movements.map((m) => m.toMap()).toList(),
      'summary': summary.map((s) => s.toMap()).toList(),
      'total_in_value': totalInValue,
      'total_out_value': totalOutValue,
      'generated_at': generatedAt.toIso8601String(),
    };
  }
}

/// ملخص حركات المخزون
class MovementSummary {
  final int itemId;
  final String itemCode;
  final String itemName;
  final int warehouseId;
  final String warehouseName;
  double totalInQuantity;
  double totalOutQuantity;
  double totalInValue;
  double totalOutValue;

  MovementSummary({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.warehouseId,
    required this.warehouseName,
    required this.totalInQuantity,
    required this.totalOutQuantity,
    required this.totalInValue,
    required this.totalOutValue,
  });

  double get netQuantity => totalInQuantity - totalOutQuantity;
  double get netValue => totalInValue - totalOutValue;

  Map<String, dynamic> toMap() {
    return {
      'item_id': itemId,
      'item_code': itemCode,
      'item_name': itemName,
      'warehouse_id': warehouseId,
      'warehouse_name': warehouseName,
      'total_in_quantity': totalInQuantity,
      'total_out_quantity': totalOutQuantity,
      'total_in_value': totalInValue,
      'total_out_value': totalOutValue,
      'net_quantity': netQuantity,
      'net_value': netValue,
    };
  }
}

// ==================== تقرير تقييم المخزون ====================

/// تقرير تقييم المخزون
class InventoryValuationReport {
  final DateTime asOfDate;
  final List<InventoryValuationItem> items;
  final double totalValue;
  final String valuationMethod;
  final DateTime generatedAt;

  InventoryValuationReport({
    required this.asOfDate,
    required this.items,
    required this.totalValue,
    required this.valuationMethod,
    required this.generatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'as_of_date': asOfDate.toIso8601String(),
      'items': items.map((item) => item.toMap()).toList(),
      'total_value': totalValue,
      'valuation_method': valuationMethod,
      'generated_at': generatedAt.toIso8601String(),
    };
  }
}

/// عنصر تقرير تقييم المخزون
class InventoryValuationItem {
  final int itemId;
  final String itemCode;
  final String itemName;
  final String category;
  final double quantity;
  final double unitCost;
  final double totalValue;
  final String warehouseName;
  final String valuationMethod;

  InventoryValuationItem({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.category,
    required this.quantity,
    required this.unitCost,
    required this.totalValue,
    required this.warehouseName,
    required this.valuationMethod,
  });

  Map<String, dynamic> toMap() {
    return {
      'item_id': itemId,
      'item_code': itemCode,
      'item_name': itemName,
      'category': category,
      'quantity': quantity,
      'unit_cost': unitCost,
      'total_value': totalValue,
      'warehouse_name': warehouseName,
      'valuation_method': valuationMethod,
    };
  }
}

// ==================== تنبيهات المخزون ====================

/// تنبيه المخزون
class StockAlert {
  final AlertType type;
  final int itemId;
  final String itemCode;
  final String itemName;
  final int warehouseId;
  final String warehouseName;
  final double currentStock;
  final double minStockLevel;
  final String message;
  final AlertSeverity severity;
  final DateTime createdAt;

  StockAlert({
    required this.type,
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.warehouseId,
    required this.warehouseName,
    required this.currentStock,
    required this.minStockLevel,
    required this.message,
    required this.severity,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'item_id': itemId,
      'item_code': itemCode,
      'item_name': itemName,
      'warehouse_id': warehouseId,
      'warehouse_name': warehouseName,
      'current_stock': currentStock,
      'min_stock_level': minStockLevel,
      'message': message,
      'severity': severity.toString(),
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// نوع التنبيه
enum AlertType { lowStock, outOfStock, overStock, expiringSoon, expired }

/// شدة التنبيه
enum AlertSeverity { info, warning, critical }

// ==================== اقتراحات تحسين المخزون ====================

/// اقتراح تحسين المخزون
class StockOptimizationSuggestion {
  final int itemId;
  final String itemCode;
  final String itemName;
  final double currentMinLevel;
  final double currentMaxLevel;
  final double suggestedMinLevel;
  final double suggestedMaxLevel;
  final double averageMonthlyUsage;
  final String reason;
  final double potentialSavings;

  StockOptimizationSuggestion({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.currentMinLevel,
    required this.currentMaxLevel,
    required this.suggestedMinLevel,
    required this.suggestedMaxLevel,
    required this.averageMonthlyUsage,
    required this.reason,
    required this.potentialSavings,
  });

  Map<String, dynamic> toMap() {
    return {
      'item_id': itemId,
      'item_code': itemCode,
      'item_name': itemName,
      'current_min_level': currentMinLevel,
      'current_max_level': currentMaxLevel,
      'suggested_min_level': suggestedMinLevel,
      'suggested_max_level': suggestedMaxLevel,
      'average_monthly_usage': averageMonthlyUsage,
      'reason': reason,
      'potential_savings': potentialSavings,
    };
  }
}

/// تحليل حركة الصنف
class ItemMovementAnalysis {
  final int itemId;
  double totalSales;
  double totalPurchases;
  int movementCount;
  double averageMonthlyUsage;

  ItemMovementAnalysis({
    required this.itemId,
    required this.totalSales,
    required this.totalPurchases,
    required this.movementCount,
    required this.averageMonthlyUsage,
  });
}
