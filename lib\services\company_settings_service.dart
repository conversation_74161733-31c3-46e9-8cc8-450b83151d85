import '../database/company_settings_dao.dart';
import '../models/company_settings.dart';
import '../utils/result.dart';

/// خدمة إدارة إعدادات الشركة
class CompanySettingsService {
  final CompanySettingsDao _settingsDao = CompanySettingsDao();

  /// الحصول على إعدادات الشركة
  Future<Result<CompanySettings>> getCompanySettings() async {
    try {
      CompanySettings? settings = await _settingsDao.getCompanySettings();
      if (settings != null) {
        return Result.success(settings);
      } else {
        // إنشاء إعدادات افتراضية إذا لم توجد
        CompanySettings defaultSettings = await _settingsDao
            .initializeDefaultSettings();
        return Result.success(defaultSettings);
      }
    } catch (e) {
      return Result.error('خطأ في الحصول على إعدادات الشركة: ${e.toString()}');
    }
  }

  /// تحديث إعدادات الشركة
  Future<Result<CompanySettings>> updateCompanySettings(
    CompanySettings settings,
  ) async {
    try {
      // التحقق من صحة البيانات
      List<String> validationErrors = settings.validate();
      if (validationErrors.isNotEmpty) {
        return Result.error('خطأ في البيانات: ${validationErrors.join(', ')}');
      }

      await _settingsDao.updateCompanySettings(settings);

      // الحصول على الإعدادات المحدثة
      CompanySettings? updatedSettings = await _settingsDao
          .getCompanySettings();
      if (updatedSettings != null) {
        return Result.success(updatedSettings);
      } else {
        return Result.error('فشل في تحديث الإعدادات');
      }
    } catch (e) {
      return Result.error('خطأ في تحديث إعدادات الشركة: ${e.toString()}');
    }
  }

  /// تحديث اسم الشركة
  Future<Result<void>> updateCompanyName(
    String companyName, {
    String? companyNameEn,
  }) async {
    try {
      if (companyName.trim().isEmpty) {
        return Result.error('اسم الشركة مطلوب');
      }

      await _settingsDao.updateCompanyName(
        companyName,
        companyNameEn: companyNameEn,
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث اسم الشركة: ${e.toString()}');
    }
  }

  /// تحديث معلومات الاتصال
  Future<Result<void>> updateContactInfo({
    String? address,
    String? phone,
    String? email,
    String? taxNumber,
  }) async {
    try {
      await _settingsDao.updateContactInfo(
        address: address,
        phone: phone,
        email: email,
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث معلومات الاتصال: ${e.toString()}');
    }
  }

  /// تحديث إعدادات العملة
  Future<Result<void>> updateCurrencySettings({
    String? currencyCode,
    String? currencySymbol,
    int? decimalPlaces,
  }) async {
    try {
      if (decimalPlaces != null && (decimalPlaces < 0 || decimalPlaces > 6)) {
        return Result.error('عدد الخانات العشرية يجب أن يكون بين 0 و 6');
      }

      await _settingsDao.updateCurrencySettings(
        currencyCode: currencyCode,
        currencySymbol: currencySymbol,
        decimalPlaces: decimalPlaces,
      );
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث إعدادات العملة: ${e.toString()}');
    }
  }

  /// تحديث إعدادات السنة المالية
  Future<Result<void>> updateFiscalYearSettings({
    String? fiscalYearStart,
    String? dateFormat,
  }) async {
    try {
      await _settingsDao.updateFiscalYearSettingsWithFormat(
        fiscalYearStart: fiscalYearStart!,
        dateFormat: dateFormat,
      );
      return Result.success(null);
    } catch (e) {
      return Result.error(
        'خطأ في تحديث إعدادات السنة المالية: ${e.toString()}',
      );
    }
  }

  /// الحصول على السنة المالية الحالية
  Future<Result<Map<String, DateTime>>> getCurrentFiscalYear() async {
    try {
      Map<String, DateTime> fiscalYear = await _settingsDao
          .getCurrentFiscalYear();
      return Result.success(fiscalYear);
    } catch (e) {
      return Result.error('خطأ في الحصول على السنة المالية: ${e.toString()}');
    }
  }

  /// التحقق من صحة الإعدادات
  Future<Result<List<String>>> validateSettings() async {
    try {
      List<String> errors = await _settingsDao.validateSettings();
      return Result.success(errors);
    } catch (e) {
      return Result.error('خطأ في التحقق من الإعدادات: ${e.toString()}');
    }
  }

  /// تصدير الإعدادات
  Future<Result<Map<String, dynamic>>> exportSettings() async {
    try {
      Map<String, dynamic>? settings = await _settingsDao.exportSettings();
      if (settings != null) {
        return Result.success(settings);
      } else {
        return Result.error('لا توجد إعدادات للتصدير');
      }
    } catch (e) {
      return Result.error('خطأ في تصدير الإعدادات: ${e.toString()}');
    }
  }

  /// استيراد الإعدادات
  Future<Result<void>> importSettings(Map<String, dynamic> settingsData) async {
    try {
      bool success = await _settingsDao.importSettings(settingsData);
      if (success) {
        return Result.success(null);
      } else {
        return Result.error('فشل في استيراد الإعدادات');
      }
    } catch (e) {
      return Result.error('خطأ في استيراد الإعدادات: ${e.toString()}');
    }
  }

  /// إعادة تعيين الإعدادات للقيم الافتراضية
  Future<Result<void>> resetToDefaults() async {
    try {
      bool success = await _settingsDao.resetToDefaults();
      if (success) {
        return Result.success(null);
      } else {
        return Result.error('فشل في إعادة تعيين الإعدادات');
      }
    } catch (e) {
      return Result.error('خطأ في إعادة تعيين الإعدادات: ${e.toString()}');
    }
  }

  /// الحصول على اسم الشركة للعرض
  Future<Result<String>> getCompanyDisplayName({
    bool useEnglish = false,
  }) async {
    try {
      String name = await _settingsDao.getCompanyDisplayName(
        useEnglish: useEnglish,
      );
      return Result.success(name);
    } catch (e) {
      return Result.error('خطأ في الحصول على اسم الشركة: ${e.toString()}');
    }
  }

  /// الحصول على معلومات الشركة للتقارير
  Future<Result<Map<String, String>>> getCompanyInfoForReports() async {
    try {
      Map<String, String> info = await _settingsDao.getCompanyInfoForReports();
      return Result.success(info);
    } catch (e) {
      return Result.error('خطأ في الحصول على معلومات الشركة: ${e.toString()}');
    }
  }

  /// التحقق من وجود إعدادات الشركة
  Future<Result<bool>> hasCompanySettings() async {
    try {
      bool hasSettings = await _settingsDao.hasCompanySettings();
      return Result.success(hasSettings);
    } catch (e) {
      return Result.error('خطأ في التحقق من الإعدادات: ${e.toString()}');
    }
  }

  /// إنشاء إعدادات افتراضية
  Future<Result<CompanySettings>> initializeDefaultSettings() async {
    try {
      CompanySettings settings = await _settingsDao.initializeDefaultSettings();
      return Result.success(settings);
    } catch (e) {
      return Result.error('خطأ في إنشاء الإعدادات الافتراضية: ${e.toString()}');
    }
  }

  /// تحديث مسار الشعار
  Future<Result<void>> updateLogoPath(String? logoPath) async {
    try {
      await _settingsDao.updateLogoPath(logoPath);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث مسار الشعار: ${e.toString()}');
    }
  }

  /// الحصول على قائمة العملات المدعومة
  List<Map<String, String>> getSupportedCurrencies() {
    return [
      {'code': 'SAR', 'symbol': 'ر.س', 'name': 'ريال سعودي'},
      {'code': 'USD', 'symbol': '\$', 'name': 'دولار أمريكي'},
      {'code': 'EUR', 'symbol': '€', 'name': 'يورو'},
      {'code': 'GBP', 'symbol': '£', 'name': 'جنيه إسترليني'},
      {'code': 'AED', 'symbol': 'د.إ', 'name': 'درهم إماراتي'},
      {'code': 'KWD', 'symbol': 'د.ك', 'name': 'دينار كويتي'},
      {'code': 'QAR', 'symbol': 'ر.ق', 'name': 'ريال قطري'},
      {'code': 'BHD', 'symbol': 'د.ب', 'name': 'دينار بحريني'},
      {'code': 'OMR', 'symbol': 'ر.ع', 'name': 'ريال عماني'},
      {'code': 'JOD', 'symbol': 'د.أ', 'name': 'دينار أردني'},
      {'code': 'EGP', 'symbol': 'ج.م', 'name': 'جنيه مصري'},
    ];
  }

  /// الحصول على قائمة تنسيقات التاريخ المدعومة
  List<Map<String, String>> getSupportedDateFormats() {
    return [
      {'format': 'dd/MM/yyyy', 'example': '31/12/2023'},
      {'format': 'MM/dd/yyyy', 'example': '12/31/2023'},
      {'format': 'yyyy-MM-dd', 'example': '2023-12-31'},
      {'format': 'dd-MM-yyyy', 'example': '31-12-2023'},
      {'format': 'yyyy/MM/dd', 'example': '2023/12/31'},
    ];
  }
}
