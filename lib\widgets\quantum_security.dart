import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🔐 نظام الأمان الكمي المستقبلي
/// Futuristic Quantum Security System
///
/// هذا الملف يحتوي على نظام أمان كمي لا مثيل له في التاريخ
/// This file contains unprecedented quantum security system in history

/// 🌟 لوحة الأمان الكمي
/// Quantum Security Dashboard
class QuantumSecurityDashboard extends StatefulWidget {
  const QuantumSecurityDashboard({super.key});

  @override
  State<QuantumSecurityDashboard> createState() =>
      _QuantumSecurityDashboardState();
}

class _QuantumSecurityDashboardState extends State<QuantumSecurityDashboard>
    with TickerProviderStateMixin {
  late AnimationController _shieldController;
  late AnimationController _scanController;
  late AnimationController _encryptionController;
  late Animation<double> _shieldAnimation;
  late Animation<double> _scanAnimation;
  late Animation<double> _encryptionAnimation;

  @override
  void initState() {
    super.initState();

    _shieldController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _scanController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _encryptionController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _shieldAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _shieldController, curve: Curves.easeInOut),
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scanController, curve: Curves.easeInOut),
    );

    _encryptionAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _encryptionController, curve: Curves.easeInOut),
    );

    _shieldController.repeat();
    _scanController.repeat();
    _encryptionController.repeat();
  }

  @override
  void dispose() {
    _shieldController.dispose();
    _scanController.dispose();
    _encryptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _shieldAnimation,
        _scanAnimation,
        _encryptionAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.0,
          child: QuantumEnergyEffect(
            intensity: 1.5 + (_shieldAnimation.value * 0.5),
            primaryColor: const Color(0xFF00E676),
            secondaryColor: const Color(0xFF4CAF50),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF1B5E20).withValues(alpha: 0.9),
                    const Color(0xFF2E7D32).withValues(alpha: 0.8),
                    const Color(0xFF388E3C).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: const Color(0xFF00E676).withValues(alpha: 0.5),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF00E676).withValues(alpha: 0.4),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس الأمان الكمي
                  Row(
                    children: [
                      Transform.scale(
                        scale: 1.0 + (_shieldAnimation.value * 0.2),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFF00E676).withValues(alpha: 0.8),
                                const Color(0xFF4CAF50).withValues(alpha: 0.6),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.4),
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.security_rounded,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🔐 الأمان الكمي',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.5,
                                  ),
                            ),
                            Text(
                              'حماية متقدمة بالتشفير الكمي',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.8),
                                  ),
                            ),
                          ],
                        ),
                      ),
                      // مؤشر الحالة
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF00E676).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: const Color(
                              0xFF00E676,
                            ).withValues(alpha: 0.5),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: const Color(0xFF00E676),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            const SizedBox(width: 6),
                            const Text(
                              'آمن',
                              style: TextStyle(
                                color: Color(0xFF00E676),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // مؤشرات الأمان
                  Row(
                    children: [
                      Expanded(
                        child: _buildSecurityMetric(
                          'مستوى التشفير',
                          'AES-256 Quantum',
                          Icons.lock_rounded,
                          const Color(0xFF00E676),
                          _encryptionAnimation.value,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: _buildSecurityMetric(
                          'المسح الأمني',
                          '99.9% آمن',
                          Icons.radar_rounded,
                          const Color(0xFF4CAF50),
                          _scanAnimation.value,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // درع الحماية الكمي
                  _buildQuantumShield(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // سجل الأمان
                  _buildSecurityLog(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء مؤشر الأمان
  Widget _buildSecurityMetric(
    String title,
    String value,
    IconData icon,
    Color color,
    double progress,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  value: progress,
                  strokeWidth: 3,
                  backgroundColor: color.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                ),
              ),
              Icon(icon, color: color, size: 20),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء درع الحماية الكمي
  Widget _buildQuantumShield() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF00E676).withValues(alpha: 0.3),
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الدوائر الكمية
          ...List.generate(4, (index) {
            final radius = 20.0 + (index * 15.0);
            final opacity = 0.8 - (index * 0.15);
            return Container(
              width: radius * 2,
              height: radius * 2,
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(
                    0xFF00E676,
                  ).withValues(alpha: opacity * _shieldAnimation.value),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(radius),
              ),
            );
          }),

          // النقاط الكمية
          ...List.generate(12, (index) {
            final angle = (index / 12) * 2 * math.pi;
            final radius = 35.0;
            return Positioned(
              left:
                  150 +
                  radius *
                      math.cos(angle + _shieldAnimation.value * 2 * math.pi),
              top:
                  60 +
                  radius *
                      math.sin(angle + _shieldAnimation.value * 2 * math.pi),
              child: Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(
                    0xFF00E676,
                  ).withValues(alpha: 0.6 + (_shieldAnimation.value * 0.4)),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            );
          }),

          // المركز
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  const Color(0xFF00E676).withValues(alpha: 0.8),
                  const Color(0xFF4CAF50).withValues(alpha: 0.4),
                  Colors.transparent,
                ],
              ),
              borderRadius: BorderRadius.circular(30),
            ),
            child: const Icon(
              Icons.shield_rounded,
              color: Colors.white,
              size: 30,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء سجل الأمان
  Widget _buildSecurityLog() {
    final logs = [
      SecurityLogEntry(
        'تم تشفير البيانات بنجاح',
        DateTime.now().subtract(const Duration(minutes: 2)),
      ),
      SecurityLogEntry(
        'فحص أمني مكتمل',
        DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      SecurityLogEntry(
        'تحديث بروتوكولات الأمان',
        DateTime.now().subtract(const Duration(minutes: 8)),
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📋 سجل الأمان',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        ...logs.map((log) => _buildLogEntry(log)),
      ],
    );
  }

  Widget _buildLogEntry(SecurityLogEntry log) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: const Color(0xFF00E676).withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: const Color(0xFF00E676),
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: Text(
              log.message,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
          Text(
            '${log.timestamp.hour.toString().padLeft(2, '0')}:${log.timestamp.minute.toString().padLeft(2, '0')}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج إدخال سجل الأمان
class SecurityLogEntry {
  final String message;
  final DateTime timestamp;

  SecurityLogEntry(this.message, this.timestamp);
}

/// 🔒 بطاقة التشفير الكمي
/// Quantum Encryption Card
class QuantumEncryptionCard extends StatefulWidget {
  const QuantumEncryptionCard({super.key});

  @override
  State<QuantumEncryptionCard> createState() => _QuantumEncryptionCardState();
}

class _QuantumEncryptionCardState extends State<QuantumEncryptionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return HologramEffect(
          intensity: 1.0 + (_animation.value * 0.5),
          child: Container(
            margin: const EdgeInsets.all(AppTheme.spacingMedium),
            padding: const EdgeInsets.all(AppTheme.spacingLarge),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF1A237E).withValues(alpha: 0.9),
                  const Color(0xFF283593).withValues(alpha: 0.8),
                  const Color(0xFF3949AB).withValues(alpha: 0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: const Color(0xFF3F51B5).withValues(alpha: 0.5),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF3F51B5).withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس التشفير
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF3F51B5), Color(0xFF5C6BC0)],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Icon(
                        Icons.enhanced_encryption_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Text(
                      '🔒 التشفير الكمي',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.spacingLarge),

                // معلومات التشفير
                _buildEncryptionInfo(
                  'خوارزمية التشفير',
                  'AES-256 Quantum Enhanced',
                ),
                _buildEncryptionInfo(
                  'طول المفتاح',
                  '2048-bit RSA + Quantum Key',
                ),
                _buildEncryptionInfo(
                  'مستوى الأمان',
                  'Military Grade + Quantum',
                ),
                _buildEncryptionInfo('حالة التشفير', 'نشط ومحمي'),

                const SizedBox(height: AppTheme.spacingLarge),

                // شريط التقدم
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'قوة التشفير',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      LinearProgressIndicator(
                        value: 0.95 + (_animation.value * 0.05),
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFF00E676),
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      const Text(
                        '99.8% محمي',
                        style: TextStyle(
                          color: Color(0xFF00E676),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEncryptionInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 13,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 13,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
