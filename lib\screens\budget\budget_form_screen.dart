/// شاشة إضافة/تعديل الموازنة
/// Budget Form Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/budget.dart';
import '../../models/account.dart';
import '../../services/budget_service.dart';
import '../../services/account_service.dart' as account_service;
import '../../utils/result.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/error_dialog.dart';
import '../../widgets/loading_overlay.dart';

class BudgetFormScreen extends StatefulWidget {
  final Budget? budget;

  const BudgetFormScreen({super.key, this.budget});

  @override
  State<BudgetFormScreen> createState() => _BudgetFormScreenState();
}

class _BudgetFormScreenState extends State<BudgetFormScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final BudgetService _budgetService = BudgetService();
  final account_service.AccountService _accountService =
      account_service.AccountService();

  // Form controllers
  late TextEditingController _codeController;
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;

  // Form state
  BudgetType _selectedType = BudgetType.operational;
  BudgetPeriod _selectedPeriod = BudgetPeriod.annual;
  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now().add(const Duration(days: 365));

  List<BudgetLine> _budgetLines = [];
  List<Account> _availableAccounts = [];
  bool _isLoading = false;
  bool get _isEditing => widget.budget != null;

  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadAccounts();
    if (_isEditing) {
      _loadBudgetData();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _codeController = TextEditingController();
    _nameController = TextEditingController();
    _descriptionController = TextEditingController();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  void _loadBudgetData() {
    final budget = widget.budget!;
    _codeController.text = budget.code;
    _nameController.text = budget.name;
    _descriptionController.text = budget.description ?? '';
    _selectedType = budget.type;
    _selectedPeriod = budget.period;
    _startDate = budget.startDate;
    _endDate = budget.endDate;
    _budgetLines = List.from(budget.lines);
  }

  Future<void> _loadAccounts() async {
    setState(() => _isLoading = true);

    try {
      final result = await _accountService.getAllAccounts();
      if (result.isSuccess) {
        setState(() {
          _availableAccounts = result.data!;
        });
      } else {
        _showError('خطأ في تحميل الحسابات: ${result.error}');
      }
    } catch (e) {
      _showError('خطأ في تحميل الحسابات: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveBudget() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_budgetLines.isEmpty) {
      _showError('يجب إضافة بند واحد على الأقل للموازنة');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final budget = Budget(
        id: _isEditing ? widget.budget!.id : null,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        type: _selectedType,
        period: _selectedPeriod,
        startDate: _startDate,
        endDate: _endDate,
        lines: _budgetLines,
        createdAt: _isEditing ? widget.budget!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final Result<Budget> result;
      if (_isEditing) {
        result = await _budgetService.updateBudget(budget);
      } else {
        result = await _budgetService.createBudget(budget);
      }

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isEditing
                    ? 'تم تحديث الموازنة بنجاح'
                    : 'تم إنشاء الموازنة بنجاح',
              ),
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        _showError(result.error!);
      }
    } catch (e) {
      _showError('خطأ في حفظ الموازنة: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل الموازنة' : 'إنشاء موازنة جديدة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _saveBudget,
            icon: const Icon(Icons.save),
            tooltip: 'حفظ',
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildBasicInfoCard(),
                    const SizedBox(height: 16),
                    _buildPeriodCard(),
                    const SizedBox(height: 16),
                    _buildBudgetLinesCard(),
                    const SizedBox(height: 24),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _codeController,
                    labelText: 'كود الموازنة *',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'كود الموازنة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<BudgetType>(
                    value: _selectedType,
                    items: BudgetType.values
                        .map(
                          (type) => DropdownMenuItem(
                            value: type,
                            child: Text(type.displayName),
                          ),
                        )
                        .toList(),
                    onChanged: (value) =>
                        setState(() => _selectedType = value!),
                    labelText: 'نوع الموازنة *',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _nameController,
              labelText: 'اسم الموازنة *',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم الموازنة مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _descriptionController,
              labelText: 'الوصف (اختياري)',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فترة الموازنة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            QuantumDropdown<BudgetPeriod>(
              value: _selectedPeriod,
              items: BudgetPeriod.values
                  .map(
                    (period) => DropdownMenuItem(
                      value: period,
                      child: Text(period.displayName),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPeriod = value!;
                  _updateEndDateBasedOnPeriod();
                });
              },
              labelText: 'نوع الفترة *',
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, true),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 16,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, color: Colors.blue),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تاريخ البداية',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                              Text(
                                '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, false),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 16,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, color: Colors.blue),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تاريخ النهاية',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                              Text(
                                '${_endDate.day}/${_endDate.month}/${_endDate.year}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetLinesCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'بنود الموازنة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                QuantumButton(
                  onPressed: _addBudgetLine,
                  text: 'إضافة بند',
                  icon: Icons.add,
                  variant: QuantumButtonVariant.outline,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_budgetLines.isEmpty)
              Container(
                padding: const EdgeInsets.all(32),
                child: const Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.account_balance_wallet_outlined,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لم يتم إضافة أي بنود للموازنة بعد',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'اضغط على "إضافة بند" لبدء إنشاء الموازنة',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              )
            else
              ...List.generate(_budgetLines.length, (index) {
                return _buildBudgetLineItem(_budgetLines[index], index);
              }),
            if (_budgetLines.isNotEmpty) ...[
              const Divider(),
              _buildBudgetSummary(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetLineItem(BudgetLine line, int index) {
    final account = _availableAccounts.firstWhere(
      (acc) => acc.id == line.accountId,
      orElse: () => Account(
        id: line.accountId,
        code: 'غير معروف',
        name: 'حساب غير معروف',
        nameEn: 'Unknown Account',
        accountType: AccountType.asset,
        parentId: null,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  account.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  account.code,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${line.budgetAmount.toStringAsFixed(2)} ر.س',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          IconButton(
            onPressed: () => _editBudgetLine(index),
            icon: const Icon(Icons.edit, color: Colors.blue),
            tooltip: 'تعديل',
          ),
          IconButton(
            onPressed: () => _removeBudgetLine(index),
            icon: const Icon(Icons.delete, color: Colors.red),
            tooltip: 'حذف',
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetSummary() {
    final totalAmount = _budgetLines.fold<double>(
      0.0,
      (sum, line) => sum + line.budgetAmount,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'إجمالي الموازنة:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(
            '${totalAmount.toStringAsFixed(2)} ر.س',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: QuantumButton(
            onPressed: () => Navigator.pop(context),
            text: 'إلغاء',
            variant: QuantumButtonVariant.outline,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: QuantumButton(
            onPressed: _saveBudget,
            text: _isEditing ? 'تحديث' : 'حفظ',
          ),
        ),
      ],
    );
  }

  // Helper methods
  void _updateEndDateBasedOnPeriod() {
    switch (_selectedPeriod) {
      case BudgetPeriod.monthly:
        _endDate = DateTime(_startDate.year, _startDate.month + 1, 0);
        break;
      case BudgetPeriod.quarterly:
        _endDate = DateTime(_startDate.year, _startDate.month + 3, 0);
        break;
      case BudgetPeriod.semiAnnual:
        _endDate = DateTime(_startDate.year, _startDate.month + 6, 0);
        break;
      case BudgetPeriod.annual:
        _endDate = DateTime(
          _startDate.year + 1,
          _startDate.month,
          _startDate.day - 1,
        );
        break;
      case BudgetPeriod.biennial:
        _endDate = DateTime(
          _startDate.year + 2,
          _startDate.month,
          _startDate.day - 1,
        );
        break;
      case BudgetPeriod.custom:
        // Keep current end date for custom period
        break;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          if (_selectedPeriod != BudgetPeriod.custom) {
            _updateEndDateBasedOnPeriod();
          }
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _addBudgetLine() {
    showDialog(
      context: context,
      builder: (context) => _BudgetLineDialog(
        availableAccounts: _availableAccounts,
        existingAccountIds: _budgetLines.map((line) => line.accountId).toSet(),
        onSave: (accountId, amount, notes) {
          setState(() {
            _budgetLines.add(
              BudgetLine(
                budgetId: 0, // Will be set when saving
                accountId: accountId,
                budgetAmount: amount,
                notes: notes,
              ),
            );
          });
        },
      ),
    );
  }

  void _editBudgetLine(int index) {
    final line = _budgetLines[index];
    showDialog(
      context: context,
      builder: (context) => _BudgetLineDialog(
        availableAccounts: _availableAccounts,
        existingAccountIds: _budgetLines
            .where((l) => l != line)
            .map((l) => l.accountId)
            .toSet(),
        initialAccountId: line.accountId,
        initialAmount: line.budgetAmount,
        initialNotes: line.notes,
        onSave: (accountId, amount, notes) {
          setState(() {
            _budgetLines[index] = BudgetLine(
              id: line.id,
              budgetId: line.budgetId,
              accountId: accountId,
              budgetAmount: amount,
              actualAmount: line.actualAmount,
              notes: notes,
              createdAt: line.createdAt,
              updatedAt: DateTime.now(),
            );
          });
        },
      ),
    );
  }

  void _removeBudgetLine(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل تريد حذف هذا البند من الموازنة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _budgetLines.removeAt(index);
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

// Budget Line Dialog Widget
class _BudgetLineDialog extends StatefulWidget {
  final List<Account> availableAccounts;
  final Set<int> existingAccountIds;
  final int? initialAccountId;
  final double? initialAmount;
  final String? initialNotes;
  final Function(int accountId, double amount, String? notes) onSave;

  const _BudgetLineDialog({
    required this.availableAccounts,
    required this.existingAccountIds,
    required this.onSave,
    this.initialAccountId,
    this.initialAmount,
    this.initialNotes,
  });

  @override
  State<_BudgetLineDialog> createState() => _BudgetLineDialogState();
}

class _BudgetLineDialogState extends State<_BudgetLineDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _amountController;
  late TextEditingController _notesController;

  int? _selectedAccountId;

  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController(
      text: widget.initialAmount?.toString() ?? '',
    );
    _notesController = TextEditingController(text: widget.initialNotes ?? '');
    _selectedAccountId = widget.initialAccountId;
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final availableAccounts = widget.availableAccounts
        .where(
          (account) =>
              !widget.existingAccountIds.contains(account.id) ||
              account.id == widget.initialAccountId,
        )
        .toList();

    return AlertDialog(
      title: Text(
        widget.initialAccountId == null ? 'إضافة بند جديد' : 'تعديل البند',
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            QuantumDropdown<int>(
              value: _selectedAccountId,
              items: availableAccounts
                  .map(
                    (account) => DropdownMenuItem(
                      value: account.id,
                      child: Text('${account.code} - ${account.name}'),
                    ),
                  )
                  .toList(),
              onChanged: (value) => setState(() => _selectedAccountId = value),
              labelText: 'الحساب *',
              validator: (value) {
                if (value == null) {
                  return 'يجب اختيار حساب';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _amountController,
              labelText: 'المبلغ المخطط *',
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'المبلغ مطلوب';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'يجب إدخال مبلغ صحيح أكبر من صفر';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _notesController,
              labelText: 'ملاحظات (اختياري)',
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(onPressed: _saveBudgetLine, child: const Text('حفظ')),
      ],
    );
  }

  void _saveBudgetLine() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text);
    final notes = _notesController.text.trim().isEmpty
        ? null
        : _notesController.text.trim();

    widget.onSave(_selectedAccountId!, amount, notes);
    Navigator.pop(context);
  }
}
