/// شاشة إعدادات الشركة المحسنة
/// Enhanced Company Settings Screen
library;

import 'package:flutter/material.dart';
import '../../models/company_settings.dart';
import '../../services/company_settings_service.dart';
import '../../theme/app_theme.dart';

class EnhancedCompanySettingsScreen extends StatefulWidget {
  const EnhancedCompanySettingsScreen({super.key});

  @override
  State<EnhancedCompanySettingsScreen> createState() =>
      _EnhancedCompanySettingsScreenState();
}

class _EnhancedCompanySettingsScreenState
    extends State<EnhancedCompanySettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final CompanySettingsService _settingsService = CompanySettingsService();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Controllers
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _companyNameEnController =
      TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _taxNumberController = TextEditingController();
  final TextEditingController _logoPathController = TextEditingController();

  CompanySettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;

  String _selectedCurrencyCode = 'SAR';
  String _selectedCurrencySymbol = 'ر.س';
  int _selectedDecimalPlaces = 2;
  String _selectedDateFormat = 'dd/MM/yyyy';
  String _selectedFiscalYearStart = '01-01';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadSettings();
  }

  void _setupAnimations() {
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _companyNameController.dispose();
    _companyNameEnController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _taxNumberController.dispose();
    _logoPathController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    final result = await _settingsService.getCompanySettings();
    if (mounted) {
      setState(() {
        if (result.isSuccess && result.data != null) {
          _currentSettings = result.data!;
          _populateFields();
        }
        _isLoading = false;
      });
    }
  }

  void _populateFields() {
    if (_currentSettings != null) {
      _companyNameController.text = _currentSettings!.companyName;
      _companyNameEnController.text = _currentSettings!.companyNameEn ?? '';
      _addressController.text = _currentSettings!.address ?? '';
      _phoneController.text = _currentSettings!.phone ?? '';
      _emailController.text = _currentSettings!.email ?? '';
      _taxNumberController.text = _currentSettings!.taxNumber ?? '';
      _logoPathController.text = _currentSettings!.logoPath ?? '';

      _selectedCurrencyCode = _currentSettings!.currencyCode;
      _selectedCurrencySymbol = _currentSettings!.currencySymbol;
      _selectedDecimalPlaces = _currentSettings!.decimalPlaces;
      _selectedDateFormat = _currentSettings!.dateFormat;
      _selectedFiscalYearStart = _currentSettings!.fiscalYearStart;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: _buildBody(),
                  ),
                );
              },
            ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'إعدادات الشركة',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: const [
          Tab(icon: Icon(Icons.business), text: 'معلومات الشركة'),
          Tab(icon: Icon(Icons.monetization_on), text: 'الإعدادات المالية'),
          Tab(icon: Icon(Icons.settings), text: 'إعدادات النظام'),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildCompanyInfoTab(),
        _buildFinancialSettingsTab(),
        _buildSystemSettingsTab(),
      ],
    );
  }

  Widget _buildCompanyInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionCard(
              title: 'المعلومات الأساسية',
              icon: Icons.business,
              children: [
                _buildTextField(
                  controller: _companyNameController,
                  label: 'اسم الشركة (عربي)',
                  icon: Icons.business,
                  required: true,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _companyNameEnController,
                  label: 'اسم الشركة (إنجليزي)',
                  icon: Icons.business_outlined,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _logoPathController,
                  label: 'مسار الشعار',
                  icon: Icons.image,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _taxNumberController,
                  label: 'الرقم الضريبي',
                  icon: Icons.receipt,
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildSectionCard(
              title: 'معلومات الاتصال',
              icon: Icons.contact_phone,
              children: [
                _buildTextField(
                  controller: _addressController,
                  label: 'العنوان',
                  icon: Icons.location_on,
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _phoneController,
                  label: 'رقم الهاتف',
                  icon: Icons.phone,
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _emailController,
                  label: 'البريد الإلكتروني',
                  icon: Icons.email,
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 16),
                // يمكن إضافة حقول أخرى هنا حسب الحاجة
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            title: 'إعدادات العملة',
            icon: Icons.monetization_on,
            children: [
              _buildCurrencyDropdown(),
              const SizedBox(height: 16),
              _buildDecimalPlacesSelector(),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: 'إعدادات السنة المالية',
            icon: Icons.calendar_today,
            children: [
              _buildFiscalYearSelector(),
              const SizedBox(height: 16),
              _buildDateFormatSelector(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            title: 'إعدادات التطبيق',
            icon: Icons.settings,
            children: [
              _buildSwitchTile(
                title: 'تفعيل الإشعارات',
                subtitle: 'استقبال إشعارات التطبيق',
                value: true,
                onChanged: (value) {},
              ),
              _buildSwitchTile(
                title: 'النسخ الاحتياطي التلقائي',
                subtitle: 'إنشاء نسخة احتياطية يومياً',
                value: false,
                onChanged: (value) {},
              ),
              _buildSwitchTile(
                title: 'الوضع المظلم',
                subtitle: 'استخدام المظهر المظلم',
                value: false,
                onChanged: (value) {},
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Colors.grey[50]!],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: AppTheme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool required = false,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppTheme.primaryColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: required
          ? (value) {
              if (value == null || value.isEmpty) {
                return 'هذا الحقل مطلوب';
              }
              return null;
            }
          : null,
    );
  }

  Widget _buildCurrencyDropdown() {
    final currencies = [
      {'code': 'SAR', 'symbol': 'ر.س', 'name': 'ريال سعودي'},
      {'code': 'USD', 'symbol': '\$', 'name': 'دولار أمريكي'},
      {'code': 'EUR', 'symbol': '€', 'name': 'يورو'},
      {'code': 'AED', 'symbol': 'د.إ', 'name': 'درهم إماراتي'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedCurrencyCode,
      decoration: InputDecoration(
        labelText: 'العملة الأساسية',
        prefixIcon: Icon(Icons.monetization_on, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: currencies.map((currency) {
        return DropdownMenuItem<String>(
          value: currency['code'],
          child: Row(
            children: [
              Text(
                currency['symbol']!,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 8),
              Text(currency['name']!),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCurrencyCode = value;
            _selectedCurrencySymbol = currencies.firstWhere(
              (c) => c['code'] == value,
            )['symbol']!;
          });
        }
      },
    );
  }

  Widget _buildDecimalPlacesSelector() {
    return DropdownButtonFormField<int>(
      value: _selectedDecimalPlaces,
      decoration: InputDecoration(
        labelText: 'عدد الخانات العشرية',
        prefixIcon: Icon(
          Icons.format_list_numbered,
          color: AppTheme.primaryColor,
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: [0, 1, 2, 3, 4].map((places) {
        return DropdownMenuItem<int>(
          value: places,
          child: Text('$places خانات'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedDecimalPlaces = value;
          });
        }
      },
    );
  }

  Widget _buildFiscalYearSelector() {
    final months = [
      {'value': '01-01', 'name': 'يناير'},
      {'value': '04-01', 'name': 'أبريل'},
      {'value': '07-01', 'name': 'يوليو'},
      {'value': '10-01', 'name': 'أكتوبر'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedFiscalYearStart,
      decoration: InputDecoration(
        labelText: 'بداية السنة المالية',
        prefixIcon: Icon(Icons.calendar_today, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: months.map((month) {
        return DropdownMenuItem<String>(
          value: month['value'],
          child: Text(month['name']!),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedFiscalYearStart = value;
          });
        }
      },
    );
  }

  Widget _buildDateFormatSelector() {
    final formats = [
      {'value': 'dd/MM/yyyy', 'name': 'يوم/شهر/سنة'},
      {'value': 'MM/dd/yyyy', 'name': 'شهر/يوم/سنة'},
      {'value': 'yyyy-MM-dd', 'name': 'سنة-شهر-يوم'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedDateFormat,
      decoration: InputDecoration(
        labelText: 'تنسيق التاريخ',
        prefixIcon: Icon(Icons.date_range, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: formats.map((format) {
        return DropdownMenuItem<String>(
          value: format['value'],
          child: Text(format['name']!),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedDateFormat = value;
          });
        }
      },
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: SwitchListTile(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[600])),
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _isSaving ? null : _saveSettings,
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      icon: _isSaving
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Icon(Icons.save),
      label: Text(_isSaving ? 'جاري الحفظ...' : 'حفظ الإعدادات'),
    );
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final settings = CompanySettings(
        id: _currentSettings?.id,
        companyName: _companyNameController.text,
        companyNameEn: _companyNameEnController.text.isEmpty
            ? null
            : _companyNameEnController.text,
        address: _addressController.text.isEmpty
            ? null
            : _addressController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        taxNumber: _taxNumberController.text.isEmpty
            ? null
            : _taxNumberController.text,
        logoPath: _logoPathController.text.isEmpty
            ? null
            : _logoPathController.text,
        currencyCode: _selectedCurrencyCode,
        currencySymbol: _selectedCurrencySymbol,
        decimalPlaces: _selectedDecimalPlaces,
        dateFormat: _selectedDateFormat,
        fiscalYearStart: _selectedFiscalYearStart,
        createdAt: _currentSettings?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = await _settingsService.updateCompanySettings(settings);

      if (mounted) {
        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ الإعدادات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حفظ الإعدادات: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
