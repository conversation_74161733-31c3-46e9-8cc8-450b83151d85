import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🧾 نظام الفواتير المتقدم
/// Advanced Invoicing System
///
/// هذا الملف يحتوي على نظام الفواتير المتقدم لا مثيل له في التاريخ
/// This file contains unprecedented advanced invoicing system in history

/// 🌟 لوحة نظام الفواتير المتقدمة
/// Advanced Invoicing System Dashboard
class AdvancedInvoicingDashboard extends StatefulWidget {
  const AdvancedInvoicingDashboard({super.key});

  @override
  State<AdvancedInvoicingDashboard> createState() =>
      _AdvancedInvoicingDashboardState();
}

class _AdvancedInvoicingDashboardState extends State<AdvancedInvoicingDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _invoiceController;
  late AnimationController _paymentController;
  late Animation<double> _mainAnimation;
  late Animation<double> _invoiceAnimation;
  late Animation<double> _paymentAnimation;

  int _selectedTab =
      0; // 0 for sales invoices, 1 for purchase invoices, 2 for payments

  final List<Invoice> _salesInvoices = [
    Invoice(
      id: 'INV-S-001',
      number: 'S-2024-001',
      type: InvoiceType.sales,
      customerId: 'CUST-001',
      customerName: 'شركة الأندلس للتجارة',
      date: DateTime.now().subtract(const Duration(days: 2)),
      dueDate: DateTime.now().add(const Duration(days: 28)),
      subtotal: 15750.0,
      taxAmount: 2362.5,
      discountAmount: 500.0,
      totalAmount: 17612.5,
      paidAmount: 10000.0,
      status: InvoiceStatus.partiallyPaid,
      paymentTerms: 30,
      notes: 'فاتورة بيع أجهزة كمبيوتر وطابعات',
      items: [
        InvoiceItem(
          itemId: 'ITEM-001',
          itemName: 'جهاز كمبيوتر محمول HP EliteBook',
          quantity: 3,
          unitPrice: 3500.0,
          discount: 0.0,
          taxRate: 15.0,
          totalAmount: 10500.0,
        ),
        InvoiceItem(
          itemId: 'ITEM-002',
          itemName: 'طابعة ليزر Canon LBP6030',
          quantity: 5,
          unitPrice: 450.0,
          discount: 100.0,
          taxRate: 15.0,
          totalAmount: 2250.0,
        ),
      ],
    ),
    Invoice(
      id: 'INV-S-002',
      number: 'S-2024-002',
      type: InvoiceType.sales,
      customerId: 'CUST-002',
      customerName: 'مؤسسة النور للمقاولات',
      date: DateTime.now().subtract(const Duration(days: 1)),
      dueDate: DateTime.now().add(const Duration(days: 14)),
      subtotal: 8500.0,
      taxAmount: 1275.0,
      discountAmount: 0.0,
      totalAmount: 9775.0,
      paidAmount: 9775.0,
      status: InvoiceStatus.paid,
      paymentTerms: 15,
      notes: 'فاتورة بيع أثاث مكتبي',
      items: [
        InvoiceItem(
          itemId: 'ITEM-003',
          itemName: 'كرسي مكتبي جلد طبيعي',
          quantity: 10,
          unitPrice: 850.0,
          discount: 0.0,
          taxRate: 15.0,
          totalAmount: 8500.0,
        ),
      ],
    ),
  ];

  final List<Invoice> _purchaseInvoices = [
    Invoice(
      id: 'INV-P-001',
      number: 'P-2024-001',
      type: InvoiceType.purchase,
      customerId: 'SUPP-001',
      customerName: 'شركة التقنية المتقدمة',
      date: DateTime.now().subtract(const Duration(days: 5)),
      dueDate: DateTime.now().add(const Duration(days: 25)),
      subtotal: 28000.0,
      taxAmount: 4200.0,
      discountAmount: 1000.0,
      totalAmount: 31200.0,
      paidAmount: 15000.0,
      status: InvoiceStatus.partiallyPaid,
      paymentTerms: 30,
      notes: 'فاتورة شراء أجهزة كمبيوتر',
      items: [
        InvoiceItem(
          itemId: 'ITEM-001',
          itemName: 'جهاز كمبيوتر محمول HP EliteBook',
          quantity: 10,
          unitPrice: 2800.0,
          discount: 1000.0,
          taxRate: 15.0,
          totalAmount: 28000.0,
        ),
      ],
    ),
    Invoice(
      id: 'INV-P-002',
      number: 'P-2024-002',
      type: InvoiceType.purchase,
      customerId: 'SUPP-002',
      customerName: 'مؤسسة الطباعة الحديثة',
      date: DateTime.now().subtract(const Duration(days: 3)),
      dueDate: DateTime.now().add(const Duration(days: 27)),
      subtotal: 9500.0,
      taxAmount: 1425.0,
      discountAmount: 0.0,
      totalAmount: 10925.0,
      paidAmount: 0.0,
      status: InvoiceStatus.pending,
      paymentTerms: 30,
      notes: 'فاتورة شراء طابعات ومستلزمات',
      items: [
        InvoiceItem(
          itemId: 'ITEM-002',
          itemName: 'طابعة ليزر Canon LBP6030',
          quantity: 20,
          unitPrice: 380.0,
          discount: 0.0,
          taxRate: 15.0,
          totalAmount: 7600.0,
        ),
        InvoiceItem(
          itemId: 'ITEM-004',
          itemName: 'ورق طباعة A4 - 500 ورقة',
          quantity: 100,
          unitPrice: 19.0,
          discount: 0.0,
          taxRate: 15.0,
          totalAmount: 1900.0,
        ),
      ],
    ),
  ];

  final List<Payment> _payments = [
    Payment(
      id: 'PAY-001',
      invoiceId: 'INV-S-001',
      invoiceNumber: 'S-2024-001',
      amount: 10000.0,
      paymentMethod: PaymentMethod.bankTransfer,
      date: DateTime.now().subtract(const Duration(days: 1)),
      reference: 'TRF-2024-001',
      notes: 'دفعة جزئية من العميل',
    ),
    Payment(
      id: 'PAY-002',
      invoiceId: 'INV-S-002',
      invoiceNumber: 'S-2024-002',
      amount: 9775.0,
      paymentMethod: PaymentMethod.cash,
      date: DateTime.now().subtract(const Duration(hours: 6)),
      reference: 'CASH-2024-002',
      notes: 'دفع نقدي كامل',
    ),
    Payment(
      id: 'PAY-003',
      invoiceId: 'INV-P-001',
      invoiceNumber: 'P-2024-001',
      amount: 15000.0,
      paymentMethod: PaymentMethod.check,
      date: DateTime.now().subtract(const Duration(days: 2)),
      reference: 'CHK-2024-001',
      notes: 'دفعة جزئية بشيك',
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _invoiceController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _paymentController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _invoiceAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _invoiceController, curve: Curves.easeInOut),
    );

    _paymentAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _paymentController, curve: Curves.linear),
    );

    _mainController.repeat(reverse: true);
    _invoiceController.repeat(reverse: true);
    _paymentController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _invoiceController.dispose();
    _paymentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _invoiceAnimation,
        _paymentAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.6 + (_mainAnimation.value * 0.8),
          child: QuantumEnergyEffect(
            intensity: 2.2 + (_invoiceAnimation.value * 0.6),
            primaryColor: const Color(0xFF2196F3),
            secondaryColor: const Color(0xFF42A5F5),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF0D47A1).withValues(alpha: 0.9),
                    const Color(0xFF1565C0).withValues(alpha: 0.8),
                    const Color(0xFF1976D2).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF2196F3).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF2196F3).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام الفواتير
                  Row(
                    children: [
                      Transform.scale(
                        scale: _invoiceAnimation.value,
                        child: Transform.rotate(
                          angle: _paymentAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF2196F3,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFF42A5F5,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.receipt_long_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🧾 نظام الفواتير المتقدم',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام شامل لإدارة فواتير البيع والشراء مع الربط التلقائي',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات الفواتير
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  if (_selectedTab == 0) _buildSalesInvoicesView(),
                  if (_selectedTab == 1) _buildPurchaseInvoicesView(),
                  if (_selectedTab == 2) _buildPaymentsView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 0),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 0
                      ? const Color(0xFF2196F3).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.point_of_sale_rounded,
                      color: _selectedTab == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'فواتير البيع',
                      style: TextStyle(
                        color: _selectedTab == 0
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 1),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 1
                      ? const Color(0xFF2196F3).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_cart_rounded,
                      color: _selectedTab == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'فواتير الشراء',
                      style: TextStyle(
                        color: _selectedTab == 1
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 2),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 2
                      ? const Color(0xFF2196F3).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.payment_rounded,
                      color: _selectedTab == 2
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'المدفوعات',
                      style: TextStyle(
                        color: _selectedTab == 2
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض فواتير البيع
  Widget _buildSalesInvoicesView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات فواتير البيع
        _buildSalesStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة فواتير البيع
        Text(
          '🧾 فواتير البيع',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_salesInvoices.length, (index) {
          return _buildInvoiceCard(_salesInvoices[index]);
        }),
      ],
    );
  }

  /// بناء عرض فواتير الشراء
  Widget _buildPurchaseInvoicesView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات فواتير الشراء
        _buildPurchaseStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة فواتير الشراء
        Text(
          '🛒 فواتير الشراء',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_purchaseInvoices.length, (index) {
          return _buildInvoiceCard(_purchaseInvoices[index]);
        }),
      ],
    );
  }

  /// بناء عرض المدفوعات
  Widget _buildPaymentsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات المدفوعات
        _buildPaymentStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة المدفوعات
        Text(
          '💳 المدفوعات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_payments.length, (index) {
          return _buildPaymentCard(_payments[index]);
        }),
      ],
    );
  }

  /// بناء إحصائيات فواتير البيع
  Widget _buildSalesStats() {
    final totalInvoices = _salesInvoices.length;
    final totalAmount = _salesInvoices.fold(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
    final paidAmount = _salesInvoices.fold(
      0.0,
      (sum, invoice) => sum + invoice.paidAmount,
    );
    final pendingAmount = totalAmount - paidAmount;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الفواتير',
            totalInvoices.toString(),
            Icons.receipt_long_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي المبلغ',
            '${totalAmount.toStringAsFixed(0)} ر.س',
            Icons.monetization_on_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'المبلغ المحصل',
            '${paidAmount.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet_rounded,
            const Color(0xFF00BCD4),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'المبلغ المعلق',
            '${pendingAmount.toStringAsFixed(0)} ر.س',
            Icons.pending_rounded,
            const Color(0xFFFF9800),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات فواتير الشراء
  Widget _buildPurchaseStats() {
    final totalInvoices = _purchaseInvoices.length;
    final totalAmount = _purchaseInvoices.fold(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
    final paidAmount = _purchaseInvoices.fold(
      0.0,
      (sum, invoice) => sum + invoice.paidAmount,
    );
    final pendingAmount = totalAmount - paidAmount;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الفواتير',
            totalInvoices.toString(),
            Icons.shopping_cart_rounded,
            const Color(0xFF9C27B0),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي المبلغ',
            '${totalAmount.toStringAsFixed(0)} ر.س',
            Icons.monetization_on_rounded,
            const Color(0xFFF44336),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'المبلغ المدفوع',
            '${paidAmount.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'المبلغ المستحق',
            '${pendingAmount.toStringAsFixed(0)} ر.س',
            Icons.schedule_rounded,
            const Color(0xFFFF9800),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات المدفوعات
  Widget _buildPaymentStats() {
    final totalPayments = _payments.length;
    final totalAmount = _payments.fold(0.0, (sum, payment) => payment.amount);
    final cashPayments = _payments
        .where((p) => p.paymentMethod == PaymentMethod.cash)
        .length;
    final bankPayments = _payments
        .where((p) => p.paymentMethod == PaymentMethod.bankTransfer)
        .length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي المدفوعات',
            totalPayments.toString(),
            Icons.payment_rounded,
            const Color(0xFF673AB7),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي المبلغ',
            '${totalAmount.toStringAsFixed(0)} ر.س',
            Icons.monetization_on_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'مدفوعات نقدية',
            cashPayments.toString(),
            Icons.money_rounded,
            const Color(0xFF00BCD4),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'تحويلات بنكية',
            bankPayments.toString(),
            Icons.account_balance_rounded,
            const Color(0xFFE91E63),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 8,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الفاتورة
  Widget _buildInvoiceCard(Invoice invoice) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getInvoiceStatusColor(invoice.status).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getInvoiceStatusColor(
                    invoice.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  _getInvoiceTypeIcon(invoice.type),
                  color: _getInvoiceStatusColor(invoice.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${invoice.number} - ${invoice.customerName}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${invoice.id} - ${_formatDate(invoice.date)}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getInvoiceStatusColor(
                    invoice.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getInvoiceStatusText(invoice.status),
                  style: TextStyle(
                    color: _getInvoiceStatusColor(invoice.status),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات الفاتورة
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'إجمالي المبلغ',
                  '${invoice.totalAmount.toStringAsFixed(2)} ر.س',
                  Icons.monetization_on_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'المبلغ المدفوع',
                  '${invoice.paidAmount.toStringAsFixed(2)} ر.س',
                  Icons.account_balance_wallet_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'تاريخ الاستحقاق',
                  _formatDate(invoice.dueDate),
                  Icons.schedule_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'عدد الأصناف',
                  invoice.items.length.toString(),
                  Icons.inventory_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المدفوعة
  Widget _buildPaymentCard(Payment payment) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getPaymentMethodColor(
            payment.paymentMethod,
          ).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getPaymentMethodColor(
                    payment.paymentMethod,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  _getPaymentMethodIcon(payment.paymentMethod),
                  color: _getPaymentMethodColor(payment.paymentMethod),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${payment.invoiceNumber} - ${payment.amount.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${payment.id} - ${payment.reference}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getPaymentMethodColor(
                    payment.paymentMethod,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getPaymentMethodText(payment.paymentMethod),
                  style: TextStyle(
                    color: _getPaymentMethodColor(payment.paymentMethod),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات المدفوعة
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'التاريخ',
                  _formatDate(payment.date),
                  Icons.calendar_today_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'الملاحظات',
                  payment.notes,
                  Icons.note_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.6), size: 12),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 9,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getInvoiceStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return const Color(0xFF9E9E9E);
      case InvoiceStatus.pending:
        return const Color(0xFFFF9800);
      case InvoiceStatus.partiallyPaid:
        return const Color(0xFF2196F3);
      case InvoiceStatus.paid:
        return const Color(0xFF4CAF50);
      case InvoiceStatus.overdue:
        return const Color(0xFFF44336);
      case InvoiceStatus.cancelled:
        return const Color(0xFF795548);
    }
  }

  String _getInvoiceStatusText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.pending:
        return 'معلقة';
      case InvoiceStatus.partiallyPaid:
        return 'مدفوعة جزئياً';
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.overdue:
        return 'متأخرة';
      case InvoiceStatus.cancelled:
        return 'ملغية';
    }
  }

  IconData _getInvoiceTypeIcon(InvoiceType type) {
    switch (type) {
      case InvoiceType.sales:
        return Icons.point_of_sale_rounded;
      case InvoiceType.purchase:
        return Icons.shopping_cart_rounded;
    }
  }

  Color _getPaymentMethodColor(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return const Color(0xFF4CAF50);
      case PaymentMethod.bankTransfer:
        return const Color(0xFF2196F3);
      case PaymentMethod.check:
        return const Color(0xFF9C27B0);
      case PaymentMethod.creditCard:
        return const Color(0xFFFF9800);
    }
  }

  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money_rounded;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance_rounded;
      case PaymentMethod.check:
        return Icons.receipt_rounded;
      case PaymentMethod.creditCard:
        return Icons.credit_card_rounded;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// نموذج الفاتورة
class Invoice {
  final String id;
  final String number;
  final InvoiceType type;
  final String customerId;
  final String customerName;
  final DateTime date;
  final DateTime dueDate;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final double paidAmount;
  final InvoiceStatus status;
  final int paymentTerms;
  final String notes;
  final List<InvoiceItem> items;

  Invoice({
    required this.id,
    required this.number,
    required this.type,
    required this.customerId,
    required this.customerName,
    required this.date,
    required this.dueDate,
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.totalAmount,
    required this.paidAmount,
    required this.status,
    required this.paymentTerms,
    required this.notes,
    required this.items,
  });
}

/// نموذج صنف الفاتورة
class InvoiceItem {
  final String itemId;
  final String itemName;
  final int quantity;
  final double unitPrice;
  final double discount;
  final double taxRate;
  final double totalAmount;

  InvoiceItem({
    required this.itemId,
    required this.itemName,
    required this.quantity,
    required this.unitPrice,
    required this.discount,
    required this.taxRate,
    required this.totalAmount,
  });
}

/// نموذج المدفوعة
class Payment {
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final double amount;
  final PaymentMethod paymentMethod;
  final DateTime date;
  final String reference;
  final String notes;

  Payment({
    required this.id,
    required this.invoiceId,
    required this.invoiceNumber,
    required this.amount,
    required this.paymentMethod,
    required this.date,
    required this.reference,
    required this.notes,
  });
}

/// نوع الفاتورة
enum InvoiceType { sales, purchase }

/// حالة الفاتورة
enum InvoiceStatus { draft, pending, partiallyPaid, paid, overdue, cancelled }

/// طريقة الدفع
enum PaymentMethod { cash, bankTransfer, check, creditCard }
