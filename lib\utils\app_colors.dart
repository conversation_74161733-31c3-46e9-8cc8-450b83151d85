/// نظام الألوان للتطبيق
/// Application Colors System for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// فئة الألوان الرئيسية للتطبيق
/// Main application colors class that maps to AppTheme colors
class AppColors {
  // منع إنشاء كائن من هذه الفئة
  AppColors._();

  // الألوان الأساسية - Primary Colors
  static const Color primary = AppTheme.primaryColor;
  static const Color primaryLight = AppTheme.primaryLightColor;
  static const Color primaryDark = AppTheme.primaryDarkColor;
  static const Color secondary = AppTheme.secondaryColor;
  static const Color accent = AppTheme.accentColor;

  // ألوان الحالة - Status Colors
  static const Color success = AppTheme.secondaryColor; // أخضر للنجاح
  static const Color warning = AppTheme.accentColor;    // برتقالي للتحذير
  static const Color error = AppTheme.errorColor;       // أحمر للخطأ
  static const Color info = Color(0xFF2196F3);          // أزرق للمعلومات

  // ألوان الخلفية - Background Colors
  static const Color background = AppTheme.backgroundColor;
  static const Color surface = AppTheme.surfaceColor;
  static const Color card = AppTheme.cardColor;

  // ألوان النص - Text Colors
  static const Color textPrimary = AppTheme.textPrimaryColor;
  static const Color textSecondary = AppTheme.textSecondaryColor;
  static const Color textLight = AppTheme.textLightColor;

  // ألوان إضافية - Additional Colors
  static const Color border = Color(0xFFE5E7EB);
  static const Color divider = Color(0xFFF3F4F6);
  static const Color shadow = Color(0x1A000000);

  // ألوان أنواع الحسابات - Account Type Colors
  static const Color asset = AppTheme.assetColor;
  static const Color liability = AppTheme.liabilityColor;
  static const Color equity = AppTheme.equityColor;
  static const Color revenue = AppTheme.revenueColor;
  static const Color expense = AppTheme.expenseColor;

  // التدرجات اللونية - Gradients
  static const LinearGradient primaryGradient = AppTheme.primaryGradient;
  static const LinearGradient successGradient = AppTheme.successGradient;

  /// الحصول على لون حسب نوع الحساب
  /// Get color by account type
  static Color getAccountTypeColor(String accountType) {
    return AppTheme.getAccountTypeColor(accountType);
  }

  /// الحصول على لون حسب حالة المشروع
  /// Get color by project status
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'planning':
        return warning;
      case 'active':
        return success;
      case 'onhold':
        return info;
      case 'completed':
        return primary;
      case 'cancelled':
        return error;
      default:
        return textSecondary;
    }
  }

  /// الحصول على لون حسب نسبة التقدم
  /// Get color by progress percentage
  static Color getProgressColor(double percentage) {
    if (percentage >= 80) return success;
    if (percentage >= 50) return warning;
    return error;
  }
}
