import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';
import '../screens/customers/customers_screen.dart';

/// 👥 نظام إدارة العملاء والموردين المتقدم
/// Advanced Customer and Supplier Management System
///
/// هذا الملف يحتوي على نظام إدارة العملاء والموردين المتقدم لا مثيل له في التاريخ
/// This file contains unprecedented advanced customer and supplier management system in history

/// 🌟 لوحة إدارة العملاء والموردين المتقدمة
/// Advanced Customer and Supplier Management Dashboard
class AdvancedCustomerSupplierDashboard extends StatefulWidget {
  const AdvancedCustomerSupplierDashboard({super.key});

  @override
  State<AdvancedCustomerSupplierDashboard> createState() =>
      _AdvancedCustomerSupplierDashboardState();
}

class _AdvancedCustomerSupplierDashboardState
    extends State<AdvancedCustomerSupplierDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _cardController;
  late AnimationController _rotationController;
  late Animation<double> _mainAnimation;
  late Animation<double> _cardAnimation;
  late Animation<double> _rotationAnimation;

  int _selectedTab = 0; // 0 for customers, 1 for suppliers

  final List<CustomerAccount> _customers = [
    CustomerAccount(
      id: 'CUST-001',
      name: 'شركة الأندلس للتجارة العامة',
      email: '<EMAIL>',
      phone: '+************',
      address: 'الرياض، المملكة العربية السعودية',
      creditLimit: 500000.0,
      currentBalance: 125000.0,
      paymentTerms: 30,
      rating: CustomerRating.excellent,
      registrationDate: DateTime.now().subtract(const Duration(days: 365)),
      lastTransactionDate: DateTime.now().subtract(const Duration(days: 5)),
      totalTransactions: 45,
      totalPurchases: 2500000.0,
    ),
    CustomerAccount(
      id: 'CUST-002',
      name: 'مؤسسة النور للمقاولات',
      email: '<EMAIL>',
      phone: '+************',
      address: 'جدة، المملكة العربية السعودية',
      creditLimit: 300000.0,
      currentBalance: 85000.0,
      paymentTerms: 45,
      rating: CustomerRating.good,
      registrationDate: DateTime.now().subtract(const Duration(days: 180)),
      lastTransactionDate: DateTime.now().subtract(const Duration(days: 12)),
      totalTransactions: 28,
      totalPurchases: 1200000.0,
    ),
    CustomerAccount(
      id: 'CUST-003',
      name: 'شركة الفجر الجديد للتطوير',
      email: '<EMAIL>',
      phone: '+************',
      address: 'الدمام، المملكة العربية السعودية',
      creditLimit: 200000.0,
      currentBalance: 45000.0,
      paymentTerms: 30,
      rating: CustomerRating.average,
      registrationDate: DateTime.now().subtract(const Duration(days: 90)),
      lastTransactionDate: DateTime.now().subtract(const Duration(days: 8)),
      totalTransactions: 15,
      totalPurchases: 650000.0,
    ),
  ];

  final List<SupplierAccount> _suppliers = [
    SupplierAccount(
      id: 'SUPP-001',
      name: 'شركة المواد الأولية المحدودة',
      email: '<EMAIL>',
      phone: '+************',
      address: 'الرياض، المملكة العربية السعودية',
      paymentTerms: 60,
      currentBalance: 75000.0,
      rating: SupplierRating.excellent,
      registrationDate: DateTime.now().subtract(const Duration(days: 500)),
      lastTransactionDate: DateTime.now().subtract(const Duration(days: 3)),
      totalTransactions: 67,
      totalPurchases: 3200000.0,
      category: 'مواد خام',
    ),
    SupplierAccount(
      id: 'SUPP-002',
      name: 'مؤسسة التوريدات الذكية',
      email: '<EMAIL>',
      phone: '+************',
      address: 'جدة، المملكة العربية السعودية',
      paymentTerms: 45,
      currentBalance: 120000.0,
      rating: SupplierRating.good,
      registrationDate: DateTime.now().subtract(const Duration(days: 300)),
      lastTransactionDate: DateTime.now().subtract(const Duration(days: 7)),
      totalTransactions: 42,
      totalPurchases: 1800000.0,
      category: 'معدات',
    ),
    SupplierAccount(
      id: 'SUPP-003',
      name: 'شركة الخدمات اللوجستية المتقدمة',
      email: '<EMAIL>',
      phone: '+************',
      address: 'الخبر، المملكة العربية السعودية',
      paymentTerms: 30,
      currentBalance: 35000.0,
      rating: SupplierRating.average,
      registrationDate: DateTime.now().subtract(const Duration(days: 150)),
      lastTransactionDate: DateTime.now().subtract(const Duration(days: 15)),
      totalTransactions: 25,
      totalPurchases: 950000.0,
      category: 'خدمات',
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _cardController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _cardAnimation = Tween<double>(begin: 0.9, end: 1.1).animate(
      CurvedAnimation(parent: _cardController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _mainController.repeat(reverse: true);
    _cardController.repeat(reverse: true);
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _cardController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _cardAnimation,
        _rotationAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.5 + (_mainAnimation.value * 0.5),
          child: QuantumEnergyEffect(
            intensity: 2.2 + (_cardAnimation.value * 0.3),
            primaryColor: const Color(0xFF673AB7),
            secondaryColor: const Color(0xFF7986CB),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF311B92).withValues(alpha: 0.9),
                    const Color(0xFF512DA8).withValues(alpha: 0.8),
                    const Color(0xFF673AB7).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF673AB7).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF673AB7).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام إدارة العملاء والموردين
                  Row(
                    children: [
                      Transform.scale(
                        scale: _cardAnimation.value,
                        child: Transform.rotate(
                          angle: _rotationAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF673AB7,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFF7986CB,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.people_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '👥 إدارة العملاء والموردين',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام متطور لإدارة العلاقات التجارية والحسابات الجارية',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات العملاء والموردين
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  _selectedTab == 0
                      ? _buildCustomersView()
                      : _buildSuppliersView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 0),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 0
                      ? const Color(0xFF673AB7).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person_rounded,
                      color: _selectedTab == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'العملاء',
                      style: TextStyle(
                        color: _selectedTab == 0
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 1),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 1
                      ? const Color(0xFF673AB7).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.business_rounded,
                      color: _selectedTab == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'الموردين',
                      style: TextStyle(
                        color: _selectedTab == 1
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض العملاء
  Widget _buildCustomersView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات العملاء
        _buildCustomerStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة العملاء
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '👥 قائمة العملاء',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CustomersScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.manage_accounts, size: 16),
              label: const Text('إدارة العملاء'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                textStyle: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_customers.length, (index) {
          return _buildCustomerCard(_customers[index]);
        }),
      ],
    );
  }

  /// بناء عرض الموردين
  Widget _buildSuppliersView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات الموردين
        _buildSupplierStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // زر إدارة الموردين
        ElevatedButton.icon(
          onPressed: () {
            Navigator.of(context).pushNamed('/suppliers');
          },
          icon: const Icon(Icons.business),
          label: const Text('إدارة الموردين'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingLarge,
              vertical: AppTheme.spacingMedium,
            ),
          ),
        ),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة الموردين
        Text(
          '🏭 قائمة الموردين',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_suppliers.length, (index) {
          return _buildSupplierCard(_suppliers[index]);
        }),
      ],
    );
  }

  /// بناء إحصائيات العملاء
  Widget _buildCustomerStats() {
    final totalCustomers = _customers.length;
    final totalBalance = _customers.fold(
      0.0,
      (sum, customer) => sum + customer.currentBalance,
    );
    final totalCreditLimit = _customers.fold(
      0.0,
      (sum, customer) => sum + customer.creditLimit,
    );
    final excellentCustomers = _customers
        .where((c) => c.rating == CustomerRating.excellent)
        .length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي العملاء',
            totalCustomers.toString(),
            Icons.people_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'الأرصدة الحالية',
            '${totalBalance.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'حدود الائتمان',
            '${totalCreditLimit.toStringAsFixed(0)} ر.س',
            Icons.credit_card_rounded,
            const Color(0xFFFF9800),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'عملاء ممتازين',
            excellentCustomers.toString(),
            Icons.star_rounded,
            const Color(0xFFF44336),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات الموردين
  Widget _buildSupplierStats() {
    final totalSuppliers = _suppliers.length;
    final totalBalance = _suppliers.fold(
      0.0,
      (sum, supplier) => sum + supplier.currentBalance,
    );
    final totalPurchases = _suppliers.fold(
      0.0,
      (sum, supplier) => sum + supplier.totalPurchases,
    );
    final excellentSuppliers = _suppliers
        .where((s) => s.rating == SupplierRating.excellent)
        .length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الموردين',
            totalSuppliers.toString(),
            Icons.business_rounded,
            const Color(0xFF9C27B0),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'الأرصدة الحالية',
            '${totalBalance.toStringAsFixed(0)} ر.س',
            Icons.account_balance_rounded,
            const Color(0xFF00BCD4),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي المشتريات',
            '${totalPurchases.toStringAsFixed(0)} ر.س',
            Icons.shopping_cart_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'موردين ممتازين',
            excellentSuppliers.toString(),
            Icons.star_rounded,
            const Color(0xFFFF5722),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 8,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة العميل
  Widget _buildCustomerCard(CustomerAccount customer) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getCustomerRatingColor(
            customer.rating,
          ).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getCustomerRatingColor(
                    customer.rating,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.person_rounded,
                  color: _getCustomerRatingColor(customer.rating),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      customer.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      customer.id,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getCustomerRatingColor(
                    customer.rating,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getCustomerRatingText(customer.rating),
                  style: TextStyle(
                    color: _getCustomerRatingColor(customer.rating),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات العميل
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الرصيد الحالي',
                  '${customer.currentBalance.toStringAsFixed(2)} ر.س',
                  Icons.account_balance_wallet_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'حد الائتمان',
                  '${customer.creditLimit.toStringAsFixed(0)} ر.س',
                  Icons.credit_card_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'شروط الدفع',
                  '${customer.paymentTerms} يوم',
                  Icons.schedule_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'إجمالي المعاملات',
                  customer.totalTransactions.toString(),
                  Icons.receipt_long_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المورد
  Widget _buildSupplierCard(SupplierAccount supplier) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getSupplierRatingColor(
            supplier.rating,
          ).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getSupplierRatingColor(
                    supplier.rating,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.business_rounded,
                  color: _getSupplierRatingColor(supplier.rating),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      supplier.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${supplier.id} - ${supplier.category}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getSupplierRatingColor(
                    supplier.rating,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getSupplierRatingText(supplier.rating),
                  style: TextStyle(
                    color: _getSupplierRatingColor(supplier.rating),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات المورد
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الرصيد الحالي',
                  '${supplier.currentBalance.toStringAsFixed(2)} ر.س',
                  Icons.account_balance_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'شروط الدفع',
                  '${supplier.paymentTerms} يوم',
                  Icons.schedule_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'إجمالي المشتريات',
                  '${supplier.totalPurchases.toStringAsFixed(0)} ر.س',
                  Icons.shopping_cart_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'إجمالي المعاملات',
                  supplier.totalTransactions.toString(),
                  Icons.receipt_long_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.6), size: 12),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 9,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getCustomerRatingColor(CustomerRating rating) {
    switch (rating) {
      case CustomerRating.excellent:
        return const Color(0xFF4CAF50);
      case CustomerRating.good:
        return const Color(0xFF2196F3);
      case CustomerRating.average:
        return const Color(0xFFFF9800);
      case CustomerRating.poor:
        return const Color(0xFFF44336);
    }
  }

  String _getCustomerRatingText(CustomerRating rating) {
    switch (rating) {
      case CustomerRating.excellent:
        return 'ممتاز';
      case CustomerRating.good:
        return 'جيد';
      case CustomerRating.average:
        return 'متوسط';
      case CustomerRating.poor:
        return 'ضعيف';
    }
  }

  Color _getSupplierRatingColor(SupplierRating rating) {
    switch (rating) {
      case SupplierRating.excellent:
        return const Color(0xFF4CAF50);
      case SupplierRating.good:
        return const Color(0xFF2196F3);
      case SupplierRating.average:
        return const Color(0xFFFF9800);
      case SupplierRating.poor:
        return const Color(0xFFF44336);
    }
  }

  String _getSupplierRatingText(SupplierRating rating) {
    switch (rating) {
      case SupplierRating.excellent:
        return 'ممتاز';
      case SupplierRating.good:
        return 'جيد';
      case SupplierRating.average:
        return 'متوسط';
      case SupplierRating.poor:
        return 'ضعيف';
    }
  }
}

/// نموذج حساب العميل
class CustomerAccount {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final double creditLimit;
  final double currentBalance;
  final int paymentTerms;
  final CustomerRating rating;
  final DateTime registrationDate;
  final DateTime lastTransactionDate;
  final int totalTransactions;
  final double totalPurchases;

  CustomerAccount({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.creditLimit,
    required this.currentBalance,
    required this.paymentTerms,
    required this.rating,
    required this.registrationDate,
    required this.lastTransactionDate,
    required this.totalTransactions,
    required this.totalPurchases,
  });
}

/// نموذج حساب المورد
class SupplierAccount {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final int paymentTerms;
  final double currentBalance;
  final SupplierRating rating;
  final DateTime registrationDate;
  final DateTime lastTransactionDate;
  final int totalTransactions;
  final double totalPurchases;
  final String category;

  SupplierAccount({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.paymentTerms,
    required this.currentBalance,
    required this.rating,
    required this.registrationDate,
    required this.lastTransactionDate,
    required this.totalTransactions,
    required this.totalPurchases,
    required this.category,
  });
}

/// تقييم العميل
enum CustomerRating { excellent, good, average, poor }

/// تقييم المورد
enum SupplierRating { excellent, good, average, poor }
