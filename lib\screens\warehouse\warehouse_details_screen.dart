/// شاشة تفاصيل المخزن
/// Warehouse Details Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/warehouse.dart';
import '../../providers/language_provider.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_loading.dart';

class WarehouseDetailsScreen extends StatefulWidget {
  final Warehouse warehouse;

  const WarehouseDetailsScreen({
    super.key,
    required this.warehouse,
  });

  @override
  State<WarehouseDetailsScreen> createState() => _WarehouseDetailsScreenState();
}

class _WarehouseDetailsScreenState extends State<WarehouseDetailsScreen> {
  final bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: HolographicAppBar(
        title: isArabic ? 'تفاصيل المخزن' : 'Warehouse Details',
        subtitle: widget.warehouse.name,
      ),
      body: _isLoading
          ? const Center(child: QuantumLoading())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBasicInfoCard(isArabic, theme),
                  const SizedBox(height: 16),
                  _buildLocationCard(isArabic, theme),
                  const SizedBox(height: 16),
                  _buildContactCard(isArabic, theme),
                  const SizedBox(height: 16),
                  _buildSettingsCard(isArabic, theme),
                  const SizedBox(height: 16),
                  _buildActionsCard(isArabic, theme),
                ],
              ),
            ),
    );
  }

  Widget _buildBasicInfoCard(bool isArabic, ThemeData theme) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'المعلومات الأساسية' : 'Basic Information',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isArabic ? 'الكود' : 'Code',
              widget.warehouse.code,
              Icons.qr_code,
            ),
            _buildInfoRow(
              isArabic ? 'الاسم' : 'Name',
              widget.warehouse.name,
              Icons.warehouse,
            ),
            if (widget.warehouse.description != null)
              _buildInfoRow(
                isArabic ? 'الوصف' : 'Description',
                widget.warehouse.description!,
                Icons.description,
              ),
            _buildInfoRow(
              isArabic ? 'النوع' : 'Type',
              isArabic ? widget.warehouse.type.arabicName : widget.warehouse.type.englishName,
              Icons.category,
            ),
            _buildInfoRow(
              isArabic ? 'الحالة' : 'Status',
              isArabic ? widget.warehouse.status.arabicName : widget.warehouse.status.englishName,
              Icons.info,
              statusColor: _getStatusColor(widget.warehouse.status),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationCard(bool isArabic, ThemeData theme) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات الموقع' : 'Location Information',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (widget.warehouse.address != null)
              _buildInfoRow(
                isArabic ? 'العنوان' : 'Address',
                widget.warehouse.address!,
                Icons.location_on,
              ),
            if (widget.warehouse.city != null)
              _buildInfoRow(
                isArabic ? 'المدينة' : 'City',
                widget.warehouse.city!,
                Icons.location_city,
              ),
            if (widget.warehouse.country != null)
              _buildInfoRow(
                isArabic ? 'البلد' : 'Country',
                widget.warehouse.country!,
                Icons.flag,
              ),
            if (widget.warehouse.area != null)
              _buildInfoRow(
                isArabic ? 'المساحة' : 'Area',
                '${widget.warehouse.area} ${isArabic ? 'متر مربع' : 'sq m'}',
                Icons.square_foot,
              ),
            if (widget.warehouse.capacity != null)
              _buildInfoRow(
                isArabic ? 'السعة التخزينية' : 'Storage Capacity',
                widget.warehouse.capacity.toString(),
                Icons.inventory,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard(bool isArabic, ThemeData theme) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات الاتصال' : 'Contact Information',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (widget.warehouse.managerName != null)
              _buildInfoRow(
                isArabic ? 'اسم المدير' : 'Manager Name',
                widget.warehouse.managerName!,
                Icons.person,
              ),
            if (widget.warehouse.phone != null)
              _buildInfoRow(
                isArabic ? 'الهاتف' : 'Phone',
                widget.warehouse.phone!,
                Icons.phone,
              ),
            if (widget.warehouse.email != null)
              _buildInfoRow(
                isArabic ? 'البريد الإلكتروني' : 'Email',
                widget.warehouse.email!,
                Icons.email,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCard(bool isArabic, ThemeData theme) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'الإعدادات' : 'Settings',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isArabic ? 'مخزن رئيسي' : 'Main Warehouse',
              isArabic ? (widget.warehouse.isMainWarehouse ? 'نعم' : 'لا') : (widget.warehouse.isMainWarehouse ? 'Yes' : 'No'),
              Icons.home,
            ),
            _buildInfoRow(
              isArabic ? 'السماح بالمخزون السالب' : 'Allow Negative Stock',
              isArabic ? (widget.warehouse.allowNegativeStock ? 'نعم' : 'لا') : (widget.warehouse.allowNegativeStock ? 'Yes' : 'No'),
              Icons.remove_circle,
            ),
            _buildInfoRow(
              isArabic ? 'يتطلب موافقة' : 'Require Approval',
              isArabic ? (widget.warehouse.requireApproval ? 'نعم' : 'لا') : (widget.warehouse.requireApproval ? 'Yes' : 'No'),
              Icons.approval,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard(bool isArabic, ThemeData theme) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'الإجراءات' : 'Actions',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumButton(
                    text: isArabic ? 'تعديل' : 'Edit',
                    icon: Icons.edit,
                    variant: QuantumButtonVariant.primary,
                    onPressed: () {
                      // Navigate to edit screen
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: QuantumButton(
                    text: isArabic ? 'حذف' : 'Delete',
                    icon: Icons.delete,
                    variant: QuantumButtonVariant.danger,
                    onPressed: () {
                      _showDeleteConfirmation(isArabic);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon, {Color? statusColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: statusColor ?? Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(WarehouseStatus status) {
    switch (status) {
      case WarehouseStatus.active:
        return Colors.green;
      case WarehouseStatus.inactive:
        return Colors.orange;
      case WarehouseStatus.maintenance:
        return Colors.blue;
      case WarehouseStatus.closed:
        return Colors.red;
    }
  }

  void _showDeleteConfirmation(bool isArabic) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isArabic ? 'تأكيد الحذف' : 'Confirm Delete'),
        content: Text(
          isArabic 
            ? 'هل أنت متأكد من حذف هذا المخزن؟'
            : 'Are you sure you want to delete this warehouse?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isArabic ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement delete logic
            },
            child: Text(
              isArabic ? 'حذف' : 'Delete',
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
