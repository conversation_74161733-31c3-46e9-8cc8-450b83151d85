import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/payroll_components.dart';

/// DAO للاستقطاعات
/// Deduction Data Access Object
class DeductionDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج استقطاع جديد
  Future<int> insertDeduction(Deduction deduction) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableDeductions,
      deduction.toMap(),
    );
  }

  /// تحديث استقطاع
  Future<int> updateDeduction(Deduction deduction) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableDeductions,
      deduction.toMap(),
      where: 'id = ?',
      whereArgs: [deduction.id],
    );
  }

  /// حذف استقطاع
  Future<int> deleteDeduction(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableDeductions,
      {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على استقطاع بالمعرف
  Future<Deduction?> getDeductionById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: 'id = ? AND is_active = 1',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Deduction.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على استقطاع بالرمز
  Future<Deduction?> getDeductionByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: 'code = ? AND is_active = 1',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Deduction.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع الاستقطاعات
  Future<List<Deduction>> getAllDeductions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }

  /// البحث في الاستقطاعات
  Future<List<Deduction>> searchDeductions(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: '''
        is_active = 1 AND (
          LOWER(code) LIKE LOWER(?) OR 
          LOWER(name) LIKE LOWER(?) OR 
          LOWER(description) LIKE LOWER(?)
        )
      ''',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }

  /// الحصول على الاستقطاعات حسب النوع
  Future<List<Deduction>> getDeductionsByType(DeductionType type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: 'type = ? AND is_active = 1',
      whereArgs: [type.name],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }

  /// الحصول على الاستقطاعات حسب نوع الحساب
  Future<List<Deduction>> getDeductionsByCalculationType(
      DeductionCalculationType calculationType) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: 'calculation_type = ? AND is_active = 1',
      whereArgs: [calculationType.name],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }

  /// التحقق من تفرد رمز الاستقطاع
  Future<bool> isDeductionCodeUnique(String code, {int? excludeId}) async {
    final db = await _databaseHelper.database;
    String whereClause = 'code = ? AND is_active = 1';
    List<dynamic> whereArgs = [code];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isEmpty;
  }

  /// الحصول على إحصائيات الاستقطاعات
  Future<Map<String, dynamic>> getDeductionStatistics() async {
    final db = await _databaseHelper.database;
    
    final totalResult = await db.rawQuery('''
      SELECT COUNT(*) as total_deductions
      FROM ${DatabaseSchema.tableDeductions}
      WHERE is_active = 1
    ''');

    final typeStatsResult = await db.rawQuery('''
      SELECT type, COUNT(*) as count
      FROM ${DatabaseSchema.tableDeductions}
      WHERE is_active = 1
      GROUP BY type
    ''');

    final calculationTypeStatsResult = await db.rawQuery('''
      SELECT calculation_type, COUNT(*) as count
      FROM ${DatabaseSchema.tableDeductions}
      WHERE is_active = 1
      GROUP BY calculation_type
    ''');

    return {
      'total_deductions': totalResult.first['total_deductions'] ?? 0,
      'by_type': {
        for (var row in typeStatsResult)
          row['type']: row['count']
      },
      'by_calculation_type': {
        for (var row in calculationTypeStatsResult)
          row['calculation_type']: row['count']
      },
    };
  }

  /// الحصول على الاستقطاعات المستخدمة
  Future<List<Deduction>> getUsedDeductions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT d.*
      FROM ${DatabaseSchema.tableDeductions} d
      INNER JOIN ${DatabaseSchema.tableEmployeeDeductions} ed ON d.id = ed.deduction_id
      WHERE d.is_active = 1 AND ed.is_active = 1
      ORDER BY d.name ASC
    ''');

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }

  /// الحصول على الاستقطاعات غير المستخدمة
  Future<List<Deduction>> getUnusedDeductions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT d.*
      FROM ${DatabaseSchema.tableDeductions} d
      LEFT JOIN ${DatabaseSchema.tableEmployeeDeductions} ed ON d.id = ed.deduction_id AND ed.is_active = 1
      WHERE d.is_active = 1 AND ed.deduction_id IS NULL
      ORDER BY d.name ASC
    ''');

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }

  /// الحصول على عدد الموظفين المطبق عليهم الاستقطاع
  Future<int> getDeductionEmployeeCount(int deductionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT COUNT(DISTINCT employee_code) as count
      FROM ${DatabaseSchema.tableEmployeeDeductions}
      WHERE deduction_id = ? AND is_active = 1
    ''', [deductionId]);

    return result.first['count'] as int? ?? 0;
  }

  /// الحصول على إجمالي مبلغ الاستقطاع لجميع الموظفين
  Future<double> getDeductionTotalAmount(int deductionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT SUM(amount) as total
      FROM ${DatabaseSchema.tableEmployeeDeductions}
      WHERE deduction_id = ? AND is_active = 1
    ''', [deductionId]);

    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  /// الحصول على متوسط مبلغ الاستقطاع
  Future<double> getDeductionAverageAmount(int deductionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT AVG(amount) as average
      FROM ${DatabaseSchema.tableEmployeeDeductions}
      WHERE deduction_id = ? AND is_active = 1
    ''', [deductionId]);

    return (result.first['average'] as num?)?.toDouble() ?? 0.0;
  }

  /// الحصول على الاستقطاعات مع تفاصيل الاستخدام
  Future<List<Map<String, dynamic>>> getDeductionsWithUsageDetails() async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        d.*,
        COUNT(DISTINCT ed.employee_code) as employee_count,
        COALESCE(SUM(ed.amount), 0) as total_amount,
        COALESCE(AVG(ed.amount), 0) as average_amount
      FROM ${DatabaseSchema.tableDeductions} d
      LEFT JOIN ${DatabaseSchema.tableEmployeeDeductions} ed ON d.id = ed.deduction_id AND ed.is_active = 1
      WHERE d.is_active = 1
      GROUP BY d.id
      ORDER BY d.name ASC
    ''');
  }

  /// الحصول على الاستقطاعات الإجبارية
  Future<List<Deduction>> getMandatoryDeductions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: 'is_mandatory = 1 AND is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }

  /// الحصول على الاستقطاعات الاختيارية
  Future<List<Deduction>> getOptionalDeductions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDeductions,
      where: 'is_mandatory = 0 AND is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Deduction.fromMap(maps[i]));
  }
}
