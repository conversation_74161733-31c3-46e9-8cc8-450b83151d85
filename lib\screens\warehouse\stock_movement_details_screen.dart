/// شاشة تفاصيل حركة المخزون
/// Stock Movement Details Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/stock_movement.dart';
import '../../models/warehouse.dart';
import '../../models/item.dart';
import '../../services/warehouse_service.dart';
import '../../services/item_service.dart';
import '../../providers/language_provider.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_loading.dart';
import '../../widgets/beautiful_buttons.dart';
import 'add_stock_movement_screen.dart';

class StockMovementDetailsScreen extends StatefulWidget {
  final StockMovement movement;

  const StockMovementDetailsScreen({super.key, required this.movement});

  @override
  State<StockMovementDetailsScreen> createState() =>
      _StockMovementDetailsScreenState();
}

class _StockMovementDetailsScreenState extends State<StockMovementDetailsScreen>
    with TickerProviderStateMixin {
  final WarehouseService _warehouseService = WarehouseService();
  final ItemService _itemService = ItemService();

  bool _isLoading = true;
  Item? _item;
  Warehouse? _warehouse;
  Warehouse? _toWarehouse;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDetails();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  Future<void> _loadDetails() async {
    try {
      final itemsResult = await _itemService.getAllItems();
      if (itemsResult.isSuccess) {
        _item = itemsResult.data!.firstWhere(
          (item) => item.id == widget.movement.itemId,
          orElse: () => Item(
            id: widget.movement.itemId,
            code: 'UNKNOWN',
            name: 'صنف غير معروف',
            category: 'other',
            unit: 'قطعة',
          ),
        );
      }

      final warehousesResult = await _warehouseService.getAllWarehouses();
      if (warehousesResult.isSuccess) {
        _warehouse = warehousesResult.data!.firstWhere(
          (warehouse) => warehouse.id == widget.movement.warehouseId,
          orElse: () => Warehouse(
            id: widget.movement.warehouseId,
            code: 'UNKNOWN',
            name: 'مخزن غير معروف',
            type: WarehouseType.main,
            status: WarehouseStatus.active,
          ),
        );

        if (widget.movement.toWarehouseId != null) {
          _toWarehouse = warehousesResult.data!.firstWhere(
            (warehouse) => warehouse.id == widget.movement.toWarehouseId,
            orElse: () => Warehouse(
              id: widget.movement.toWarehouseId!,
              code: 'UNKNOWN',
              name: 'مخزن غير معروف',
              type: WarehouseType.main,
              status: WarehouseStatus.active,
            ),
          );
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التفاصيل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;

    return Scaffold(
      body: Column(
        children: [
          // App Bar
          HolographicAppBar(
            title: isArabic ? 'تفاصيل حركة المخزون' : 'Stock Movement Details',
            subtitle: widget.movement.documentNumber,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              if (widget.movement.status == MovementStatus.pending)
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: _editMovement,
                  tooltip: isArabic ? 'تعديل' : 'Edit',
                ),
              IconButton(
                icon: const Icon(Icons.print),
                onPressed: _printMovement,
                tooltip: isArabic ? 'طباعة' : 'Print',
              ),
            ],
          ),

          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: QuantumLoading())
                : _buildContent(theme, isArabic),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme, bool isArabic) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeaderCard(theme, isArabic),
              const SizedBox(height: 16),
              _buildItemCard(theme, isArabic),
              const SizedBox(height: 16),
              _buildWarehouseCard(theme, isArabic),
              const SizedBox(height: 16),
              _buildQuantityCard(theme, isArabic),
              const SizedBox(height: 16),
              _buildDatesCard(theme, isArabic),
              if (widget.movement.notes?.isNotEmpty == true) ...[
                const SizedBox(height: 16),
                _buildNotesCard(theme, isArabic),
              ],
              if (widget.movement.batchNumber != null ||
                  widget.movement.serialNumber != null) ...[
                const SizedBox(height: 16),
                _buildTrackingCard(theme, isArabic),
              ],
              const SizedBox(height: 16),
              _buildStatusCard(theme, isArabic),
              const SizedBox(height: 24),
              _buildActionButtons(theme, isArabic),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildMovementTypeIcon(),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.movement.documentNumber,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        widget.movement.typeNameAr,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: _getMovementTypeColor(),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isArabic ? 'السبب' : 'Reason',
              widget.movement.reasonNameAr,
              Icons.info_outline,
            ),
            if (widget.movement.referenceDocument?.isNotEmpty == true)
              _buildInfoRow(
                isArabic ? 'المرجع' : 'Reference',
                widget.movement.referenceDocument!,
                Icons.description,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات الصنف' : 'Item Information',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isArabic ? 'كود الصنف' : 'Item Code',
              _item?.code ?? 'غير محدد',
              Icons.qr_code,
            ),
            _buildInfoRow(
              isArabic ? 'اسم الصنف' : 'Item Name',
              _item?.name ?? 'غير محدد',
              Icons.inventory,
            ),
            _buildInfoRow(
              isArabic ? 'الوحدة' : 'Unit',
              _item?.unit ?? 'غير محدد',
              Icons.straighten,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات المخزن' : 'Warehouse Information',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isArabic ? 'المخزن' : 'Warehouse',
              _warehouse?.name ?? 'غير محدد',
              Icons.warehouse,
            ),
            if (widget.movement.type == MovementType.transfer &&
                _toWarehouse != null)
              _buildInfoRow(
                isArabic ? 'إلى المخزن' : 'To Warehouse',
                _toWarehouse!.name,
                Icons.arrow_forward,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic
                  ? 'معلومات الكمية والتكلفة'
                  : 'Quantity & Cost Information',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isArabic ? 'الكمية' : 'Quantity',
              '${widget.movement.quantity}',
              Icons.numbers,
            ),
            _buildInfoRow(
              isArabic ? 'تكلفة الوحدة' : 'Unit Cost',
              '${widget.movement.unitCost.toStringAsFixed(2)} ر.س',
              Icons.attach_money,
            ),
            _buildInfoRow(
              isArabic ? 'التكلفة الإجمالية' : 'Total Cost',
              '${widget.movement.totalCost.toStringAsFixed(2)} ر.س',
              Icons.calculate,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatesCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'التواريخ' : 'Dates',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              isArabic ? 'تاريخ الحركة' : 'Movement Date',
              _formatDate(widget.movement.movementDate),
              Icons.calendar_today,
            ),
            _buildInfoRow(
              isArabic ? 'تاريخ الإنشاء' : 'Created Date',
              _formatDate(widget.movement.createdAt),
              Icons.add_circle_outline,
            ),
            _buildInfoRow(
              isArabic ? 'تاريخ التحديث' : 'Updated Date',
              _formatDate(widget.movement.updatedAt),
              Icons.update,
            ),
            if (widget.movement.expiryDate != null)
              _buildInfoRow(
                isArabic ? 'تاريخ انتهاء الصلاحية' : 'Expiry Date',
                _formatDate(widget.movement.expiryDate!),
                Icons.warning,
                valueColor: Colors.orange,
              ),
            if (widget.movement.approvedAt != null)
              _buildInfoRow(
                isArabic ? 'تاريخ الاعتماد' : 'Approved Date',
                _formatDate(widget.movement.approvedAt!),
                Icons.check_circle,
                valueColor: Colors.green,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'الملاحظات' : 'Notes',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                widget.movement.notes!,
                style: theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackingCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات التتبع' : 'Tracking Information',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (widget.movement.batchNumber != null)
              _buildInfoRow(
                isArabic ? 'رقم الدفعة' : 'Batch Number',
                widget.movement.batchNumber!,
                Icons.batch_prediction,
              ),
            if (widget.movement.serialNumber != null)
              _buildInfoRow(
                isArabic ? 'الرقم التسلسلي' : 'Serial Number',
                widget.movement.serialNumber!,
                Icons.confirmation_number,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(ThemeData theme, bool isArabic) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات الحالة' : 'Status Information',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(_getStatusIcon(), color: _getStatusColor(), size: 24),
                const SizedBox(width: 12),
                Text(
                  widget.movement.statusNameAr,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: _getStatusColor(),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            if (widget.movement.approvedBy != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(
                isArabic ? 'معتمد بواسطة' : 'Approved By',
                'المستخدم ${widget.movement.approvedBy}',
                Icons.person,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme, bool isArabic) {
    return Row(
      children: [
        if (widget.movement.status == MovementStatus.pending) ...[
          Expanded(
            child: BeautifulButton(
              text: isArabic ? 'اعتماد' : 'Approve',
              onPressed: _approveMovement,
              backgroundColor: Colors.green,
              icon: Icons.check,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: BeautifulButton(
              text: isArabic ? 'رفض' : 'Reject',
              onPressed: _rejectMovement,
              backgroundColor: Colors.red,
              icon: Icons.close,
            ),
          ),
        ] else ...[
          Expanded(
            child: BeautifulButton(
              text: isArabic ? 'طباعة' : 'Print',
              onPressed: _printMovement,
              backgroundColor: theme.primaryColor,
              icon: Icons.print,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(fontWeight: FontWeight.w600, color: valueColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMovementTypeIcon() {
    IconData iconData;
    Color color;

    switch (widget.movement.type) {
      case MovementType.receipt:
        iconData = Icons.arrow_downward;
        color = Colors.green;
        break;
      case MovementType.issue:
        iconData = Icons.arrow_upward;
        color = Colors.red;
        break;
      case MovementType.transfer:
        iconData = Icons.swap_horiz;
        color = Colors.blue;
        break;
      case MovementType.adjustment:
        iconData = Icons.tune;
        color = Colors.orange;
        break;
      case MovementType.return_:
        iconData = Icons.undo;
        color = Colors.purple;
        break;
      case MovementType.damage:
        iconData = Icons.warning;
        color = Colors.red;
        break;
      case MovementType.loss:
        iconData = Icons.remove_circle;
        color = Colors.red;
        break;
      case MovementType.found:
        iconData = Icons.add_circle;
        color = Colors.green;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Icon(iconData, color: color, size: 24),
    );
  }

  Widget _buildStatusChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getStatusColor().withValues(alpha: 0.3)),
      ),
      child: Text(
        widget.movement.statusNameAr,
        style: TextStyle(
          color: _getStatusColor(),
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getMovementTypeColor() {
    switch (widget.movement.type) {
      case MovementType.receipt:
      case MovementType.found:
        return Colors.green;
      case MovementType.issue:
      case MovementType.damage:
      case MovementType.loss:
        return Colors.red;
      case MovementType.transfer:
        return Colors.blue;
      case MovementType.adjustment:
        return Colors.orange;
      case MovementType.return_:
        return Colors.purple;
    }
  }

  Color _getStatusColor() {
    switch (widget.movement.status) {
      case MovementStatus.pending:
        return Colors.orange;
      case MovementStatus.approved:
      case MovementStatus.completed:
        return Colors.green;
      case MovementStatus.rejected:
      case MovementStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon() {
    switch (widget.movement.status) {
      case MovementStatus.pending:
        return Icons.pending;
      case MovementStatus.approved:
        return Icons.check_circle;
      case MovementStatus.rejected:
        return Icons.cancel;
      case MovementStatus.completed:
        return Icons.check_circle_outline;
      case MovementStatus.cancelled:
        return Icons.block;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _editMovement() {
    if (widget.movement.status == MovementStatus.pending) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              AddStockMovementScreen(movement: widget.movement),
        ),
      ).then((result) {
        if (result == true && mounted) {
          Navigator.pop(
            context,
            true,
          ); // Return to previous screen with refresh
        }
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن تعديل الحركة بعد اعتمادها'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _printMovement() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طباعة الحركة'),
        content: const Text('هل تريد طباعة تفاصيل هذه الحركة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Here you would implement actual printing logic
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الحركة للطباعة'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('طباعة'),
          ),
        ],
      ),
    );
  }

  void _approveMovement() {
    if (widget.movement.status != MovementStatus.pending) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن اعتماد هذه الحركة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اعتماد الحركة'),
        content: const Text('هل أنت متأكد من اعتماد هذه الحركة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              // Here you would implement actual approval logic
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم اعتماد الحركة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              // Refresh the screen
              setState(() {
                // Update movement status locally for immediate UI feedback
              });
            },
            child: const Text('اعتماد'),
          ),
        ],
      ),
    );
  }

  void _rejectMovement() {
    if (widget.movement.status != MovementStatus.pending) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن رفض هذه الحركة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رفض الحركة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('هل أنت متأكد من رفض هذه الحركة؟'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'سبب الرفض',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              reasonController.dispose();
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (reasonController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال سبب الرفض'),
                    backgroundColor: Colors.orange,
                  ),
                );
                return;
              }

              Navigator.pop(context);
              reasonController.dispose();

              // Here you would implement actual rejection logic
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم رفض الحركة'),
                  backgroundColor: Colors.red,
                ),
              );

              // Refresh the screen
              setState(() {
                // Update movement status locally for immediate UI feedback
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('رفض'),
          ),
        ],
      ),
    );
  }
}
