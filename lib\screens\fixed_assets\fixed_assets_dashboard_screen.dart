import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../models/fixed_asset.dart';
import '../../services/fixed_asset_service.dart';
import 'fixed_assets_list_screen.dart';
import 'fixed_asset_form_screen.dart';

/// شاشة لوحة تحكم الأصول الثابتة
class FixedAssetsDashboardScreen extends StatefulWidget {
  const FixedAssetsDashboardScreen({super.key});

  @override
  State<FixedAssetsDashboardScreen> createState() =>
      _FixedAssetsDashboardScreenState();
}

class _FixedAssetsDashboardScreenState
    extends State<FixedAssetsDashboardScreen> {
  final FixedAssetService _assetService = FixedAssetService();

  bool _isLoading = true;
  String? _error;

  // إحصائيات الأصول
  int _totalAssets = 0;
  double _totalValue = 0;
  double _totalDepreciation = 0;
  double _currentBookValue = 0;

  // توزيع الأصول حسب الفئة
  final Map<AssetCategory, int> _assetsByCategory = {};
  final Map<AssetCategory, double> _valuesByCategory = {};

  // توزيع الأصول حسب الحالة
  final Map<AssetStatus, int> _assetsByStatus = {};

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل جميع الأصول
      final assetsResult = await _assetService.getAllFixedAssets();
      if (!assetsResult.isSuccess) {
        throw Exception(assetsResult.error);
      }

      final assets = assetsResult.data!;

      // حساب الإحصائيات
      _calculateStatistics(assets);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });
      }
    }
  }

  void _calculateStatistics(List<FixedAsset> assets) {
    _totalAssets = assets.length;
    _totalValue = 0;
    _totalDepreciation = 0;
    _currentBookValue = 0;

    _assetsByCategory.clear();
    _valuesByCategory.clear();
    _assetsByStatus.clear();

    for (final asset in assets) {
      _totalValue += asset.purchasePrice;
      _totalDepreciation += asset.getTotalDepreciation();
      _currentBookValue += asset.getCurrentBookValue();

      // توزيع حسب الفئة
      _assetsByCategory[asset.category] =
          (_assetsByCategory[asset.category] ?? 0) + 1;
      _valuesByCategory[asset.category] =
          (_valuesByCategory[asset.category] ?? 0) + asset.purchasePrice;

      // توزيع حسب الحالة
      _assetsByStatus[asset.status] = (_assetsByStatus[asset.status] ?? 0) + 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم الأصول الثابتة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.list),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FixedAssetsListScreen(),
                ),
              );
            },
            tooltip: 'قائمة الأصول',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDashboardData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCards(),
            const SizedBox(height: 24),
            _buildCategoryChart(),
            const SizedBox(height: 24),
            _buildStatusChart(),
            const SizedBox(height: 24),
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildSummaryCard(
          'إجمالي الأصول',
          _totalAssets.toString(),
          Icons.account_balance,
          Colors.blue,
        ),
        _buildSummaryCard(
          'إجمالي القيمة',
          '${_totalValue.toStringAsFixed(0)} ر.س',
          Icons.attach_money,
          Colors.green,
        ),
        _buildSummaryCard(
          'إجمالي الاستهلاك',
          '${_totalDepreciation.toStringAsFixed(0)} ر.س',
          Icons.trending_down,
          Colors.orange,
        ),
        _buildSummaryCard(
          'القيمة الدفترية',
          '${_currentBookValue.toStringAsFixed(0)} ر.س',
          Icons.account_balance_wallet,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChart() {
    if (_assetsByCategory.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع الأصول حسب الفئة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: _assetsByCategory.entries.map((entry) {
                    final category = entry.key;
                    final count = entry.value;
                    final percentage = (count / _totalAssets) * 100;

                    return PieChartSectionData(
                      value: count.toDouble(),
                      title: '${percentage.toStringAsFixed(1)}%',
                      color: _getCategoryColor(category),
                      radius: 80,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: _assetsByCategory.entries.map((entry) {
                final category = entry.key;
                final count = entry.value;

                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(category),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${_getCategoryName(category)} ($count)',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChart() {
    if (_assetsByStatus.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع الأصول حسب الحالة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...AssetStatus.values.map((status) {
              final count = _assetsByStatus[status] ?? 0;
              final percentage = _totalAssets > 0
                  ? (count / _totalAssets) * 100
                  : 0;

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(_getStatusName(status)),
                        Text('$count (${percentage.toStringAsFixed(1)}%)'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: _totalAssets > 0 ? count / _totalAssets : 0,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation(
                        _getStatusColor(status),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildQuickActionButton('عرض جميع الأصول', Icons.list, () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FixedAssetsListScreen(),
                    ),
                  );
                }),
                _buildQuickActionButton('إضافة أصل جديد', Icons.add, () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FixedAssetFormScreen(),
                    ),
                  );
                  if (result == true) {
                    _loadDashboardData();
                  }
                }),
                _buildQuickActionButton(
                  'تقرير الاستهلاك',
                  Icons.trending_down,
                  () {
                    // سيتم تنفيذه لاحقاً
                  },
                ),
                _buildQuickActionButton('تقرير القيم', Icons.assessment, () {
                  // سيتم تنفيذه لاحقاً
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, size: 20, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  String _getCategoryName(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return 'مباني';
      case AssetCategory.machinery:
        return 'آلات ومعدات';
      case AssetCategory.vehicle:
        return 'مركبات';
      case AssetCategory.furniture:
        return 'أثاث ومفروشات';
      case AssetCategory.computer:
        return 'أجهزة حاسوب';
      case AssetCategory.other:
        return 'أخرى';
    }
  }

  String _getStatusName(AssetStatus status) {
    switch (status) {
      case AssetStatus.active:
        return 'نشط';
      case AssetStatus.inactive:
        return 'غير نشط';
      case AssetStatus.disposed:
        return 'مستبعد';
      case AssetStatus.underMaintenance:
        return 'تحت الصيانة';
    }
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return Colors.brown;
      case AssetCategory.machinery:
        return Colors.orange;
      case AssetCategory.vehicle:
        return Colors.blue;
      case AssetCategory.furniture:
        return Colors.green;
      case AssetCategory.computer:
        return Colors.purple;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  Color _getStatusColor(AssetStatus status) {
    switch (status) {
      case AssetStatus.active:
        return Colors.green;
      case AssetStatus.inactive:
        return Colors.orange;
      case AssetStatus.disposed:
        return Colors.red;
      case AssetStatus.underMaintenance:
        return Colors.blue;
    }
  }
}
