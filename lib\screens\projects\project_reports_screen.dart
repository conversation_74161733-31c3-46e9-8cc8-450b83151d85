/// شاشة تقارير المشروع
/// Project Reports Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/project.dart';
import '../../services/project_service.dart';
import '../../widgets/quantum_card.dart';

import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_text_styles.dart';

class ProjectReportsScreen extends StatefulWidget {
  final Project project;

  const ProjectReportsScreen({super.key, required this.project});

  @override
  State<ProjectReportsScreen> createState() => _ProjectReportsScreenState();
}

class _ProjectReportsScreenState extends State<ProjectReportsScreen> {
  final ProjectService _projectService = ProjectService();
  bool _isLoading = false;
  ProjectProfitability? _projectAnalytics;

  @override
  void initState() {
    super.initState();
    _loadProjectAnalytics();
  }

  Future<void> _loadProjectAnalytics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load project analytics data
      final profitabilityResult = await _projectService
          .calculateProjectProfitability(widget.project.id!);

      if (profitabilityResult.isSuccess && profitabilityResult.data != null) {
        _projectAnalytics = profitabilityResult.data!;
      }

      setState(() {});
    } catch (e) {
      _showErrorDialog('خطأ في تحميل تحليلات المشروع: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقارير ${widget.project.name}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // ملخص المشروع
              _buildProjectSummaryCard(),

              const SizedBox(height: 16),

              // تقارير مالية
              _buildFinancialReportsCard(),

              const SizedBox(height: 16),

              // تقارير التقدم والأداء
              _buildPerformanceReportsCard(),

              const SizedBox(height: 16),

              // تقارير الموارد والوقت
              _buildResourceReportsCard(),

              const SizedBox(height: 16),

              // تقارير مخصصة
              _buildCustomReportsCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectSummaryCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص المشروع',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'الميزانية المخططة',
                    _formatCurrency(widget.project.budgetAmount),
                    Icons.account_balance_wallet,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryItem(
                    'التكلفة الفعلية',
                    _formatCurrency(widget.project.actualCost),
                    Icons.receipt_long,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'المبلغ المفوتر',
                    _formatCurrency(widget.project.billedAmount),
                    Icons.payment,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryItem(
                    'هامش الربح',
                    '${widget.project.profitMargin.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    widget.project.profitMargin >= 0
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialReportsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التقارير المالية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            _buildReportButton(
              'تحليل الربحية',
              'تحليل مفصل لربحية المشروع والعوائد',
              Icons.analytics,
              Colors.green,
              () => _showProfitabilityReport(),
            ),

            const SizedBox(height: 12),

            _buildReportButton(
              'تقرير التكاليف',
              'تفصيل جميع تكاليف المشروع حسب الفئة',
              Icons.receipt_long,
              Colors.orange,
              () => _showCostReport(),
            ),

            const SizedBox(height: 12),

            _buildReportButton(
              'تقرير الفوترة',
              'تتبع المبالغ المفوترة والمدفوعة',
              Icons.payment,
              Colors.blue,
              () => _showBillingReport(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceReportsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقارير الأداء والتقدم',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            _buildReportButton(
              'تقرير التقدم',
              'نسبة الإنجاز والمراحل المكتملة',
              Icons.timeline,
              Colors.purple,
              () => _showProgressReport(),
            ),

            const SizedBox(height: 12),

            _buildReportButton(
              'تحليل الجدولة',
              'مقارنة الجدولة المخططة مع الفعلية',
              Icons.schedule,
              Colors.indigo,
              () => _showScheduleAnalysis(),
            ),

            const SizedBox(height: 12),

            _buildReportButton(
              'مؤشرات الأداء الرئيسية',
              'KPIs ومقاييس الأداء المهمة',
              Icons.dashboard,
              Colors.teal,
              () => _showKPIReport(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResourceReportsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقارير الموارد والوقت',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            _buildReportButton(
              'تقرير الموارد',
              'استخدام الموارد البشرية والمادية',
              Icons.people,
              Colors.brown,
              () => _showResourceReport(),
            ),

            const SizedBox(height: 12),

            _buildReportButton(
              'تتبع الوقت',
              'ساعات العمل المسجلة والإنتاجية',
              Icons.access_time,
              Colors.cyan,
              () => _showTimeTrackingReport(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomReportsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقارير مخصصة',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            _buildReportButton(
              'تقرير شامل',
              'تقرير شامل يحتوي على جميع جوانب المشروع',
              Icons.description,
              Colors.deepPurple,
              () => _showComprehensiveReport(),
            ),

            const SizedBox(height: 12),

            _buildReportButton(
              'تصدير البيانات',
              'تصدير بيانات المشروع بصيغ مختلفة',
              Icons.file_download,
              Colors.grey[700]!,
              () => _showExportOptions(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportButton(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.titleSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Report methods
  void _showProfitabilityReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('تحليل الربحية', _buildProfitabilityContent()),
    );
  }

  void _showCostReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('تقرير التكاليف', _buildCostContent()),
    );
  }

  void _showBillingReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('تقرير الفوترة', _buildBillingContent()),
    );
  }

  void _showProgressReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('تقرير التقدم', _buildProgressContent()),
    );
  }

  void _showScheduleAnalysis() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('تحليل الجدولة', _buildScheduleContent()),
    );
  }

  void _showKPIReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('مؤشرات الأداء الرئيسية', _buildKPIContent()),
    );
  }

  void _showResourceReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('تقرير الموارد', _buildResourceContent()),
    );
  }

  void _showTimeTrackingReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('تتبع الوقت', _buildTimeTrackingContent()),
    );
  }

  void _showComprehensiveReport() {
    showDialog(
      context: context,
      builder: (context) =>
          _buildReportDialog('التقرير الشامل', _buildComprehensiveContent()),
    );
  }

  void _showExportOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('تصدير PDF'),
              onTap: () => _exportToPDF(),
            ),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('تصدير Excel'),
              onTap: () => _exportToExcel(),
            ),
            ListTile(
              leading: const Icon(Icons.code, color: Colors.blue),
              title: const Text('تصدير JSON'),
              onTap: () => _exportToJSON(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildReportDialog(String title, Widget content) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),
            Expanded(child: content),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitabilityContent() {
    final profitability = _projectAnalytics;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'إجمالي الإيرادات',
            _formatCurrency(widget.project.billedAmount),
            Icons.attach_money,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'إجمالي التكاليف',
            _formatCurrency(widget.project.actualCost),
            Icons.money_off,
            Colors.red,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'صافي الربح',
            _formatCurrency(
              widget.project.billedAmount - widget.project.actualCost,
            ),
            Icons.trending_up,
            widget.project.billedAmount - widget.project.actualCost >= 0
                ? Colors.green
                : Colors.red,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'هامش الربح',
            '${widget.project.profitMargin.toStringAsFixed(2)}%',
            Icons.percent,
            widget.project.profitMargin >= 0 ? Colors.green : Colors.red,
          ),
          if (profitability != null) ...[
            const SizedBox(height: 16),
            Text(
              'تفاصيل إضافية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildDetailRow(
              'إجمالي الإيرادات',
              _formatCurrency(profitability.totalRevenue),
            ),
            _buildDetailRow(
              'إجمالي التكاليف',
              _formatCurrency(profitability.totalCosts),
            ),
            _buildDetailRow(
              'إجمالي الربح',
              _formatCurrency(profitability.grossProfit),
            ),
            _buildDetailRow(
              'انحراف الميزانية',
              _formatCurrency(profitability.budgetVariance),
            ),
            _buildDetailRow(
              'نسبة انحراف الميزانية',
              '${profitability.budgetVariancePercentage.toStringAsFixed(2)}%',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCostContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'الميزانية المخططة',
            _formatCurrency(widget.project.budgetAmount),
            Icons.account_balance_wallet,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'التكلفة الفعلية',
            _formatCurrency(widget.project.actualCost),
            Icons.receipt_long,
            Colors.orange,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'الانحراف',
            _formatCurrency(
              widget.project.actualCost - widget.project.budgetAmount,
            ),
            Icons.compare_arrows,
            widget.project.actualCost <= widget.project.budgetAmount
                ? Colors.green
                : Colors.red,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'نسبة الانحراف',
            widget.project.budgetAmount > 0
                ? '${((widget.project.actualCost - widget.project.budgetAmount) / widget.project.budgetAmount * 100).toStringAsFixed(2)}%'
                : '0%',
            Icons.percent,
            widget.project.actualCost <= widget.project.budgetAmount
                ? Colors.green
                : Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'تفصيل التكاليف',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildDetailRow('تكاليف المواد', 'قيد التحديث'),
          _buildDetailRow('تكاليف العمالة', 'قيد التحديث'),
          _buildDetailRow('تكاليف المعدات', 'قيد التحديث'),
          _buildDetailRow('تكاليف أخرى', 'قيد التحديث'),
        ],
      ),
    );
  }

  Widget _buildBillingContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'المبلغ المفوتر',
            _formatCurrency(widget.project.billedAmount),
            Icons.payment,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'المبلغ المحصل',
            'قيد التحديث',
            Icons.account_balance,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'المبلغ المتبقي',
            'قيد التحديث',
            Icons.pending_actions,
            Colors.orange,
          ),
          const SizedBox(height: 16),
          Text(
            'تفاصيل الفوترة',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildDetailRow('عدد الفواتير', 'قيد التحديث'),
          _buildDetailRow('متوسط قيمة الفاتورة', 'قيد التحديث'),
          _buildDetailRow('آخر فاتورة', 'قيد التحديث'),
        ],
      ),
    );
  }

  Widget _buildProgressContent() {
    final completedPhases = widget.project.phases
        .where((p) => p.status == ProjectPhaseStatus.completed)
        .length;
    final totalPhases = widget.project.phases.length;
    final progressPercentage = totalPhases > 0
        ? (completedPhases / totalPhases * 100)
        : 0.0;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'نسبة الإنجاز',
            '${progressPercentage.toStringAsFixed(1)}%',
            Icons.timeline,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'المراحل المكتملة',
            '$completedPhases من $totalPhases',
            Icons.check_circle,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'الأيام المتبقية',
            widget.project.endDate != null
                ? '${widget.project.endDate!.difference(DateTime.now()).inDays} يوم'
                : 'غير محدد',
            Icons.calendar_today,
            Colors.orange,
          ),
          const SizedBox(height: 16),
          Text(
            'تفاصيل المراحل',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...widget.project.phases.map(
            (phase) => _buildDetailRow(phase.name, phase.status.nameAr),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleContent() {
    final startDate = widget.project.startDate;
    final endDate = widget.project.endDate;
    final actualEndDate = widget.project.actualEndDate;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'تاريخ البداية',
            '${startDate.day}/${startDate.month}/${startDate.year}',
            Icons.play_arrow,
            Colors.green,
          ),
          const SizedBox(height: 12),
          if (endDate != null)
            _buildMetricCard(
              'تاريخ النهاية المخطط',
              '${endDate.day}/${endDate.month}/${endDate.year}',
              Icons.flag,
              Colors.blue,
            ),
          const SizedBox(height: 12),
          if (actualEndDate != null)
            _buildMetricCard(
              'تاريخ النهاية الفعلي',
              '${actualEndDate.day}/${actualEndDate.month}/${actualEndDate.year}',
              Icons.stop,
              Colors.orange,
            ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'المدة المخططة',
            endDate != null
                ? '${endDate.difference(startDate).inDays} يوم'
                : 'غير محدد',
            Icons.schedule,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildKPIContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'العائد على الاستثمار',
            widget.project.budgetAmount > 0
                ? '${((widget.project.billedAmount - widget.project.actualCost) / widget.project.budgetAmount * 100).toStringAsFixed(2)}%'
                : '0%',
            Icons.trending_up,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'كفاءة التكلفة',
            widget.project.actualCost > 0
                ? '(widget.project.billedAmount / widget.project.actualCost).toStringAsFixed(2)'
                : '0',
            Icons.trending_up,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'مؤشر الأداء الزمني',
            'قيد التحديث',
            Icons.timer,
            Colors.orange,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'رضا العميل',
            'قيد التحديث',
            Icons.sentiment_satisfied,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildResourceContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'عدد الموارد',
            '${widget.project.resources.length}',
            Icons.people,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'ساعات العمل المخططة',
            'قيد التحديث',
            Icons.schedule,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'ساعات العمل الفعلية',
            'قيد التحديث',
            Icons.access_time,
            Colors.orange,
          ),
          const SizedBox(height: 16),
          Text(
            'تفاصيل الموارد',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...widget.project.resources.map(
            (resource) =>
                _buildDetailRow(resource.name, resource.resourceType.nameAr),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeTrackingContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard(
            'إجمالي الساعات',
            '${widget.project.timeEntries.fold(0.0, (sum, entry) => sum + entry.hours)} ساعة',
            Icons.access_time,
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'عدد الإدخالات',
            '${widget.project.timeEntries.length}',
            Icons.list,
            Colors.green,
          ),
          const SizedBox(height: 12),
          _buildMetricCard(
            'متوسط الساعات اليومية',
            widget.project.timeEntries.isNotEmpty
                ? '${(widget.project.timeEntries.fold(0.0, (sum, entry) => sum + entry.hours) / widget.project.timeEntries.length).toStringAsFixed(2)} ساعة'
                : '0 ساعة',
            Icons.timeline,
            Colors.orange,
          ),
          const SizedBox(height: 16),
          Text(
            'آخر الإدخالات',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...widget.project.timeEntries
              .take(5)
              .map(
                (entry) => _buildDetailRow(
                  '${entry.date.day}/${entry.date.month}/${entry.date.year}',
                  '${entry.hours} ساعة',
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildComprehensiveContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص شامل للمشروع',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildDetailRow('اسم المشروع', widget.project.name),
          _buildDetailRow('كود المشروع', widget.project.code),
          _buildDetailRow('نوع المشروع', widget.project.type.nameAr),
          _buildDetailRow('حالة المشروع', widget.project.status.nameAr),
          _buildDetailRow('الأولوية', widget.project.priority.nameAr),
          if (widget.project.customer != null)
            _buildDetailRow('العميل', widget.project.customer!.name),
          _buildDetailRow(
            'تاريخ البداية',
            '${widget.project.startDate.day}/${widget.project.startDate.month}/${widget.project.startDate.year}',
          ),
          if (widget.project.endDate != null)
            _buildDetailRow(
              'تاريخ النهاية',
              '${widget.project.endDate!.day}/${widget.project.endDate!.month}/${widget.project.endDate!.year}',
            ),
          _buildDetailRow(
            'الميزانية',
            '${widget.project.budgetAmount.toStringAsFixed(2)} ر.س',
          ),
          _buildDetailRow(
            'التكلفة الفعلية',
            '${widget.project.actualCost.toStringAsFixed(2)} ر.س',
          ),
          _buildDetailRow(
            'المبلغ المفوتر',
            '${widget.project.billedAmount.toStringAsFixed(2)} ر.س',
          ),
          _buildDetailRow(
            'هامش الربح',
            '${widget.project.profitMargin.toStringAsFixed(2)}%',
          ),
          if (widget.project.location != null)
            _buildDetailRow('الموقع', widget.project.location!),
          if (widget.project.notes != null)
            _buildDetailRow('ملاحظات', widget.project.notes!),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(child: Text(value, style: AppTextStyles.bodyMedium)),
        ],
      ),
    );
  }

  // Export methods
  void _exportToPDF() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير PDF قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _exportToExcel() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير Excel قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _exportToJSON() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير JSON قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
