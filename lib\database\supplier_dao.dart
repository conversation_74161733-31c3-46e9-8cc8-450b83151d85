import '../models/supplier.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class SupplierDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Create a new supplier
  Future<int> insertSupplier(Supplier supplier) async {
    Map<String, dynamic> supplierMap = supplier.toMap();
    supplierMap.remove('id'); // Remove id for insert
    return await _dbHelper.insert(DatabaseSchema.tableSuppliers, supplierMap);
  }

  // Get all suppliers
  Future<List<Supplier>> getAllSuppliers() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      orderBy: 'name ASC',
    );
    return maps.map((map) => Supplier.fromMap(map)).toList();
  }

  // Get supplier by ID
  Future<Supplier?> getSupplierById(int id) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Supplier.fromMap(maps.first);
    }
    return null;
  }

  // Get supplier by code
  Future<Supplier?> getSupplierByCode(String code) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: 'code = ?',
      whereArgs: [code],
    );
    if (maps.isNotEmpty) {
      return Supplier.fromMap(maps.first);
    }
    return null;
  }

  // Get active suppliers only
  Future<List<Supplier>> getActiveSuppliers() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );
    return maps.map((map) => Supplier.fromMap(map)).toList();
  }

  // Search suppliers by name, code, phone, or email
  Future<List<Supplier>> searchSuppliers(String query) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: 'name LIKE ? OR code LIKE ? OR phone LIKE ? OR email LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => Supplier.fromMap(map)).toList();
  }

  // Get suppliers with debit balance
  Future<List<Supplier>> getSuppliersWithDebitBalance() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: 'current_balance > 0 AND is_active = 1',
      orderBy: 'current_balance DESC',
    );
    return maps.map((map) => Supplier.fromMap(map)).toList();
  }

  // Get suppliers with credit balance
  Future<List<Supplier>> getSuppliersWithCreditBalance() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: 'current_balance < 0 AND is_active = 1',
      orderBy: 'current_balance ASC',
    );
    return maps.map((map) => Supplier.fromMap(map)).toList();
  }

  // Update supplier
  Future<int> updateSupplier(Supplier supplier) async {
    return await _dbHelper.update(
      DatabaseSchema.tableSuppliers,
      supplier.toMap(),
      where: 'id = ?',
      whereArgs: [supplier.id],
    );
  }

  // Update supplier balance
  Future<int> updateSupplierBalance(int supplierId, double newBalance) async {
    return await _dbHelper.update(
      DatabaseSchema.tableSuppliers,
      {
        'current_balance': newBalance,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [supplierId],
    );
  }

  // Deactivate supplier (soft delete)
  Future<int> deactivateSupplier(int supplierId) async {
    return await _dbHelper.update(
      DatabaseSchema.tableSuppliers,
      {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [supplierId],
    );
  }

  // Hard delete supplier (use with caution)
  Future<int> deleteSupplier(int supplierId) async {
    return await _dbHelper.delete(
      DatabaseSchema.tableSuppliers,
      where: 'id = ?',
      whereArgs: [supplierId],
    );
  }

  // Check if supplier code exists
  Future<bool> isCodeExists(String code, {int? excludeId}) async {
    String whereClause = 'code = ?';
    List<dynamic> whereArgs = [code];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.isNotEmpty;
  }

  // Check if supplier has transactions
  Future<bool> hasTransactions(int supplierId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'supplier_id = ?',
      whereArgs: [supplierId],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  // Get next supplier code
  Future<String> getNextSupplierCode() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      orderBy: 'code DESC',
      limit: 1,
    );

    if (maps.isEmpty) {
      return 'S001';
    }

    String lastCode = maps.first['code'] as String;
    // Extract number from code (assuming format like S001, S002, etc.)
    RegExp regExp = RegExp(r'(\d+)$');
    Match? match = regExp.firstMatch(lastCode);

    if (match != null) {
      int lastNumber = int.parse(match.group(1)!);
      int nextNumber = lastNumber + 1;
      String prefix = lastCode.substring(0, match.start);
      return '$prefix${nextNumber.toString().padLeft(3, '0')}';
    }

    return 'S001';
  }

  // Get supplier balance summary
  Future<Map<String, double>> getSupplierBalanceSummary() async {
    final debitResult = await _dbHelper.rawQuery(
      'SELECT SUM(current_balance) as total FROM ${DatabaseSchema.tableSuppliers} WHERE current_balance > 0 AND is_active = 1',
    );

    final creditResult = await _dbHelper.rawQuery(
      'SELECT SUM(ABS(current_balance)) as total FROM ${DatabaseSchema.tableSuppliers} WHERE current_balance < 0 AND is_active = 1',
    );

    double totalDebit = 0.0;
    double totalCredit = 0.0;

    if (debitResult.isNotEmpty && debitResult.first['total'] != null) {
      totalDebit = (debitResult.first['total'] as num).toDouble();
    }

    if (creditResult.isNotEmpty && creditResult.first['total'] != null) {
      totalCredit = (creditResult.first['total'] as num).toDouble();
    }

    return {
      'total_debit': totalDebit,
      'total_credit': totalCredit,
      'net_balance': totalDebit - totalCredit,
    };
  }

  // Get supplier statistics
  Future<Map<String, dynamic>> getSupplierStatistics() async {
    final totalResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as total FROM ${DatabaseSchema.tableSuppliers}',
    );

    final activeResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as active FROM ${DatabaseSchema.tableSuppliers} WHERE is_active = 1',
    );

    final withBalanceResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as with_balance FROM ${DatabaseSchema.tableSuppliers} WHERE current_balance != 0',
    );

    return {
      'total': totalResult.first['total'] as int,
      'active': activeResult.first['active'] as int,
      'with_balance': withBalanceResult.first['with_balance'] as int,
    };
  }

  // Get supplier transactions for statement
  Future<List<SupplierTransaction>> getSupplierTransactions(
    int supplierId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    String query =
        '''
      SELECT 
        je.date,
        je.entry_number as reference,
        je.description,
        jel.debit_amount,
        jel.credit_amount,
        'journal_entry' as transaction_type
      FROM ${DatabaseSchema.tableJournalEntries} je
      INNER JOIN ${DatabaseSchema.tableJournalEntryLines} jel ON je.id = jel.journal_entry_id
      INNER JOIN ${DatabaseSchema.tableAccounts} a ON jel.account_id = a.id
      INNER JOIN ${DatabaseSchema.tableSuppliers} s ON a.id = s.account_id
      WHERE s.id = ? AND je.is_posted = 1
    ''';

    List<dynamic> args = [supplierId];

    if (startDate != null) {
      query += ' AND je.date >= ?';
      args.add(startDate.toIso8601String().split('T')[0]);
    }

    if (endDate != null) {
      query += ' AND je.date <= ?';
      args.add(endDate.toIso8601String().split('T')[0]);
    }

    query += ' ORDER BY je.date ASC, je.entry_number ASC';

    final List<Map<String, dynamic>> maps = await _dbHelper.rawQuery(
      query,
      args,
    );

    List<SupplierTransaction> transactions = [];
    double runningBalance = 0.0;

    for (Map<String, dynamic> map in maps) {
      double debitAmount = (map['debit_amount'] as num?)?.toDouble() ?? 0.0;
      double creditAmount = (map['credit_amount'] as num?)?.toDouble() ?? 0.0;
      runningBalance += debitAmount - creditAmount;

      transactions.add(
        SupplierTransaction(
          date: DateTime.parse(map['date'] as String),
          reference: map['reference'] as String,
          description: map['description'] as String,
          debitAmount: debitAmount,
          creditAmount: creditAmount,
          runningBalance: runningBalance,
          transactionType: map['transaction_type'] as String,
        ),
      );
    }

    return transactions;
  }

  // Get supplier summary for a period
  Future<SupplierSummary?> getSupplierSummary(
    int supplierId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    Supplier? supplier = await getSupplierById(supplierId);
    if (supplier == null) return null;

    List<SupplierTransaction> transactions = await getSupplierTransactions(
      supplierId,
      startDate: startDate,
      endDate: endDate,
    );

    double totalDebits = 0.0;
    double totalCredits = 0.0;

    for (SupplierTransaction transaction in transactions) {
      totalDebits += transaction.debitAmount;
      totalCredits += transaction.creditAmount;
    }

    return SupplierSummary(
      supplierId: supplierId,
      supplierCode: supplier.code,
      supplierName: supplier.name,
      openingBalance: supplier.currentBalance - (totalDebits - totalCredits),
      totalDebits: totalDebits,
      totalCredits: totalCredits,
      closingBalance: supplier.currentBalance,
      transactionCount: transactions.length,
    );
  }

  // Get specific supplier statistics by ID
  Future<Map<String, dynamic>> getSupplierStatisticsById(int supplierId) async {
    // Get total purchases from invoices
    final purchasesResult = await _dbHelper.rawQuery(
      'SELECT SUM(total_amount) as total_purchases, COUNT(*) as invoice_count FROM ${DatabaseSchema.tableInvoices} WHERE supplier_id = ? AND status != "cancelled"',
      [supplierId],
    );

    // Get current balance
    final balanceResult = await _dbHelper.rawQuery(
      'SELECT current_balance FROM ${DatabaseSchema.tableSuppliers} WHERE id = ?',
      [supplierId],
    );

    // Get last purchase date
    final lastPurchaseResult = await _dbHelper.rawQuery(
      'SELECT MAX(date) as last_purchase_date FROM ${DatabaseSchema.tableInvoices} WHERE supplier_id = ? AND status != "cancelled"',
      [supplierId],
    );

    return {
      'totalPurchases': purchasesResult.first['total_purchases'] ?? 0.0,
      'invoiceCount': purchasesResult.first['invoice_count'] ?? 0,
      'currentBalance': balanceResult.isNotEmpty
          ? (balanceResult.first['current_balance'] ?? 0.0)
          : 0.0,
      'lastPurchaseDate': lastPurchaseResult.first['last_purchase_date'],
    };
  }

  // Get supplier balance
  Future<double> getSupplierBalance(int supplierId) async {
    final result = await _dbHelper.rawQuery(
      'SELECT current_balance FROM ${DatabaseSchema.tableSuppliers} WHERE id = ?',
      [supplierId],
    );

    if (result.isNotEmpty) {
      return result.first['current_balance'] ?? 0.0;
    }
    return 0.0;
  }

  // Get supplier total purchases
  Future<double> getSupplierTotalPurchases(
    int supplierId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    String query =
        'SELECT SUM(total_amount) as total FROM ${DatabaseSchema.tableInvoices} WHERE supplier_id = ? AND status != "cancelled"';
    List<dynamic> args = [supplierId];

    if (startDate != null) {
      query += ' AND date >= ?';
      args.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      query += ' AND date <= ?';
      args.add(endDate.toIso8601String());
    }

    final result = await _dbHelper.rawQuery(query, args);
    return result.first['total'] ?? 0.0;
  }

  // Get suppliers count
  Future<int> getSuppliersCount() async {
    final result = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableSuppliers}',
    );
    return result.first['count'] ?? 0;
  }

  // Get suppliers by category
  Future<List<Supplier>> getSuppliersByCategory(String category) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableSuppliers,
      where: 'category = ? AND is_active = 1',
      whereArgs: [category],
      orderBy: 'name ASC',
    );
    return maps.map((map) => Supplier.fromMap(map)).toList();
  }

  // Get all supplier categories
  Future<List<String>> getSupplierCategories() async {
    final result = await _dbHelper.rawQuery(
      'SELECT DISTINCT category FROM ${DatabaseSchema.tableSuppliers} WHERE category IS NOT NULL AND category != "" ORDER BY category ASC',
    );
    return result.map((row) => row['category'] as String).toList();
  }
}
