import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/invoice.dart';
import '../../services/invoice_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_inputs.dart';
import 'add_invoice_screen.dart';
import 'invoice_details_screen.dart';

class InvoicesScreen extends StatefulWidget {
  const InvoicesScreen({super.key});

  @override
  State<InvoicesScreen> createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends State<InvoicesScreen>
    with SingleTickerProviderStateMixin {
  final InvoiceService _invoiceService = InvoiceService();
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;

  List<Invoice> _invoices = [];
  List<Invoice> _filteredInvoices = [];
  bool _isLoading = true;
  InvoiceType _selectedType = InvoiceType.sales;
  InvoiceStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadInvoices();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    setState(() {
      _selectedType = _tabController.index == 0
          ? InvoiceType.sales
          : InvoiceType.purchase;
    });
    _filterInvoices();
  }

  Future<void> _loadInvoices() async {
    setState(() => _isLoading = true);

    final result = await _invoiceService.getAllInvoices();
    if (result.isSuccess && result.data != null) {
      setState(() {
        _invoices = result.data!;
        _isLoading = false;
      });
      _filterInvoices();
    } else {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.error ?? 'خطأ في تحميل الفواتير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterInvoices() {
    setState(() {
      _filteredInvoices = _invoices.where((invoice) {
        final matchesType = invoice.invoiceType == _selectedType;
        final matchesStatus =
            _selectedStatus == null || invoice.status == _selectedStatus;
        final matchesSearch =
            _searchController.text.isEmpty ||
            invoice.invoiceNumber.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            ) ||
            (invoice.customer?.name.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ??
                false) ||
            (invoice.supplier?.name.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ??
                false);

        return matchesType && matchesStatus && matchesSearch;
      }).toList();
    });
  }

  Future<void> _deleteInvoice(Invoice invoice) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف الفاتورة "${invoice.invoiceNumber}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await _invoiceService.deleteInvoice(invoice.id!);
      if (mounted) {
        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الفاتورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          _loadInvoices();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'خطأ في حذف الفاتورة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('إدارة الفواتير'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadInvoices),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'فواتير البيع', icon: Icon(Icons.point_of_sale)),
            Tab(text: 'فواتير الشراء', icon: Icon(Icons.shopping_cart)),
          ],
        ),
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
            ),
            child: Column(
              children: [
                // شريط البحث
                BeautifulTextFormField(
                  controller: _searchController,
                  labelText: 'البحث في الفواتير',
                  prefixIcon: Icons.search,
                  onChanged: (_) => _filterInvoices(),
                ),
                const SizedBox(height: AppTheme.spacingMedium),

                // فلتر الحالة
                Row(
                  children: [
                    const Icon(Icons.filter_list, color: Colors.white),
                    const SizedBox(width: AppTheme.spacingSmall),
                    Expanded(
                      child: DropdownButtonFormField<InvoiceStatus?>(
                        value: _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: 'حالة الفاتورة',
                          labelStyle: TextStyle(color: Colors.white),
                          enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.white),
                          ),
                        ),
                        dropdownColor: AppTheme.primaryColor,
                        style: const TextStyle(color: Colors.white),
                        items: [
                          const DropdownMenuItem<InvoiceStatus?>(
                            value: null,
                            child: Text('جميع الحالات'),
                          ),
                          ...InvoiceStatus.values.map((status) {
                            return DropdownMenuItem<InvoiceStatus?>(
                              value: status,
                              child: Text(_getStatusText(status)),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setState(() => _selectedStatus = value);
                          _filterInvoices();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // قائمة الفواتير
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [_buildInvoicesList(), _buildInvoicesList()],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  AddInvoiceScreen(invoiceType: _selectedType),
            ),
          );
          if (result == true) {
            _loadInvoices();
          }
        },
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: Text(
          _selectedType == InvoiceType.sales ? 'فاتورة بيع' : 'فاتورة شراء',
        ),
      ),
    );
  }

  Widget _buildInvoicesList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'لا توجد فواتير',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        itemCount: _filteredInvoices.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildInvoiceCard(_filteredInvoices[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildInvoiceCard(Invoice invoice) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: InkWell(
        borderRadius: BorderRadius.circular(15),
        onTap: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) => InvoiceDetailsScreen(invoice: invoice),
            ),
          );
          if (result == true) {
            _loadInvoices();
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة الفاتورة
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        invoice.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      invoice.invoiceType == InvoiceType.sales
                          ? Icons.point_of_sale
                          : Icons.shopping_cart,
                      color: _getStatusColor(invoice.status),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingMedium),

                  // معلومات الفاتورة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invoice.invoiceNumber,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          invoice.customer?.name ??
                              invoice.supplier?.name ??
                              'غير محدد',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _formatDate(invoice.date),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // حالة الفاتورة
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        invoice.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _getStatusColor(
                          invoice.status,
                        ).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      _getStatusText(invoice.status),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor(invoice.status),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // قائمة الإجراءات
                  PopupMenuButton<String>(
                    onSelected: (value) async {
                      switch (value) {
                        case 'edit':
                          final result = await Navigator.push<bool>(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AddInvoiceScreen(
                                invoice: invoice,
                                invoiceType: invoice.invoiceType,
                              ),
                            ),
                          );
                          if (result == true) {
                            _loadInvoices();
                          }
                          break;
                        case 'delete':
                          _deleteInvoice(invoice);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacingMedium),

              // معلومات المبالغ
              Row(
                children: [
                  Expanded(
                    child: _buildAmountChip(
                      'المجموع الفرعي',
                      '${invoice.subtotal.toStringAsFixed(2)} ر.س',
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  Expanded(
                    child: _buildAmountChip(
                      'الإجمالي',
                      '${invoice.totalAmount.toStringAsFixed(2)} ر.س',
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingSmall),
                  Expanded(
                    child: _buildAmountChip(
                      'المدفوع',
                      '${invoice.paidAmount.toStringAsFixed(2)} ر.س',
                      Colors.orange,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.posted:
        return Colors.blue.shade300;
      case InvoiceStatus.sent:
        return Colors.blue;
      case InvoiceStatus.partiallyPaid:
        return Colors.orange;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.red.shade300;
    }
  }

  String _getStatusText(InvoiceStatus status) {
    return status.nameAr;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
