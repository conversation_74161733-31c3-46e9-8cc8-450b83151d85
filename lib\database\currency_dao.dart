import '../models/currency.dart';
import '../utils/result.dart';
import 'database_helper.dart';
import 'database_schema.dart';

/// DAO لإدارة العملات
class CurrencyDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إدراج عملة جديدة
  Future<Result<int>> insertCurrency(Currency currency) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        DatabaseSchema.tableCurrencies,
        currency.toMap()..remove('id'),
      );
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إدراج العملة: ${e.toString()}');
    }
  }

  /// تحديث عملة موجودة
  Future<Result<bool>> updateCurrency(Currency currency) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.update(
        DatabaseSchema.tableCurrencies,
        currency.toMap(),
        where: 'id = ?',
        whereArgs: [currency.id],
      );
      return Result.success(count > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث العملة: ${e.toString()}');
    }
  }

  /// حذف عملة
  Future<Result<bool>> deleteCurrency(int id) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.delete(
        DatabaseSchema.tableCurrencies,
        where: 'id = ?',
        whereArgs: [id],
      );
      return Result.success(count > 0);
    } catch (e) {
      return Result.error('خطأ في حذف العملة: ${e.toString()}');
    }
  }

  /// الحصول على عملة بالمعرف
  Future<Currency?> getCurrencyById(int id) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableCurrencies,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Currency.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على عملة بالرمز
  Future<Currency?> getCurrencyByCode(String code) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableCurrencies,
        where: 'code = ?',
        whereArgs: [code],
      );

      if (maps.isNotEmpty) {
        return Currency.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع العملات
  Future<List<Currency>> getAllCurrencies() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableCurrencies,
        orderBy: 'is_base_currency DESC, name_ar ASC',
      );

      return maps.map((map) => Currency.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على العملات النشطة
  Future<List<Currency>> getActiveCurrencies() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableCurrencies,
        where: 'is_active = ?',
        whereArgs: [1],
        orderBy: 'is_base_currency DESC, name_ar ASC',
      );

      return maps.map((map) => Currency.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على العملة الأساسية
  Future<Currency?> getBaseCurrency() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableCurrencies,
        where: 'is_base_currency = ? AND is_active = ?',
        whereArgs: [1, 1],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return Currency.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// تعيين عملة كعملة أساسية
  Future<Result<bool>> setBaseCurrency(String currencyCode) async {
    try {
      final db = await _dbHelper.database;
      
      // إزالة العلامة الأساسية من جميع العملات
      await db.update(
        DatabaseSchema.tableCurrencies,
        {'is_base_currency': 0, 'updated_at': DateTime.now().toIso8601String()},
      );

      // تعيين العملة الجديدة كأساسية
      final count = await db.update(
        DatabaseSchema.tableCurrencies,
        {'is_base_currency': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'code = ?',
        whereArgs: [currencyCode],
      );

      return Result.success(count > 0);
    } catch (e) {
      return Result.error('خطأ في تعيين العملة الأساسية: ${e.toString()}');
    }
  }

  /// البحث في العملات
  Future<List<Currency>> searchCurrencies(String query) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableCurrencies,
        where: 'name_ar LIKE ? OR name_en LIKE ? OR code LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'is_base_currency DESC, name_ar ASC',
      );

      return maps.map((map) => Currency.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// تفعيل/إلغاء تفعيل عملة
  Future<Result<bool>> toggleCurrencyStatus(int id, bool isActive) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.update(
        DatabaseSchema.tableCurrencies,
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
      return Result.success(count > 0);
    } catch (e) {
      return Result.error('خطأ في تغيير حالة العملة: ${e.toString()}');
    }
  }

  /// التحقق من وجود عملة بالرمز
  Future<bool> currencyExists(String code) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableCurrencies,
        where: 'code = ?',
        whereArgs: [code],
        limit: 1,
      );
      return maps.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// إدراج العملات الافتراضية
  Future<Result<bool>> insertDefaultCurrencies() async {
    try {
      final db = await _dbHelper.database;
      
      for (Currency currency in Currency.predefinedCurrencies) {
        // التحقق من عدم وجود العملة مسبقاً
        bool exists = await currencyExists(currency.code);
        if (!exists) {
          await db.insert(
            DatabaseSchema.tableCurrencies,
            currency.toMap()..remove('id'),
          );
        }
      }
      
      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إدراج العملات الافتراضية: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات العملات
  Future<Map<String, int>> getCurrencyStats() async {
    try {
      final db = await _dbHelper.database;
      
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${DatabaseSchema.tableCurrencies}',
      );
      
      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as active FROM ${DatabaseSchema.tableCurrencies} WHERE is_active = 1',
      );
      
      final baseResult = await db.rawQuery(
        'SELECT COUNT(*) as base FROM ${DatabaseSchema.tableCurrencies} WHERE is_base_currency = 1',
      );

      return {
        'total': (totalResult.first['total'] as int?) ?? 0,
        'active': (activeResult.first['active'] as int?) ?? 0,
        'base': (baseResult.first['base'] as int?) ?? 0,
      };
    } catch (e) {
      return {'total': 0, 'active': 0, 'base': 0};
    }
  }
}
