import '../models/invoice.dart';
import '../database/invoice_dao.dart';

/// نتيجة العملية
class Result<T> {
  final bool isSuccess;
  final T? data;
  final String? error;

  Result.success(this.data) : isSuccess = true, error = null;
  Result.error(this.error) : isSuccess = false, data = null;
}

/// خدمة إدارة الفواتير
class InvoiceService {
  final InvoiceDao _invoiceDao = InvoiceDao();

  /// الحصول على جميع الفواتير
  Future<Result<List<Invoice>>> getAllInvoices() async {
    try {
      final invoices = await _invoiceDao.getAllInvoices();
      return Result.success(invoices);
    } catch (e) {
      return Result.error('خطأ في جلب الفواتير: ${e.toString()}');
    }
  }

  /// الحصول على فاتورة بالمعرف
  Future<Result<Invoice>> getInvoiceById(int id) async {
    try {
      final invoice = await _invoiceDao.getInvoiceById(id);
      if (invoice != null) {
        return Result.success(invoice);
      } else {
        return Result.error('الفاتورة غير موجودة');
      }
    } catch (e) {
      return Result.error('خطأ في جلب الفاتورة: ${e.toString()}');
    }
  }

  /// الحصول على فواتير العميل
  Future<Result<List<Invoice>>> getInvoicesByCustomer(int customerId) async {
    try {
      final invoices = await _invoiceDao.getInvoicesByCustomer(customerId);
      return Result.success(invoices);
    } catch (e) {
      return Result.error('خطأ في جلب فواتير العميل: ${e.toString()}');
    }
  }

  /// الحصول على فواتير المورد
  Future<Result<List<Invoice>>> getInvoicesBySupplier(int supplierId) async {
    try {
      final invoices = await _invoiceDao.getInvoicesBySupplier(supplierId);
      return Result.success(invoices);
    } catch (e) {
      return Result.error('خطأ في جلب فواتير المورد: ${e.toString()}');
    }
  }

  /// إنشاء فاتورة جديدة
  Future<Result<Invoice>> createInvoice(Invoice invoice) async {
    try {
      final id = await _invoiceDao.insertInvoice(invoice);
      final newInvoice = invoice.copyWith(id: id);
      return Result.success(newInvoice);
    } catch (e) {
      return Result.error('خطأ في إنشاء الفاتورة: ${e.toString()}');
    }
  }

  /// تحديث فاتورة
  Future<Result<Invoice>> updateInvoice(Invoice invoice) async {
    try {
      if (invoice.id == null) {
        return Result.error('معرف الفاتورة مطلوب للتحديث');
      }

      await _invoiceDao.updateInvoice(invoice);
      return Result.success(invoice);
    } catch (e) {
      return Result.error('خطأ في تحديث الفاتورة: ${e.toString()}');
    }
  }

  /// حذف فاتورة
  Future<Result<void>> deleteInvoice(int id) async {
    try {
      await _invoiceDao.deleteInvoice(id);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف الفاتورة: ${e.toString()}');
    }
  }

  /// البحث في الفواتير
  Future<Result<List<Invoice>>> searchInvoices(String query) async {
    try {
      final invoices = await _invoiceDao.searchInvoices(query);
      return Result.success(invoices);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  /// الحصول على الفواتير حسب الحالة
  Future<Result<List<Invoice>>> getInvoicesByStatus(
    InvoiceStatus status,
  ) async {
    try {
      final invoices = await _invoiceDao.getInvoicesByStatus(status);
      return Result.success(invoices);
    } catch (e) {
      return Result.error('خطأ في جلب الفواتير: ${e.toString()}');
    }
  }

  /// الحصول على الفواتير المتأخرة
  Future<Result<List<Invoice>>> getOverdueInvoices() async {
    try {
      final invoices = await _invoiceDao.getOverdueInvoices();
      return Result.success(invoices);
    } catch (e) {
      return Result.error('خطأ في جلب الفواتير المتأخرة: ${e.toString()}');
    }
  }

  /// الحصول على رقم الفاتورة التالي
  Future<Result<String>> getNextInvoiceNumber(InvoiceType type) async {
    try {
      final number = await _invoiceDao.getNextInvoiceNumber(type);
      return Result.success(number);
    } catch (e) {
      return Result.error('خطأ في توليد رقم الفاتورة: ${e.toString()}');
    }
  }

  /// تحديث حالة الفاتورة
  Future<Result<void>> updateInvoiceStatus(
    int invoiceId,
    InvoiceStatus status,
  ) async {
    try {
      await _invoiceDao.updateInvoiceStatus(invoiceId, status);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث حالة الفاتورة: ${e.toString()}');
    }
  }

  /// تسجيل دفعة للفاتورة
  Future<Result<void>> recordPayment(int invoiceId, double amount) async {
    try {
      await _invoiceDao.recordPayment(invoiceId, amount);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تسجيل الدفعة: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات الفواتير
  Future<Result<Map<String, dynamic>>> getInvoiceStatistics() async {
    try {
      final stats = await _invoiceDao.getInvoiceStatistics();
      return Result.success(stats);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات الفواتير: ${e.toString()}');
    }
  }

  /// الحصول على إجمالي المبيعات
  Future<Result<double>> getTotalSales({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final total = await _invoiceDao.getTotalSales(
        startDate: startDate,
        endDate: endDate,
      );
      return Result.success(total);
    } catch (e) {
      return Result.error('خطأ في جلب إجمالي المبيعات: ${e.toString()}');
    }
  }

  /// الحصول على إجمالي المبالغ المستحقة
  Future<Result<double>> getTotalOutstanding() async {
    try {
      final total = await _invoiceDao.getTotalOutstanding();
      return Result.success(total);
    } catch (e) {
      return Result.error('خطأ في جلب المبالغ المستحقة: ${e.toString()}');
    }
  }

  /// الحصول على عدد الفواتير
  Future<Result<int>> getInvoicesCount() async {
    try {
      final count = await _invoiceDao.getInvoicesCount();
      return Result.success(count);
    } catch (e) {
      return Result.error('خطأ في جلب عدد الفواتير: ${e.toString()}');
    }
  }

  /// الحصول على الفواتير في فترة زمنية
  Future<Result<List<Invoice>>> getInvoicesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final invoices = await _invoiceDao.getInvoicesByDateRange(
        startDate,
        endDate,
      );
      return Result.success(invoices);
    } catch (e) {
      return Result.error('خطأ في جلب الفواتير: ${e.toString()}');
    }
  }

  /// إلغاء فاتورة
  Future<Result<void>> cancelInvoice(int invoiceId, String reason) async {
    try {
      await _invoiceDao.cancelInvoice(invoiceId);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في إلغاء الفاتورة: ${e.toString()}');
    }
  }

  /// تحويل فاتورة إلى PDF
  Future<Result<String>> generateInvoicePdf(int invoiceId) async {
    try {
      final invoice = await _invoiceDao.getInvoiceById(invoiceId);
      if (invoice == null) {
        return Result.error('الفاتورة غير موجودة');
      }

      // هنا يمكن إضافة منطق توليد PDF
      // للآن سنعيد مسار وهمي
      final pdfPath = 'invoices/invoice_${invoice.number}.pdf';
      return Result.success(pdfPath);
    } catch (e) {
      return Result.error('خطأ في توليد PDF: ${e.toString()}');
    }
  }

  /// إرسال فاتورة بالبريد الإلكتروني
  Future<Result<void>> sendInvoiceByEmail(int invoiceId, String email) async {
    try {
      final invoice = await _invoiceDao.getInvoiceById(invoiceId);
      if (invoice == null) {
        return Result.error('الفاتورة غير موجودة');
      }

      // هنا يمكن إضافة منطق إرسال البريد الإلكتروني
      // للآن سنعتبر أن الإرسال تم بنجاح
      await _invoiceDao.updateInvoiceStatus(invoiceId, InvoiceStatus.sent);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في إرسال الفاتورة: ${e.toString()}');
    }
  }

  /// نسخ فاتورة
  Future<Result<Invoice>> duplicateInvoice(int invoiceId) async {
    try {
      final originalInvoice = await _invoiceDao.getInvoiceById(invoiceId);
      if (originalInvoice == null) {
        return Result.error('الفاتورة الأصلية غير موجودة');
      }

      final newNumber = await _invoiceDao.getNextInvoiceNumber(
        originalInvoice.invoiceType,
      );
      final duplicatedInvoice = originalInvoice.copyWith(
        id: null,
        invoiceNumber: newNumber,
        date: DateTime.now(),
        status: InvoiceStatus.draft,
        paidAmount: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final id = await _invoiceDao.insertInvoice(duplicatedInvoice);
      final newInvoice = duplicatedInvoice.copyWith(id: id);
      return Result.success(newInvoice);
    } catch (e) {
      return Result.error('خطأ في نسخ الفاتورة: ${e.toString()}');
    }
  }
}
