import 'package:flutter/material.dart';
import '../widgets/quantum_button.dart';

/// مربع حوار لعرض رسائل الخطأ
class ErrorDialog extends StatelessWidget {
  final String message;
  final String? title;
  final VoidCallback? onConfirm;

  const ErrorDialog({
    super.key,
    required this.message,
    this.title,
    this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        title ?? 'خطأ',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: Theme.of(context).colorScheme.error,
        ),
      ),
      content: Text(
        message,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      actions: [
        QuantumButton(
          text: 'موافق',
          onPressed: () {
            Navigator.of(context).pop();
            onConfirm?.call();
          },
          variant: QuantumButtonVariant.primary,
        ),
      ],
    );
  }
}

/// دالة مساعدة لعرض رسالة خطأ
void showErrorDialog(
  BuildContext context,
  String message, {
  String? title,
  VoidCallback? onConfirm,
}) {
  showDialog(
    context: context,
    builder: (context) => ErrorDialog(
      message: message,
      title: title,
      onConfirm: onConfirm,
    ),
  );
}
