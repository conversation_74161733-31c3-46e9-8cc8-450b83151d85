import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🎤 نظام التحكم الصوتي المستقبلي
/// Futuristic Voice Control System
///
/// هذا الملف يحتوي على نظام تحكم صوتي بالذكاء الاصطناعي لا مثيل له في التاريخ
/// This file contains unprecedented AI voice control system in history

/// 🌟 لوحة التحكم الصوتي الذكي
/// Smart Voice Control Dashboard
class VoiceControlDashboard extends StatefulWidget {
  const VoiceControlDashboard({super.key});

  @override
  State<VoiceControlDashboard> createState() => _VoiceControlDashboardState();
}

class _VoiceControlDashboardState extends State<VoiceControlDashboard>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _pulseController;
  late AnimationController _listeningController;
  late Animation<double> _waveAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _listeningAnimation;

  bool _isListening = false;
  bool _isProcessing = false;
  String _lastCommand = '';
  String _response = '';

  @override
  void initState() {
    super.initState();

    _waveController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _listeningController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _listeningAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _listeningController, curve: Curves.easeInOut),
    );

    _waveController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _waveController.dispose();
    _pulseController.dispose();
    _listeningController.dispose();
    super.dispose();
  }

  void _toggleListening() {
    setState(() {
      _isListening = !_isListening;
      if (_isListening) {
        _listeningController.repeat(reverse: true);
        _simulateVoiceCommand();
      } else {
        _listeningController.stop();
      }
    });
  }

  void _simulateVoiceCommand() {
    Future.delayed(const Duration(seconds: 3), () {
      if (_isListening) {
        setState(() {
          _isProcessing = true;
          _lastCommand = 'عرض التقرير المالي للشهر الحالي';
        });

        Future.delayed(const Duration(seconds: 2), () {
          setState(() {
            _isProcessing = false;
            _response =
                'تم عرض التقرير المالي بنجاح. إجمالي الإيرادات: 125,000 ر.س';
            _isListening = false;
            _listeningController.stop();
          });
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _waveAnimation,
        _pulseAnimation,
        _listeningAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: _isListening ? 3.0 : 1.5,
          child: QuantumEnergyEffect(
            intensity: _isListening ? 2.5 : 1.0 + (_pulseAnimation.value * 0.5),
            primaryColor: const Color(0xFF8BC34A),
            secondaryColor: const Color(0xFF9CCC65),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF33691E).withValues(alpha: 0.9),
                    const Color(0xFF558B2F).withValues(alpha: 0.8),
                    const Color(0xFF689F38).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(
                    0xFF8BC34A,
                  ).withValues(alpha: _isListening ? 0.8 : 0.5),
                  width: _isListening ? 4 : 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(
                      0xFF8BC34A,
                    ).withValues(alpha: _isListening ? 0.6 : 0.3),
                    blurRadius: _isListening ? 40 : 25,
                    offset: const Offset(0, 15),
                    spreadRadius: _isListening ? 8 : 3,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس التحكم الصوتي
                  Row(
                    children: [
                      Transform.scale(
                        scale: _isListening
                            ? _listeningAnimation.value * 0.3 + 1.0
                            : _pulseAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(18),
                          decoration: BoxDecoration(
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFF8BC34A).withValues(alpha: 0.9),
                                const Color(0xFF9CCC65).withValues(alpha: 0.6),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.5),
                              width: 3,
                            ),
                          ),
                          child: Icon(
                            _isListening
                                ? Icons.mic_rounded
                                : Icons.mic_none_rounded,
                            color: Colors.white,
                            size: 36,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🎤 التحكم الصوتي',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              _isListening
                                  ? 'أستمع إليك...'
                                  : _isProcessing
                                  ? 'معالجة الأمر...'
                                  : 'اضغط للتحدث',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      // زر التحكم
                      GestureDetector(
                        onTap: _toggleListening,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _isListening
                                ? const Color(0xFFF44336).withValues(alpha: 0.8)
                                : const Color(
                                    0xFF8BC34A,
                                  ).withValues(alpha: 0.8),
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.4),
                            ),
                          ),
                          child: Icon(
                            _isListening
                                ? Icons.stop_rounded
                                : Icons.play_arrow_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // عرض الموجات الصوتية
                  _buildVoiceWaveVisualization(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // عرض الأوامر والاستجابات
                  _buildCommandResponse(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء تصور الموجات الصوتية
  Widget _buildVoiceWaveVisualization() {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF8BC34A).withValues(alpha: 0.3),
        ),
      ),
      child: Stack(
        children: [
          // الخلفية الصوتية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF8BC34A).withValues(alpha: 0.1),
                  const Color(0xFF9CCC65).withValues(alpha: 0.05),
                  Colors.transparent,
                ],
              ),
              borderRadius: BorderRadius.circular(20),
            ),
          ),

          // الموجات الصوتية
          Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(20, (index) {
                final height = _isListening
                    ? 20.0 +
                          (math.sin(
                                (_waveAnimation.value * 4 * math.pi) +
                                    (index * 0.5),
                              ) *
                              25)
                    : 5.0 +
                          (math.sin(
                                (_waveAnimation.value * 2 * math.pi) +
                                    (index * 0.3),
                              ) *
                              10);

                return Container(
                  width: 3,
                  height: height.abs(),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(
                          0xFF8BC34A,
                        ).withValues(alpha: _isListening ? 0.8 : 0.4),
                        const Color(
                          0xFF9CCC65,
                        ).withValues(alpha: _isListening ? 0.6 : 0.2),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                );
              }),
            ),
          ),

          // مؤشر الحالة
          Positioned(
            top: 10,
            right: 15,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _isListening
                    ? const Color(0xFFF44336).withValues(alpha: 0.8)
                    : _isProcessing
                    ? const Color(0xFFFF9800).withValues(alpha: 0.8)
                    : const Color(0xFF8BC34A).withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                _isListening
                    ? 'مستمع'
                    : _isProcessing
                    ? 'معالجة'
                    : 'جاهز',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض الأوامر والاستجابات
  Widget _buildCommandResponse() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '💬 الأوامر والاستجابات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        if (_lastCommand.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF2196F3).withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.person_rounded,
                      color: Color(0xFF2196F3),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'الأمر الصوتي:',
                      style: TextStyle(
                        color: Color(0xFF2196F3),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _lastCommand,
                  style: const TextStyle(color: Colors.white, fontSize: 13),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
        ],

        if (_response.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF8BC34A).withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.smart_toy_rounded,
                      color: Color(0xFF8BC34A),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'استجابة النظام:',
                      style: TextStyle(
                        color: Color(0xFF8BC34A),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _response,
                  style: const TextStyle(color: Colors.white, fontSize: 13),
                ),
              ],
            ),
          ),
        ],

        if (_lastCommand.isEmpty && _response.isEmpty) ...[
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingLarge),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.mic_rounded,
                    color: Colors.white.withValues(alpha: 0.5),
                    size: 32,
                  ),
                  const SizedBox(height: AppTheme.spacingSmall),
                  Text(
                    'اضغط على زر التحكم وقل أمرك',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppTheme.spacingSmall),
                  Text(
                    'مثال: "عرض التقرير المالي" أو "إضافة عملية جديدة"',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.5),
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// 🎯 بطاقة الأوامر الصوتية السريعة
/// Quick Voice Commands Card
class QuickVoiceCommandsCard extends StatefulWidget {
  const QuickVoiceCommandsCard({super.key});

  @override
  State<QuickVoiceCommandsCard> createState() => _QuickVoiceCommandsCardState();
}

class _QuickVoiceCommandsCardState extends State<QuickVoiceCommandsCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  final List<VoiceCommand> _commands = [
    VoiceCommand(
      title: 'عرض الملخص المالي',
      description: 'يعرض ملخص شامل للوضع المالي الحالي',
      icon: Icons.assessment_rounded,
      color: const Color(0xFF2196F3),
    ),
    VoiceCommand(
      title: 'إضافة عملية جديدة',
      description: 'يفتح نموذج إضافة عملية مالية جديدة',
      icon: Icons.add_circle_rounded,
      color: const Color(0xFF4CAF50),
    ),
    VoiceCommand(
      title: 'البحث في السجلات',
      description: 'يبحث في جميع السجلات المالية',
      icon: Icons.search_rounded,
      color: const Color(0xFFFF9800),
    ),
    VoiceCommand(
      title: 'تصدير التقارير',
      description: 'يصدر التقارير المالية بصيغة PDF',
      icon: Icons.file_download_rounded,
      color: const Color(0xFF9C27B0),
    ),
  ];

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return HologramEffect(
          intensity: 1.0 + (_animation.value * 0.5),
          child: Container(
            margin: const EdgeInsets.all(AppTheme.spacingMedium),
            padding: const EdgeInsets.all(AppTheme.spacingLarge),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF1A1A2E).withValues(alpha: 0.9),
                  const Color(0xFF16213E).withValues(alpha: 0.8),
                  const Color(0xFF0F3460).withValues(alpha: 0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2196F3).withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الأوامر السريعة
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF2196F3), Color(0xFF03A9F4)],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Icon(
                        Icons.record_voice_over_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Text(
                      '⚡ الأوامر السريعة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.spacingLarge),

                // قائمة الأوامر
                ...List.generate(_commands.length, (index) {
                  return _buildCommandItem(_commands[index], index);
                }),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommandItem(VoiceCommand command, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: command.color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: command.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(command.icon, color: command.color, size: 20),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  command.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXSmall),
                Text(
                  command.description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.mic_rounded,
            color: command.color.withValues(alpha: 0.6),
            size: 16,
          ),
        ],
      ),
    );
  }
}

/// نموذج الأمر الصوتي
class VoiceCommand {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  VoiceCommand({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
