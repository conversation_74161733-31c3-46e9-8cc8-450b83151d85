/// شاشة التقارير الضريبية
/// Tax Reports Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/tax_calculation.dart';
import '../../services/tax_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';

class TaxReportsScreen extends StatefulWidget {
  const TaxReportsScreen({super.key});

  @override
  State<TaxReportsScreen> createState() => _TaxReportsScreenState();
}

class _TaxReportsScreenState extends State<TaxReportsScreen>
    with TickerProviderStateMixin {
  final TaxService _taxService = TaxService();

  late TabController _tabController;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  bool _isLoading = false;

  TaxReport? _vatReport;
  TaxReport? _withholdingReport;
  Map<String, double>? _taxSummary;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReports() async {
    setState(() => _isLoading = true);

    try {
      // تحميل تقرير ضريبة القيمة المضافة
      final vatResult = await _taxService.generateVATReport(
        _startDate,
        _endDate,
      );
      if (vatResult.isSuccess) {
        _vatReport = vatResult.data;
      }

      // تحميل تقرير ضريبة الاستقطاع
      final withholdingResult = await _taxService.generateWithholdingTaxReport(
        _startDate,
        _endDate,
      );
      if (withholdingResult.isSuccess) {
        _withholdingReport = withholdingResult.data;
      }

      // تحميل ملخص الضرائب
      final summaryResult = await _taxService.getTaxSummaryForPeriod(
        _startDate,
        _endDate,
      );
      if (summaryResult.isSuccess) {
        _taxSummary = summaryResult.data;
      }

      setState(() {});
    } catch (e) {
      _showError('خطأ في تحميل التقارير: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadReports();
    }
  }

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isLoading,
      child: Column(
        children: [
          // شريط التحكم في الفترة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'الفترة: ${_startDate.toString().split(' ')[0]} - ${_endDate.toString().split(' ')[0]}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                QuantumButton(
                  onPressed: _selectDateRange,
                  text: 'تغيير الفترة',
                  icon: Icons.date_range,
                  variant: QuantumButtonVariant.outline,
                ),
                const SizedBox(width: 8),
                QuantumButton(
                  onPressed: _loadReports,
                  text: 'تحديث',
                  icon: Icons.refresh,
                ),
              ],
            ),
          ),

          // التبويبات
          TabBar(
            controller: _tabController,
            labelColor: Theme.of(context).primaryColor,
            unselectedLabelColor: Colors.grey,
            indicatorColor: Theme.of(context).primaryColor,
            tabs: const [
              Tab(text: 'ضريبة القيمة المضافة', icon: Icon(Icons.receipt)),
              Tab(text: 'ضريبة الاستقطاع', icon: Icon(Icons.remove_circle)),
              Tab(text: 'ملخص الضرائب', icon: Icon(Icons.analytics)),
            ],
          ),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildVATReportTab(),
                _buildWithholdingReportTab(),
                _buildTaxSummaryTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVATReportTab() {
    if (_vatReport == null) {
      return const Center(
        child: Text('لا توجد بيانات لضريبة القيمة المضافة في هذه الفترة'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // ملخص ضريبة القيمة المضافة
          QuantumCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص ضريبة القيمة المضافة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildSummaryRow(
                    'ضريبة المبيعات',
                    _vatReport!.totalTaxCollected,
                  ),
                  _buildSummaryRow('ضريبة المشتريات', _vatReport!.totalTaxPaid),
                  const Divider(),
                  _buildSummaryRow(
                    'صافي ضريبة القيمة المضافة',
                    _vatReport!.netTaxDue,
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // تفاصيل ضريبة المبيعات
          if (_vatReport!.salesTaxCalculations.isNotEmpty) ...[
            QuantumCard(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل ضريبة المبيعات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ..._vatReport!.salesTaxCalculations.map(
                      (calc) => _buildCalculationTile(calc),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // تفاصيل ضريبة المشتريات
          if (_vatReport!.purchaseTaxCalculations.isNotEmpty) ...[
            QuantumCard(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل ضريبة المشتريات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ..._vatReport!.purchaseTaxCalculations.map(
                      (calc) => _buildCalculationTile(calc),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildWithholdingReportTab() {
    if (_withholdingReport == null) {
      return const Center(
        child: Text('لا توجد بيانات لضريبة الاستقطاع في هذه الفترة'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // ملخص ضريبة الاستقطاع
          QuantumCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص ضريبة الاستقطاع',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildSummaryRow(
                    'إجمالي ضريبة الاستقطاع',
                    _withholdingReport!.totalTaxPaid,
                    isTotal: true,
                  ),
                  _buildSummaryRow(
                    'عدد المعاملات',
                    _withholdingReport!.calculations.length.toDouble(),
                    isCount: true,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // تفاصيل ضريبة الاستقطاع
          if (_withholdingReport!.calculations.isNotEmpty) ...[
            QuantumCard(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل ضريبة الاستقطاع',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ..._withholdingReport!.calculations.map(
                      (calc) => _buildCalculationTile(calc),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTaxSummaryTab() {
    if (_taxSummary == null || _taxSummary!.isEmpty) {
      return const Center(child: Text('لا توجد بيانات ضريبية في هذه الفترة'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          QuantumCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص جميع الضرائب',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ..._taxSummary!.entries.map(
                    (entry) => _buildSummaryRow(entry.key, entry.value),
                  ),
                  const Divider(),
                  _buildSummaryRow(
                    'إجمالي الضرائب',
                    _taxSummary!.values.fold(0.0, (sum, value) => sum + value),
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    double amount, {
    bool isTotal = false,
    bool isCount = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            isCount
                ? amount.toInt().toString()
                : '${amount.toStringAsFixed(2)} ريال',
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationTile(TaxCalculation calculation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text('${calculation.documentType} #${calculation.documentId}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المبلغ الأساسي: ${calculation.baseAmount.toStringAsFixed(2)} ريال',
            ),
            Text('معدل الضريبة: ${calculation.taxRate}%'),
            Text(
              'تاريخ الحساب: ${calculation.calculatedAt.toString().split(' ')[0]}',
            ),
          ],
        ),
        trailing: Text(
          '${calculation.taxAmount.toStringAsFixed(2)} ريال',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        isThreeLine: true,
      ),
    );
  }
}
