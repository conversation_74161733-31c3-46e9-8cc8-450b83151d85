/// Data Access Object لمواقع المخازن
/// Warehouse Location DAO for Smart Ledger
library;

import 'dart:developer' as developer;
import '../models/warehouse.dart';
import 'database_Helper.dart';

class WarehouseLocationDao {
  final DatabaseHelper _databaseService = DatabaseHelper();

  /// الحصول على جميع مواقع المخازن
  Future<List<WarehouseLocation>> getAllLocations() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouse_locations',
      orderBy: 'warehouse_id ASC, code ASC',
    );

    return List.generate(maps.length, (i) {
      return WarehouseLocation.fromMap(maps[i]);
    });
  }

  /// الحصول على مواقع مخزن معين
  Future<List<WarehouseLocation>> getLocationsByWarehouse(
    int warehouseId,
  ) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouse_locations',
      where: 'warehouse_id = ? AND is_active = ?',
      whereArgs: [warehouseId, 1],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return WarehouseLocation.fromMap(maps[i]);
    });
  }

  /// الحصول على موقع بالمعرف
  Future<WarehouseLocation?> getLocationById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouse_locations',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return WarehouseLocation.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على موقع بالكود
  Future<WarehouseLocation?> getLocationByCode(
    String code,
    int warehouseId,
  ) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouse_locations',
      where: 'code = ? AND warehouse_id = ?',
      whereArgs: [code, warehouseId],
    );

    if (maps.isNotEmpty) {
      return WarehouseLocation.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة موقع جديد
  Future<int> insertLocation(WarehouseLocation location) async {
    final db = await _databaseService.database;
    return await db.insert('warehouse_locations', location.toMap());
  }

  /// تحديث موقع
  Future<int> updateLocation(WarehouseLocation location) async {
    final db = await _databaseService.database;
    return await db.update(
      'warehouse_locations',
      location.toMap(),
      where: 'id = ?',
      whereArgs: [location.id],
    );
  }

  /// حذف موقع
  Future<int> deleteLocation(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'warehouse_locations',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// البحث في المواقع
  Future<List<WarehouseLocation>> searchLocations(
    String searchTerm, {
    int? warehouseId,
  }) async {
    final db = await _databaseService.database;

    String whereClause = 'code LIKE ? OR name LIKE ? OR description LIKE ?';
    List<dynamic> whereArgs = [
      '%$searchTerm%',
      '%$searchTerm%',
      '%$searchTerm%',
    ];

    if (warehouseId != null) {
      whereClause += ' AND warehouse_id = ?';
      whereArgs.add(warehouseId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'warehouse_locations',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'warehouse_id ASC, code ASC',
    );

    return List.generate(maps.length, (i) {
      return WarehouseLocation.fromMap(maps[i]);
    });
  }

  /// الحصول على المواقع حسب المنطقة
  Future<List<WarehouseLocation>> getLocationsByZone(
    int warehouseId,
    String zone,
  ) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouse_locations',
      where: 'warehouse_id = ? AND zone = ? AND is_active = ?',
      whereArgs: [warehouseId, zone, 1],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return WarehouseLocation.fromMap(maps[i]);
    });
  }

  /// الحصول على جميع المناطق في مخزن
  Future<List<String>> getZonesByWarehouse(int warehouseId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT DISTINCT zone 
      FROM warehouse_locations 
      WHERE warehouse_id = ? AND zone IS NOT NULL AND zone != '' AND is_active = 1
      ORDER BY zone ASC
    ''',
      [warehouseId],
    );

    return maps.map((map) => map['zone'] as String).toList();
  }

  /// الحصول على الممرات في منطقة
  Future<List<String>> getAislesByZone(int warehouseId, String zone) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT DISTINCT aisle 
      FROM warehouse_locations 
      WHERE warehouse_id = ? AND zone = ? AND aisle IS NOT NULL AND aisle != '' AND is_active = 1
      ORDER BY aisle ASC
    ''',
      [warehouseId, zone],
    );

    return maps.map((map) => map['aisle'] as String).toList();
  }

  /// الحصول على الأرفف في ممر
  Future<List<String>> getRacksByAisle(
    int warehouseId,
    String zone,
    String aisle,
  ) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT DISTINCT rack 
      FROM warehouse_locations 
      WHERE warehouse_id = ? AND zone = ? AND aisle = ? AND rack IS NOT NULL AND rack != '' AND is_active = 1
      ORDER BY rack ASC
    ''',
      [warehouseId, zone, aisle],
    );

    return maps.map((map) => map['rack'] as String).toList();
  }

  /// الحصول على إحصائيات المواقع
  Future<Map<String, dynamic>> getLocationStatistics(int warehouseId) async {
    final db = await _databaseService.database;

    // إجمالي عدد المواقع
    final totalResult = await db.rawQuery(
      'SELECT COUNT(*) as total FROM warehouse_locations WHERE warehouse_id = ?',
      [warehouseId],
    );
    final total = totalResult.first['total'] as int;

    // المواقع النشطة
    final activeResult = await db.rawQuery(
      'SELECT COUNT(*) as active FROM warehouse_locations WHERE warehouse_id = ? AND is_active = ?',
      [warehouseId, 1],
    );
    final active = activeResult.first['active'] as int;

    // المواقع المستخدمة (التي تحتوي على مخزون)
    final usedResult = await db.rawQuery(
      '''
      SELECT COUNT(DISTINCT wl.id) as used 
      FROM warehouse_locations wl
      INNER JOIN stock_balances sb ON wl.id = sb.location_id
      WHERE wl.warehouse_id = ? AND sb.quantity > 0
    ''',
      [warehouseId],
    );
    final used = usedResult.first['used'] as int;

    // عدد المناطق
    final zonesResult = await db.rawQuery(
      '''
      SELECT COUNT(DISTINCT zone) as zones 
      FROM warehouse_locations 
      WHERE warehouse_id = ? AND zone IS NOT NULL AND zone != '' AND is_active = 1
    ''',
      [warehouseId],
    );
    final zones = zonesResult.first['zones'] as int;

    return {
      'total': total,
      'active': active,
      'used': used,
      'available': active - used,
      'zones': zones,
    };
  }

  /// التحقق من وجود كود موقع في مخزن
  Future<bool> isLocationCodeExists(
    String code,
    int warehouseId, {
    int? excludeId,
  }) async {
    final db = await _databaseService.database;
    String whereClause = 'code = ? AND warehouse_id = ?';
    List<dynamic> whereArgs = [code, warehouseId];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'warehouse_locations',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }

  /// الحصول على المواقع مع تفاصيل المخزون
  Future<List<Map<String, dynamic>>> getLocationsWithStock(
    int warehouseId,
  ) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT wl.*, 
             COALESCE(s.item_count, 0) as item_count,
             COALESCE(s.total_quantity, 0) as total_quantity,
             COALESCE(s.total_value, 0) as total_value
      FROM warehouse_locations wl
      LEFT JOIN (
        SELECT location_id, 
               COUNT(DISTINCT item_id) as item_count,
               SUM(quantity) as total_quantity,
               SUM(total_value) as total_value
        FROM stock_balances
        WHERE quantity > 0
        GROUP BY location_id
      ) s ON wl.id = s.location_id
      WHERE wl.warehouse_id = ? AND wl.is_active = 1
      ORDER BY wl.code ASC
    ''',
      [warehouseId],
    );

    return maps;
  }

  /// تحديث حالة موقع
  Future<int> updateLocationStatus(int id, bool isActive) async {
    final db = await _databaseService.database;
    return await db.update(
      'warehouse_locations',
      {
        'is_active': isActive ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على المواقع الفارغة
  Future<List<WarehouseLocation>> getEmptyLocations(int warehouseId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT wl.* 
      FROM warehouse_locations wl
      LEFT JOIN stock_balances sb ON wl.id = sb.location_id AND sb.quantity > 0
      WHERE wl.warehouse_id = ? AND wl.is_active = 1 AND sb.id IS NULL
      ORDER BY wl.code ASC
    ''',
      [warehouseId],
    );

    return List.generate(maps.length, (i) {
      return WarehouseLocation.fromMap(maps[i]);
    });
  }

  /// الحصول على المواقع المشغولة
  Future<List<WarehouseLocation>> getOccupiedLocations(int warehouseId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT DISTINCT wl.* 
      FROM warehouse_locations wl
      INNER JOIN stock_balances sb ON wl.id = sb.location_id
      WHERE wl.warehouse_id = ? AND wl.is_active = 1 AND sb.quantity > 0
      ORDER BY wl.code ASC
    ''',
      [warehouseId],
    );

    return List.generate(maps.length, (i) {
      return WarehouseLocation.fromMap(maps[i]);
    });
  }

  /// نسخ احتياطي للمواقع
  Future<List<Map<String, dynamic>>> exportLocations(int warehouseId) async {
    final db = await _databaseService.database;
    return await db.query(
      'warehouse_locations',
      where: 'warehouse_id = ?',
      whereArgs: [warehouseId],
      orderBy: 'code ASC',
    );
  }

  /// استيراد المواقع
  Future<int> importLocations(List<Map<String, dynamic>> locations) async {
    final db = await _databaseService.database;
    int importedCount = 0;

    await db.transaction((txn) async {
      for (final locationData in locations) {
        try {
          await txn.insert('warehouse_locations', locationData);
          importedCount++;
        } catch (e) {
          // تجاهل الأخطاء والمتابعة
          developer.log(
            'Error importing location: $e',
            name: 'WarehouseLocationDao',
          );
        }
      }
    });

    return importedCount;
  }

  /// حذف جميع مواقع مخزن
  Future<int> deleteAllLocationsByWarehouse(int warehouseId) async {
    final db = await _databaseService.database;
    return await db.delete(
      'warehouse_locations',
      where: 'warehouse_id = ?',
      whereArgs: [warehouseId],
    );
  }
}
