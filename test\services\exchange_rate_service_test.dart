import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:smart_ledger/services/exchange_rate_service.dart';
import 'package:smart_ledger/models/exchange_rate.dart';

void main() {
  group('ExchangeRateService API Tests', () {
    late ExchangeRateService exchangeRateService;

    setUp(() {
      exchangeRateService = ExchangeRateService();
    });

    test('should check API connectivity', () async {
      final result = await exchangeRateService.checkAPIConnectivity();

      // هذا الاختبار قد يفشل إذا لم يكن هناك اتصال بالإنترنت
      // لكنه يجب أن يعيد نتيجة صحيحة (true أو false)
      expect(result.isSuccess, true);
      expect(result.data, isA<bool>());
    });

    test('should fetch supported currencies from API', () async {
      final result = await exchangeRateService.getSupportedCurrenciesFromAPI();

      if (result.isSuccess) {
        expect(result.data, isA<List<String>>());
        expect(result.data!.isNotEmpty, true);
        expect(result.data!.contains('USD'), true);
        debugPrint('العملات المدعومة: ${result.data!.take(10).join(', ')}...');
      } else {
        debugPrint('خطأ في جلب العملات المدعومة: ${result.error}');
      }
    });

    test('should fetch specific exchange rate from API', () async {
      final result = await exchangeRateService.fetchSpecificRateFromAPI(
        'USD',
        'EUR',
      );

      if (result.isSuccess) {
        expect(result.data, isA<double>());
        expect(result.data! > 0, true);
        debugPrint('سعر صرف USD/EUR: ${result.data}');
      } else {
        debugPrint('خطأ في جلب سعر الصرف: ${result.error}');
      }
    });

    test('should update exchange rates from API', () async {
      final result = await exchangeRateService.updateExchangeRatesFromAPI(
        baseCurrency: 'USD',
        targetCurrencies: ['EUR', 'GBP', 'JPY'],
      );

      if (result.isSuccess) {
        expect(result.data, true);
        debugPrint('تم تحديث أسعار الصرف بنجاح');
      } else {
        debugPrint('خطأ في تحديث أسعار الصرف: ${result.error}');
      }
    });

    test('should update specific currency rates', () async {
      final result = await exchangeRateService.updateSpecificCurrencyRates(
        'USD',
        ['EUR', 'SAR'],
      );

      if (result.isSuccess) {
        expect(result.data, isA<List<ExchangeRate>>());
        debugPrint('تم تحديث ${result.data!.length} أسعار صرف');

        for (final rate in result.data!) {
          debugPrint('${rate.currencyPair}: ${rate.rate}');
        }
      } else {
        debugPrint('خطأ في تحديث أسعار العملات المحددة: ${result.error}');
      }
    });

    test('should validate exchange rates', () {
      expect(exchangeRateService.isValidExchangeRate(1.5), true);
      expect(exchangeRateService.isValidExchangeRate(0.0), false);
      expect(exchangeRateService.isValidExchangeRate(-1.0), false);
      expect(exchangeRateService.isValidExchangeRate(double.infinity), false);
      expect(exchangeRateService.isValidExchangeRate(double.nan), false);
    });
  });

  group('ExchangeRate Model Tests', () {
    test('should create exchange rate with API source', () {
      final exchangeRate = ExchangeRate(
        fromCurrencyCode: 'USD',
        toCurrencyCode: 'EUR',
        rate: 0.85,
        effectiveDate: DateTime.now(),
        source: ExchangeRateSource.api,
        notes: 'تم جلبه من API',
      );

      expect(exchangeRate.fromCurrencyCode, 'USD');
      expect(exchangeRate.toCurrencyCode, 'EUR');
      expect(exchangeRate.rate, 0.85);
      expect(exchangeRate.source, ExchangeRateSource.api);
      expect(exchangeRate.currencyPair, 'USD/EUR');
      expect(exchangeRate.isActive, true);
    });

    test('should convert amounts correctly', () {
      final exchangeRate = ExchangeRate(
        fromCurrencyCode: 'USD',
        toCurrencyCode: 'EUR',
        rate: 0.85,
        effectiveDate: DateTime.now(),
      );

      expect(exchangeRate.convertAmount(100), 85.0);
      expect(exchangeRate.convertAmountReverse(85), 100.0);
    });
  });
}
