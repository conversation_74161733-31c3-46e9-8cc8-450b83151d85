import 'package:flutter/material.dart';
import '../../models/invoice.dart';
import '../../services/invoice_service.dart';
import '../../services/pdf_service.dart';
import '../../theme/app_theme.dart';
import 'add_invoice_screen.dart';

/// طريقة الدفع
enum PaymentMethod { cash, bankTransfer, check, creditCard }

class InvoiceDetailsScreen extends StatefulWidget {
  final Invoice invoice;

  const InvoiceDetailsScreen({super.key, required this.invoice});

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen>
    with SingleTickerProviderStateMixin {
  final InvoiceService _invoiceService = InvoiceService();
  final PdfService _pdfService = PdfService();
  late TabController _tabController;
  late Invoice _invoice;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _invoice = widget.invoice;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _updateInvoiceStatus(InvoiceStatus newStatus) async {
    final result = await _invoiceService.updateInvoiceStatus(
      _invoice.id!,
      newStatus,
    );
    if (mounted) {
      if (result.isSuccess) {
        setState(() {
          _invoice = _invoice.copyWith(status: newStatus);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث حالة الفاتورة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.error ?? 'خطأ في تحديث حالة الفاتورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handlePrintInvoice() async {
    if (!mounted) return;

    // عرض خيارات الطباعة
    final action = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خيارات الطباعة'),
        content: const Text('اختر الإجراء المطلوب:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('print'),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.print, size: 18),
                SizedBox(width: 8),
                Text('طباعة'),
              ],
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop('share'),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.share, size: 18),
                SizedBox(width: 8),
                Text('مشاركة'),
              ],
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop('save'),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.save, size: 18),
                SizedBox(width: 8),
                Text('حفظ PDF'),
              ],
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (action == null || !mounted) return;

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      switch (action) {
        case 'print':
          final result = await _pdfService.printInvoice(_invoice);
          if (mounted) {
            Navigator.of(context).pop(); // إغلاق مؤشر التحميل
            if (result.isSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم فتح نافذة الطباعة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(result.error ?? 'خطأ في طباعة الفاتورة'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          break;

        case 'share':
          final result = await _pdfService.shareInvoice(_invoice);
          if (mounted) {
            Navigator.of(context).pop(); // إغلاق مؤشر التحميل
            if (result.isSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم فتح نافذة المشاركة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(result.error ?? 'خطأ في مشاركة الفاتورة'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          break;

        case 'save':
          final result = await _pdfService.generateInvoicePdf(_invoice);
          if (mounted) {
            Navigator.of(context).pop(); // إغلاق مؤشر التحميل
            if (result.isSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حفظ الفاتورة في: ${result.data}'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 4),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(result.error ?? 'خطأ في حفظ الفاتورة'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          break;
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text('فاتورة ${_invoice.invoiceNumber}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) async {
              switch (value) {
                case 'edit':
                  final navigator = Navigator.of(context);
                  final result = await navigator.push<bool>(
                    MaterialPageRoute(
                      builder: (context) => AddInvoiceScreen(
                        invoice: _invoice,
                        invoiceType: _invoice.invoiceType,
                      ),
                    ),
                  );
                  if (result == true && mounted) {
                    navigator.pop(true);
                  }
                  break;
                case 'print':
                  await _handlePrintInvoice();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print, size: 20),
                    SizedBox(width: 8),
                    Text('طباعة ومشاركة'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'التفاصيل', icon: Icon(Icons.info)),
            Tab(text: 'العناصر', icon: Icon(Icons.list)),
            Tab(text: 'المدفوعات', icon: Icon(Icons.payment)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildDetailsTab(), _buildItemsTab(), _buildPaymentsTab()],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الفاتورة الأساسية
          _buildInfoCard(),
          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات العميل/المورد
          _buildCustomerSupplierCard(),
          const SizedBox(height: AppTheme.spacingMedium),

          // ملخص المبالغ
          _buildTotalsCard(),
          const SizedBox(height: AppTheme.spacingMedium),

          // إجراءات الحالة
          _buildStatusActionsCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            _buildInfoRow('رقم الفاتورة', _invoice.invoiceNumber),
            _buildInfoRow(
              'النوع',
              _invoice.invoiceType == InvoiceType.sales
                  ? 'فاتورة بيع'
                  : 'فاتورة شراء',
            ),
            _buildInfoRow('التاريخ', _formatDate(_invoice.date)),
            if (_invoice.dueDate != null)
              _buildInfoRow('تاريخ الاستحقاق', _formatDate(_invoice.dueDate!)),
            _buildInfoRow(
              'الحالة',
              _invoice.status.nameAr,
              statusColor: _getStatusColor(_invoice.status),
            ),
            if (_invoice.notes != null && _invoice.notes!.isNotEmpty)
              _buildInfoRow('ملاحظات', _invoice.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSupplierCard() {
    final isCustomer = _invoice.invoiceType == InvoiceType.sales;
    final name = isCustomer
        ? (_invoice.customer?.name ?? 'عميل محذوف')
        : (_invoice.supplier?.name ?? 'مورد محذوف');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isCustomer ? 'معلومات العميل' : 'معلومات المورد',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            _buildInfoRow('الاسم', name),
            if (isCustomer && _invoice.customer != null) ...[
              if (_invoice.customer!.email?.isNotEmpty == true)
                _buildInfoRow('البريد الإلكتروني', _invoice.customer!.email!),
              if (_invoice.customer!.phone?.isNotEmpty == true)
                _buildInfoRow('الهاتف', _invoice.customer!.phone!),
              if (_invoice.customer!.address?.isNotEmpty == true)
                _buildInfoRow('العنوان', _invoice.customer!.address!),
            ] else if (!isCustomer && _invoice.supplier != null) ...[
              if (_invoice.supplier!.email?.isNotEmpty == true)
                _buildInfoRow('البريد الإلكتروني', _invoice.supplier!.email!),
              if (_invoice.supplier!.phone?.isNotEmpty == true)
                _buildInfoRow('الهاتف', _invoice.supplier!.phone!),
              if (_invoice.supplier!.address?.isNotEmpty == true)
                _buildInfoRow('العنوان', _invoice.supplier!.address!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص المبالغ',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            _buildAmountRow('المجموع الفرعي', _invoice.subtotal, false),
            _buildAmountRow('الضريبة', _invoice.taxAmount, false),
            _buildAmountRow('الخصم', _invoice.discountAmount, false),
            const Divider(),
            _buildAmountRow('الإجمالي', _invoice.totalAmount, true),
            _buildAmountRow('المدفوع', _invoice.paidAmount, false),
            const Divider(),
            _buildAmountRow(
              'المتبقي',
              _invoice.totalAmount - _invoice.paidAmount,
              true,
              color: _invoice.totalAmount - _invoice.paidAmount > 0
                  ? Colors.red
                  : Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusActionsCard() {
    if (_invoice.status == InvoiceStatus.paid ||
        _invoice.status == InvoiceStatus.cancelled) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات الحالة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            Wrap(
              spacing: AppTheme.spacingSmall,
              children: [
                if (_invoice.status == InvoiceStatus.draft)
                  ElevatedButton(
                    onPressed: () => _updateInvoiceStatus(InvoiceStatus.posted),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('ترحيل'),
                  ),
                if (_invoice.status == InvoiceStatus.posted)
                  ElevatedButton(
                    onPressed: () => _updateInvoiceStatus(InvoiceStatus.paid),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('تسديد كامل'),
                  ),
                ElevatedButton(
                  onPressed: () =>
                      _updateInvoiceStatus(InvoiceStatus.cancelled),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('إلغاء'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عناصر الفاتورة (${_invoice.lines.length})',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppTheme.spacingMedium),

          if (_invoice.lines.isEmpty)
            const Center(child: Text('لا توجد عناصر في هذه الفاتورة'))
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _invoice.lines.length,
              itemBuilder: (context, index) {
                final line = _invoice.lines[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppTheme.primaryColor,
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(line.description ?? 'عنصر غير محدد'),
                    subtitle: Text(
                      'الكمية: ${line.quantity} × ${line.unitPrice.toStringAsFixed(2)} ر.س',
                    ),
                    trailing: Text(
                      '${line.lineTotal.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildPaymentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Summary Card
          _buildPaymentSummaryCard(),
          const SizedBox(height: AppTheme.spacingMedium),

          // Add Payment Button
          if (_invoice.status != InvoiceStatus.paid &&
              _invoice.status != InvoiceStatus.cancelled)
            _buildAddPaymentButton(),

          const SizedBox(height: AppTheme.spacingMedium),

          // Payments History
          Text(
            'سجل المدفوعات',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppTheme.spacingSmall),

          _buildPaymentsHistory(),
        ],
      ),
    );
  }

  Widget _buildPaymentSummaryCard() {
    final remainingAmount = _invoice.remainingAmount;
    final paymentProgress = _invoice.totalAmount > 0
        ? _invoice.paidAmount / _invoice.totalAmount
        : 0.0;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص المدفوعات',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingSmall),

            // Payment Progress Bar
            LinearProgressIndicator(
              value: paymentProgress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                paymentProgress >= 1.0 ? Colors.green : AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المبلغ الإجمالي',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      '${_invoice.totalAmount.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المبلغ المدفوع',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      '${_invoice.paidAmount.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المبلغ المتبقي',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      '${remainingAmount.toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: remainingAmount > 0
                            ? Colors.orange
                            : Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddPaymentButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _showAddPaymentDialog(),
        icon: const Icon(Icons.add),
        label: const Text('إضافة دفعة جديدة'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  Future<void> _showAddPaymentDialog() async {
    final TextEditingController amountController = TextEditingController();
    final TextEditingController notesController = TextEditingController();
    PaymentMethod selectedPaymentMethod = PaymentMethod.cash;
    DateTime selectedDate = DateTime.now();

    // Set default amount to remaining amount
    amountController.text = _invoice.remainingAmount.toStringAsFixed(2);

    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('إضافة دفعة جديدة'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Amount field
                    TextField(
                      controller: amountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'مبلغ الدفعة',
                        suffixText: 'ر.س',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Payment method dropdown
                    DropdownButtonFormField<PaymentMethod>(
                      value: selectedPaymentMethod,
                      decoration: const InputDecoration(
                        labelText: 'طريقة الدفع',
                        border: OutlineInputBorder(),
                      ),
                      items: PaymentMethod.values.map((method) {
                        return DropdownMenuItem(
                          value: method,
                          child: Text(_getPaymentMethodText(method)),
                        );
                      }).toList(),
                      onChanged: (PaymentMethod? value) {
                        if (value != null) {
                          setState(() {
                            selectedPaymentMethod = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Date picker
                    InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: selectedDate,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (picked != null) {
                          setState(() {
                            selectedDate = picked;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الدفعة',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Notes field
                    TextField(
                      controller: notesController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Payment summary
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ملخص الدفعة',
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('إجمالي الفاتورة:'),
                              Text(
                                '${_invoice.totalAmount.toStringAsFixed(2)} ر.س',
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('المدفوع سابقاً:'),
                              Text(
                                '${_invoice.paidAmount.toStringAsFixed(2)} ر.س',
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('المتبقي:'),
                              Text(
                                '${_invoice.remainingAmount.toStringAsFixed(2)} ر.س',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    final amount = double.tryParse(amountController.text);
                    if (amount == null || amount <= 0) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى إدخال مبلغ صحيح'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    if (amount > _invoice.remainingAmount) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('المبلغ أكبر من المبلغ المتبقي'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    Navigator.of(context).pop(true);
                  },
                  child: const Text('تسجيل الدفعة'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result == true) {
      final amount = double.parse(amountController.text);
      await _recordPayment(amount);
    }
  }

  Future<void> _recordPayment(double amount) async {
    try {
      final result = await _invoiceService.recordPayment(_invoice.id!, amount);
      if (mounted) {
        if (result.isSuccess) {
          // Update the local invoice state
          setState(() {
            _invoice = _invoice.copyWith(
              paidAmount: _invoice.paidAmount + amount,
            );
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تسجيل الدفعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تسجيل الدفعة: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الدفعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
    }
  }

  Widget _buildPaymentsHistory() {
    // For now, we'll create mock payment data based on the invoice's paid amount
    // In a real implementation, this would come from a payments database table
    final List<Map<String, dynamic>> mockPayments = _generateMockPayments();

    if (mockPayments.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingLarge),
          child: Column(
            children: [
              Icon(Icons.payment_outlined, size: 48, color: Colors.grey[400]),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                'لا توجد مدفوعات مسجلة',
                style: TextStyle(color: Colors.grey[600], fontSize: 16),
              ),
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                'سيتم عرض جميع المدفوعات المسجلة للفاتورة هنا',
                style: TextStyle(color: Colors.grey[500], fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: mockPayments
          .map((payment) => _buildPaymentCard(payment))
          .toList(),
    );
  }

  List<Map<String, dynamic>> _generateMockPayments() {
    // Generate mock payment data based on the invoice's paid amount
    // In a real implementation, this would come from a payments database table
    if (_invoice.paidAmount <= 0) {
      return [];
    }

    return [
      {
        'id': 'PAY-${_invoice.id}',
        'amount': _invoice.paidAmount,
        'paymentMethod': PaymentMethod.cash,
        'date': DateTime.now().subtract(const Duration(days: 1)),
        'reference': 'REF-${_invoice.invoiceNumber}',
        'notes': 'دفعة مسجلة للفاتورة',
      },
    ];
  }

  Widget _buildPaymentCard(Map<String, dynamic> payment) {
    final PaymentMethod method = payment['paymentMethod'] as PaymentMethod;
    final DateTime date = payment['date'] as DateTime;
    final double amount = payment['amount'] as double;
    final String reference = payment['reference'] as String;
    final String notes = payment['notes'] as String;

    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getPaymentMethodText(method),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  '${amount.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${date.day}/${date.month}/${date.year}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 16),
                Icon(Icons.receipt, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(reference, style: TextStyle(color: Colors.grey[600])),
              ],
            ),
            if (notes.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                notes,
                style: TextStyle(color: Colors.grey[700], fontSize: 12),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? statusColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: statusColor,
                fontWeight: statusColor != null ? FontWeight.bold : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(
    String label,
    double amount,
    bool isTotal, {
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ر.س',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color ?? (isTotal ? AppTheme.primaryColor : null),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.posted:
        return Colors.blue.shade300;
      case InvoiceStatus.sent:
        return Colors.blue;
      case InvoiceStatus.partiallyPaid:
        return Colors.orange;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.red.shade300;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
