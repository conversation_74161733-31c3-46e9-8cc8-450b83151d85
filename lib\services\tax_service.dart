/// خدمة الضرائب والامتثال
/// Tax and Compliance Service for Smart Ledger
library;

import '../models/tax.dart';
import '../models/tax_calculation.dart';
import '../models/invoice.dart';
import '../database/tax_dao.dart';
import '../utils/result.dart';

class TaxService {
  final TaxDao _taxDao = TaxDao();

  // ==================== إدارة الضرائب ====================

  /// إضافة ضريبة جديدة
  Future<Result<Tax>> createTax(Tax tax) async {
    try {
      // التحقق من صحة البيانات
      final validationErrors = tax.validate();
      if (validationErrors.isNotEmpty) {
        return Result.error('خطأ في البيانات: ${validationErrors.join(', ')}');
      }

      // التحقق من عدم تكرار الرمز
      final existingTaxResult = await _taxDao.getTaxByCode(tax.code);
      if (existingTaxResult.isSuccess && existingTaxResult.data != null) {
        return Result.error('رمز الضريبة موجود مسبقاً');
      }

      return await _taxDao.insertTax(tax);
    } catch (e) {
      return Result.error('خطأ في إنشاء الضريبة: ${e.toString()}');
    }
  }

  /// تحديث ضريبة
  Future<Result<Tax>> updateTax(Tax tax) async {
    try {
      // التحقق من صحة البيانات
      final validationErrors = tax.validate();
      if (validationErrors.isNotEmpty) {
        return Result.error('خطأ في البيانات: ${validationErrors.join(', ')}');
      }

      return await _taxDao.updateTax(tax);
    } catch (e) {
      return Result.error('خطأ في تحديث الضريبة: ${e.toString()}');
    }
  }

  /// حذف ضريبة
  Future<Result<void>> deleteTax(int id) async {
    try {
      // التحقق من عدم استخدام الضريبة في حسابات
      final calculationsResult = await _taxDao.getTaxCalculationsForDocument(
        'invoice',
        id,
      );
      if (calculationsResult.isSuccess && calculationsResult.data!.isNotEmpty) {
        return Result.error('لا يمكن حذف الضريبة لأنها مستخدمة في حسابات');
      }

      return await _taxDao.deleteTax(id);
    } catch (e) {
      return Result.error('خطأ في حذف الضريبة: ${e.toString()}');
    }
  }

  /// الحصول على جميع الضرائب
  Future<Result<List<Tax>>> getAllTaxes() async {
    return await _taxDao.getAllTaxes();
  }

  /// الحصول على الضرائب النشطة
  Future<Result<List<Tax>>> getActiveTaxes() async {
    return await _taxDao.getActiveTaxes();
  }

  /// الحصول على الضرائب حسب النوع
  Future<Result<List<Tax>>> getTaxesByType(TaxType type) async {
    return await _taxDao.getTaxesByType(type);
  }

  /// البحث في الضرائب
  Future<Result<List<Tax>>> searchTaxes(String query) async {
    return await _taxDao.searchTaxes(query);
  }

  // ==================== إدارة مجموعات الضرائب ====================

  /// إنشاء مجموعة ضرائب جديدة
  Future<Result<TaxGroup>> createTaxGroup(TaxGroup taxGroup) async {
    try {
      return await _taxDao.insertTaxGroup(taxGroup);
    } catch (e) {
      return Result.error('خطأ في إنشاء مجموعة الضرائب: ${e.toString()}');
    }
  }

  /// تحديث مجموعة ضرائب
  Future<Result<TaxGroup>> updateTaxGroup(TaxGroup taxGroup) async {
    try {
      return await _taxDao.updateTaxGroup(taxGroup);
    } catch (e) {
      return Result.error('خطأ في تحديث مجموعة الضرائب: ${e.toString()}');
    }
  }

  /// حذف مجموعة ضرائب
  Future<Result<void>> deleteTaxGroup(int id) async {
    try {
      return await _taxDao.deleteTaxGroup(id);
    } catch (e) {
      return Result.error('خطأ في حذف مجموعة الضرائب: ${e.toString()}');
    }
  }

  /// الحصول على جميع مجموعات الضرائب
  Future<Result<List<TaxGroup>>> getAllTaxGroups() async {
    return await _taxDao.getAllTaxGroups();
  }

  /// إضافة ضريبة إلى مجموعة
  Future<Result<void>> addTaxToGroup(
    int groupId,
    int taxId, {
    int sortOrder = 0,
  }) async {
    try {
      return await _taxDao.addTaxToGroup(groupId, taxId, sortOrder: sortOrder);
    } catch (e) {
      return Result.error('خطأ في إضافة الضريبة للمجموعة: ${e.toString()}');
    }
  }

  /// إزالة ضريبة من مجموعة
  Future<Result<void>> removeTaxFromGroup(int groupId, int taxId) async {
    try {
      return await _taxDao.removeTaxFromGroup(groupId, taxId);
    } catch (e) {
      return Result.error('خطأ في إزالة الضريبة من المجموعة: ${e.toString()}');
    }
  }

  // ==================== حساب الضرائب ====================

  /// حساب الضرائب للفاتورة
  Future<Result<Map<String, dynamic>>> calculateInvoiceTaxes(
    Invoice invoice, {
    int? taxGroupId,
  }) async {
    try {
      // الحصول على مجموعة الضرائب
      TaxGroup? taxGroup;
      if (taxGroupId != null) {
        final taxGroupResult = await _taxDao.getTaxGroupById(taxGroupId);
        if (taxGroupResult.isSuccess) {
          taxGroup = taxGroupResult.data;
        }
      }

      // إذا لم توجد مجموعة محددة، استخدم المجموعة الافتراضية
      if (taxGroup == null) {
        final allGroupsResult = await _taxDao.getAllTaxGroups();
        if (allGroupsResult.isSuccess) {
          taxGroup = allGroupsResult.data!.firstWhere(
            (group) => group.isDefault,
            orElse: () => allGroupsResult.data!.first,
          );
        }
      }

      if (taxGroup == null || taxGroup.taxes.isEmpty) {
        return Result.success({
          'baseAmount': invoice.subtotal,
          'taxBreakdown': <String, double>{},
          'totalTax': 0.0,
          'totalAmount': invoice.subtotal,
          'calculations': <TaxCalculation>[],
        });
      }

      // حساب الضرائب
      final taxBreakdown = taxGroup.getTaxBreakdown(invoice.subtotal);
      final calculations = <TaxCalculation>[];

      // إنشاء حسابات الضرائب
      for (final tax in taxGroup.taxes) {
        if (tax.isEffective) {
          final taxAmount = tax.calculateTaxAmount(invoice.subtotal);
          if (taxAmount > 0) {
            final calculation = TaxCalculation(
              documentType: 'invoice',
              documentId: invoice.id ?? 0,
              taxId: tax.id!,
              baseAmount: invoice.subtotal,
              taxAmount: taxAmount,
              taxRate: tax.rate,
              isInclusive: tax.isInclusive,
            );
            calculations.add(calculation);
          }
        }
      }

      return Result.success({
        'baseAmount': taxBreakdown['baseAmount'],
        'taxBreakdown': taxBreakdown['taxBreakdown'],
        'totalTax': taxBreakdown['totalTax'],
        'totalAmount': taxBreakdown['totalAmount'],
        'calculations': calculations,
      });
    } catch (e) {
      return Result.error('خطأ في حساب ضرائب الفاتورة: ${e.toString()}');
    }
  }

  /// حفظ حسابات الضرائب
  Future<Result<void>> saveTaxCalculations(
    List<TaxCalculation> calculations,
  ) async {
    try {
      for (final calculation in calculations) {
        await _taxDao.insertTaxCalculation(calculation);
      }
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حفظ حسابات الضرائب: ${e.toString()}');
    }
  }

  /// حذف حسابات الضرائب للمستند
  Future<Result<void>> deleteTaxCalculationsForDocument(
    String documentType,
    int documentId,
  ) async {
    try {
      return await _taxDao.deleteTaxCalculationsForDocument(
        documentType,
        documentId,
      );
    } catch (e) {
      return Result.error('خطأ في حذف حسابات الضرائب: ${e.toString()}');
    }
  }

  // ==================== التقارير الضريبية ====================

  /// إنشاء تقرير ضريبة القيمة المضافة
  Future<Result<TaxReport>> generateVATReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // الحصول على حسابات ضريبة القيمة المضافة
      final calculationsResult = await _taxDao.getTaxCalculationsForPeriod(
        startDate,
        endDate,
      );
      if (!calculationsResult.isSuccess) {
        return Result.error(calculationsResult.error!);
      }

      final allCalculations = calculationsResult.data!;

      // فصل ضرائب المبيعات والمشتريات
      final salesTax = allCalculations
          .where(
            (calc) =>
                calc.documentType == 'invoice' && calc.tax?.type == TaxType.vat,
          )
          .toList();

      final purchaseTax = allCalculations
          .where(
            (calc) =>
                calc.documentType == 'purchase' &&
                calc.tax?.type == TaxType.vat,
          )
          .toList();

      final report = TaxReport.vatReturn(
        startDate: startDate,
        endDate: endDate,
        salesTax: salesTax,
        purchaseTax: purchaseTax,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير ضريبة القيمة المضافة: ${e.toString()}',
      );
    }
  }

  /// إنشاء تقرير ضريبة الاستقطاع
  Future<Result<TaxReport>> generateWithholdingTaxReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final calculationsResult = await _taxDao.getTaxCalculationsForPeriod(
        startDate,
        endDate,
      );
      if (!calculationsResult.isSuccess) {
        return Result.error(calculationsResult.error!);
      }

      final withholdingCalculations = calculationsResult.data!
          .where((calc) => calc.tax?.type == TaxType.withholdingTax)
          .toList();

      final report = TaxReport.withholdingTaxReport(
        startDate: startDate,
        endDate: endDate,
        withholdingCalculations: withholdingCalculations,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير ضريبة الاستقطاع: ${e.toString()}',
      );
    }
  }

  /// الحصول على ملخص الضرائب لفترة
  Future<Result<Map<String, double>>> getTaxSummaryForPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    return await _taxDao.getTaxSummaryForPeriod(startDate, endDate);
  }

  // ==================== إعدادات الضرائب ====================

  /// حفظ إعدادات الضرائب للشركة
  Future<Result<CompanyTaxSettings>> saveCompanyTaxSettings(
    CompanyTaxSettings settings,
  ) async {
    try {
      return await _taxDao.saveCompanyTaxSettings(settings);
    } catch (e) {
      return Result.error('خطأ في حفظ إعدادات الضرائب: ${e.toString()}');
    }
  }

  /// الحصول على إعدادات الضرائب للشركة
  Future<Result<CompanyTaxSettings?>> getCompanyTaxSettings() async {
    return await _taxDao.getCompanyTaxSettings();
  }

  // ==================== تهيئة البيانات الافتراضية ====================

  /// تهيئة الضرائب الافتراضية
  Future<Result<void>> initializeDefaultTaxes() async {
    try {
      // إضافة الضرائب الافتراضية
      for (final tax in DefaultTaxes.saudiTaxes) {
        final existingResult = await _taxDao.getTaxByCode(tax.code);
        if (!existingResult.isSuccess || existingResult.data == null) {
          await _taxDao.insertTax(tax);
        }
      }

      // إضافة مجموعات الضرائب الافتراضية
      for (final group in DefaultTaxes.defaultTaxGroups) {
        await _taxDao.insertTaxGroup(group);
      }

      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تهيئة الضرائب الافتراضية: ${e.toString()}');
    }
  }

  // ==================== حسابات الضرائب المتقدمة ====================

  /// حساب الضريبة لمبلغ معين
  Future<Result<double>> calculateTaxForAmount(
    int taxGroupId,
    double amount,
  ) async {
    try {
      final taxGroupResult = await _taxDao.getTaxGroupById(taxGroupId);
      if (!taxGroupResult.isSuccess || taxGroupResult.data == null) {
        return Result.error('مجموعة الضرائب غير موجودة');
      }

      final taxesResult = await _taxDao.getTaxesByGroupId(taxGroupId);
      if (!taxesResult.isSuccess) {
        return Result.error('خطأ في جلب الضرائب: ${taxesResult.error}');
      }

      final taxes = taxesResult.data!;
      double totalTax = 0.0;

      for (final tax in taxes) {
        if (tax.status == TaxStatus.active) {
          double taxAmount = 0.0;
          if (tax.calculationMethod == TaxCalculationMethod.percentage) {
            taxAmount = amount * (tax.rate / 100);
          } else {
            taxAmount = tax.rate; // Fixed amount
          }
          totalTax += taxAmount;
        }
      }

      return Result.success(totalTax);
    } catch (e) {
      return Result.error('خطأ في حساب الضريبة: ${e.toString()}');
    }
  }

  /// حساب ضريبة القيمة المضافة (VAT) المتقدم
  Future<Result<Map<String, double>>> calculateVATAdvanced({
    required double amount,
    required double vatRate,
    required bool isInclusive,
  }) async {
    try {
      double vatAmount = 0.0;
      double netAmount = 0.0;
      double grossAmount = 0.0;

      if (isInclusive) {
        // الضريبة مشمولة في السعر
        grossAmount = amount;
        vatAmount = amount * vatRate / (100 + vatRate);
        netAmount = amount - vatAmount;
      } else {
        // الضريبة مضافة للسعر
        netAmount = amount;
        vatAmount = amount * vatRate / 100;
        grossAmount = amount + vatAmount;
      }

      return Result.success({
        'netAmount': netAmount,
        'vatAmount': vatAmount,
        'grossAmount': grossAmount,
        'vatRate': vatRate,
      });
    } catch (e) {
      return Result.error('خطأ في حساب ضريبة القيمة المضافة: ${e.toString()}');
    }
  }

  /// حساب الضريبة المقتطعة (Withholding Tax)
  Future<Result<Map<String, double>>> calculateWithholdingTax({
    required double amount,
    required double withholdingRate,
    required String taxType,
  }) async {
    try {
      final withholdingAmount = amount * withholdingRate / 100;
      final netAmount = amount - withholdingAmount;

      return Result.success({
        'grossAmount': amount,
        'withholdingAmount': withholdingAmount,
        'netAmount': netAmount,
        'withholdingRate': withholdingRate,
      });
    } catch (e) {
      return Result.error('خطأ في حساب الضريبة المقتطعة: ${e.toString()}');
    }
  }

  /// حساب الضرائب المركبة (Multiple Taxes)
  Future<Result<Map<String, dynamic>>> calculateCompoundTaxes({
    required double baseAmount,
    required List<int> taxGroupIds,
  }) async {
    try {
      double totalTaxAmount = 0.0;
      final taxBreakdown = <Map<String, dynamic>>[];

      for (final taxGroupId in taxGroupIds) {
        final taxResult = await calculateTaxForAmount(taxGroupId, baseAmount);
        if (taxResult.isSuccess) {
          final taxAmount = taxResult.data!;
          totalTaxAmount += taxAmount;

          // الحصول على تفاصيل مجموعة الضرائب
          final taxGroupResult = await _taxDao.getTaxGroupById(taxGroupId);
          if (taxGroupResult.isSuccess && taxGroupResult.data != null) {
            final taxGroup = taxGroupResult.data!;
            taxBreakdown.add({
              'taxGroupId': taxGroupId,
              'taxGroupName': taxGroup.nameAr,
              'taxAmount': taxAmount,
              'baseAmount': baseAmount,
            });
          }
        }
      }

      return Result.success({
        'baseAmount': baseAmount,
        'totalTaxAmount': totalTaxAmount,
        'finalAmount': baseAmount + totalTaxAmount,
        'taxBreakdown': taxBreakdown,
      });
    } catch (e) {
      return Result.error('خطأ في حساب الضرائب المركبة: ${e.toString()}');
    }
  }

  /// حساب ضريبة الدخل المقتطعة للموظفين
  Future<Result<Map<String, double>>> calculateIncomeTaxWithholding({
    required double grossSalary,
    required double exemptionAmount,
    required List<Map<String, dynamic>> taxBrackets,
  }) async {
    try {
      final taxableIncome = grossSalary - exemptionAmount;
      if (taxableIncome <= 0) {
        return Result.success({
          'grossSalary': grossSalary,
          'exemptionAmount': exemptionAmount,
          'taxableIncome': 0.0,
          'incomeTax': 0.0,
          'netSalary': grossSalary,
        });
      }

      double incomeTax = 0.0;
      double remainingIncome = taxableIncome;

      // حساب الضريبة حسب الشرائح
      for (final bracket in taxBrackets) {
        final double minAmount = bracket['minAmount'] ?? 0.0;
        final double maxAmount = bracket['maxAmount'] ?? double.infinity;
        final double rate = bracket['rate'] ?? 0.0;

        if (remainingIncome <= 0) break;

        final double bracketIncome = (remainingIncome + minAmount > maxAmount)
            ? maxAmount - minAmount
            : remainingIncome;

        if (bracketIncome > 0) {
          incomeTax += bracketIncome * rate / 100;
          remainingIncome -= bracketIncome;
        }
      }

      final netSalary = grossSalary - incomeTax;

      return Result.success({
        'grossSalary': grossSalary,
        'exemptionAmount': exemptionAmount,
        'taxableIncome': taxableIncome,
        'incomeTax': incomeTax,
        'netSalary': netSalary,
      });
    } catch (e) {
      return Result.error('خطأ في حساب ضريبة الدخل المقتطعة: ${e.toString()}');
    }
  }
}
