import 'package:flutter/material.dart';
import '../models/currency.dart';
import '../models/exchange_rate.dart';
import '../services/currency_service.dart';
import '../services/exchange_rate_service.dart';
import '../widgets/quantum_card.dart';
import '../widgets/quantum_text_field.dart';
import '../widgets/quantum_dropdown.dart';
import 'add_edit_currency_screen.dart';
import 'exchange_rates_screen.dart';

/// شاشة إدارة العملات
class CurrencyManagementScreen extends StatefulWidget {
  const CurrencyManagementScreen({super.key});

  @override
  State<CurrencyManagementScreen> createState() =>
      _CurrencyManagementScreenState();
}

class _CurrencyManagementScreenState extends State<CurrencyManagementScreen>
    with TickerProviderStateMixin {
  final CurrencyService _currencyService = CurrencyService();
  final ExchangeRateService _exchangeRateService = ExchangeRateService();
  final TextEditingController _searchController = TextEditingController();

  List<Currency> _currencies = [];
  List<Currency> _filteredCurrencies = [];
  final Map<String, ExchangeRate?> _latestRates = {};
  bool _isLoading = true;
  bool _isUpdatingRates = false;
  String _selectedFilter = 'all';
  final String _selectedBaseCurrency = 'SAR';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadCurrencies();
    _loadExchangeRates();
    _searchController.addListener(_filterCurrencies);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _loadCurrencies() async {
    setState(() => _isLoading = true);

    try {
      final currencies = await _currencyService.getAllCurrencies();
      setState(() {
        _currencies = currencies;
        _filteredCurrencies = currencies;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل العملات: ${e.toString()}')),
        );
      }
    }
  }

  void _filterCurrencies() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredCurrencies = _currencies.where((currency) {
        final matchesSearch =
            currency.nameAr.toLowerCase().contains(query) ||
            currency.nameEn.toLowerCase().contains(query) ||
            currency.code.toLowerCase().contains(query);

        final matchesFilter =
            _selectedFilter == 'all' ||
            (_selectedFilter == 'active' && currency.isActive) ||
            (_selectedFilter == 'inactive' && !currency.isActive) ||
            (_selectedFilter == 'base' && currency.isBaseCurrency);

        return matchesSearch && matchesFilter;
      }).toList();
    });
  }

  /// تحميل أسعار الصرف الحديثة
  Future<void> _loadExchangeRates() async {
    try {
      for (final currency in _currencies) {
        if (currency.code != _selectedBaseCurrency) {
          final rate = await _exchangeRateService.getLatestExchangeRate(
            currency.code,
            _selectedBaseCurrency,
          );
          _latestRates[currency.code] = rate;
        }
      }
      if (mounted) setState(() {});
    } catch (e) {
      // Handle error silently or show notification
      debugPrint('خطأ في تحميل أسعار الصرف: $e');
    }
  }

  /// تحديث أسعار الصرف من الإنترنت
  Future<void> _updateExchangeRates() async {
    setState(() => _isUpdatingRates = true);
    _pulseController.repeat(reverse: true);

    try {
      await _exchangeRateService.updateExchangeRatesFromAPI();
      await _loadExchangeRates();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث أسعار الصرف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث أسعار الصرف: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isUpdatingRates = false);
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  Future<void> _toggleCurrencyStatus(Currency currency) async {
    final result = await _currencyService.toggleCurrencyStatus(
      currency.id!,
      !currency.isActive,
    );

    if (result.isSuccess) {
      await _loadCurrencies();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              currency.isActive
                  ? 'تم إلغاء تفعيل العملة بنجاح'
                  : 'تم تفعيل العملة بنجاح',
            ),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(result.error!)));
      }
    }
  }

  Future<void> _setBaseCurrency(Currency currency) async {
    final result = await _currencyService.setBaseCurrency(currency.code);

    if (result.isSuccess) {
      await _loadCurrencies();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تعيين العملة الأساسية بنجاح')),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(result.error!)));
      }
    }
  }

  Future<void> _setupDefaultCurrencies() async {
    final result = await _currencyService.setupDefaultCurrencies();

    if (result.isSuccess) {
      await _loadCurrencies();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إعداد العملات الافتراضية بنجاح')),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(result.error!)));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة العملات'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          // زر تحديث أسعار الصرف
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isUpdatingRates ? _pulseAnimation.value : 1.0,
                child: IconButton(
                  icon: _isUpdatingRates
                      ? const CircularProgressIndicator(strokeWidth: 2)
                      : const Icon(Icons.refresh),
                  onPressed: _isUpdatingRates ? null : _updateExchangeRates,
                  tooltip: 'تحديث أسعار الصرف',
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.currency_exchange),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ExchangeRatesScreen(),
                ),
              );
            },
            tooltip: 'أسعار الصرف',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _setupDefaultCurrencies,
            tooltip: 'إعداد العملات الافتراضية',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildHeader(),
            _buildSearchAndFilter(),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildCurrencyList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditCurrencyScreen(),
            ),
          );
          if (result == true) {
            await _loadCurrencies();
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildHeader() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            const Icon(Icons.account_balance, size: 32, color: Colors.blue),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إدارة العملات',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'إدارة العملات المدعومة في النظام',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Text(
              '${_filteredCurrencies.length}',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: QuantumTextField(
              controller: _searchController,
              labelText: 'البحث في العملات',
              prefixIcon: Icons.search,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: QuantumDropdown<String>(
              value: _selectedFilter,
              items: const [
                DropdownMenuItem(value: 'all', child: Text('الكل')),
                DropdownMenuItem(value: 'active', child: Text('نشطة')),
                DropdownMenuItem(value: 'inactive', child: Text('غير نشطة')),
                DropdownMenuItem(value: 'base', child: Text('أساسية')),
              ],
              onChanged: (value) {
                setState(() => _selectedFilter = value!);
                _filterCurrencies();
              },
              labelText: 'التصفية',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyList() {
    if (_filteredCurrencies.isEmpty) {
      return const Center(child: Text('لا توجد عملات'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _filteredCurrencies.length,
      itemBuilder: (context, index) {
        final currency = _filteredCurrencies[index];
        return _buildCurrencyCard(currency);
      },
    );
  }

  Widget _buildCurrencyCard(Currency currency) {
    return QuantumCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: currency.isActive ? Colors.green : Colors.grey,
          child: Text(
            currency.symbol,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          '${currency.nameAr} (${currency.code})',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(currency.nameEn),
            if (currency.isBaseCurrency)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'عملة أساسية',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) async {
            switch (value) {
              case 'edit':
                final result = await Navigator.push<bool>(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        AddEditCurrencyScreen(currency: currency),
                  ),
                );
                if (result == true) {
                  await _loadCurrencies();
                }
                break;
              case 'toggle':
                await _toggleCurrencyStatus(currency);
                break;
              case 'set_base':
                await _setBaseCurrency(currency);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(leading: Icon(Icons.edit), title: Text('تعديل')),
            ),
            PopupMenuItem(
              value: 'toggle',
              child: ListTile(
                leading: Icon(
                  currency.isActive ? Icons.visibility_off : Icons.visibility,
                ),
                title: Text(currency.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
              ),
            ),
            if (!currency.isBaseCurrency && currency.isActive)
              const PopupMenuItem(
                value: 'set_base',
                child: ListTile(
                  leading: Icon(Icons.star),
                  title: Text('تعيين كعملة أساسية'),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
