/// خدمة إدارة الخزائن النقدية
/// Cash Management Service for Smart Ledger
library;

import '../models/cash_vault.dart';
import '../database/cash_vault_dao.dart';
import '../database/cash_transaction_dao.dart';
import '../utils/result.dart';

class CashService {
  static final CashService _instance = CashService._internal();
  factory CashService() => _instance;
  CashService._internal();

  final CashVaultDao _cashVaultDao = CashVaultDao();
  final CashTransactionDao _cashTransactionDao = CashTransactionDao();

  /// Get all cash vaults
  Future<Result<List<CashVault>>> getAllCashVaults() async {
    try {
      final vaults = await _cashVaultDao.getAllCashVaults();
      return Result.success(vaults);
    } catch (e) {
      return Result.error('خطأ في جلب الخزائن النقدية: ${e.toString()}');
    }
  }

  /// Get active cash vaults
  Future<Result<List<CashVault>>> getActiveCashVaults() async {
    try {
      final vaults = await _cashVaultDao.getActiveCashVaults();
      return Result.success(vaults);
    } catch (e) {
      return Result.error('خطأ في جلب الخزائن النشطة: ${e.toString()}');
    }
  }

  /// Get cash vault by ID
  Future<Result<CashVault?>> getCashVaultById(int id) async {
    try {
      final vault = await _cashVaultDao.getCashVaultById(id);
      return Result.success(vault);
    } catch (e) {
      return Result.error('خطأ في جلب الخزينة: ${e.toString()}');
    }
  }

  /// Create new cash vault
  Future<Result<int>> createCashVault(CashVault vault) async {
    try {
      // Validate vault data
      final validation = _validateCashVault(vault);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      final id = await _cashVaultDao.insertCashVault(vault);
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إنشاء الخزينة: ${e.toString()}');
    }
  }

  /// Update cash vault
  Future<Result<bool>> updateCashVault(CashVault vault) async {
    try {
      // Validate vault data
      final validation = _validateCashVault(vault);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      final rowsAffected = await _cashVaultDao.updateCashVault(vault);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث الخزينة: ${e.toString()}');
    }
  }

  /// Delete cash vault
  Future<Result<bool>> deleteCashVault(int id) async {
    try {
      final rowsAffected = await _cashVaultDao.deleteCashVault(id);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في حذف الخزينة: ${e.toString()}');
    }
  }

  /// Create cash transaction
  Future<Result<int>> createCashTransaction(CashTransaction transaction) async {
    try {
      // Validate transaction data
      final validation = _validateCashTransaction(transaction);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      // Generate transaction number if not provided
      if (transaction.transactionNumber.isEmpty) {
        final transactionNumber = await _cashTransactionDao.generateTransactionNumber();
        transaction = CashTransaction(
          cashVaultId: transaction.cashVaultId,
          transactionNumber: transactionNumber,
          type: transaction.type,
          status: transaction.status,
          amount: transaction.amount,
          currency: transaction.currency,
          description: transaction.description,
          reference: transaction.reference,
          receivedFrom: transaction.receivedFrom,
          paidTo: transaction.paidTo,
          toCashVaultId: transaction.toCashVaultId,
          bankAccountId: transaction.bankAccountId,
          journalEntryId: transaction.journalEntryId,
          transactionDate: transaction.transactionDate,
        );
      }

      final id = await _cashTransactionDao.insertCashTransaction(transaction);
      
      // Update vault balance if transaction is completed
      if (transaction.status == CashTransactionStatus.completed) {
        await _updateVaultBalanceAfterTransaction(transaction);
      }

      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إنشاء المعاملة النقدية: ${e.toString()}');
    }
  }

  /// Complete cash transaction
  Future<Result<bool>> completeCashTransaction(int transactionId) async {
    try {
      final transaction = await _cashTransactionDao.getCashTransactionById(transactionId);
      if (transaction == null) {
        return Result.error('المعاملة غير موجودة');
      }

      if (transaction.status != CashTransactionStatus.pending) {
        return Result.error('لا يمكن إكمال هذه المعاملة');
      }

      // Update transaction status
      await _cashTransactionDao.updateTransactionStatus(
        transactionId,
        CashTransactionStatus.completed,
      );

      // Update vault balance
      await _updateVaultBalanceAfterTransaction(transaction);

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إكمال المعاملة النقدية: ${e.toString()}');
    }
  }

  /// Cancel cash transaction
  Future<Result<bool>> cancelCashTransaction(int transactionId) async {
    try {
      final transaction = await _cashTransactionDao.getCashTransactionById(transactionId);
      if (transaction == null) {
        return Result.error('المعاملة غير موجودة');
      }

      if (transaction.status != CashTransactionStatus.pending) {
        return Result.error('لا يمكن إلغاء هذه المعاملة');
      }

      // Update transaction status
      await _cashTransactionDao.updateTransactionStatus(
        transactionId,
        CashTransactionStatus.cancelled,
      );

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إلغاء المعاملة النقدية: ${e.toString()}');
    }
  }

  /// Get cash transactions by vault
  Future<Result<List<CashTransaction>>> getCashTransactionsByVault(int vaultId) async {
    try {
      final transactions = await _cashTransactionDao.getCashTransactionsByVaultId(vaultId);
      return Result.success(transactions);
    } catch (e) {
      return Result.error('خطأ في جلب معاملات الخزينة: ${e.toString()}');
    }
  }

  /// Get vault balance summary
  Future<Result<Map<String, dynamic>>> getVaultBalanceSummary() async {
    try {
      final summary = await _cashVaultDao.getCashVaultBalanceSummary();
      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب ملخص الأرصدة: ${e.toString()}');
    }
  }

  /// Search cash vaults
  Future<Result<List<CashVault>>> searchCashVaults(String query) async {
    try {
      final vaults = await _cashVaultDao.searchCashVaults(query);
      return Result.success(vaults);
    } catch (e) {
      return Result.error('خطأ في البحث عن الخزائن: ${e.toString()}');
    }
  }

  /// Get vaults near limit
  Future<Result<List<CashVault>>> getVaultsNearLimit() async {
    try {
      final vaults = await _cashVaultDao.getVaultsNearLimit();
      return Result.success(vaults);
    } catch (e) {
      return Result.error('خطأ في جلب الخزائن القريبة من الحد الأقصى: ${e.toString()}');
    }
  }

  /// Get daily cash summary
  Future<Result<List<Map<String, dynamic>>>> getDailyCashSummary(DateTime date) async {
    try {
      final summary = await _cashTransactionDao.getDailyCashSummary(date);
      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب ملخص النقدية اليومي: ${e.toString()}');
    }
  }

  /// Transfer cash between vaults
  Future<Result<bool>> transferCashBetweenVaults({
    required int fromVaultId,
    required int toVaultId,
    required double amount,
    required String description,
    String? reference,
  }) async {
    try {
      // Validate vaults exist and are active
      final fromVault = await _cashVaultDao.getCashVaultById(fromVaultId);
      final toVault = await _cashVaultDao.getCashVaultById(toVaultId);

      if (fromVault == null || toVault == null) {
        return Result.error('إحدى الخزائن غير موجودة');
      }

      if (fromVault.status != CashVaultStatus.active || toVault.status != CashVaultStatus.active) {
        return Result.error('إحدى الخزائن غير نشطة');
      }

      if (fromVault.currentBalance < amount) {
        return Result.error('رصيد الخزينة المصدر غير كافي');
      }

      // Generate transaction number
      final transactionNumber = await _cashTransactionDao.generateTransactionNumber();

      // Create transfer out transaction
      final transferOut = CashTransaction(
        cashVaultId: fromVaultId,
        transactionNumber: '${transactionNumber}_OUT',
        type: CashTransactionType.transfer,
        status: CashTransactionStatus.completed,
        amount: amount,
        currency: fromVault.currency,
        description: 'تحويل إلى ${toVault.name} - $description',
        reference: reference,
        toCashVaultId: toVaultId,
      );

      // Create transfer in transaction
      final transferIn = CashTransaction(
        cashVaultId: toVaultId,
        transactionNumber: '${transactionNumber}_IN',
        type: CashTransactionType.receipt,
        status: CashTransactionStatus.completed,
        amount: amount,
        currency: toVault.currency,
        description: 'تحويل من ${fromVault.name} - $description',
        reference: reference,
        receivedFrom: fromVault.name,
      );

      // Insert transactions
      await _cashTransactionDao.insertCashTransaction(transferOut);
      await _cashTransactionDao.insertCashTransaction(transferIn);

      // Update vault balances
      await _cashVaultDao.updateCashVaultBalance(
        fromVaultId,
        fromVault.currentBalance - amount,
      );
      await _cashVaultDao.updateCashVaultBalance(
        toVaultId,
        toVault.currentBalance + amount,
      );

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في تحويل النقدية: ${e.toString()}');
    }
  }

  /// Validate cash vault data
  Result<bool> _validateCashVault(CashVault vault) {
    if (vault.code.trim().isEmpty) {
      return Result.error('رمز الخزينة مطلوب');
    }

    if (vault.name.trim().isEmpty) {
      return Result.error('اسم الخزينة مطلوب');
    }

    if (vault.currency.trim().isEmpty) {
      return Result.error('العملة مطلوبة');
    }

    if (vault.maxLimit != null && vault.maxLimit! <= 0) {
      return Result.error('الحد الأقصى يجب أن يكون أكبر من صفر');
    }

    return Result.success(true);
  }

  /// Validate cash transaction data
  Result<bool> _validateCashTransaction(CashTransaction transaction) {
    if (transaction.amount <= 0) {
      return Result.error('المبلغ يجب أن يكون أكبر من صفر');
    }

    if (transaction.description.trim().isEmpty) {
      return Result.error('وصف المعاملة مطلوب');
    }

    if (transaction.currency.trim().isEmpty) {
      return Result.error('العملة مطلوبة');
    }

    // Validate transfer transactions
    if (transaction.type == CashTransactionType.transfer) {
      if (transaction.toCashVaultId == null) {
        return Result.error('خزينة الوجهة مطلوبة للتحويل');
      }
      if (transaction.toCashVaultId == transaction.cashVaultId) {
        return Result.error('لا يمكن التحويل إلى نفس الخزينة');
      }
    }

    return Result.success(true);
  }

  /// Update vault balance after transaction
  Future<void> _updateVaultBalanceAfterTransaction(CashTransaction transaction) async {
    final vault = await _cashVaultDao.getCashVaultById(transaction.cashVaultId);
    if (vault != null) {
      final newBalance = vault.currentBalance + transaction.signedAmount;
      await _cashVaultDao.updateCashVaultBalance(vault.id!, newBalance);
    }

    // Update destination vault for transfers
    if (transaction.type == CashTransactionType.transfer && transaction.toCashVaultId != null) {
      final toVault = await _cashVaultDao.getCashVaultById(transaction.toCashVaultId!);
      if (toVault != null) {
        final newBalance = toVault.currentBalance + transaction.amount;
        await _cashVaultDao.updateCashVaultBalance(toVault.id!, newBalance);
      }
    }
  }
}
