import 'package:flutter/material.dart';
import '../models/journal_entry.dart';
import '../services/journal_entry_service.dart';
import 'package:intl/intl.dart';

class RecentTransactionsWidget extends StatefulWidget {
  final int limit;

  const RecentTransactionsWidget({super.key, this.limit = 5});

  @override
  State<RecentTransactionsWidget> createState() =>
      _RecentTransactionsWidgetState();
}

class _RecentTransactionsWidgetState extends State<RecentTransactionsWidget> {
  final JournalEntryService _journalEntryService = JournalEntryService();
  List<JournalEntry> _recentEntries = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadRecentTransactions();
  }

  Future<void> _loadRecentTransactions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _journalEntryService.getAllJournalEntries();
      if (result.isSuccess) {
        // Sort by date descending and take the limit
        List<JournalEntry> entries = result.data!;
        entries.sort((a, b) => b.date.compareTo(a.date));

        setState(() {
          _recentEntries = entries.take(widget.limit).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = result.error;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل المعاملات: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    if (_errorMessage != null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.error,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: _loadRecentTransactions,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_recentEntries.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                Icons.receipt_long_outlined,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 48,
              ),
              const SizedBox(height: 8),
              Text(
                'لا توجد معاملات حديثة',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 4),
              Text(
                'ابدأ بإنشاء قيد محاسبي جديد',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Column(
        children: [
          ...(_recentEntries.map((entry) => _buildTransactionItem(entry))),
          if (_recentEntries.length >= widget.limit)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/journal-entries');
                },
                child: const Text('عرض جميع المعاملات'),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(JournalEntry entry) {
    final dateFormatter = DateFormat('dd/MM/yyyy', 'ar_SA');
    final currencyFormatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 2,
    );

    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: entry.isPosted
              ? Colors.green.withValues(alpha: 0.1)
              : Colors.orange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          entry.isPosted ? Icons.check_circle : Icons.pending,
          color: entry.isPosted ? Colors.green : Colors.orange,
          size: 20,
        ),
      ),
      title: Text(
        entry.description,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'رقم القيد: ${entry.entryNumber}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            dateFormatter.format(entry.date),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            currencyFormatter.format(entry.totalDebit),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          Text(
            entry.isPosted ? 'مرحل' : 'غير مرحل',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: entry.isPosted ? Colors.green : Colors.orange,
            ),
          ),
        ],
      ),
      onTap: () {
        Navigator.pushNamed(
          context,
          '/journal-entry-details',
          arguments: entry.id,
        );
      },
    );
  }
}
