# Arabic Font for PDF Generation

## Overview
This folder should contain Arabic fonts for proper PDF generation with Arabic text support.

## Recommended Font
For best Arabic text rendering in PDFs, download and place the following font file here:
- **NotoSansArabic-Regular.ttf**

## How to Add the Font

1. Download Noto Sans Arabic from Google Fonts:
   - Visit: https://fonts.google.com/noto/specimen/Noto+Sans+Arabic
   - Download the font family
   - Extract the `NotoSansArabic-Regular.ttf` file

2. Place the font file in this directory:
   ```
   assets/fonts/NotoSansArabic-Regular.ttf
   ```

3. The PDF service will automatically use this font for Arabic text rendering.

## Fallback Behavior
If the Arabic font is not found, the PDF service will fall back to the default Helvetica font, which may not display Arabic text correctly.

## Alternative Fonts
You can also use other Arabic fonts by:
1. Placing the font file in this directory
2. Updating the font name in `lib/services/pdf_service.dart` in the `_loadArabicFont()` method

## Supported Font Formats
- TTF (TrueType Font) - Recommended
- OTF (OpenType Font) - Also supported

## File Size Considerations
Arabic fonts can be large (2-5MB). Consider the app size impact when choosing fonts.
