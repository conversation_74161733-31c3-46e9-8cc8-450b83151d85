import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 📊 نظام التقارير المالية المحسن
/// Enhanced Financial Reports System
///
/// هذا الملف يحتوي على نظام التقارير المالية المحسن لا مثيل له في التاريخ
/// This file contains unprecedented enhanced financial reports system in history

/// 🌟 لوحة التقارير المالية المحسنة
/// Enhanced Financial Reports Dashboard
class EnhancedFinancialReportsDashboard extends StatefulWidget {
  const EnhancedFinancialReportsDashboard({super.key});

  @override
  State<EnhancedFinancialReportsDashboard> createState() =>
      _EnhancedFinancialReportsDashboardState();
}

class _EnhancedFinancialReportsDashboardState
    extends State<EnhancedFinancialReportsDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _reportController;
  late AnimationController _chartController;
  late Animation<double> _mainAnimation;
  late Animation<double> _reportAnimation;
  late Animation<double> _chartAnimation;

  int _selectedTab =
      0; // 0 for balance sheet, 1 for income statement, 2 for trial balance, 3 for account statements

  final List<BalanceSheetItem> _balanceSheetItems = [
    BalanceSheetItem(
      category: 'الأصول المتداولة',
      items: [
        FinancialItem(
          name: 'النقدية في الصندوق',
          amount: 25000.0,
          accountCode: '1001',
        ),
        FinancialItem(
          name: 'البنك - الحساب الجاري',
          amount: 150000.0,
          accountCode: '1002',
        ),
        FinancialItem(name: 'العملاء', amount: 85000.0, accountCode: '1101'),
        FinancialItem(name: 'المخزون', amount: 120000.0, accountCode: '1201'),
      ],
    ),
    BalanceSheetItem(
      category: 'الأصول الثابتة',
      items: [
        FinancialItem(
          name: 'الأثاث والمعدات',
          amount: 75000.0,
          accountCode: '1301',
        ),
        FinancialItem(name: 'السيارات', amount: 180000.0, accountCode: '1302'),
        FinancialItem(name: 'المباني', amount: 500000.0, accountCode: '1303'),
      ],
    ),
    BalanceSheetItem(
      category: 'الخصوم المتداولة',
      items: [
        FinancialItem(name: 'الموردون', amount: -65000.0, accountCode: '2001'),
        FinancialItem(
          name: 'المصروفات المستحقة',
          amount: -15000.0,
          accountCode: '2002',
        ),
        FinancialItem(
          name: 'القروض قصيرة الأجل',
          amount: -50000.0,
          accountCode: '2003',
        ),
      ],
    ),
    BalanceSheetItem(
      category: 'الخصوم طويلة الأجل',
      items: [
        FinancialItem(
          name: 'القروض طويلة الأجل',
          amount: -200000.0,
          accountCode: '2101',
        ),
      ],
    ),
    BalanceSheetItem(
      category: 'حقوق الملكية',
      items: [
        FinancialItem(
          name: 'رأس المال',
          amount: -600000.0,
          accountCode: '3001',
        ),
        FinancialItem(
          name: 'الأرباح المحتجزة',
          amount: -205000.0,
          accountCode: '3002',
        ),
      ],
    ),
  ];

  final List<IncomeStatementItem> _incomeStatementItems = [
    IncomeStatementItem(
      category: 'الإيرادات',
      items: [
        FinancialItem(
          name: 'مبيعات البضائع',
          amount: 450000.0,
          accountCode: '4001',
        ),
        FinancialItem(
          name: 'إيرادات الخدمات',
          amount: 85000.0,
          accountCode: '4002',
        ),
        FinancialItem(
          name: 'إيرادات أخرى',
          amount: 15000.0,
          accountCode: '4003',
        ),
      ],
    ),
    IncomeStatementItem(
      category: 'تكلفة البضاعة المباعة',
      items: [
        FinancialItem(
          name: 'تكلفة المبيعات',
          amount: -280000.0,
          accountCode: '5001',
        ),
      ],
    ),
    IncomeStatementItem(
      category: 'المصروفات التشغيلية',
      items: [
        FinancialItem(
          name: 'رواتب الموظفين',
          amount: -120000.0,
          accountCode: '6001',
        ),
        FinancialItem(
          name: 'إيجار المكتب',
          amount: -36000.0,
          accountCode: '6002',
        ),
        FinancialItem(
          name: 'مصروفات الكهرباء',
          amount: -18000.0,
          accountCode: '6003',
        ),
        FinancialItem(
          name: 'مصروفات الهاتف والإنترنت',
          amount: -12000.0,
          accountCode: '6004',
        ),
        FinancialItem(
          name: 'مصروفات التسويق',
          amount: -25000.0,
          accountCode: '6005',
        ),
      ],
    ),
    IncomeStatementItem(
      category: 'المصروفات الأخرى',
      items: [
        FinancialItem(
          name: 'فوائد القروض',
          amount: -8000.0,
          accountCode: '7001',
        ),
        FinancialItem(
          name: 'مصروفات متنوعة',
          amount: -5000.0,
          accountCode: '7002',
        ),
      ],
    ),
  ];

  final List<TrialBalanceItem> _trialBalanceItems = [
    TrialBalanceItem(
      accountCode: '1001',
      accountName: 'النقدية في الصندوق',
      debitBalance: 25000.0,
      creditBalance: 0.0,
    ),
    TrialBalanceItem(
      accountCode: '1002',
      accountName: 'البنك - الحساب الجاري',
      debitBalance: 150000.0,
      creditBalance: 0.0,
    ),
    TrialBalanceItem(
      accountCode: '1101',
      accountName: 'العملاء',
      debitBalance: 85000.0,
      creditBalance: 0.0,
    ),
    TrialBalanceItem(
      accountCode: '1201',
      accountName: 'المخزون',
      debitBalance: 120000.0,
      creditBalance: 0.0,
    ),
    TrialBalanceItem(
      accountCode: '2001',
      accountName: 'الموردون',
      debitBalance: 0.0,
      creditBalance: 65000.0,
    ),
    TrialBalanceItem(
      accountCode: '3001',
      accountName: 'رأس المال',
      debitBalance: 0.0,
      creditBalance: 600000.0,
    ),
    TrialBalanceItem(
      accountCode: '4001',
      accountName: 'مبيعات البضائع',
      debitBalance: 0.0,
      creditBalance: 450000.0,
    ),
    TrialBalanceItem(
      accountCode: '5001',
      accountName: 'تكلفة المبيعات',
      debitBalance: 280000.0,
      creditBalance: 0.0,
    ),
    TrialBalanceItem(
      accountCode: '6001',
      accountName: 'رواتب الموظفين',
      debitBalance: 120000.0,
      creditBalance: 0.0,
    ),
  ];

  final List<AccountStatement> _accountStatements = [
    AccountStatement(
      accountCode: '1001',
      accountName: 'النقدية في الصندوق',
      openingBalance: 20000.0,
      transactions: [
        AccountTransaction(
          date: DateTime.now().subtract(const Duration(days: 10)),
          description: 'إيداع نقدي من المبيعات',
          reference: 'JE-001',
          debit: 15000.0,
          credit: 0.0,
          balance: 35000.0,
        ),
        AccountTransaction(
          date: DateTime.now().subtract(const Duration(days: 8)),
          description: 'سحب نقدي لمصروفات',
          reference: 'JE-002',
          debit: 0.0,
          credit: 5000.0,
          balance: 30000.0,
        ),
        AccountTransaction(
          date: DateTime.now().subtract(const Duration(days: 5)),
          description: 'إيداع نقدي من عميل',
          reference: 'JE-003',
          debit: 8000.0,
          credit: 0.0,
          balance: 38000.0,
        ),
        AccountTransaction(
          date: DateTime.now().subtract(const Duration(days: 2)),
          description: 'دفع مصروفات نقدية',
          reference: 'JE-004',
          debit: 0.0,
          credit: 13000.0,
          balance: 25000.0,
        ),
      ],
      closingBalance: 25000.0,
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _reportController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _chartController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _reportAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _reportController, curve: Curves.easeInOut),
    );

    _chartAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(parent: _chartController, curve: Curves.linear));

    _mainController.repeat(reverse: true);
    _reportController.repeat(reverse: true);
    _chartController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _reportController.dispose();
    _chartController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _reportAnimation,
        _chartAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.8 + (_mainAnimation.value * 0.8),
          child: QuantumEnergyEffect(
            intensity: 2.4 + (_reportAnimation.value * 0.6),
            primaryColor: const Color(0xFF4CAF50),
            secondaryColor: const Color(0xFF66BB6A),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF1B5E20).withValues(alpha: 0.9),
                    const Color(0xFF2E7D32).withValues(alpha: 0.8),
                    const Color(0xFF388E3C).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام التقارير المالية
                  Row(
                    children: [
                      Transform.scale(
                        scale: _reportAnimation.value,
                        child: Transform.rotate(
                          angle: _chartAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF4CAF50,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFF66BB6A,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.analytics_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '📊 التقارير المالية المحسنة',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'تقارير مالية شاملة ومتقدمة مع تحليلات تفاعلية',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات التقارير
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  if (_selectedTab == 0) _buildBalanceSheetView(),
                  if (_selectedTab == 1) _buildIncomeStatementView(),
                  if (_selectedTab == 2) _buildTrialBalanceView(),
                  if (_selectedTab == 3) _buildAccountStatementsView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 0),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 0
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.account_balance_rounded,
                      color: _selectedTab == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'الميزانية',
                      style: TextStyle(
                        color: _selectedTab == 0
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 1),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 1
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.trending_up_rounded,
                      color: _selectedTab == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'قائمة الدخل',
                      style: TextStyle(
                        color: _selectedTab == 1
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 2),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 2
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.balance_rounded,
                      color: _selectedTab == 2
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'ميزان المراجعة',
                      style: TextStyle(
                        color: _selectedTab == 2
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 3),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 3
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.receipt_long_rounded,
                      color: _selectedTab == 3
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'كشف الحساب',
                      style: TextStyle(
                        color: _selectedTab == 3
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض الميزانية العمومية
  Widget _buildBalanceSheetView() {
    double totalAssets = 0.0;
    double totalLiabilities = 0.0;
    double totalEquity = 0.0;

    for (var category in _balanceSheetItems) {
      double categoryTotal = category.items.fold(
        0.0,
        (sum, item) => sum + item.amount,
      );
      if (category.category.contains('الأصول')) {
        totalAssets += categoryTotal;
      } else if (category.category.contains('الخصوم')) {
        totalLiabilities += categoryTotal.abs();
      } else if (category.category.contains('حقوق الملكية')) {
        totalEquity += categoryTotal.abs();
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ملخص الميزانية
        _buildBalanceSheetSummary(totalAssets, totalLiabilities, totalEquity),

        const SizedBox(height: AppTheme.spacingLarge),

        // تفاصيل الميزانية
        Text(
          '📊 الميزانية العمومية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_balanceSheetItems.length, (index) {
          return _buildBalanceSheetCategoryCard(_balanceSheetItems[index]);
        }),
      ],
    );
  }

  /// بناء ملخص الميزانية
  Widget _buildBalanceSheetSummary(
    double totalAssets,
    double totalLiabilities,
    double totalEquity,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الأصول',
            '${totalAssets.toStringAsFixed(0)} ر.س',
            Icons.trending_up_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الخصوم',
            '${totalLiabilities.toStringAsFixed(0)} ر.س',
            Icons.trending_down_rounded,
            const Color(0xFFF44336),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildSummaryCard(
            'حقوق الملكية',
            '${totalEquity.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet_rounded,
            const Color(0xFF2196F3),
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 8,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة فئة الميزانية
  Widget _buildBalanceSheetCategoryCard(BalanceSheetItem category) {
    double categoryTotal = category.items.fold(
      0.0,
      (sum, item) => sum + item.amount,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس الفئة
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Icon(
                  Icons.category_rounded,
                  color: Color(0xFF4CAF50),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.category,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${category.items.length} حساب',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${categoryTotal.abs().toStringAsFixed(0)} ر.س',
                  style: const TextStyle(
                    color: Color(0xFF4CAF50),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // عناصر الفئة
          ...List.generate(category.items.length, (index) {
            return _buildFinancialItemRow(category.items[index]);
          }),
        ],
      ),
    );
  }

  Widget _buildFinancialItemRow(FinancialItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            item.accountCode,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 10,
              fontFamily: 'monospace',
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: Text(
              item.name,
              style: const TextStyle(color: Colors.white, fontSize: 11),
            ),
          ),
          Text(
            '${item.amount.abs().toStringAsFixed(0)} ر.س',
            style: TextStyle(
              color: item.amount >= 0
                  ? const Color(0xFF4CAF50)
                  : const Color(0xFFF44336),
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض قائمة الدخل
  Widget _buildIncomeStatementView() {
    double totalRevenue = 0.0;
    double totalExpenses = 0.0;

    for (var category in _incomeStatementItems) {
      double categoryTotal = category.items.fold(
        0.0,
        (sum, item) => sum + item.amount,
      );
      if (category.category.contains('الإيرادات')) {
        totalRevenue += categoryTotal;
      } else {
        totalExpenses += categoryTotal.abs();
      }
    }

    double netIncome = totalRevenue - totalExpenses;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ملخص قائمة الدخل
        _buildIncomeStatementSummary(totalRevenue, totalExpenses, netIncome),

        const SizedBox(height: AppTheme.spacingLarge),

        // تفاصيل قائمة الدخل
        Text(
          '📈 قائمة الدخل',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_incomeStatementItems.length, (index) {
          return _buildIncomeStatementCategoryCard(
            _incomeStatementItems[index],
          );
        }),
      ],
    );
  }

  Widget _buildIncomeStatementSummary(
    double totalRevenue,
    double totalExpenses,
    double netIncome,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الإيرادات',
            '${totalRevenue.toStringAsFixed(0)} ر.س',
            Icons.trending_up_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي المصروفات',
            '${totalExpenses.toStringAsFixed(0)} ر.س',
            Icons.trending_down_rounded,
            const Color(0xFFF44336),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildSummaryCard(
            'صافي الدخل',
            '${netIncome.toStringAsFixed(0)} ر.س',
            netIncome >= 0
                ? Icons.trending_up_rounded
                : Icons.trending_down_rounded,
            netIncome >= 0 ? const Color(0xFF4CAF50) : const Color(0xFFF44336),
          ),
        ),
      ],
    );
  }

  Widget _buildIncomeStatementCategoryCard(IncomeStatementItem category) {
    double categoryTotal = category.items.fold(
      0.0,
      (sum, item) => sum + item.amount,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس الفئة
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  categoryTotal >= 0
                      ? Icons.trending_up_rounded
                      : Icons.trending_down_rounded,
                  color: const Color(0xFF4CAF50),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.category,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${category.items.length} حساب',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color:
                      (categoryTotal >= 0
                              ? const Color(0xFF4CAF50)
                              : const Color(0xFFF44336))
                          .withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${categoryTotal.abs().toStringAsFixed(0)} ر.س',
                  style: TextStyle(
                    color: categoryTotal >= 0
                        ? const Color(0xFF4CAF50)
                        : const Color(0xFFF44336),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // عناصر الفئة
          ...List.generate(category.items.length, (index) {
            return _buildFinancialItemRow(category.items[index]);
          }),
        ],
      ),
    );
  }

  /// بناء عرض ميزان المراجعة
  Widget _buildTrialBalanceView() {
    double totalDebits = _trialBalanceItems.fold(
      0.0,
      (sum, item) => sum + item.debitBalance,
    );
    double totalCredits = _trialBalanceItems.fold(
      0.0,
      (sum, item) => sum + item.creditBalance,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ملخص ميزان المراجعة
        _buildTrialBalanceSummary(totalDebits, totalCredits),

        const SizedBox(height: AppTheme.spacingLarge),

        // تفاصيل ميزان المراجعة
        Text(
          '⚖️ ميزان المراجعة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        // رأس الجدول
        _buildTrialBalanceHeader(),

        const SizedBox(height: AppTheme.spacingSmall),

        // عناصر ميزان المراجعة
        ...List.generate(_trialBalanceItems.length, (index) {
          return _buildTrialBalanceRow(_trialBalanceItems[index]);
        }),

        const SizedBox(height: AppTheme.spacingMedium),

        // إجمالي ميزان المراجعة
        _buildTrialBalanceTotal(totalDebits, totalCredits),
      ],
    );
  }

  Widget _buildTrialBalanceSummary(double totalDebits, double totalCredits) {
    bool isBalanced = (totalDebits - totalCredits).abs() < 0.01;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي المدين',
            '${totalDebits.toStringAsFixed(0)} ر.س',
            Icons.trending_up_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الدائن',
            '${totalCredits.toStringAsFixed(0)} ر.س',
            Icons.trending_down_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildSummaryCard(
            'حالة التوازن',
            isBalanced ? 'متوازن' : 'غير متوازن',
            isBalanced ? Icons.check_circle_rounded : Icons.error_rounded,
            isBalanced ? const Color(0xFF4CAF50) : const Color(0xFFF44336),
          ),
        ),
      ],
    );
  }

  Widget _buildTrialBalanceHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'رقم الحساب',
              style: TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              'اسم الحساب',
              style: TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'مدين',
              style: TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'دائن',
              style: TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrialBalanceRow(TrialBalanceItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              item.accountCode,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 10,
                fontFamily: 'monospace',
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              item.accountName,
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.debitBalance > 0
                  ? item.debitBalance.toStringAsFixed(0)
                  : '-',
              style: TextStyle(
                color: item.debitBalance > 0
                    ? const Color(0xFF4CAF50)
                    : Colors.white.withValues(alpha: 0.5),
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.creditBalance > 0
                  ? item.creditBalance.toStringAsFixed(0)
                  : '-',
              style: TextStyle(
                color: item.creditBalance > 0
                    ? const Color(0xFF2196F3)
                    : Colors.white.withValues(alpha: 0.5),
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrialBalanceTotal(double totalDebits, double totalCredits) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: const Color(0xFF4CAF50).withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
          width: 2,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 6,
            child: Text(
              'الإجمالي',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              totalDebits.toStringAsFixed(0),
              style: const TextStyle(
                color: Color(0xFF4CAF50),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              totalCredits.toStringAsFixed(0),
              style: const TextStyle(
                color: Color(0xFF2196F3),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض كشف الحساب
  Widget _buildAccountStatementsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📋 كشف الحساب',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_accountStatements.length, (index) {
          return _buildAccountStatementCard(_accountStatements[index]);
        }),
      ],
    );
  }

  Widget _buildAccountStatementCard(AccountStatement statement) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingLarge),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس كشف الحساب
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: const Icon(
                  Icons.receipt_long_rounded,
                  color: Color(0xFF4CAF50),
                  size: 28,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${statement.accountCode} - ${statement.accountName}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'الرصيد الختامي: ${statement.closingBalance.toStringAsFixed(0)} ر.س',
                      style: TextStyle(
                        color: const Color(0xFF4CAF50),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // رأس جدول الحركات
          _buildTransactionHeader(),

          const SizedBox(height: AppTheme.spacingSmall),

          // الرصيد الافتتاحي
          _buildOpeningBalanceRow(statement.openingBalance),

          // الحركات
          ...List.generate(statement.transactions.length, (index) {
            return _buildTransactionRow(statement.transactions[index]);
          }),

          const SizedBox(height: AppTheme.spacingSmall),

          // الرصيد الختامي
          _buildClosingBalanceRow(statement.closingBalance),
        ],
      ),
    );
  }

  Widget _buildTransactionHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: const Color(0xFF4CAF50).withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'التاريخ',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'البيان',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'مدين',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'دائن',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'الرصيد',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOpeningBalanceRow(double openingBalance) {
    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      padding: const EdgeInsets.all(AppTheme.spacingXSmall),
      decoration: BoxDecoration(
        color: const Color(0xFF2196F3).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '-',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 9,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'الرصيد الافتتاحي',
              style: const TextStyle(
                color: Color(0xFF2196F3),
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '-',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 9,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '-',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 9,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              openingBalance.toStringAsFixed(0),
              style: const TextStyle(
                color: Color(0xFF2196F3),
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionRow(AccountTransaction transaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      padding: const EdgeInsets.all(AppTheme.spacingXSmall),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.02),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '${transaction.date.day}/${transaction.date.month}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 8,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              transaction.description,
              style: const TextStyle(color: Colors.white, fontSize: 8),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              transaction.debit > 0
                  ? transaction.debit.toStringAsFixed(0)
                  : '-',
              style: TextStyle(
                color: transaction.debit > 0
                    ? const Color(0xFF4CAF50)
                    : Colors.white.withValues(alpha: 0.3),
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              transaction.credit > 0
                  ? transaction.credit.toStringAsFixed(0)
                  : '-',
              style: TextStyle(
                color: transaction.credit > 0
                    ? const Color(0xFFF44336)
                    : Colors.white.withValues(alpha: 0.3),
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              transaction.balance.toStringAsFixed(0),
              style: TextStyle(
                color: transaction.balance >= 0
                    ? const Color(0xFF4CAF50)
                    : const Color(0xFFF44336),
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClosingBalanceRow(double closingBalance) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: const Color(0xFF4CAF50).withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '-',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 9,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'الرصيد الختامي',
              style: const TextStyle(
                color: Color(0xFF4CAF50),
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '-',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 9,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '-',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 9,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              closingBalance.toStringAsFixed(0),
              style: const TextStyle(
                color: Color(0xFF4CAF50),
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج عنصر الميزانية العمومية
class BalanceSheetItem {
  final String category;
  final List<FinancialItem> items;

  BalanceSheetItem({required this.category, required this.items});
}

/// نموذج عنصر قائمة الدخل
class IncomeStatementItem {
  final String category;
  final List<FinancialItem> items;

  IncomeStatementItem({required this.category, required this.items});
}

/// نموذج العنصر المالي
class FinancialItem {
  final String name;
  final double amount;
  final String accountCode;

  FinancialItem({
    required this.name,
    required this.amount,
    required this.accountCode,
  });
}

/// نموذج عنصر ميزان المراجعة
class TrialBalanceItem {
  final String accountCode;
  final String accountName;
  final double debitBalance;
  final double creditBalance;

  TrialBalanceItem({
    required this.accountCode,
    required this.accountName,
    required this.debitBalance,
    required this.creditBalance,
  });
}

/// نموذج كشف الحساب
class AccountStatement {
  final String accountCode;
  final String accountName;
  final double openingBalance;
  final List<AccountTransaction> transactions;
  final double closingBalance;

  AccountStatement({
    required this.accountCode,
    required this.accountName,
    required this.openingBalance,
    required this.transactions,
    required this.closingBalance,
  });
}

/// نموذج حركة الحساب
class AccountTransaction {
  final DateTime date;
  final String description;
  final String reference;
  final double debit;
  final double credit;
  final double balance;

  AccountTransaction({
    required this.date,
    required this.description,
    required this.reference,
    required this.debit,
    required this.credit,
    required this.balance,
  });
}
