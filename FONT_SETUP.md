# Arabic Font Setup for PDF Export

## Quick Setup Instructions

To enable proper Arabic text rendering in PDF exports, you need to download and add an Arabic font:

### Step 1: Download the Font
1. Visit [Google Fonts - Noto Sans Arabic](https://fonts.google.com/noto/specimen/Noto+Sans+Arabic)
2. Click "Download family"
3. Extract the downloaded ZIP file
4. Find the file `NotoSansArabic-Regular.ttf`

### Step 2: Add to Project
1. Copy `NotoSansArabic-Regular.ttf` to the `assets/fonts/` directory
2. The final path should be: `assets/fonts/NotoSansArabic-Regular.ttf`

### Step 3: Verify
- The PDF export functionality will automatically use the Arabic font
- If the font is not found, it will fall back to Helvetica (which may not display Arabic correctly)

## What's Implemented

✅ **PDF Export**: Generate professional PDF reports with Arabic text support
✅ **Print**: Direct printing of reports
✅ **Share**: Share reports as PDF files
✅ **Company Branding**: Include company information in PDF headers
✅ **Responsive Layout**: Proper table formatting and section organization
✅ **Error Handling**: Graceful error handling with user feedback

## Features

- **Multi-language Support**: Arabic text rendering with RTL support
- **Professional Layout**: Company header, report sections, and summary
- **Table Format**: Structured data presentation with proper formatting
- **Loading Indicators**: User feedback during PDF generation
- **File Management**: Automatic file naming and storage
- **Cross-platform**: Works on both Windows and Android

## Usage

The PDF export functionality is now available in the Enhanced Report Viewer screen:

1. Open any financial report
2. Click the "تصدير PDF" (Export PDF) button
3. The system will generate a PDF and save it to the device
4. You can open, print, or share the generated PDF

## Technical Details

- Uses the `pdf` and `printing` packages for PDF generation
- Supports Arabic fonts through TTF files
- Implements proper RTL text direction
- Includes company settings integration
- Handles various report types (Balance Sheet, Income Statement, etc.)
