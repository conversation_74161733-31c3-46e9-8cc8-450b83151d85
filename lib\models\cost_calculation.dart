/// نماذج حساب التكلفة
/// Cost Calculation Models for Smart Ledger
library;

enum CostingMethod {
  fifo('fifo', 'الوارد أولاً صادر أولاً'),
  lifo('lifo', 'الوارد أخيراً صادر أولاً'),
  weightedAverage('weighted_average', 'المتوسط المرجح'),
  standardCost('standard_cost', 'التكلفة المعيارية'),
  specificIdentification('specific_identification', 'التحديد المحدد');

  const CostingMethod(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// نموذج طبقة التكلفة
/// Cost Layer Model for tracking inventory costs
class CostLayer {
  final int? id;
  final int itemId;
  final int warehouseId;
  final int? locationId;
  final String? batchNumber;
  final String? serialNumber;
  final double quantity;
  final double unitCost;
  final double totalCost;
  final DateTime receivedDate;
  final String? referenceDocument;
  final int? movementId;
  final DateTime createdAt;
  final DateTime updatedAt;

  CostLayer({
    this.id,
    required this.itemId,
    required this.warehouseId,
    this.locationId,
    this.batchNumber,
    this.serialNumber,
    required this.quantity,
    required this.unitCost,
    required this.totalCost,
    DateTime? receivedDate,
    this.referenceDocument,
    this.movementId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : receivedDate = receivedDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory CostLayer.fromMap(Map<String, dynamic> map) {
    return CostLayer(
      id: map['id'] as int?,
      itemId: map['item_id'] as int,
      warehouseId: map['warehouse_id'] as int,
      locationId: map['location_id'] as int?,
      batchNumber: map['batch_number'] as String?,
      serialNumber: map['serial_number'] as String?,
      quantity: (map['quantity'] as num).toDouble(),
      unitCost: (map['unit_cost'] as num).toDouble(),
      totalCost: (map['total_cost'] as num).toDouble(),
      receivedDate: DateTime.parse(map['received_date'] as String),
      referenceDocument: map['reference_document'] as String?,
      movementId: map['movement_id'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'batch_number': batchNumber,
      'serial_number': serialNumber,
      'quantity': quantity,
      'unit_cost': unitCost,
      'total_cost': totalCost,
      'received_date': receivedDate.toIso8601String(),
      'reference_document': referenceDocument,
      'movement_id': movementId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  CostLayer copyWith({
    int? id,
    int? itemId,
    int? warehouseId,
    int? locationId,
    String? batchNumber,
    String? serialNumber,
    double? quantity,
    double? unitCost,
    double? totalCost,
    DateTime? receivedDate,
    String? referenceDocument,
    int? movementId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CostLayer(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      batchNumber: batchNumber ?? this.batchNumber,
      serialNumber: serialNumber ?? this.serialNumber,
      quantity: quantity ?? this.quantity,
      unitCost: unitCost ?? this.unitCost,
      totalCost: totalCost ?? this.totalCost,
      receivedDate: receivedDate ?? this.receivedDate,
      referenceDocument: referenceDocument ?? this.referenceDocument,
      movementId: movementId ?? this.movementId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CostLayer{itemId: $itemId, quantity: $quantity, unitCost: $unitCost}';
  }
}

/// نموذج نتيجة حساب التكلفة
/// Cost Calculation Result
class CostCalculationResult {
  final double totalCost;
  final double averageCost;
  final List<CostLayerConsumption> consumedLayers;
  final List<CostLayer> remainingLayers;

  CostCalculationResult({
    required this.totalCost,
    required this.averageCost,
    required this.consumedLayers,
    required this.remainingLayers,
  });

  @override
  String toString() {
    return 'CostCalculationResult{totalCost: $totalCost, averageCost: $averageCost}';
  }
}

/// نموذج استهلاك طبقة التكلفة
/// Cost Layer Consumption
class CostLayerConsumption {
  final CostLayer layer;
  final double consumedQuantity;
  final double consumedCost;

  CostLayerConsumption({
    required this.layer,
    required this.consumedQuantity,
    required this.consumedCost,
  });

  @override
  String toString() {
    return 'CostLayerConsumption{layerId: ${layer.id}, consumedQuantity: $consumedQuantity, consumedCost: $consumedCost}';
  }
}

/// نموذج إعدادات التكلفة للصنف
/// Item Costing Settings
class ItemCostingSettings {
  final int? id;
  final int itemId;
  final CostingMethod costingMethod;
  final double? standardCost;
  final bool trackBatches;
  final bool trackSerialNumbers;
  final bool allowNegativeStock;
  final DateTime createdAt;
  final DateTime updatedAt;

  ItemCostingSettings({
    this.id,
    required this.itemId,
    this.costingMethod = CostingMethod.weightedAverage,
    this.standardCost,
    this.trackBatches = false,
    this.trackSerialNumbers = false,
    this.allowNegativeStock = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory ItemCostingSettings.fromMap(Map<String, dynamic> map) {
    return ItemCostingSettings(
      id: map['id'] as int?,
      itemId: map['item_id'] as int,
      costingMethod: CostingMethod.values.firstWhere(
        (e) => e.value == map['costing_method'],
        orElse: () => CostingMethod.weightedAverage,
      ),
      standardCost: (map['standard_cost'] as num?)?.toDouble(),
      trackBatches: (map['track_batches'] as int) == 1,
      trackSerialNumbers: (map['track_serial_numbers'] as int) == 1,
      allowNegativeStock: (map['allow_negative_stock'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'costing_method': costingMethod.value,
      'standard_cost': standardCost,
      'track_batches': trackBatches ? 1 : 0,
      'track_serial_numbers': trackSerialNumbers ? 1 : 0,
      'allow_negative_stock': allowNegativeStock ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  ItemCostingSettings copyWith({
    int? id,
    int? itemId,
    CostingMethod? costingMethod,
    double? standardCost,
    bool? trackBatches,
    bool? trackSerialNumbers,
    bool? allowNegativeStock,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ItemCostingSettings(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      costingMethod: costingMethod ?? this.costingMethod,
      standardCost: standardCost ?? this.standardCost,
      trackBatches: trackBatches ?? this.trackBatches,
      trackSerialNumbers: trackSerialNumbers ?? this.trackSerialNumbers,
      allowNegativeStock: allowNegativeStock ?? this.allowNegativeStock,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ItemCostingSettings{itemId: $itemId, costingMethod: ${costingMethod.arabicName}}';
  }
}

/// نموذج تقرير تحليل التكلفة
/// Cost Analysis Report
class CostAnalysisReport {
  final int itemId;
  final String itemName;
  final String itemCode;
  final double totalQuantity;
  final double totalValue;
  final double averageCost;
  final double lastCost;
  final List<CostLayer> costLayers;
  final DateTime reportDate;

  CostAnalysisReport({
    required this.itemId,
    required this.itemName,
    required this.itemCode,
    required this.totalQuantity,
    required this.totalValue,
    required this.averageCost,
    required this.lastCost,
    required this.costLayers,
    DateTime? reportDate,
  }) : reportDate = reportDate ?? DateTime.now();

  @override
  String toString() {
    return 'CostAnalysisReport{itemName: $itemName, totalQuantity: $totalQuantity, averageCost: $averageCost}';
  }
}
