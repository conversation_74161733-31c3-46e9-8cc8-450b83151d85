/// شاشة إضافة حركة مخزون
/// Add Stock Movement Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/stock_movement.dart';
import '../../models/warehouse.dart';
import '../../models/item.dart';
import '../../services/warehouse_service.dart';
import '../../services/item_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/beautiful_buttons.dart';
import '../../widgets/quantum_loading.dart';
import '../../theme/app_theme.dart';

class AddStockMovementScreen extends StatefulWidget {
  final int? warehouseId;
  final int? itemId;
  final StockMovement? movement; // For editing

  const AddStockMovementScreen({
    super.key,
    this.warehouseId,
    this.itemId,
    this.movement,
  });

  @override
  State<AddStockMovementScreen> createState() => _AddStockMovementScreenState();
}

class _AddStockMovementScreenState extends State<AddStockMovementScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final WarehouseService _warehouseService = WarehouseService();
  final ItemService _itemService = ItemService();

  // Form Controllers
  final _documentNumberController = TextEditingController();
  final _quantityController = TextEditingController();
  final _unitCostController = TextEditingController();
  final _notesController = TextEditingController();
  final _batchNumberController = TextEditingController();
  final _serialNumberController = TextEditingController();

  // Form Data
  MovementType? _selectedType;
  MovementReason? _selectedReason;
  int? _selectedItemId;
  int? _selectedWarehouseId;
  int? _selectedLocationId;
  int? _selectedToWarehouseId;
  int? _selectedToLocationId;
  DateTime _movementDate = DateTime.now();
  DateTime? _expiryDate;

  // Data Lists
  List<Warehouse> _warehouses = [];
  List<Item> _items = [];
  List<WarehouseLocation> _locations = [];
  List<WarehouseLocation> _toLocations = [];

  bool _isLoading = false;
  bool _isLoadingData = true;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _selectedWarehouseId = widget.warehouseId;
    _selectedItemId = widget.itemId;
    _initializeAnimations();
    _loadData();
    _generateDocumentNumber();

    if (widget.movement != null) {
      _loadMovementData();
    }
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  Future<void> _loadData() async {
    try {
      final warehousesResult = await _warehouseService.getActiveWarehouses();
      final itemsResult = await _itemService.getActiveItems();

      if (warehousesResult.isSuccess) {
        setState(() => _warehouses = warehousesResult.data!);
      }

      if (itemsResult.isSuccess) {
        setState(() => _items = itemsResult.data!);
      }

      if (_selectedWarehouseId != null) {
        await _loadWarehouseLocations(_selectedWarehouseId!);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: ${e.toString()}');
    } finally {
      setState(() => _isLoadingData = false);
    }
  }

  void _loadMovementData() {
    final movement = widget.movement!;
    _documentNumberController.text = movement.documentNumber;
    _quantityController.text = movement.quantity.toString();
    _unitCostController.text = movement.unitCost.toString();
    _notesController.text = movement.notes ?? '';
    _batchNumberController.text = movement.batchNumber ?? '';
    _serialNumberController.text = movement.serialNumber ?? '';

    setState(() {
      _selectedType = movement.type;
      _selectedReason = movement.reason;
      _selectedItemId = movement.itemId;
      _selectedWarehouseId = movement.warehouseId;
      _selectedLocationId = movement.locationId;
      _selectedToWarehouseId = movement.toWarehouseId;
      _selectedToLocationId = movement.toLocationId;
      _movementDate = movement.movementDate;
      _expiryDate = movement.expiryDate;
    });
  }

  Future<void> _loadWarehouseLocations(int warehouseId) async {
    try {
      final result = await _warehouseService.getWarehouseLocations(warehouseId);
      if (result.isSuccess) {
        setState(() => _locations = result.data!);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل مواقع المخزن: ${e.toString()}');
    }
  }

  Future<void> _loadToWarehouseLocations(int warehouseId) async {
    try {
      final result = await _warehouseService.getWarehouseLocations(warehouseId);
      if (result.isSuccess) {
        setState(() => _toLocations = result.data!);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل مواقع المخزن المستهدف: ${e.toString()}');
    }
  }

  void _generateDocumentNumber() {
    final now = DateTime.now();
    final docNumber =
        'SM${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.millisecond}';
    _documentNumberController.text = docNumber;
  }

  Future<void> _selectDate(BuildContext context, bool isExpiryDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isExpiryDate
          ? (_expiryDate ?? DateTime.now())
          : _movementDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isExpiryDate) {
          _expiryDate = picked;
        } else {
          _movementDate = picked;
        }
      });
    }
  }

  Future<void> _saveMovement() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final movement = StockMovement(
        id: widget.movement?.id,
        documentNumber: _documentNumberController.text,
        type: _selectedType!,
        reason: _selectedReason!,
        itemId: _selectedItemId!,
        warehouseId: _selectedWarehouseId!,
        locationId: _selectedLocationId,
        toWarehouseId: _selectedToWarehouseId,
        toLocationId: _selectedToLocationId,
        quantity: double.parse(_quantityController.text),
        unitCost: double.parse(_unitCostController.text),
        totalCost:
            double.parse(_quantityController.text) *
            double.parse(_unitCostController.text),
        batchNumber: _batchNumberController.text.isEmpty
            ? null
            : _batchNumberController.text,
        serialNumber: _serialNumberController.text.isEmpty
            ? null
            : _serialNumberController.text,
        expiryDate: _expiryDate,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        movementDate: _movementDate,
        status: MovementStatus.pending,
      );

      final result = widget.movement == null
          ? await _warehouseService.addStockMovement(movement)
          : await _warehouseService.updateStockMovement(movement);

      if (result.isSuccess) {
        _showSuccessSnackBar(
          widget.movement == null
              ? 'تم إضافة الحركة بنجاح'
              : 'تم تحديث الحركة بنجاح',
        );
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        _showErrorSnackBar(result.error!);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ الحركة: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _documentNumberController.dispose();
    _quantityController.dispose();
    _unitCostController.dispose();
    _notesController.dispose();
    _batchNumberController.dispose();
    _serialNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.backgroundColor,
              AppTheme.backgroundColor.withValues(alpha: 0.8),
              AppTheme.surfaceColor,
            ],
          ),
        ),
        child: Column(
          children: [
            // App Bar
            HolographicAppBar(
              title: widget.movement == null
                  ? 'إضافة حركة مخزون'
                  : 'تعديل حركة مخزون',
              subtitle: 'إدارة حركات المخزون',
            ),

            // Content
            Expanded(
              child: _isLoadingData
                  ? const Center(child: QuantumLoading())
                  : _buildForm(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForm() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                // Basic Information Card
                QuantumCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'المعلومات الأساسية',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Document Number
                        QuantumTextField(
                          controller: _documentNumberController,
                          labelText: 'رقم المستند',
                          prefixIcon: Icons.numbers,
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'رقم المستند مطلوب';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Movement Type
                        QuantumDropdown<MovementType>(
                          value: _selectedType,
                          hintText: 'نوع الحركة',
                          prefixIcon: Icons.category,
                          items: MovementType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Text(_getMovementTypeText(type)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value;
                              _selectedReason =
                                  null; // Reset reason when type changes
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'نوع الحركة مطلوب';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Movement Reason
                        if (_selectedType != null)
                          QuantumDropdown<MovementReason>(
                            value: _selectedReason,
                            hintText: 'سبب الحركة',
                            prefixIcon: Icons.info,
                            items: _getReasonsByType(_selectedType!).map((
                              reason,
                            ) {
                              return DropdownMenuItem(
                                value: reason,
                                child: Text(_getMovementReasonText(reason)),
                              );
                            }).toList(),
                            onChanged: (value) =>
                                setState(() => _selectedReason = value),
                            validator: (value) {
                              if (value == null) {
                                return 'سبب الحركة مطلوب';
                              }
                              return null;
                            },
                          ),
                        if (_selectedType != null) const SizedBox(height: 16),

                        // Movement Date
                        InkWell(
                          onTap: () => _selectDate(context, false),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.calendar_today),
                                const SizedBox(width: 12),
                                Text(
                                  'تاريخ الحركة: ${_formatDate(_movementDate)}',
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Item and Warehouse Information Card
                QuantumCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'معلومات الصنف والمخزن',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Item Selection
                        QuantumDropdown<int>(
                          value: _selectedItemId,
                          hintText: 'الصنف',
                          prefixIcon: Icons.inventory,
                          items: _items.map((item) {
                            return DropdownMenuItem(
                              value: item.id,
                              child: Text('${item.code} - ${item.name}'),
                            );
                          }).toList(),
                          onChanged: (value) =>
                              setState(() => _selectedItemId = value),
                          validator: (value) {
                            if (value == null) {
                              return 'الصنف مطلوب';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Warehouse Selection
                        QuantumDropdown<int>(
                          value: _selectedWarehouseId,
                          hintText: 'المخزن',
                          prefixIcon: Icons.warehouse,
                          items: _warehouses.map((warehouse) {
                            return DropdownMenuItem(
                              value: warehouse.id,
                              child: Text(
                                '${warehouse.code} - ${warehouse.name}',
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedWarehouseId = value;
                              _selectedLocationId = null;
                            });
                            if (value != null) {
                              _loadWarehouseLocations(value);
                            }
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'المخزن مطلوب';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Location Selection
                        if (_locations.isNotEmpty)
                          QuantumDropdown<int>(
                            value: _selectedLocationId,
                            hintText: 'الموقع (اختياري)',
                            prefixIcon: Icons.location_on,
                            items: _locations.map((location) {
                              return DropdownMenuItem(
                                value: location.id,
                                child: Text(
                                  '${location.code} - ${location.name}',
                                ),
                              );
                            }).toList(),
                            onChanged: (value) =>
                                setState(() => _selectedLocationId = value),
                          ),
                        if (_locations.isNotEmpty) const SizedBox(height: 16),

                        // Transfer destination (for transfer movements)
                        if (_selectedType == MovementType.transfer) ...[
                          QuantumDropdown<int>(
                            value: _selectedToWarehouseId,
                            hintText: 'المخزن المستهدف',
                            prefixIcon: Icons.warehouse,
                            items: _warehouses
                                .where((w) => w.id != _selectedWarehouseId)
                                .map((warehouse) {
                                  return DropdownMenuItem(
                                    value: warehouse.id,
                                    child: Text(
                                      '${warehouse.code} - ${warehouse.name}',
                                    ),
                                  );
                                })
                                .toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedToWarehouseId = value;
                                _selectedToLocationId = null;
                              });
                              if (value != null) {
                                _loadToWarehouseLocations(value);
                              }
                            },
                            validator: (value) {
                              if (_selectedType == MovementType.transfer &&
                                  value == null) {
                                return 'المخزن المستهدف مطلوب للنقل';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          if (_toLocations.isNotEmpty)
                            QuantumDropdown<int>(
                              value: _selectedToLocationId,
                              hintText: 'الموقع المستهدف (اختياري)',
                              prefixIcon: Icons.location_on,
                              items: _toLocations.map((location) {
                                return DropdownMenuItem(
                                  value: location.id,
                                  child: Text(
                                    '${location.code} - ${location.name}',
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) =>
                                  setState(() => _selectedToLocationId = value),
                            ),
                          if (_toLocations.isNotEmpty)
                            const SizedBox(height: 16),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Quantity and Cost Information Card
                QuantumCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'معلومات الكمية والتكلفة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Quantity
                        QuantumTextField(
                          controller: _quantityController,
                          labelText: 'الكمية',
                          prefixIcon: Icons.numbers,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'^\d+\.?\d{0,3}'),
                            ),
                          ],
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'الكمية مطلوبة';
                            }
                            final quantity = double.tryParse(value!);
                            if (quantity == null || quantity <= 0) {
                              return 'الكمية يجب أن تكون أكبر من صفر';
                            }
                            return null;
                          },
                          onChanged: (value) => _calculateTotalCost(),
                        ),
                        const SizedBox(height: 16),

                        // Unit Cost
                        QuantumTextField(
                          controller: _unitCostController,
                          labelText: 'تكلفة الوحدة',
                          prefixIcon: Icons.attach_money,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'^\d+\.?\d{0,2}'),
                            ),
                          ],
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'تكلفة الوحدة مطلوبة';
                            }
                            final cost = double.tryParse(value!);
                            if (cost == null || cost < 0) {
                              return 'تكلفة الوحدة يجب أن تكون صفر أو أكبر';
                            }
                            return null;
                          },
                          onChanged: (value) => _calculateTotalCost(),
                        ),
                        const SizedBox(height: 16),

                        // Total Cost Display
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calculate),
                              const SizedBox(width: 12),
                              Text(
                                'التكلفة الإجمالية: ${_calculateTotalCost()} ر.س',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Additional Information Card
                QuantumCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'معلومات إضافية',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Batch Number
                        QuantumTextField(
                          controller: _batchNumberController,
                          labelText: 'رقم الدفعة (اختياري)',
                          prefixIcon: Icons.batch_prediction,
                        ),
                        const SizedBox(height: 16),

                        // Serial Number
                        QuantumTextField(
                          controller: _serialNumberController,
                          labelText: 'الرقم التسلسلي (اختياري)',
                          prefixIcon: Icons.qr_code,
                        ),
                        const SizedBox(height: 16),

                        // Expiry Date
                        InkWell(
                          onTap: () => _selectDate(context, true),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.event_available),
                                const SizedBox(width: 12),
                                Text(
                                  _expiryDate == null
                                      ? 'تاريخ انتهاء الصلاحية (اختياري)'
                                      : 'تاريخ انتهاء الصلاحية: ${_formatDate(_expiryDate!)}',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: _expiryDate == null
                                        ? Colors.grey[600]
                                        : null,
                                  ),
                                ),
                                const Spacer(),
                                if (_expiryDate != null)
                                  IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () =>
                                        setState(() => _expiryDate = null),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Notes
                        QuantumTextField(
                          controller: _notesController,
                          labelText: 'الملاحظات (اختياري)',
                          prefixIcon: Icons.note,
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: BeautifulButton(
                    text: widget.movement == null
                        ? 'إضافة الحركة'
                        : 'تحديث الحركة',
                    icon: widget.movement == null ? Icons.add : Icons.update,
                    onPressed: _isLoading ? null : _saveMovement,
                    isLoading: _isLoading,
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _calculateTotalCost() {
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    final unitCost = double.tryParse(_unitCostController.text) ?? 0;
    final total = quantity * unitCost;
    return total.toStringAsFixed(2);
  }

  List<MovementReason> _getReasonsByType(MovementType type) {
    switch (type) {
      case MovementType.receipt:
        return [
          MovementReason.purchase,
          MovementReason.production,
          MovementReason.return_,
          MovementReason.other,
        ];
      case MovementType.issue:
        return [
          MovementReason.sale,
          MovementReason.consumption,
          MovementReason.other,
        ];
      case MovementType.transfer:
        return [MovementReason.transfer];
      case MovementType.adjustment:
        return [MovementReason.adjustment];
      case MovementType.return_:
        return [MovementReason.return_];
      case MovementType.damage:
        return [MovementReason.damage];
      case MovementType.loss:
        return [
          MovementReason.theft,
          MovementReason.expiry,
          MovementReason.other,
        ];
      case MovementType.found:
        return [MovementReason.other];
    }
  }

  String _getMovementTypeText(MovementType type) {
    switch (type) {
      case MovementType.receipt:
        return 'استلام';
      case MovementType.issue:
        return 'صرف';
      case MovementType.transfer:
        return 'نقل';
      case MovementType.adjustment:
        return 'تسوية';
      case MovementType.return_:
        return 'مرتجع';
      case MovementType.damage:
        return 'تالف';
      case MovementType.loss:
        return 'فقدان';
      case MovementType.found:
        return 'عثور';
    }
  }

  String _getMovementReasonText(MovementReason reason) {
    switch (reason) {
      case MovementReason.purchase:
        return 'شراء';
      case MovementReason.sale:
        return 'بيع';
      case MovementReason.production:
        return 'إنتاج';
      case MovementReason.consumption:
        return 'استهلاك';
      case MovementReason.transfer:
        return 'نقل';
      case MovementReason.adjustment:
        return 'تسوية';
      case MovementReason.return_:
        return 'مرتجع';
      case MovementReason.damage:
        return 'تلف';
      case MovementReason.theft:
        return 'سرقة';
      case MovementReason.expiry:
        return 'انتهاء صلاحية';
      case MovementReason.other:
        return 'أخرى';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
