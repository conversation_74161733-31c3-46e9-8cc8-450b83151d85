import 'dart:math' as math;
import 'package:flutter/material.dart';

/// 🌟 مجموعة التأثيرات الثورية الخارقة
/// Revolutionary Extraordinary Effects Collection
///
/// هذا الملف يحتوي على تأثيرات بصرية لا مثيل لها في التاريخ
/// This file contains unprecedented visual effects in history

/// 🎯 تأثير الهولوجرام ثلاثي الأبعاد
/// 3D Hologram Effect
class HologramEffect extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double intensity;

  const HologramEffect({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 3),
    this.intensity = 1.0,
  });

  @override
  State<HologramEffect> createState() => _HologramEffectState();
}

class _HologramEffectState extends State<HologramEffect>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            boxShadow: [
              // تأثير الهولوجرام الأزرق
              BoxShadow(
                color: Colors.cyan.withValues(
                  alpha: 0.3 * widget.intensity * _animation.value,
                ),
                blurRadius: 20 * widget.intensity,
                offset: Offset(-5 * _animation.value, -5 * _animation.value),
                spreadRadius: 2,
              ),
              // تأثير الهولوجرام الوردي
              BoxShadow(
                color: Colors.pink.withValues(
                  alpha: 0.3 * widget.intensity * (1 - _animation.value),
                ),
                blurRadius: 20 * widget.intensity,
                offset: Offset(
                  5 * (1 - _animation.value),
                  5 * (1 - _animation.value),
                ),
                spreadRadius: 2,
              ),
              // تأثير الهولوجرام الأخضر
              BoxShadow(
                color: Colors.green.withValues(
                  alpha: 0.2 * widget.intensity * _animation.value,
                ),
                blurRadius: 30 * widget.intensity,
                offset: Offset(0, 10 * _animation.value),
                spreadRadius: 1,
              ),
            ],
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// 🌌 تأثير الطاقة الكمية
/// Quantum Energy Effect
class QuantumEnergyEffect extends StatefulWidget {
  final Widget child;
  final Color primaryColor;
  final Color secondaryColor;
  final double intensity;

  const QuantumEnergyEffect({
    super.key,
    required this.child,
    this.primaryColor = const Color(0xFF00D4FF),
    this.secondaryColor = const Color(0xFFFF6B6B),
    this.intensity = 1.0,
  });

  @override
  State<QuantumEnergyEffect> createState() => _QuantumEnergyEffectState();
}

class _QuantumEnergyEffectState extends State<QuantumEnergyEffect>
    with TickerProviderStateMixin {
  late AnimationController _energyController;
  late AnimationController _pulseController;
  late Animation<double> _energyAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _energyController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _energyAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _energyController, curve: Curves.easeInOutQuart),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.elasticOut),
    );

    _energyController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _energyController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_energyAnimation, _pulseAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                // طاقة كمية أولى
                BoxShadow(
                  color: widget.primaryColor.withValues(
                    alpha: 0.4 * widget.intensity * _energyAnimation.value,
                  ),
                  blurRadius: 25 * widget.intensity,
                  offset: const Offset(0, 0),
                  spreadRadius: 3,
                ),
                // طاقة كمية ثانية
                BoxShadow(
                  color: widget.secondaryColor.withValues(
                    alpha:
                        0.3 * widget.intensity * (1 - _energyAnimation.value),
                  ),
                  blurRadius: 35 * widget.intensity,
                  offset: const Offset(0, 0),
                  spreadRadius: 5,
                ),
                // نبضة الطاقة المركزية
                BoxShadow(
                  color: Colors.white.withValues(
                    alpha: 0.2 * widget.intensity * _pulseAnimation.value,
                  ),
                  blurRadius: 15 * widget.intensity,
                  offset: const Offset(0, 0),
                  spreadRadius: 1,
                ),
              ],
            ),
            child: widget.child,
          ),
        );
      },
    );
  }
}

/// 🎆 تأثير الجسيمات المتفجرة
/// Particle Explosion Effect
class ParticleExplosionEffect extends StatefulWidget {
  final Widget child;
  final int particleCount;
  final Color particleColor;
  final double explosionRadius;

  const ParticleExplosionEffect({
    super.key,
    required this.child,
    this.particleCount = 20,
    this.particleColor = Colors.white,
    this.explosionRadius = 100.0,
  });

  @override
  State<ParticleExplosionEffect> createState() =>
      _ParticleExplosionEffectState();
}

class _ParticleExplosionEffectState extends State<ParticleExplosionEffect>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  List<Offset> particlePositions = [];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutQuart));

    // إنشاء مواضع الجسيمات العشوائية
    _generateParticlePositions();
    _controller.repeat();
  }

  void _generateParticlePositions() {
    particlePositions.clear();
    for (int i = 0; i < widget.particleCount; i++) {
      final angle = (i / widget.particleCount) * 2 * math.pi;
      final radius = widget.explosionRadius * (0.5 + (i % 3) * 0.25);
      particlePositions.add(
        Offset(radius * math.cos(angle), radius * math.sin(angle)),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            widget.child,
            ...particlePositions.asMap().entries.map((entry) {
              final index = entry.key;
              final position = entry.value;
              final progress = _animation.value;
              final delay = (index / widget.particleCount) * 0.5;
              final adjustedProgress = (progress - delay).clamp(0.0, 1.0);

              return Positioned(
                left: position.dx * adjustedProgress,
                top: position.dy * adjustedProgress,
                child: Opacity(
                  opacity: (1 - adjustedProgress) * 0.8,
                  child: Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: widget.particleColor,
                      borderRadius: BorderRadius.circular(2),
                      boxShadow: [
                        BoxShadow(
                          color: widget.particleColor.withValues(alpha: 0.5),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        );
      },
    );
  }
}

/// 🌈 تأثير الطيف الضوئي
/// Light Spectrum Effect
class LightSpectrumEffect extends StatefulWidget {
  final Widget child;
  final double intensity;
  final Duration duration;

  const LightSpectrumEffect({
    super.key,
    required this.child,
    this.intensity = 1.0,
    this.duration = const Duration(seconds: 3),
  });

  @override
  State<LightSpectrumEffect> createState() => _LightSpectrumEffectState();
}

class _LightSpectrumEffectState extends State<LightSpectrumEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: SweepGradient(
              center: Alignment.center,
              startAngle: _animation.value * 2 * math.pi,
              colors: [
                Colors.red.withValues(alpha: 0.3 * widget.intensity),
                Colors.orange.withValues(alpha: 0.3 * widget.intensity),
                Colors.yellow.withValues(alpha: 0.3 * widget.intensity),
                Colors.green.withValues(alpha: 0.3 * widget.intensity),
                Colors.blue.withValues(alpha: 0.3 * widget.intensity),
                Colors.indigo.withValues(alpha: 0.3 * widget.intensity),
                Colors.purple.withValues(alpha: 0.3 * widget.intensity),
                Colors.red.withValues(alpha: 0.3 * widget.intensity),
              ],
            ),
          ),
          child: widget.child,
        );
      },
    );
  }
}
