import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

/// شاشة إعدادات النظام
class SystemSettingsScreen extends StatefulWidget {
  const SystemSettingsScreen({super.key});

  @override
  State<SystemSettingsScreen> createState() => _SystemSettingsScreenState();
}

class _SystemSettingsScreenState extends State<SystemSettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // إعدادات النظام
  bool _enableNotifications = true;
  bool _enableSoundEffects = true;
  bool _enableHapticFeedback = true;
  bool _enableAnimations = true;
  bool _enableAutoBackup = true;
  bool _enableDarkMode = false;
  
  String _selectedLanguage = 'ar';
  String _selectedTheme = 'system';
  double _fontSize = 14.0;
  int _autoBackupInterval = 24;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _loadSettings();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    // هنا يمكن تحميل الإعدادات من قاعدة البيانات أو SharedPreferences
    // في الوقت الحالي نستخدم القيم الافتراضية
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(child: _buildSettingsContent()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إعدادات النظام',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'إعدادات التطبيق والواجهة والإشعارات',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.settings_applications,
                color: Colors.white,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsContent() {
    return AnimationLimiter(
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            _buildSection(
              'المظهر والواجهة',
              Icons.palette,
              [
                _buildLanguageDropdown(),
                const SizedBox(height: 16),
                _buildThemeDropdown(),
                const SizedBox(height: 16),
                _buildFontSizeSlider(),
                const SizedBox(height: 16),
                _buildSwitchTile(
                  'الوضع المظلم',
                  'تفعيل الوضع المظلم للتطبيق',
                  Icons.dark_mode,
                  _enableDarkMode,
                  (value) => setState(() => _enableDarkMode = value),
                ),
                _buildSwitchTile(
                  'الرسوم المتحركة',
                  'تفعيل الرسوم المتحركة في التطبيق',
                  Icons.animation,
                  _enableAnimations,
                  (value) => setState(() => _enableAnimations = value),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'الإشعارات والأصوات',
              Icons.notifications,
              [
                _buildSwitchTile(
                  'الإشعارات',
                  'تفعيل إشعارات التطبيق',
                  Icons.notifications,
                  _enableNotifications,
                  (value) => setState(() => _enableNotifications = value),
                ),
                _buildSwitchTile(
                  'الأصوات',
                  'تفعيل الأصوات في التطبيق',
                  Icons.volume_up,
                  _enableSoundEffects,
                  (value) => setState(() => _enableSoundEffects = value),
                ),
                _buildSwitchTile(
                  'الاهتزاز',
                  'تفعيل الاهتزاز عند اللمس',
                  Icons.vibration,
                  _enableHapticFeedback,
                  (value) => setState(() => _enableHapticFeedback = value),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'النسخ الاحتياطي',
              Icons.backup,
              [
                _buildSwitchTile(
                  'النسخ الاحتياطي التلقائي',
                  'إنشاء نسخة احتياطية تلقائياً',
                  Icons.backup,
                  _enableAutoBackup,
                  (value) => setState(() => _enableAutoBackup = value),
                ),
                const SizedBox(height: 16),
                _buildBackupIntervalDropdown(),
              ],
            ),
            const SizedBox(height: 32),
            _buildSaveButton(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.05),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.02),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Theme.of(context).primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageDropdown() {
    final languages = [
      {'code': 'ar', 'name': 'العربية'},
      {'code': 'en', 'name': 'English'},
    ];
    
    return DropdownButtonFormField<String>(
      value: _selectedLanguage,
      decoration: InputDecoration(
        labelText: 'اللغة',
        prefixIcon: const Icon(Icons.language),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      items: languages.map((language) {
        return DropdownMenuItem<String>(
          value: language['code'],
          child: Text(language['name']!),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedLanguage = value;
          });
        }
      },
    );
  }

  Widget _buildThemeDropdown() {
    final themes = [
      {'code': 'system', 'name': 'تلقائي (حسب النظام)'},
      {'code': 'light', 'name': 'فاتح'},
      {'code': 'dark', 'name': 'مظلم'},
    ];
    
    return DropdownButtonFormField<String>(
      value: _selectedTheme,
      decoration: InputDecoration(
        labelText: 'المظهر',
        prefixIcon: const Icon(Icons.palette),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      items: themes.map((theme) {
        return DropdownMenuItem<String>(
          value: theme['code'],
          child: Text(theme['name']!),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedTheme = value;
          });
        }
      },
    );
  }

  Widget _buildFontSizeSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حجم الخط: ${_fontSize.toInt()}',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Slider(
          value: _fontSize,
          min: 10.0,
          max: 20.0,
          divisions: 10,
          label: _fontSize.toInt().toString(),
          onChanged: (value) {
            setState(() {
              _fontSize = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildBackupIntervalDropdown() {
    final intervals = [
      {'hours': 6, 'name': 'كل 6 ساعات'},
      {'hours': 12, 'name': 'كل 12 ساعة'},
      {'hours': 24, 'name': 'يومياً'},
      {'hours': 168, 'name': 'أسبوعياً'},
      {'hours': 720, 'name': 'شهرياً'},
    ];
    
    return DropdownButtonFormField<int>(
      value: _autoBackupInterval,
      decoration: InputDecoration(
        labelText: 'فترة النسخ الاحتياطي',
        prefixIcon: const Icon(Icons.schedule),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      items: intervals.map((interval) {
        return DropdownMenuItem<int>(
          value: interval['hours'] as int,
          child: Text(interval['name'] as String),
        );
      }).toList(),
      onChanged: _enableAutoBackup ? (value) {
        if (value != null) {
          setState(() {
            _autoBackupInterval = value;
          });
        }
      } : null,
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
        ),
      ),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        secondary: Icon(icon, color: Theme.of(context).primaryColor),
        value: value,
        onChanged: onChanged,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _saveSettings,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
        ),
        child: const Text(
          'حفظ الإعدادات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    // هنا يمكن حفظ الإعدادات في قاعدة البيانات أو SharedPreferences
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ إعدادات النظام بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
    Navigator.of(context).pop();
  }
}
