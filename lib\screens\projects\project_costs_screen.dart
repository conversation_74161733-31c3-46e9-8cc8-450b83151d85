/// شاشة إدارة تكاليف المشروع
/// Project Costs Management Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/project.dart';
import '../../models/project_cost.dart';
import '../../services/project_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/quantum_loading.dart';
import '../../theme/app_theme.dart';

class ProjectCostsScreen extends StatefulWidget {
  final Project project;

  const ProjectCostsScreen({super.key, required this.project});

  @override
  State<ProjectCostsScreen> createState() => _ProjectCostsScreenState();
}

class _ProjectCostsScreenState extends State<ProjectCostsScreen> {
  final ProjectService _projectService = ProjectService();
  final TextEditingController _searchController = TextEditingController();

  List<ProjectCost> _costs = [];
  List<ProjectCost> _filteredCosts = [];
  bool _isLoading = false;
  CostType? _selectedType;
  String _searchTerm = '';

  @override
  void initState() {
    super.initState();
    _loadCosts();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchTerm = _searchController.text;
      _filterCosts();
    });
  }

  void _filterCosts() {
    _filteredCosts = _costs.where((cost) {
      final matchesSearch =
          _searchTerm.isEmpty ||
          cost.description.toLowerCase().contains(_searchTerm.toLowerCase()) ||
          (cost.notes?.toLowerCase().contains(_searchTerm.toLowerCase()) ??
              false);

      final matchesType =
          _selectedType == null || cost.costType == _selectedType;

      return matchesSearch && matchesType;
    }).toList();
  }

  Future<void> _loadCosts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final costs = await _projectService.projectDao.getProjectCosts(
        widget.project.id!,
      );
      setState(() {
        _costs = costs;
        _filterCosts();
      });
    } catch (e) {
      _showErrorDialog('خطأ في تحميل التكاليف: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _onTypeFilterChanged(CostType? type) {
    setState(() {
      _selectedType = type;
      _filterCosts();
    });
  }

  Future<void> _deleteCost(ProjectCost cost) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف التكلفة "${cost.description}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final result = await _projectService.deleteProjectCost(
          cost.id!,
          widget.project.id!,
        );
        if (result.isSuccess) {
          _loadCosts();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم حذف التكلفة بنجاح')),
            );
          }
        } else {
          _showErrorDialog(result.error ?? 'فشل في حذف التكلفة');
        }
      } catch (e) {
        _showErrorDialog('خطأ في حذف التكلفة: ${e.toString()}');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalCost = _filteredCosts.fold<double>(
      0.0,
      (sum, cost) => sum + cost.amount,
    );

    return Scaffold(
      appBar: AppBar(
        title: Text('تكاليف ${widget.project.name}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: QuantumLoading())
          : Column(
              children: [
                // Header with search and filters
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.surfaceColor,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: QuantumTextField(
                              controller: _searchController,
                              labelText: 'البحث في التكاليف',
                              prefixIcon: Icons.search,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: QuantumDropdown<CostType>(
                              value: _selectedType,
                              items: CostType.values
                                  .map(
                                    (type) => DropdownMenuItem(
                                      value: type,
                                      child: Text(type.nameAr),
                                    ),
                                  )
                                  .toList(),
                              onChanged: _onTypeFilterChanged,
                              labelText: 'فلترة حسب النوع',
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          QuantumButton(
                            onPressed: () => _showAddCostDialog(),
                            text: 'إضافة تكلفة',
                            icon: Icons.add,
                          ),
                          const Spacer(),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                'إجمالي التكاليف: ${totalCost.toStringAsFixed(2)} ر.س',
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.primaryColor,
                                    ),
                              ),
                              Text(
                                'عدد التكاليف: ${_filteredCosts.length}',
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Costs list
                Expanded(
                  child: _filteredCosts.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.receipt_long,
                                size: 64,
                                color: AppTheme.textSecondaryColor,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _costs.isEmpty
                                    ? 'لا توجد تكاليف'
                                    : 'لا توجد تكاليف تطابق البحث',
                                style: Theme.of(context).textTheme.headlineSmall
                                    ?.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredCosts.length,
                          itemBuilder: (context, index) {
                            final cost = _filteredCosts[index];
                            return _buildCostCard(cost);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildCostCard(ProjectCost cost) {
    return QuantumCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cost.description,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${cost.date.day}/${cost.date.month}/${cost.date.year}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${cost.amount.toStringAsFixed(2)} ر.س',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    _buildTypeChip(cost.costType),
                  ],
                ),
              ],
            ),
            if (cost.notes != null && cost.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                cost.notes!,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (cost.isApproved)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'معتمد',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.secondaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'في انتظار الاعتماد',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.accentColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                IconButton(
                  onPressed: () => _deleteCost(cost),
                  icon: const Icon(Icons.delete),
                  color: AppTheme.errorColor,
                  tooltip: 'حذف',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeChip(CostType type) {
    Color backgroundColor;
    Color textColor;

    switch (type) {
      case CostType.labor:
        backgroundColor = AppTheme.primaryColor.withValues(alpha: 0.1);
        textColor = AppTheme.primaryColor;
        break;
      case CostType.material:
        backgroundColor = AppTheme.secondaryColor.withValues(alpha: 0.1);
        textColor = AppTheme.secondaryColor;
        break;
      case CostType.equipment:
        backgroundColor = AppTheme.accentColor.withValues(alpha: 0.1);
        textColor = AppTheme.accentColor;
        break;
      case CostType.overhead:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue;
        break;
      case CostType.subcontractor:
        backgroundColor = AppTheme.textSecondaryColor.withValues(alpha: 0.1);
        textColor = AppTheme.textSecondaryColor;
        break;
      case CostType.travel:
        backgroundColor = Colors.purple.withValues(alpha: 0.1);
        textColor = Colors.purple;
        break;
      case CostType.other:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        type.nameAr,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Future<void> _showAddCostDialog() async {
    final result = await showDialog<ProjectCost>(
      context: context,
      builder: (context) => AddCostDialog(projectId: widget.project.id!),
    );

    if (result != null) {
      await _addCost(result);
    }
  }

  Future<void> _addCost(ProjectCost cost) async {
    setState(() => _isLoading = true);

    try {
      final result = await _projectService.addProjectCost(cost);

      if (result.isSuccess) {
        setState(() {
          _costs.add(result.data!);
          _filterCosts();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة التكلفة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'خطأ في إضافة التكلفة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

/// نافذة إضافة تكلفة جديدة للمشروع
class AddCostDialog extends StatefulWidget {
  final int projectId;

  const AddCostDialog({super.key, required this.projectId});

  @override
  State<AddCostDialog> createState() => _AddCostDialogState();
}

class _AddCostDialogState extends State<AddCostDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _quantityController = TextEditingController();
  final _unitCostController = TextEditingController();
  final _referenceController = TextEditingController();
  final _notesController = TextEditingController();

  CostType _selectedCostType = CostType.material;
  CostCategory _selectedCategory = CostCategory.direct;
  DateTime _selectedDate = DateTime.now();
  bool _isBillable = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _quantityController.text = '1.0';
    _unitCostController.text = '0.0';
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _quantityController.dispose();
    _unitCostController.dispose();
    _referenceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 600,
        height: 700,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF1A1A2E), Color(0xFF16213E), Color(0xFF0F3460)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF2196F3).withValues(alpha: 0.4),
              blurRadius: 30,
              offset: const Offset(0, 15),
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(child: _buildForm()),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF2196F3), Color(0xFF03A9F4)],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.add_circle_outline, color: Colors.white, size: 28),
          const SizedBox(width: AppTheme.spacingMedium),
          const Text(
            'إضافة تكلفة جديدة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Icon(
              Icons.close_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // وصف التكلفة
            QuantumTextField(
              controller: _descriptionController,
              labelText: 'وصف التكلفة *',
              prefixIcon: Icons.description,
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return 'وصف التكلفة مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // نوع التكلفة وفئتها
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<CostType>(
                    value: _selectedCostType,
                    labelText: 'نوع التكلفة *',
                    prefixIcon: Icons.category,
                    items: CostType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.nameAr),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedCostType = value!);
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'نوع التكلفة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: QuantumDropdown<CostCategory>(
                    value: _selectedCategory,
                    labelText: 'فئة التكلفة *',
                    prefixIcon: Icons.label,
                    items: CostCategory.values.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category.nameAr),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedCategory = value!);
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'فئة التكلفة مطلوبة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // المبلغ والكمية
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _amountController,
                    labelText: 'المبلغ الإجمالي *',
                    prefixIcon: Icons.attach_money,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return 'المبلغ مطلوب';
                      }
                      final amount = double.tryParse(value!);
                      if (amount == null || amount <= 0) {
                        return 'المبلغ يجب أن يكون أكبر من صفر';
                      }
                      return null;
                    },
                    onChanged: _calculateUnitCost,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: QuantumTextField(
                    controller: _quantityController,
                    labelText: 'الكمية',
                    prefixIcon: Icons.numbers,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return 'الكمية مطلوبة';
                      }
                      final quantity = double.tryParse(value!);
                      if (quantity == null || quantity <= 0) {
                        return 'الكمية يجب أن تكون أكبر من صفر';
                      }
                      return null;
                    },
                    onChanged: _calculateUnitCost,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // تكلفة الوحدة (للقراءة فقط)
            QuantumTextField(
              controller: _unitCostController,
              labelText: 'تكلفة الوحدة',
              prefixIcon: Icons.calculate,
              readOnly: true,
              enabled: false,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // التاريخ والمرجع
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: _selectDate,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'التاريخ: ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: QuantumTextField(
                    controller: _referenceController,
                    labelText: 'المرجع',
                    prefixIcon: Icons.receipt,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // الملاحظات
            QuantumTextField(
              controller: _notesController,
              labelText: 'ملاحظات',
              prefixIcon: Icons.note,
              maxLines: 3,
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            // قابل للفوترة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(Icons.receipt_long, color: Colors.white70),
                  const SizedBox(width: 12),
                  const Text(
                    'قابل للفوترة',
                    style: TextStyle(color: Colors.white),
                  ),
                  const Spacer(),
                  Switch(
                    value: _isBillable,
                    onChanged: (value) {
                      setState(() => _isBillable = value);
                    },
                    activeColor: const Color(0xFF2196F3),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.2),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: QuantumButton(
              text: 'إلغاء',
              onPressed: () => Navigator.of(context).pop(),
              variant: QuantumButtonVariant.secondary,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: QuantumButton(
              text: 'إضافة التكلفة',
              onPressed: _isLoading ? null : _saveCost,
              variant: QuantumButtonVariant.primary,
              isLoading: _isLoading,
            ),
          ),
        ],
      ),
    );
  }

  void _calculateUnitCost(String? value) {
    final amount = double.tryParse(_amountController.text) ?? 0.0;
    final quantity = double.tryParse(_quantityController.text) ?? 1.0;

    if (quantity > 0) {
      final unitCost = amount / quantity;
      _unitCostController.text = unitCost.toStringAsFixed(2);
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _saveCost() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final cost = ProjectCost(
        projectId: widget.projectId,
        description: _descriptionController.text.trim(),
        costType: _selectedCostType,
        category: _selectedCategory,
        amount: double.parse(_amountController.text),
        quantity: double.parse(_quantityController.text),
        unitCost: double.parse(_unitCostController.text),
        date: _selectedDate,
        reference: _referenceController.text.trim().isEmpty
            ? null
            : _referenceController.text.trim(),
        isBillable: _isBillable,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      Navigator.of(context).pop(cost);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في البيانات: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
