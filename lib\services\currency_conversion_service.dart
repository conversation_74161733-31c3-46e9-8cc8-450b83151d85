import '../models/currency.dart';
import '../models/invoice.dart';
import '../models/journal_entry.dart';
import 'currency_service.dart';
import 'exchange_rate_service.dart';

/// خدمة التحويل التلقائي للعملات
/// تقوم بتحويل المبالغ بين العملات المختلفة وتحديث الحسابات
class CurrencyConversionService {
  final CurrencyService _currencyService = CurrencyService();
  final ExchangeRateService _exchangeRateService = ExchangeRateService();

  /// الحصول على العملة الأساسية للنظام
  Future<Currency?> getBaseCurrency() async {
    final currencies = await _currencyService.getAllCurrencies();
    return currencies.where((c) => c.isBaseCurrency).firstOrNull;
  }

  /// تحويل مبلغ من عملة إلى أخرى
  Future<ConversionResult> convertAmount({
    required double amount,
    required String fromCurrency,
    required String toCurrency,
    DateTime? asOfDate,
  }) async {
    try {
      // إذا كانت العملتان متشابهتان، لا حاجة للتحويل
      if (fromCurrency == toCurrency) {
        return ConversionResult(
          originalAmount: amount,
          convertedAmount: amount,
          fromCurrency: fromCurrency,
          toCurrency: toCurrency,
          exchangeRate: 1.0,
          conversionDate: DateTime.now(),
        );
      }

      // الحصول على سعر الصرف
      final exchangeRate = await _exchangeRateService.getLatestExchangeRate(
        fromCurrency,
        toCurrency,
        asOfDate: asOfDate,
      );

      if (exchangeRate == null) {
        throw Exception('لا يوجد سعر صرف متاح من $fromCurrency إلى $toCurrency');
      }

      // حساب المبلغ المحول
      final convertedAmount = exchangeRate.convertAmount(amount);

      return ConversionResult(
        originalAmount: amount,
        convertedAmount: convertedAmount,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        exchangeRate: exchangeRate.rate,
        conversionDate: exchangeRate.effectiveDate,
        exchangeRateId: exchangeRate.id,
      );
    } catch (e) {
      throw Exception('خطأ في تحويل العملة: ${e.toString()}');
    }
  }

  /// تحويل فاتورة إلى العملة الأساسية
  Future<Invoice> convertInvoiceToBaseCurrency(Invoice invoice) async {
    final baseCurrency = await getBaseCurrency();
    if (baseCurrency == null) {
      throw Exception('لم يتم تحديد العملة الأساسية للنظام');
    }

    // إذا كانت الفاتورة بالعملة الأساسية بالفعل
    if (invoice.currencyCode == baseCurrency.code) {
      return invoice.copyWith(
        baseCurrencyCode: baseCurrency.code,
        baseCurrencySubtotal: invoice.subtotal,
        baseCurrencyTaxAmount: invoice.taxAmount,
        baseCurrencyDiscountAmount: invoice.discountAmount,
        baseCurrencyTotalAmount: invoice.totalAmount,
        baseCurrencyPaidAmount: invoice.paidAmount,
        exchangeRate: 1.0,
      );
    }

    // تحويل المبالغ إلى العملة الأساسية
    final subtotalResult = await convertAmount(
      amount: invoice.subtotal,
      fromCurrency: invoice.currencyCode,
      toCurrency: baseCurrency.code,
    );

    final taxResult = await convertAmount(
      amount: invoice.taxAmount,
      fromCurrency: invoice.currencyCode,
      toCurrency: baseCurrency.code,
    );

    final discountResult = await convertAmount(
      amount: invoice.discountAmount,
      fromCurrency: invoice.currencyCode,
      toCurrency: baseCurrency.code,
    );

    final totalResult = await convertAmount(
      amount: invoice.totalAmount,
      fromCurrency: invoice.currencyCode,
      toCurrency: baseCurrency.code,
    );

    final paidResult = await convertAmount(
      amount: invoice.paidAmount,
      fromCurrency: invoice.currencyCode,
      toCurrency: baseCurrency.code,
    );

    return invoice.copyWith(
      baseCurrencyCode: baseCurrency.code,
      baseCurrencySubtotal: subtotalResult.convertedAmount,
      baseCurrencyTaxAmount: taxResult.convertedAmount,
      baseCurrencyDiscountAmount: discountResult.convertedAmount,
      baseCurrencyTotalAmount: totalResult.convertedAmount,
      baseCurrencyPaidAmount: paidResult.convertedAmount,
      exchangeRate: subtotalResult.exchangeRate,
      updatedAt: DateTime.now(),
    );
  }

  /// تحويل قيد محاسبي إلى العملة الأساسية
  Future<JournalEntry> convertJournalEntryToBaseCurrency(JournalEntry entry) async {
    final baseCurrency = await getBaseCurrency();
    if (baseCurrency == null) {
      throw Exception('لم يتم تحديد العملة الأساسية للنظام');
    }

    // إذا كان القيد بالعملة الأساسية بالفعل
    if (entry.currencyCode == baseCurrency.code) {
      return entry.copyWith(
        baseCurrencyCode: baseCurrency.code,
        baseCurrencyTotalDebit: entry.totalDebit,
        baseCurrencyTotalCredit: entry.totalCredit,
        exchangeRate: 1.0,
      );
    }

    // تحويل المبالغ إلى العملة الأساسية
    final debitResult = await convertAmount(
      amount: entry.totalDebit,
      fromCurrency: entry.currencyCode,
      toCurrency: baseCurrency.code,
    );

    final creditResult = await convertAmount(
      amount: entry.totalCredit,
      fromCurrency: entry.currencyCode,
      toCurrency: baseCurrency.code,
    );

    return entry.copyWith(
      baseCurrencyCode: baseCurrency.code,
      baseCurrencyTotalDebit: debitResult.convertedAmount,
      baseCurrencyTotalCredit: creditResult.convertedAmount,
      exchangeRate: debitResult.exchangeRate,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث أسعار الصرف وإعادة حساب المبالغ
  Future<void> updateExchangeRatesAndRecalculate() async {
    try {
      // تحديث أسعار الصرف من الإنترنت
      await _exchangeRateService.updateExchangeRatesFromAPI();
      
      // يمكن إضافة منطق إعادة حساب الفواتير والقيود هنا
      // بناءً على الأسعار الجديدة
      
    } catch (e) {
      throw Exception('خطأ في تحديث أسعار الصرف: ${e.toString()}');
    }
  }

  /// تنسيق المبلغ حسب العملة
  String formatAmount(double amount, String currencyCode) {
    return _currencyService.formatAmount(amount, currencyCode);
  }

  /// التحقق من صحة العملة
  Future<bool> isCurrencyValid(String currencyCode) async {
    final currencies = await _currencyService.getAllCurrencies();
    return currencies.any((c) => c.code == currencyCode && c.isActive);
  }

  /// الحصول على قائمة العملات النشطة
  Future<List<Currency>> getActiveCurrencies() async {
    final currencies = await _currencyService.getAllCurrencies();
    return currencies.where((c) => c.isActive).toList();
  }
}

/// نتيجة عملية التحويل
class ConversionResult {
  final double originalAmount;
  final double convertedAmount;
  final String fromCurrency;
  final String toCurrency;
  final double exchangeRate;
  final DateTime conversionDate;
  final int? exchangeRateId;

  ConversionResult({
    required this.originalAmount,
    required this.convertedAmount,
    required this.fromCurrency,
    required this.toCurrency,
    required this.exchangeRate,
    required this.conversionDate,
    this.exchangeRateId,
  });

  /// نسبة التغيير
  double get changePercentage {
    if (originalAmount == 0) return 0.0;
    return ((convertedAmount - originalAmount) / originalAmount) * 100;
  }

  /// هل التحويل مربح (المبلغ المحول أكبر)
  bool get isProfitable => convertedAmount > originalAmount;

  /// الفرق بين المبلغين
  double get difference => convertedAmount - originalAmount;

  @override
  String toString() {
    return '$originalAmount $fromCurrency = $convertedAmount $toCurrency (سعر الصرف: $exchangeRate)';
  }
}
