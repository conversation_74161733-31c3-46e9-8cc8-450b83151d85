import '../models/journal_entry.dart';
import '../models/account.dart';
import '../database/journal_entry_dao.dart';
import '../database/account_dao.dart';
import 'account_service.dart';

class JournalEntryService {
  final JournalEntryDao _journalEntryDao = JournalEntryDao();
  final AccountDao _accountDao = AccountDao();

  // Create a new journal entry
  Future<Result<JournalEntry>> createJournalEntry(JournalEntry entry) async {
    try {
      // Validate journal entry
      List<String> errors = entry.validate();
      if (errors.isNotEmpty) {
        return Result.error(errors.join(', '));
      }

      // Check if entry number already exists
      bool numberExists = await _journalEntryDao.isEntryNumberExists(
        entry.entryNumber,
      );
      if (numberExists) {
        return Result.error('رقم القيد موجود مسبقاً');
      }

      // Validate all accounts exist and are active
      for (JournalEntryLine line in entry.lines) {
        Account? account = await _accountDao.getAccountById(line.accountId);
        if (account == null) {
          return Result.error('الحساب ${line.accountId} غير موجود');
        }
        if (!account.isActive) {
          return Result.error('الحساب ${account.name} غير نشط');
        }
      }

      // Check balance
      if (!entry.checkBalance()) {
        return Result.error(
          'القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن',
        );
      }

      // Create the journal entry
      int entryId = await _journalEntryDao.insertJournalEntry(entry);
      JournalEntry createdEntry = entry.copyWith(id: entryId);

      return Result.success(createdEntry);
    } catch (e) {
      return Result.error('خطأ في إنشاء القيد: ${e.toString()}');
    }
  }

  // Update an existing journal entry
  Future<Result<JournalEntry>> updateJournalEntry(JournalEntry entry) async {
    try {
      if (entry.id == null) {
        return Result.error('معرف القيد مطلوب للتحديث');
      }

      // Check if entry exists
      JournalEntry? existingEntry = await _journalEntryDao.getJournalEntryById(
        entry.id!,
      );
      if (existingEntry == null) {
        return Result.error('القيد غير موجود');
      }

      // Check if entry is already posted
      if (existingEntry.isPosted) {
        return Result.error('لا يمكن تعديل قيد مرحل');
      }

      // Validate journal entry
      List<String> errors = entry.validate();
      if (errors.isNotEmpty) {
        return Result.error(errors.join(', '));
      }

      // Check if entry number already exists (excluding current entry)
      bool numberExists = await _journalEntryDao.isEntryNumberExists(
        entry.entryNumber,
        excludeId: entry.id,
      );
      if (numberExists) {
        return Result.error('رقم القيد موجود مسبقاً');
      }

      // Validate all accounts exist and are active
      for (JournalEntryLine line in entry.lines) {
        Account? account = await _accountDao.getAccountById(line.accountId);
        if (account == null) {
          return Result.error('الحساب ${line.accountId} غير موجود');
        }
        if (!account.isActive) {
          return Result.error('الحساب ${account.name} غير نشط');
        }
      }

      // Check balance
      if (!entry.checkBalance()) {
        return Result.error(
          'القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن',
        );
      }

      // Update the journal entry
      await _journalEntryDao.updateJournalEntry(entry);

      return Result.success(entry);
    } catch (e) {
      return Result.error('خطأ في تحديث القيد: ${e.toString()}');
    }
  }

  // Delete a journal entry
  Future<Result<bool>> deleteJournalEntry(int entryId) async {
    try {
      // Check if entry exists
      JournalEntry? entry = await _journalEntryDao.getJournalEntryById(entryId);
      if (entry == null) {
        return Result.error('القيد غير موجود');
      }

      // Check if entry is posted
      if (entry.isPosted) {
        return Result.error('لا يمكن حذف قيد مرحل');
      }

      // Delete the journal entry
      await _journalEntryDao.deleteJournalEntry(entryId);

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في حذف القيد: ${e.toString()}');
    }
  }

  // Post a journal entry (update account balances)
  Future<Result<bool>> postJournalEntry(int entryId) async {
    try {
      // Check if entry exists
      JournalEntry? entry = await _journalEntryDao.getJournalEntryById(entryId);
      if (entry == null) {
        return Result.error('القيد غير موجود');
      }

      // Check if entry is already posted
      if (entry.isPosted) {
        return Result.error('القيد مرحل مسبقاً');
      }

      // Check if entry is balanced
      if (!entry.checkBalance()) {
        return Result.error('لا يمكن ترحيل قيد غير متوازن');
      }

      // Post the entry
      bool success = await _journalEntryDao.postJournalEntry(entryId);
      if (!success) {
        return Result.error('فشل في ترحيل القيد');
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في ترحيل القيد: ${e.toString()}');
    }
  }

  // Unpost a journal entry (reverse account balance updates)
  Future<Result<bool>> unpostJournalEntry(int entryId) async {
    try {
      // Check if entry exists
      JournalEntry? entry = await _journalEntryDao.getJournalEntryById(entryId);
      if (entry == null) {
        return Result.error('القيد غير موجود');
      }

      // Check if entry is posted
      if (!entry.isPosted) {
        return Result.error('القيد غير مرحل');
      }

      // Unpost the entry
      bool success = await _journalEntryDao.unpostJournalEntry(entryId);
      if (!success) {
        return Result.error('فشل في إلغاء ترحيل القيد');
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إلغاء ترحيل القيد: ${e.toString()}');
    }
  }

  // Get journal entry by ID
  Future<Result<JournalEntry>> getJournalEntryById(int entryId) async {
    try {
      JournalEntry? entry = await _journalEntryDao.getJournalEntryById(entryId);
      if (entry == null) {
        return Result.error('القيد غير موجود');
      }
      return Result.success(entry);
    } catch (e) {
      return Result.error('خطأ في جلب القيد: ${e.toString()}');
    }
  }

  // Get journal entry by number
  Future<Result<JournalEntry>> getJournalEntryByNumber(
    String entryNumber,
  ) async {
    try {
      JournalEntry? entry = await _journalEntryDao.getJournalEntryByNumber(
        entryNumber,
      );
      if (entry == null) {
        return Result.error('القيد غير موجود');
      }
      return Result.success(entry);
    } catch (e) {
      return Result.error('خطأ في جلب القيد: ${e.toString()}');
    }
  }

  // Get all journal entries
  Future<Result<List<JournalEntry>>> getAllJournalEntries() async {
    try {
      List<JournalEntry> entries = await _journalEntryDao
          .getAllJournalEntries();
      return Result.success(entries);
    } catch (e) {
      return Result.error('خطأ في جلب القيود: ${e.toString()}');
    }
  }

  // Get posted journal entries
  Future<Result<List<JournalEntry>>> getPostedJournalEntries() async {
    try {
      List<JournalEntry> entries = await _journalEntryDao
          .getPostedJournalEntries();
      return Result.success(entries);
    } catch (e) {
      return Result.error('خطأ في جلب القيود المرحلة: ${e.toString()}');
    }
  }

  // Get unposted journal entries
  Future<Result<List<JournalEntry>>> getUnpostedJournalEntries() async {
    try {
      List<JournalEntry> entries = await _journalEntryDao
          .getUnpostedJournalEntries();
      return Result.success(entries);
    } catch (e) {
      return Result.error('خطأ في جلب القيود غير المرحلة: ${e.toString()}');
    }
  }

  // Get journal entries by date range
  Future<Result<List<JournalEntry>>> getJournalEntriesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      List<JournalEntry> entries = await _journalEntryDao
          .getJournalEntriesByDateRange(startDate, endDate);
      return Result.success(entries);
    } catch (e) {
      return Result.error('خطأ في جلب القيود: ${e.toString()}');
    }
  }

  // Search journal entries
  Future<Result<List<JournalEntry>>> searchJournalEntries(String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getAllJournalEntries();
      }

      List<JournalEntry> entries = await _journalEntryDao.searchJournalEntries(
        query,
      );
      return Result.success(entries);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  // Get next entry number
  Future<Result<String>> getNextEntryNumber() async {
    try {
      String nextNumber = await _journalEntryDao.getNextEntryNumber();
      return Result.success(nextNumber);
    } catch (e) {
      return Result.error('خطأ في توليد رقم القيد: ${e.toString()}');
    }
  }

  // Get journal entry statistics
  Future<Result<Map<String, dynamic>>> getJournalEntryStatistics() async {
    try {
      Map<String, dynamic> stats = await _journalEntryDao
          .getJournalEntryStatistics();
      return Result.success(stats);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات القيود: ${e.toString()}');
    }
  }

  // Create a simple journal entry (2 lines: debit and credit)
  Future<Result<JournalEntry>> createSimpleEntry({
    required String entryNumber,
    required DateTime date,
    required String description,
    required int debitAccountId,
    required int creditAccountId,
    required double amount,
    String? reference,
  }) async {
    try {
      if (amount <= 0) {
        return Result.error('المبلغ يجب أن يكون أكبر من صفر');
      }

      if (debitAccountId == creditAccountId) {
        return Result.error('حساب المدين يجب أن يختلف عن حساب الدائن');
      }

      // Create journal entry lines
      List<JournalEntryLine> lines = [
        JournalEntryLine(
          journalEntryId: 0, // Will be updated after entry creation
          accountId: debitAccountId,
          description: description,
          debitAmount: amount,
          creditAmount: 0.0,
          lineOrder: 1,
        ),
        JournalEntryLine(
          journalEntryId: 0, // Will be updated after entry creation
          accountId: creditAccountId,
          description: description,
          debitAmount: 0.0,
          creditAmount: amount,
          lineOrder: 2,
        ),
      ];

      // Create journal entry
      JournalEntry entry = JournalEntry(
        entryNumber: entryNumber,
        date: date,
        description: description,
        reference: reference,
        lines: lines,
      );

      return await createJournalEntry(entry);
    } catch (e) {
      return Result.error('خطأ في إنشاء القيد البسيط: ${e.toString()}');
    }
  }

  // Validate journal entry before saving
  Future<Result<bool>> validateJournalEntry(JournalEntry entry) async {
    try {
      // Basic validation
      List<String> errors = entry.validate();
      if (errors.isNotEmpty) {
        return Result.error(errors.join(', '));
      }

      // Check if entry number exists (if updating, exclude current entry)
      bool numberExists = await _journalEntryDao.isEntryNumberExists(
        entry.entryNumber,
        excludeId: entry.id,
      );
      if (numberExists) {
        return Result.error('رقم القيد موجود مسبقاً');
      }

      // Validate all accounts
      for (JournalEntryLine line in entry.lines) {
        Account? account = await _accountDao.getAccountById(line.accountId);
        if (account == null) {
          return Result.error('الحساب ${line.accountId} غير موجود');
        }
        if (!account.isActive) {
          return Result.error('الحساب ${account.name} غير نشط');
        }
      }

      // Check balance
      if (!entry.checkBalance()) {
        return Result.error('القيد غير متوازن');
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في التحقق من القيد: ${e.toString()}');
    }
  }

  Future<Result<List<JournalEntryLine>>> getAccountTransactions(
    int accountId,
  ) async {
    try {
      final transactions = await _journalEntryDao.getAccountTransactions(
        accountId,
      );
      return Result.success(transactions);
    } catch (e) {
      return Result.error('خطأ في استرجاع حركات الحساب: ${e.toString()}');
    }
  }
}
