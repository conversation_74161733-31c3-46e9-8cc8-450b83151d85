/// ويدجت التأثيرات البصرية المتقدمة لتطبيق Smart Ledger
/// Advanced Visual Effects Widgets for Smart Ledger Application
library;

import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/app_theme.dart';

/// ويدجت الجسيمات المتحركة
class AnimatedParticles extends StatefulWidget {
  final int particleCount;
  final Color particleColor;
  final double particleSize;
  final Duration animationDuration;

  const AnimatedParticles({
    super.key,
    this.particleCount = 20,
    this.particleColor = AppTheme.primaryColor,
    this.particleSize = 3.0,
    this.animationDuration = const Duration(seconds: 3),
  });

  @override
  State<AnimatedParticles> createState() => _AnimatedParticlesState();
}

class _AnimatedParticlesState extends State<AnimatedParticles>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    )..repeat();

    particles = List.generate(
      widget.particleCount,
      (index) => Particle(
        x: math.Random().nextDouble(),
        y: math.Random().nextDouble(),
        vx: (math.Random().nextDouble() - 0.5) * 0.02,
        vy: (math.Random().nextDouble() - 0.5) * 0.02,
        opacity: math.Random().nextDouble(),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        _updateParticles();
        return CustomPaint(
          painter: ParticlePainter(
            particles: particles,
            color: widget.particleColor,
            size: widget.particleSize,
          ),
          size: Size.infinite,
        );
      },
    );
  }

  void _updateParticles() {
    for (var particle in particles) {
      particle.x += particle.vx;
      particle.y += particle.vy;

      if (particle.x < 0 || particle.x > 1) particle.vx *= -1;
      if (particle.y < 0 || particle.y > 1) particle.vy *= -1;

      particle.x = particle.x.clamp(0.0, 1.0);
      particle.y = particle.y.clamp(0.0, 1.0);
    }
  }
}

class Particle {
  double x, y, vx, vy, opacity;

  Particle({
    required this.x,
    required this.y,
    required this.vx,
    required this.vy,
    required this.opacity,
  });
}

class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final Color color;
  final double size;

  ParticlePainter({
    required this.particles,
    required this.color,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (var particle in particles) {
      paint.color = color.withValues(alpha: particle.opacity);
      canvas.drawCircle(
        Offset(
          particle.x * canvasSize.width,
          particle.y * canvasSize.height,
        ),
        size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// ويدجت التأثير الهولوجرافي
class HolographicCard extends StatefulWidget {
  final Widget child;
  final double borderRadius;
  final EdgeInsets padding;
  final bool enableAnimation;

  const HolographicCard({
    super.key,
    required this.child,
    this.borderRadius = AppTheme.borderRadiusMedium,
    this.padding = const EdgeInsets.all(AppTheme.spacingMedium),
    this.enableAnimation = true,
  });

  @override
  State<HolographicCard> createState() => _HolographicCardState();
}

class _HolographicCardState extends State<HolographicCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppTheme.holographicAnimation,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: AppTheme.holographicCurve),
    );

    if (widget.enableAnimation) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          padding: widget.padding,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: AppTheme.holographicGradient.colors
                  .map((color) => color.withValues(
                        alpha: 0.3 + (_animation.value * 0.4),
                      ))
                  .toList(),
              stops: AppTheme.holographicGradient.stops,
            ),
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3 + (_animation.value * 0.3)),
              width: 1 + (_animation.value * 0.5),
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withValues(alpha: 0.2 + (_animation.value * 0.3)),
                blurRadius: 20 + (_animation.value * 10),
                offset: const Offset(0, 8),
                spreadRadius: _animation.value * 2,
              ),
            ],
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// ويدجت التأثير الزجاجي (Glass Morphism)
class GlassMorphismCard extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final EdgeInsets padding;
  final double opacity;

  const GlassMorphismCard({
    super.key,
    required this.child,
    this.borderRadius = AppTheme.borderRadiusMedium,
    this.padding = const EdgeInsets.all(AppTheme.spacingMedium),
    this.opacity = 0.1,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: AppTheme.getGlassMorphismDecoration(
        borderRadius: borderRadius,
        opacity: opacity,
      ),
      child: child,
    );
  }
}

/// ويدجت الوهج المتحرك
class PulsingGlow extends StatefulWidget {
  final Widget child;
  final Color glowColor;
  final double glowRadius;
  final Duration duration;

  const PulsingGlow({
    super.key,
    required this.child,
    this.glowColor = AppTheme.primaryColor,
    this.glowRadius = 20.0,
    this.duration = const Duration(seconds: 2),
  });

  @override
  State<PulsingGlow> createState() => _PulsingGlowState();
}

class _PulsingGlowState extends State<PulsingGlow>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: widget.glowColor.withValues(alpha: _animation.value * 0.6),
                blurRadius: widget.glowRadius * _animation.value,
                spreadRadius: 2 * _animation.value,
              ),
            ],
          ),
          child: widget.child,
        );
      },
    );
  }
}
