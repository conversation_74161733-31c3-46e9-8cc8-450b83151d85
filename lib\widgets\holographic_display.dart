import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🌌 نظام العرض الهولوجرافي المستقبلي
/// Futuristic Holographic Display System
///
/// هذا الملف يحتوي على نظام عرض هولوجرافي ثلاثي الأبعاد لا مثيل له في التاريخ
/// This file contains unprecedented 3D holographic display system in history

/// 🌟 شاشة العرض الهولوجرافي
/// Holographic Display Screen
class HolographicDisplayScreen extends StatefulWidget {
  const HolographicDisplayScreen({super.key});

  @override
  State<HolographicDisplayScreen> createState() =>
      _HolographicDisplayScreenState();
}

class _HolographicDisplayScreenState extends State<HolographicDisplayScreen>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _particleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _particleAnimation;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );

    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
    _particleController.repeat();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _rotationAnimation,
        _pulseAnimation,
        _particleAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.5,
          child: QuantumEnergyEffect(
            intensity: 2.0 + (_pulseAnimation.value * 0.5),
            primaryColor: const Color(0xFF00BCD4),
            secondaryColor: const Color(0xFF0097A7),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF006064).withValues(alpha: 0.9),
                    const Color(0xFF00838F).withValues(alpha: 0.8),
                    const Color(0xFF0097A7).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF00BCD4).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF00BCD4).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس العرض الهولوجرافي
                  Row(
                    children: [
                      Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(18),
                          decoration: BoxDecoration(
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFF00BCD4).withValues(alpha: 0.9),
                                const Color(0xFF0097A7).withValues(alpha: 0.6),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.5),
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.view_in_ar_rounded,
                            color: Colors.white,
                            size: 36,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🌌 العرض الهولوجرافي',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'تقنية الواقع المعزز ثلاثي الأبعاد',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // العرض الهولوجرافي ثلاثي الأبعاد
                  _buildHolographic3DDisplay(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // مؤشرات الواقع المعزز
                  Row(
                    children: [
                      Expanded(
                        child: _buildARMetric(
                          'دقة العرض',
                          '8K Ultra HD',
                          Icons.high_quality_rounded,
                          const Color(0xFF00BCD4),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: _buildARMetric(
                          'معدل الإطارات',
                          '120 FPS',
                          Icons.speed_rounded,
                          const Color(0xFF0097A7),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  Row(
                    children: [
                      Expanded(
                        child: _buildARMetric(
                          'زمن الاستجابة',
                          '< 1ms',
                          Icons.flash_on_rounded,
                          const Color(0xFF00ACC1),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: _buildARMetric(
                          'التفاعل',
                          'متقدم',
                          Icons.touch_app_rounded,
                          const Color(0xFF26C6DA),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء العرض الهولوجرافي ثلاثي الأبعاد
  Widget _buildHolographic3DDisplay() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: const Color(0xFF00BCD4).withValues(alpha: 0.4),
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // الخلفية الكمية
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                colors: [
                  const Color(0xFF00BCD4).withValues(alpha: 0.1),
                  const Color(0xFF0097A7).withValues(alpha: 0.05),
                  Colors.transparent,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
            ),
          ),

          // الكائن الهولوجرافي المركزي
          Center(
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateX(_rotationAnimation.value * 0.5)
                ..rotateY(_rotationAnimation.value)
                ..rotateZ(_rotationAnimation.value * 0.3),
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF00BCD4).withValues(alpha: 0.8),
                      const Color(0xFF0097A7).withValues(alpha: 0.6),
                      const Color(0xFF00838F).withValues(alpha: 0.4),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.6),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF00BCD4).withValues(alpha: 0.6),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.account_balance_rounded,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ),
          ),

          // الجسيمات المدارية
          ...List.generate(16, (index) {
            final angle = (index / 16) * 2 * math.pi;
            final radius =
                60.0 +
                (math.sin(_particleAnimation.value * 2 * math.pi + index) * 10);
            final centerX = 150.0;
            final centerY = 100.0;
            return Positioned(
              left:
                  centerX +
                  radius * math.cos(angle + _rotationAnimation.value * 2),
              top:
                  centerY +
                  radius * math.sin(angle + _rotationAnimation.value * 2),
              child: Transform.scale(
                scale: 0.5 + (_pulseAnimation.value * 0.3),
                child: Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: const Color(
                      0xFF00BCD4,
                    ).withValues(alpha: 0.7 + (_pulseAnimation.value * 0.3)),
                    borderRadius: BorderRadius.circular(3),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF00BCD4).withValues(alpha: 0.5),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),

          // الحلقات الهولوجرافية
          ...List.generate(3, (index) {
            final radius = 40.0 + (index * 25.0);
            final opacity = 0.6 - (index * 0.15);
            return Center(
              child: Transform.rotate(
                angle: _rotationAnimation.value * (1 + index * 0.5),
                child: Container(
                  width: radius * 2,
                  height: radius * 2,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(
                        0xFF00BCD4,
                      ).withValues(alpha: opacity * _pulseAnimation.value),
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(radius),
                  ),
                ),
              ),
            );
          }),

          // خطوط الطاقة
          ...List.generate(8, (index) {
            final angle = (index / 8) * 2 * math.pi;
            final length = 80.0;
            return Positioned(
              left: 150,
              top: 100,
              child: Transform.rotate(
                angle: angle + _rotationAnimation.value,
                child: Container(
                  width: length,
                  height: 2,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF00BCD4).withValues(alpha: 0.8),
                        Colors.transparent,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// بناء مؤشر الواقع المعزز
  Widget _buildARMetric(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: color.withValues(alpha: 0.4), width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// 🎯 بطاقة التفاعل الهولوجرافي
/// Holographic Interaction Card
class HolographicInteractionCard extends StatefulWidget {
  const HolographicInteractionCard({super.key});

  @override
  State<HolographicInteractionCard> createState() =>
      _HolographicInteractionCardState();
}

class _HolographicInteractionCardState extends State<HolographicInteractionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isInteracting = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return GestureDetector(
          onTapDown: (_) => setState(() => _isInteracting = true),
          onTapUp: (_) => setState(() => _isInteracting = false),
          onTapCancel: () => setState(() => _isInteracting = false),
          child: HologramEffect(
            intensity: _isInteracting ? 3.0 : 1.5 + (_animation.value * 0.5),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF4A148C).withValues(alpha: 0.9),
                    const Color(0xFF6A1B9A).withValues(alpha: 0.8),
                    const Color(0xFF8E24AA).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: const Color(
                    0xFFBA68C8,
                  ).withValues(alpha: _isInteracting ? 0.8 : 0.5),
                  width: _isInteracting ? 4 : 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(
                      0xFFBA68C8,
                    ).withValues(alpha: _isInteracting ? 0.6 : 0.3),
                    blurRadius: _isInteracting ? 30 : 20,
                    offset: const Offset(0, 10),
                    spreadRadius: _isInteracting ? 5 : 2,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس التفاعل
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFBA68C8), Color(0xFFCE93D8)],
                          ),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: const Icon(
                          Icons.gesture_rounded,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Text(
                        '🎯 التفاعل الهولوجرافي',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // منطقة التفاعل
                  Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: const Color(0xFFBA68C8).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Stack(
                      children: [
                        // تأثير التفاعل
                        if (_isInteracting)
                          Container(
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFFBA68C8,
                                  ).withValues(alpha: 0.3),
                                  Colors.transparent,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),

                        // النص التفاعلي
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _isInteracting
                                    ? Icons.touch_app_rounded
                                    : Icons.pan_tool_rounded,
                                color: const Color(0xFFBA68C8),
                                size: 32,
                              ),
                              const SizedBox(height: AppTheme.spacingSmall),
                              Text(
                                _isInteracting ? 'تفاعل نشط!' : 'اضغط للتفاعل',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  // معلومات التفاعل
                  Text(
                    'تقنية التفاعل الهولوجرافي تتيح لك التحكم في البيانات المالية بطريقة ثلاثية الأبعاد مع استجابة فورية وتأثيرات بصرية متقدمة.',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 13,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
