import 'package:flutter/material.dart';
import '../../models/journal_entry.dart';
import '../../models/account.dart';
import '../../services/journal_entry_service.dart';
import '../../services/account_service.dart';
import '../../theme/app_theme.dart';
import 'add_journal_entry_screen.dart';

class JournalEntryDetailsScreen extends StatefulWidget {
  final JournalEntry entry;

  const JournalEntryDetailsScreen({super.key, required this.entry});

  @override
  State<JournalEntryDetailsScreen> createState() => _JournalEntryDetailsScreenState();
}

class _JournalEntryDetailsScreenState extends State<JournalEntryDetailsScreen>
    with SingleTickerProviderStateMixin {
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();
  
  late TabController _tabController;
  late JournalEntry _entry;
  Map<int, Account> _accountsMap = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _entry = widget.entry;
    _loadAccountsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAccountsData() async {
    setState(() => _isLoading = true);
    
    try {
      final result = await _accountService.getAllAccounts();
      if (mounted && result.isSuccess) {
        setState(() {
          _accountsMap = {
            for (var account in result.data!) account.id!: account
          };
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات الحسابات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _refreshEntry() async {
    try {
      final result = await _journalEntryService.getJournalEntryById(_entry.id!);
      if (mounted && result.isSuccess) {
        setState(() {
          _entry = result.data!;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث بيانات القيد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _postEntry() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الترحيل'),
        content: const Text('هل أنت متأكد من ترحيل هذا القيد؟ لن يمكن تعديله بعد الترحيل.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.green),
            child: const Text('ترحيل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);
      
      try {
        final result = await _journalEntryService.postJournalEntry(_entry.id!);
        if (mounted) {
          if (result.isSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم ترحيل القيد بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            _refreshEntry();
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(result.error ?? 'خطأ في ترحيل القيد'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في ترحيل القيد: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text('قيد رقم: ${_entry.entryNumber}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (!_entry.isPosted) ...[
            IconButton(
              onPressed: () async {
                final result = await Navigator.push<bool>(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AddJournalEntryScreen(entry: _entry),
                  ),
                );
                if (result == true) {
                  _refreshEntry();
                }
              },
              icon: const Icon(Icons.edit),
            ),
            IconButton(
              onPressed: _entry.isBalanced ? _postEntry : null,
              icon: const Icon(Icons.check),
            ),
          ],
          IconButton(
            onPressed: _refreshEntry,
            icon: const Icon(Icons.refresh),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'تفاصيل القيد', icon: Icon(Icons.info)),
            Tab(text: 'أسطر القيد', icon: Icon(Icons.list)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDetailsTab(),
                _buildLinesTab(),
              ],
            ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة المعلومات الأساسية
          _buildInfoCard(),
          
          const SizedBox(height: AppTheme.spacingMedium),
          
          // بطاقة الحالة والتوازن
          _buildStatusCard(),
          
          const SizedBox(height: AppTheme.spacingMedium),
          
          // بطاقة المبالغ
          _buildAmountsCard(),
          
          const SizedBox(height: AppTheme.spacingMedium),
          
          // بطاقة الإحصائيات
          _buildStatisticsCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات الأساسية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppTheme.spacingMedium),
            
            _buildDetailRow('رقم القيد', _entry.entryNumber),
            _buildDetailRow('التاريخ', _formatDate(_entry.date)),
            _buildDetailRow('الوصف', _entry.description),
            
            if (_entry.reference != null && _entry.reference!.isNotEmpty)
              _buildDetailRow('المرجع', _entry.reference!),
            
            if (_entry.createdBy != null)
              _buildDetailRow('أنشأ بواسطة', _entry.createdBy!),
            
            _buildDetailRow('تاريخ الإنشاء', _formatDateTime(_entry.createdAt)),
            _buildDetailRow('تاريخ التحديث', _formatDateTime(_entry.updatedAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.assignment, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'الحالة والتوازن',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppTheme.spacingMedium),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatusChip(
                    _entry.isPosted ? 'مرحل' : 'غير مرحل',
                    _entry.isPosted ? Colors.green : Colors.orange,
                    _entry.isPosted ? Icons.check_circle : Icons.pending,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: _buildStatusChip(
                    _entry.isBalanced ? 'متوازن' : 'غير متوازن',
                    _entry.isBalanced ? Colors.green : Colors.red,
                    _entry.isBalanced ? Icons.balance : Icons.warning,
                  ),
                ),
              ],
            ),
            
            if (!_entry.isBalanced) ...[
              const SizedBox(height: AppTheme.spacingMedium),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.red),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'القيد غير متوازن. يجب أن يكون مجموع المدين مساوياً لمجموع الدائن.',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAmountsCard() {
    final totals = _entry.getCalculatedTotals();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.account_balance, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'المبالغ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppTheme.spacingMedium),
            
            Row(
              children: [
                Expanded(
                  child: _buildAmountCard(
                    'إجمالي المدين',
                    totals['debit']!,
                    Colors.green,
                    Icons.add_circle,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: _buildAmountCard(
                    'إجمالي الدائن',
                    totals['credit']!,
                    Colors.red,
                    Icons.remove_circle,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppTheme.spacingMedium),
            
            _buildAmountCard(
              'الفرق',
              totals['difference']!,
              totals['difference']! == 0 ? Colors.green : Colors.orange,
              totals['difference']! == 0 ? Icons.check_circle : Icons.warning,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppTheme.spacingMedium),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'عدد الأسطر',
                    _entry.lines.length.toString(),
                    Icons.list,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: _buildStatCard(
                    'عدد الحسابات',
                    _entry.lines.map((line) => line.accountId).toSet().length.toString(),
                    Icons.account_balance,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinesTab() {
    if (_entry.lines.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد أسطر في هذا القيد',
          style: TextStyle(fontSize: 18, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      itemCount: _entry.lines.length,
      itemBuilder: (context, index) {
        return _buildLineCard(_entry.lines[index], index + 1);
      },
    );
  }

  Widget _buildLineCard(JournalEntryLine line, int lineNumber) {
    final account = _accountsMap[line.accountId];
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس السطر
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'سطر $lineNumber',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: line.isDebit ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: line.isDebit ? Colors.green : Colors.red,
                    ),
                  ),
                  child: Text(
                    line.type,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: line.isDebit ? Colors.green : Colors.red,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppTheme.spacingMedium),
            
            // معلومات الحساب
            if (account != null) ...[
              _buildDetailRow('الحساب', '${account.code} - ${account.name}'),
              if (account.description != null && account.description!.isNotEmpty)
                _buildDetailRow('وصف الحساب', account.description!),
            ] else ...[
              _buildDetailRow('الحساب', 'حساب غير موجود (${line.accountId})'),
            ],
            
            // المبلغ
            _buildDetailRow(
              'المبلغ',
              '${line.amount.toStringAsFixed(2)} ر.س',
            ),
            
            // وصف السطر
            if (line.description != null && line.description!.isNotEmpty)
              _buildDetailRow('الوصف', line.description!),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountCard(String title, double amount, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} ر.س',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
