import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/fixed_asset.dart';
import '../../services/fixed_asset_service.dart';
import 'fixed_asset_form_screen.dart';
import 'fixed_asset_details_screen.dart';

/// شاشة قائمة الأصول الثابتة
class FixedAssetsListScreen extends StatefulWidget {
  const FixedAssetsListScreen({super.key});

  @override
  State<FixedAssetsListScreen> createState() => _FixedAssetsListScreenState();
}

class _FixedAssetsListScreenState extends State<FixedAssetsListScreen> {
  final FixedAssetService _assetService = FixedAssetService();
  final TextEditingController _searchController = TextEditingController();

  List<FixedAsset> _assets = [];
  List<FixedAsset> _filteredAssets = [];
  bool _isLoading = true;
  String? _error;
  AssetCategory? _selectedCategory;
  AssetStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _loadAssets();
    _searchController.addListener(_filterAssets);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAssets() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    final result = await _assetService.getAllFixedAssets();

    if (mounted) {
      setState(() {
        _isLoading = false;
        if (result.isSuccess) {
          _assets = result.data!;
          _filterAssets();
        } else {
          _error = result.error;
        }
      });
    }
  }

  void _filterAssets() {
    final query = _searchController.text.toLowerCase();

    setState(() {
      _filteredAssets = _assets.where((asset) {
        final matchesSearch =
            query.isEmpty ||
            asset.code.toLowerCase().contains(query) ||
            asset.name.toLowerCase().contains(query) ||
            (asset.description?.toLowerCase().contains(query) ?? false);

        final matchesCategory =
            _selectedCategory == null || asset.category == _selectedCategory;

        final matchesStatus =
            _selectedStatus == null || asset.status == _selectedStatus;

        return matchesSearch && matchesCategory && matchesStatus;
      }).toList();
    });
  }

  void _navigateToAssetForm([FixedAsset? asset]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FixedAssetFormScreen(asset: asset),
      ),
    );

    if (result == true) {
      _loadAssets();
    }
  }

  void _navigateToAssetDetails(FixedAsset asset) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FixedAssetDetailsScreen(asset: asset),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأصول الثابتة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToAssetForm(),
            tooltip: 'إضافة أصل ثابت',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(child: _buildContent()),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الأصول الثابتة...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).scaffoldBackgroundColor,
            ),
          ),
          const SizedBox(height: 12),
          // المرشحات
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<AssetCategory>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'الفئة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<AssetCategory>(
                      value: null,
                      child: Text('جميع الفئات'),
                    ),
                    ...AssetCategory.values.map(
                      (category) => DropdownMenuItem(
                        value: category,
                        child: Text(_getCategoryName(category)),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value;
                    });
                    _filterAssets();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<AssetStatus>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'الحالة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<AssetStatus>(
                      value: null,
                      child: Text('جميع الحالات'),
                    ),
                    ...AssetStatus.values.map(
                      (status) => DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusName(status)),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                    _filterAssets();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAssets,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_filteredAssets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.account_balance, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(_assets.isEmpty ? 'لا توجد أصول ثابتة' : 'لا توجد نتائج'),
            const SizedBox(height: 8),
            Text(
              _assets.isEmpty
                  ? 'ابدأ بإضافة أول أصل ثابت'
                  : 'جرب تغيير معايير البحث',
              style: TextStyle(color: Colors.grey[600]),
            ),
            if (_assets.isEmpty) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _navigateToAssetForm(),
                child: const Text('إضافة أصل ثابت'),
              ),
            ],
          ],
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredAssets.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildAssetCard(_filteredAssets[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAssetCard(FixedAsset asset) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToAssetDetails(asset),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getCategoryColor(
                        asset.category,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(asset.category),
                      color: _getCategoryColor(asset.category),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          asset.name,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          asset.code,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        asset.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusName(asset.status),
                      style: TextStyle(
                        color: _getStatusColor(asset.status),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'سعر الشراء',
                      '${asset.purchasePrice.toStringAsFixed(2)} ر.س',
                      Icons.attach_money,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'القيمة الدفترية',
                      '${asset.getCurrentBookValue().toStringAsFixed(2)} ر.س',
                      Icons.account_balance_wallet,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'تاريخ الشراء',
                      '${asset.purchaseDate.day}/${asset.purchaseDate.month}/${asset.purchaseDate.year}',
                      Icons.calendar_today,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'العمر الإنتاجي',
                      '${asset.usefulLifeYears} سنة',
                      Icons.schedule,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 11, color: Colors.grey[600]),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getCategoryName(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return 'مباني';
      case AssetCategory.machinery:
        return 'آلات ومعدات';
      case AssetCategory.vehicle:
        return 'مركبات';
      case AssetCategory.furniture:
        return 'أثاث ومفروشات';
      case AssetCategory.computer:
        return 'أجهزة حاسوب';
      case AssetCategory.other:
        return 'أخرى';
    }
  }

  String _getStatusName(AssetStatus status) {
    switch (status) {
      case AssetStatus.active:
        return 'نشط';
      case AssetStatus.inactive:
        return 'غير نشط';
      case AssetStatus.disposed:
        return 'مستبعد';
      case AssetStatus.underMaintenance:
        return 'تحت الصيانة';
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return Icons.business;
      case AssetCategory.machinery:
        return Icons.precision_manufacturing;
      case AssetCategory.vehicle:
        return Icons.directions_car;
      case AssetCategory.furniture:
        return Icons.chair;
      case AssetCategory.computer:
        return Icons.computer;
      case AssetCategory.other:
        return Icons.category;
    }
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return Colors.brown;
      case AssetCategory.machinery:
        return Colors.orange;
      case AssetCategory.vehicle:
        return Colors.blue;
      case AssetCategory.furniture:
        return Colors.green;
      case AssetCategory.computer:
        return Colors.purple;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  Color _getStatusColor(AssetStatus status) {
    switch (status) {
      case AssetStatus.active:
        return Colors.green;
      case AssetStatus.inactive:
        return Colors.orange;
      case AssetStatus.disposed:
        return Colors.red;
      case AssetStatus.underMaintenance:
        return Colors.blue;
    }
  }
}
