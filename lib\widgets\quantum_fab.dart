/// زر عائم كمي متقدم لتطبيق Smart Ledger
/// Advanced Quantum Floating Action Button for Smart Ledger Application
library;

import 'package:flutter/material.dart';

/// زر عائم كمي مع تأثيرات بصرية متقدمة
/// Quantum floating action button with advanced visual effects
class QuantumFAB extends StatefulWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double size;
  final bool enableGlowEffect;
  final bool enableHoverEffect;
  final bool enablePulseEffect;
  final String? heroTag;

  const QuantumFAB({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.size = 56.0,
    this.enableGlowEffect = true,
    this.enableHoverEffect = true,
    this.enablePulseEffect = true,
    this.heroTag,
  });

  @override
  State<QuantumFAB> createState() => _QuantumFABState();
}

class _QuantumFABState extends State<QuantumFAB> with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _glowController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  late Animation<double> _pulseAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    if (widget.enableGlowEffect) {
      _glowController.repeat(reverse: true);
    }

    if (widget.enablePulseEffect) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _glowController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (!widget.enableHoverEffect || widget.onPressed == null) return;

    setState(() => _isHovered = isHovered);

    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = widget.backgroundColor ?? theme.primaryColor;
    final foregroundColor = widget.foregroundColor ?? Colors.white;
    final isDisabled = widget.onPressed == null;

    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimation,
        _glowAnimation,
        _pulseAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * _pulseAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _onHover(true),
            onExit: (_) => _onHover(false),
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                  if (widget.enableGlowEffect && !isDisabled)
                    BoxShadow(
                      color: backgroundColor.withValues(
                        alpha: _glowAnimation.value * 0.6,
                      ),
                      blurRadius: 20,
                      offset: const Offset(0, 0),
                    ),
                  if (_isHovered && !isDisabled)
                    BoxShadow(
                      color: backgroundColor.withValues(alpha: 0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 6),
                    ),
                ],
              ),
              child: FloatingActionButton(
                onPressed: widget.onPressed,
                tooltip: widget.tooltip,
                heroTag: widget.heroTag,
                backgroundColor: backgroundColor.withValues(
                  alpha: isDisabled ? 0.5 : 1.0,
                ),
                foregroundColor: foregroundColor.withValues(
                  alpha: isDisabled ? 0.5 : 1.0,
                ),
                elevation: 0,
                highlightElevation: 0,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        backgroundColor.withValues(
                          alpha: isDisabled ? 0.5 : 1.0,
                        ),
                        backgroundColor.withValues(
                          alpha: isDisabled ? 0.3 : 0.8,
                        ),
                      ],
                    ),
                  ),
                  child: Icon(
                    widget.icon,
                    color: foregroundColor.withValues(
                      alpha: isDisabled ? 0.5 : 1.0,
                    ),
                    size: widget.size * 0.4,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// زر عائم كمي مبسط للاستخدام السريع
/// Simple quantum FAB for quick usage
class SimpleQuantumFAB extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;

  const SimpleQuantumFAB({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return QuantumFAB(
      onPressed: onPressed,
      icon: icon,
      tooltip: tooltip,
      enableGlowEffect: false,
      enablePulseEffect: false,
    );
  }
}

/// زر عائم كمي صغير
/// Small quantum FAB
class SmallQuantumFAB extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const SmallQuantumFAB({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return QuantumFAB(
      onPressed: onPressed,
      icon: icon,
      tooltip: tooltip,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      size: 40.0,
    );
  }
}
