import '../models/exchange_rate.dart';
import '../utils/result.dart';
import 'database_helper.dart';
import 'database_schema.dart';

/// DAO لإدارة أسعار الصرف
class ExchangeRateDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إدراج سعر صرف جديد
  Future<Result<int>> insertExchangeRate(ExchangeRate exchangeRate) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        DatabaseSchema.tableExchangeRates,
        exchangeRate.toMap()..remove('id'),
      );
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إدراج سعر الصرف: ${e.toString()}');
    }
  }

  /// تحديث سعر صرف موجود
  Future<Result<bool>> updateExchangeRate(ExchangeRate exchangeRate) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.update(
        DatabaseSchema.tableExchangeRates,
        exchangeRate.toMap(),
        where: 'id = ?',
        whereArgs: [exchangeRate.id],
      );
      return Result.success(count > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث سعر الصرف: ${e.toString()}');
    }
  }

  /// حذف سعر صرف
  Future<Result<bool>> deleteExchangeRate(int id) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.delete(
        DatabaseSchema.tableExchangeRates,
        where: 'id = ?',
        whereArgs: [id],
      );
      return Result.success(count > 0);
    } catch (e) {
      return Result.error('خطأ في حذف سعر الصرف: ${e.toString()}');
    }
  }

  /// الحصول على سعر صرف بالمعرف
  Future<ExchangeRate?> getExchangeRateById(int id) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableExchangeRates,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return ExchangeRate.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على أحدث سعر صرف فعال
  Future<ExchangeRate?> getLatestExchangeRate(
    String fromCurrency,
    String toCurrency, {
    DateTime? asOfDate,
  }) async {
    try {
      final db = await _dbHelper.database;
      final date = asOfDate ?? DateTime.now();
      
      final maps = await db.query(
        DatabaseSchema.tableExchangeRates,
        where: '''
          from_currency_code = ? AND 
          to_currency_code = ? AND 
          is_active = 1 AND 
          effective_date <= ? AND 
          (expiry_date IS NULL OR expiry_date > ?)
        ''',
        whereArgs: [fromCurrency, toCurrency, date.toIso8601String(), date.toIso8601String()],
        orderBy: 'effective_date DESC',
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return ExchangeRate.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع أسعار الصرف
  Future<List<ExchangeRate>> getAllExchangeRates() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableExchangeRates,
        orderBy: 'from_currency_code ASC, to_currency_code ASC, effective_date DESC',
      );

      return maps.map((map) => ExchangeRate.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على أسعار الصرف النشطة
  Future<List<ExchangeRate>> getActiveExchangeRates() async {
    try {
      final db = await _dbHelper.database;
      final now = DateTime.now().toIso8601String();
      
      final maps = await db.query(
        DatabaseSchema.tableExchangeRates,
        where: '''
          is_active = 1 AND 
          effective_date <= ? AND 
          (expiry_date IS NULL OR expiry_date > ?)
        ''',
        whereArgs: [now, now],
        orderBy: 'from_currency_code ASC, to_currency_code ASC, effective_date DESC',
      );

      return maps.map((map) => ExchangeRate.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على أسعار الصرف لعملة معينة
  Future<List<ExchangeRate>> getExchangeRatesForCurrency(String currencyCode) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableExchangeRates,
        where: 'from_currency_code = ? OR to_currency_code = ?',
        whereArgs: [currencyCode, currencyCode],
        orderBy: 'effective_date DESC',
      );

      return maps.map((map) => ExchangeRate.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// البحث في أسعار الصرف
  Future<List<ExchangeRate>> searchExchangeRates(String query) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        DatabaseSchema.tableExchangeRates,
        where: '''
          from_currency_code LIKE ? OR 
          to_currency_code LIKE ? OR 
          notes LIKE ?
        ''',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'effective_date DESC',
      );

      return maps.map((map) => ExchangeRate.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// تفعيل/إلغاء تفعيل سعر صرف
  Future<Result<bool>> toggleExchangeRateStatus(int id, bool isActive) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.update(
        DatabaseSchema.tableExchangeRates,
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
      return Result.success(count > 0);
    } catch (e) {
      return Result.error('خطأ في تغيير حالة سعر الصرف: ${e.toString()}');
    }
  }

  /// إدراج سجل في تاريخ أسعار الصرف
  Future<Result<int>> insertExchangeRateHistory(ExchangeRateHistory history) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        DatabaseSchema.tableExchangeRateHistory,
        history.toMap()..remove('id'),
      );
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إدراج تاريخ سعر الصرف: ${e.toString()}');
    }
  }

  /// الحصول على تاريخ أسعار الصرف
  Future<List<ExchangeRateHistory>> getExchangeRateHistory(
    String fromCurrency,
    String toCurrency, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      String whereClause = 'from_currency_code = ? AND to_currency_code = ?';
      List<dynamic> whereArgs = [fromCurrency, toCurrency];
      
      if (startDate != null) {
        whereClause += ' AND date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }
      
      if (endDate != null) {
        whereClause += ' AND date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }
      
      final maps = await db.query(
        DatabaseSchema.tableExchangeRateHistory,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'date DESC',
        limit: limit,
      );

      return maps.map((map) => ExchangeRateHistory.fromMap(map)).toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على إحصائيات أسعار الصرف
  Future<Map<String, dynamic>> getExchangeRateStats() async {
    try {
      final db = await _dbHelper.database;
      
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${DatabaseSchema.tableExchangeRates}',
      );
      
      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as active FROM ${DatabaseSchema.tableExchangeRates} WHERE is_active = 1',
      );
      
      final currencyPairsResult = await db.rawQuery(
        'SELECT COUNT(DISTINCT from_currency_code || to_currency_code) as pairs FROM ${DatabaseSchema.tableExchangeRates}',
      );

      final historyCountResult = await db.rawQuery(
        'SELECT COUNT(*) as history_count FROM ${DatabaseSchema.tableExchangeRateHistory}',
      );

      return {
        'total_rates': (totalResult.first['total'] as int?) ?? 0,
        'active_rates': (activeResult.first['active'] as int?) ?? 0,
        'currency_pairs': (currencyPairsResult.first['pairs'] as int?) ?? 0,
        'history_records': (historyCountResult.first['history_count'] as int?) ?? 0,
      };
    } catch (e) {
      return {
        'total_rates': 0,
        'active_rates': 0,
        'currency_pairs': 0,
        'history_records': 0,
      };
    }
  }
}
