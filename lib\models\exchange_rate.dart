/// نموذج سعر الصرف
class ExchangeRate {
  final int? id;
  final String fromCurrencyCode;
  final String toCurrencyCode;
  final double rate;
  final DateTime effectiveDate;
  final DateTime? expiryDate;
  final ExchangeRateSource source;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  ExchangeRate({
    this.id,
    required this.fromCurrencyCode,
    required this.toCurrencyCode,
    required this.rate,
    required this.effectiveDate,
    this.expiryDate,
    this.source = ExchangeRateSource.manual,
    this.isActive = true,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from database map
  factory ExchangeRate.fromMap(Map<String, dynamic> map) {
    return ExchangeRate(
      id: map['id'] as int?,
      fromCurrencyCode: map['from_currency_code'] as String,
      toCurrencyCode: map['to_currency_code'] as String,
      rate: (map['rate'] as num).toDouble(),
      effectiveDate: DateTime.parse(map['effective_date'] as String),
      expiryDate: map['expiry_date'] != null 
          ? DateTime.parse(map['expiry_date'] as String) 
          : null,
      source: ExchangeRateSource.fromString(map['source'] as String? ?? 'manual'),
      isActive: (map['is_active'] as int) == 1,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'from_currency_code': fromCurrencyCode,
      'to_currency_code': toCurrencyCode,
      'rate': rate,
      'effective_date': effectiveDate.toIso8601String(),
      'expiry_date': expiryDate?.toIso8601String(),
      'source': source.value,
      'is_active': isActive ? 1 : 0,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  ExchangeRate copyWith({
    int? id,
    String? fromCurrencyCode,
    String? toCurrencyCode,
    double? rate,
    DateTime? effectiveDate,
    DateTime? expiryDate,
    ExchangeRateSource? source,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExchangeRate(
      id: id ?? this.id,
      fromCurrencyCode: fromCurrencyCode ?? this.fromCurrencyCode,
      toCurrencyCode: toCurrencyCode ?? this.toCurrencyCode,
      rate: rate ?? this.rate,
      effectiveDate: effectiveDate ?? this.effectiveDate,
      expiryDate: expiryDate ?? this.expiryDate,
      source: source ?? this.source,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  String get currencyPair => '$fromCurrencyCode/$toCurrencyCode';
  
  bool get isExpired => expiryDate != null && DateTime.now().isAfter(expiryDate!);
  
  bool get isEffective => DateTime.now().isAfter(effectiveDate) && !isExpired;

  double convertAmount(double amount) {
    return amount * rate;
  }

  double convertAmountReverse(double amount) {
    return amount / rate;
  }

  @override
  String toString() => '$currencyPair: $rate';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExchangeRate &&
          runtimeType == other.runtimeType &&
          fromCurrencyCode == other.fromCurrencyCode &&
          toCurrencyCode == other.toCurrencyCode &&
          effectiveDate == other.effectiveDate;

  @override
  int get hashCode =>
      fromCurrencyCode.hashCode ^
      toCurrencyCode.hashCode ^
      effectiveDate.hashCode;
}

/// مصدر سعر الصرف
enum ExchangeRateSource {
  manual('manual', 'يدوي', 'Manual'),
  centralBank('central_bank', 'البنك المركزي', 'Central Bank'),
  commercialBank('commercial_bank', 'البنك التجاري', 'Commercial Bank'),
  api('api', 'واجهة برمجية', 'API'),
  market('market', 'السوق', 'Market');

  const ExchangeRateSource(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static ExchangeRateSource fromString(String value) {
    return ExchangeRateSource.values.firstWhere(
      (source) => source.value == value,
      orElse: () => ExchangeRateSource.manual,
    );
  }

  String get displayName => nameAr;
}

/// تاريخ أسعار الصرف
class ExchangeRateHistory {
  final int? id;
  final String fromCurrencyCode;
  final String toCurrencyCode;
  final double rate;
  final DateTime date;
  final ExchangeRateSource source;
  final String? notes;
  final DateTime createdAt;

  ExchangeRateHistory({
    this.id,
    required this.fromCurrencyCode,
    required this.toCurrencyCode,
    required this.rate,
    required this.date,
    this.source = ExchangeRateSource.manual,
    this.notes,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  // Factory constructor from database map
  factory ExchangeRateHistory.fromMap(Map<String, dynamic> map) {
    return ExchangeRateHistory(
      id: map['id'] as int?,
      fromCurrencyCode: map['from_currency_code'] as String,
      toCurrencyCode: map['to_currency_code'] as String,
      rate: (map['rate'] as num).toDouble(),
      date: DateTime.parse(map['date'] as String),
      source: ExchangeRateSource.fromString(map['source'] as String? ?? 'manual'),
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'from_currency_code': fromCurrencyCode,
      'to_currency_code': toCurrencyCode,
      'rate': rate,
      'date': date.toIso8601String(),
      'source': source.value,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get currencyPair => '$fromCurrencyCode/$toCurrencyCode';

  @override
  String toString() => '$currencyPair: $rate (${date.toString().split(' ')[0]})';
}

/// نتيجة تحويل العملة
class CurrencyConversion {
  final String fromCurrencyCode;
  final String toCurrencyCode;
  final double fromAmount;
  final double toAmount;
  final double exchangeRate;
  final DateTime conversionDate;
  final ExchangeRateSource source;

  CurrencyConversion({
    required this.fromCurrencyCode,
    required this.toCurrencyCode,
    required this.fromAmount,
    required this.toAmount,
    required this.exchangeRate,
    required this.conversionDate,
    required this.source,
  });

  String get currencyPair => '$fromCurrencyCode/$toCurrencyCode';
  
  String get formattedConversion => 
      '$fromAmount $fromCurrencyCode = $toAmount $toCurrencyCode';

  @override
  String toString() => formattedConversion;
}
