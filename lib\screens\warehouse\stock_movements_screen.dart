/// شاشة حركات المخزون
/// Stock Movements Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/stock_movement.dart';
import '../../models/warehouse.dart';
import '../../models/item.dart';
import '../../services/warehouse_service.dart';
import '../../services/item_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_search_bar.dart';
import '../../widgets/quantum_loading.dart';
import '../../widgets/quantum_fab.dart';
import '../../widgets/quantum_dropdown.dart';

import 'add_stock_movement_screen.dart';
import 'stock_movement_details_screen.dart';

class StockMovementsScreen extends StatefulWidget {
  final int? warehouseId;
  final int? itemId;

  const StockMovementsScreen({super.key, this.warehouseId, this.itemId});

  @override
  State<StockMovementsScreen> createState() => _StockMovementsScreenState();
}

class _StockMovementsScreenState extends State<StockMovementsScreen>
    with TickerProviderStateMixin {
  final WarehouseService _warehouseService = WarehouseService();
  final ItemService _itemService = ItemService();
  final TextEditingController _searchController = TextEditingController();

  List<StockMovement> _movements = [];
  List<StockMovement> _filteredMovements = [];
  List<Warehouse> _warehouses = [];
  List<Item> _items = [];
  bool _isLoading = true;
  String _searchQuery = '';
  MovementType? _selectedType;
  MovementStatus? _selectedStatus;
  int? _selectedWarehouseId;
  int? _selectedItemId;
  DateTime? _startDate;
  DateTime? _endDate;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _selectedWarehouseId = widget.warehouseId;
    _selectedItemId = widget.itemId;
    _initializeAnimations();
    _loadData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل البيانات الأساسية
      final warehousesResult = await _warehouseService.getActiveWarehouses();
      final itemsResult = await _itemService.getActiveItems();

      if (warehousesResult.isSuccess) {
        _warehouses = warehousesResult.data!;
      }

      if (itemsResult.isSuccess) {
        _items = itemsResult.data!;
      }

      // تحميل الحركات
      await _loadMovements();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMovements() async {
    try {
      final result = await _warehouseService.getMovements(
        warehouseId: _selectedWarehouseId,
        itemId: _selectedItemId,
        type: _selectedType,
        status: _selectedStatus,
        startDate: _startDate,
        endDate: _endDate,
      );

      if (result.isSuccess) {
        setState(() {
          _movements = result.data!;
          _filterMovements();
        });
      } else {
        _showErrorSnackBar(result.error!);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل الحركات: ${e.toString()}');
    }
  }

  void _filterMovements() {
    setState(() {
      _filteredMovements = _movements.where((movement) {
        final matchesSearch =
            _searchQuery.isEmpty ||
            movement.documentNumber.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            (movement.notes?.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ??
                false);

        return matchesSearch;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _filterMovements();
    });
  }

  Future<void> _showFilterDialog() async {
    await showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        selectedType: _selectedType,
        selectedStatus: _selectedStatus,
        selectedWarehouseId: _selectedWarehouseId,
        selectedItemId: _selectedItemId,
        startDate: _startDate,
        endDate: _endDate,
        warehouses: _warehouses,
        items: _items,
        onApply: (type, status, warehouseId, itemId, startDate, endDate) {
          setState(() {
            _selectedType = type;
            _selectedStatus = status;
            _selectedWarehouseId = warehouseId;
            _selectedItemId = itemId;
            _startDate = startDate;
            _endDate = endDate;
          });
          _loadMovements();
        },
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              theme.primaryColor.withValues(alpha: 0.1),
              theme.colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          children: [
            // App Bar
            HolographicAppBar(
              title: 'حركات المخزون',
              subtitle: 'إدارة وتتبع حركات المخزون',
              actions: [
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: _showFilterDialog,
                  tooltip: 'فلترة',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadMovements,
                  tooltip: 'تحديث',
                ),
              ],
            ),

            // Search Bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: QuantumSearchBar(
                controller: _searchController,
                hintText: 'البحث في الحركات...',
                onChanged: _onSearchChanged,
              ),
            ),

            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: QuantumLoading())
                  : _buildMovementsList(),
            ),
          ],
        ),
      ),
      floatingActionButton: QuantumFAB(
        onPressed: () => _navigateToAddMovement(),
        icon: Icons.add,
        tooltip: 'إضافة حركة',
      ),
    );
  }

  Widget _buildMovementsList() {
    if (_filteredMovements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد حركات مخزون',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة حركة مخزون جديدة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _filteredMovements.length,
          itemBuilder: (context, index) {
            final movement = _filteredMovements[index];
            return _buildMovementCard(movement, index);
          },
        ),
      ),
    );
  }

  Widget _buildMovementCard(StockMovement movement, int index) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final delay = index * 0.1;
        final animationValue = Curves.easeOutCubic.transform(
          (_animationController.value - delay).clamp(0.0, 1.0),
        );

        return Transform.translate(
          offset: Offset(0, 50 * (1 - animationValue)),
          child: Opacity(opacity: animationValue, child: child),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: QuantumCard(
          child: InkWell(
            onTap: () => _showMovementDetails(movement),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildMovementTypeIcon(movement.type),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              movement.documentNumber,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _getMovementTypeText(movement.type),
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildStatusChip(movement.status),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildMovementInfo(movement),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMovementTypeIcon(MovementType type) {
    IconData iconData;
    Color color;

    switch (type) {
      case MovementType.receipt:
        iconData = Icons.arrow_downward;
        color = Colors.green;
        break;
      case MovementType.issue:
        iconData = Icons.arrow_upward;
        color = Colors.red;
        break;
      case MovementType.transfer:
        iconData = Icons.swap_horiz;
        color = Colors.blue;
        break;
      case MovementType.adjustment:
        iconData = Icons.tune;
        color = Colors.orange;
        break;
      case MovementType.return_:
        iconData = Icons.keyboard_return;
        color = Colors.purple;
        break;
      case MovementType.damage:
        iconData = Icons.warning;
        color = Colors.red[700]!;
        break;
      case MovementType.loss:
        iconData = Icons.error;
        color = Colors.red[900]!;
        break;
      case MovementType.found:
        iconData = Icons.search;
        color = Colors.green[700]!;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(iconData, color: color, size: 24),
    );
  }

  Widget _buildStatusChip(MovementStatus status) {
    Color color;
    String text;

    switch (status) {
      case MovementStatus.pending:
        color = Colors.orange;
        text = 'معلق';
        break;
      case MovementStatus.approved:
        color = Colors.blue;
        text = 'معتمد';
        break;
      case MovementStatus.rejected:
        color = Colors.red;
        text = 'مرفوض';
        break;
      case MovementStatus.completed:
        color = Colors.green;
        text = 'مكتمل';
        break;
      case MovementStatus.cancelled:
        color = Colors.grey;
        text = 'ملغي';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMovementInfo(StockMovement movement) {
    return Column(
      children: [
        _buildInfoRow('الكمية', '${movement.quantity}'),
        _buildInfoRow('التكلفة الوحدة', '${movement.unitCost} ر.س'),
        _buildInfoRow('التكلفة الإجمالية', '${movement.totalCost} ر.س'),
        _buildInfoRow('التاريخ', _formatDate(movement.movementDate)),
        if (movement.notes?.isNotEmpty == true)
          _buildInfoRow('الملاحظات', movement.notes!),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  String _getMovementTypeText(MovementType type) {
    switch (type) {
      case MovementType.receipt:
        return 'استلام';
      case MovementType.issue:
        return 'صرف';
      case MovementType.transfer:
        return 'نقل';
      case MovementType.adjustment:
        return 'تسوية';
      case MovementType.return_:
        return 'مرتجع';
      case MovementType.damage:
        return 'تالف';
      case MovementType.loss:
        return 'فقدان';
      case MovementType.found:
        return 'عثور';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showMovementDetails(StockMovement movement) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StockMovementDetailsScreen(movement: movement),
      ),
    );
  }

  void _navigateToAddMovement() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddStockMovementScreen(
          warehouseId: _selectedWarehouseId,
          itemId: _selectedItemId,
        ),
      ),
    );

    if (result == true) {
      _loadMovements();
      _showSuccessSnackBar('تم إضافة الحركة بنجاح');
    }
  }
}

// Filter Dialog Widget
class _FilterDialog extends StatefulWidget {
  final MovementType? selectedType;
  final MovementStatus? selectedStatus;
  final int? selectedWarehouseId;
  final int? selectedItemId;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<Warehouse> warehouses;
  final List<Item> items;
  final Function(
    MovementType?,
    MovementStatus?,
    int?,
    int?,
    DateTime?,
    DateTime?,
  )
  onApply;

  const _FilterDialog({
    required this.selectedType,
    required this.selectedStatus,
    required this.selectedWarehouseId,
    required this.selectedItemId,
    required this.startDate,
    required this.endDate,
    required this.warehouses,
    required this.items,
    required this.onApply,
  });

  @override
  State<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<_FilterDialog> {
  MovementType? _type;
  MovementStatus? _status;
  int? _warehouseId;
  int? _itemId;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _type = widget.selectedType;
    _status = widget.selectedStatus;
    _warehouseId = widget.selectedWarehouseId;
    _itemId = widget.selectedItemId;
    _startDate = widget.startDate;
    _endDate = widget.endDate;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('فلترة الحركات'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Movement Type Filter
            QuantumDropdown<MovementType>(
              value: _type,
              hintText: 'نوع الحركة',
              items: MovementType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_getMovementTypeText(type)),
                );
              }).toList(),
              onChanged: (value) => setState(() => _type = value),
            ),
            const SizedBox(height: 16),

            // Movement Status Filter
            QuantumDropdown<MovementStatus>(
              value: _status,
              hintText: 'حالة الحركة',
              items: MovementStatus.values.map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(_getMovementStatusText(status)),
                );
              }).toList(),
              onChanged: (value) => setState(() => _status = value),
            ),
            const SizedBox(height: 16),

            // Warehouse Filter
            QuantumDropdown<int>(
              value: _warehouseId,
              hintText: 'المخزن',
              items: widget.warehouses.map((warehouse) {
                return DropdownMenuItem(
                  value: warehouse.id,
                  child: Text(warehouse.name),
                );
              }).toList(),
              onChanged: (value) => setState(() => _warehouseId = value),
            ),
            const SizedBox(height: 16),

            // Item Filter
            QuantumDropdown<int>(
              value: _itemId,
              hintText: 'الصنف',
              items: widget.items.map((item) {
                return DropdownMenuItem(value: item.id, child: Text(item.name));
              }).toList(),
              onChanged: (value) => setState(() => _itemId = value),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            setState(() {
              _type = null;
              _status = null;
              _warehouseId = null;
              _itemId = null;
              _startDate = null;
              _endDate = null;
            });
          },
          child: const Text('مسح الفلاتر'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApply(
              _type,
              _status,
              _warehouseId,
              _itemId,
              _startDate,
              _endDate,
            );
            Navigator.pop(context);
          },
          child: const Text('تطبيق'),
        ),
      ],
    );
  }

  String _getMovementTypeText(MovementType type) {
    switch (type) {
      case MovementType.receipt:
        return 'استلام';
      case MovementType.issue:
        return 'صرف';
      case MovementType.transfer:
        return 'نقل';
      case MovementType.adjustment:
        return 'تسوية';
      case MovementType.return_:
        return 'مرتجع';
      case MovementType.damage:
        return 'تالف';
      case MovementType.loss:
        return 'فقدان';
      case MovementType.found:
        return 'عثور';
    }
  }

  String _getMovementStatusText(MovementStatus status) {
    switch (status) {
      case MovementStatus.pending:
        return 'معلق';
      case MovementStatus.approved:
        return 'معتمد';
      case MovementStatus.rejected:
        return 'مرفوض';
      case MovementStatus.completed:
        return 'مكتمل';
      case MovementStatus.cancelled:
        return 'ملغي';
    }
  }
}
