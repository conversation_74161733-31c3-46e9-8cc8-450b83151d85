import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../services/performance_service.dart';
import '../../theme/app_theme.dart';

/// 📊 شاشة مراقبة الأداء
/// Performance Monitor Screen
class PerformanceMonitorScreen extends StatefulWidget {
  const PerformanceMonitorScreen({super.key});

  @override
  State<PerformanceMonitorScreen> createState() =>
      _PerformanceMonitorScreenState();
}

class _PerformanceMonitorScreenState extends State<PerformanceMonitorScreen> {
  final PerformanceService _performanceService = PerformanceService();

  Map<String, dynamic> _performanceReport = {};
  Map<String, dynamic> _healthCheck = {};
  bool _isOptimizing = false;
  bool _isMonitoring = false;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadPerformanceData();
    _startMonitoring();
  }

  @override
  void dispose() {
    _stopMonitoring();
    super.dispose();
  }

  void _startMonitoring() {
    _isMonitoring = true;
    _performanceService.startPerformanceMonitoring();

    _refreshTimer = Timer.periodic(
      const Duration(seconds: 5),
      (_) => _loadPerformanceData(),
    );
  }

  void _stopMonitoring() {
    _isMonitoring = false;
    _performanceService.stopPerformanceMonitoring();
    _refreshTimer?.cancel();
  }

  Future<void> _loadPerformanceData() async {
    if (!mounted) return;

    final report = _performanceService.getPerformanceReport();
    final health = await _performanceService.performHealthCheck();

    if (mounted) {
      setState(() {
        _performanceReport = report;
        _healthCheck = health;
      });
    }
  }

  Future<void> _optimizePerformance() async {
    setState(() => _isOptimizing = true);

    try {
      final results = await _performanceService.optimizePerformance();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تحسين الأداء بنجاح في ${results['total_time_ms']} مللي ثانية',
            ),
            backgroundColor: Colors.green,
          ),
        );

        await _loadPerformanceData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحسين الأداء: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isOptimizing = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقب الأداء'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _isMonitoring ? _stopMonitoring : _startMonitoring,
            icon: Icon(_isMonitoring ? Icons.pause : Icons.play_arrow),
          ),
          IconButton(
            onPressed: _isOptimizing ? null : _optimizePerformance,
            icon: _isOptimizing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.tune),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadPerformanceData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHealthOverview(),
              const SizedBox(height: 16),
              _buildPerformanceCharts(),
              const SizedBox(height: 16),
              _buildDetailedMetrics(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHealthOverview() {
    final overallStatus = _healthCheck['overall_status'] ?? 'غير معروف';
    final statusColor = _getStatusColor(overallStatus);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.health_and_safety, color: statusColor),
                const SizedBox(width: 8),
                Text(
                  'الحالة العامة للنظام',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: statusColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.circle, color: statusColor, size: 12),
                  const SizedBox(width: 8),
                  Text(
                    overallStatus,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildHealthItem(
                    'قاعدة البيانات',
                    _healthCheck['database_integrity'] ?? 'غير معروف',
                    Icons.storage,
                  ),
                ),
                Expanded(
                  child: _buildHealthItem(
                    'الذاكرة',
                    _healthCheck['memory_status'] ?? 'غير معروف',
                    Icons.memory,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildHealthItem(
                    'التخزين المؤقت',
                    _healthCheck['cache_status'] ?? 'غير معروف',
                    Icons.cached,
                  ),
                ),
                Expanded(
                  child: _buildHealthItem(
                    'الاستجابة',
                    _healthCheck['response_status'] ?? 'غير معروف',
                    Icons.speed,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthItem(String title, String status, IconData icon) {
    final statusColor = _getStatusColor(status);

    return Container(
      margin: const EdgeInsets.all(4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: statusColor, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
          Text(
            status,
            style: TextStyle(
              fontSize: 10,
              color: statusColor,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceCharts() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مخططات الأداء',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            SizedBox(height: 200, child: _buildMemoryChart()),
          ],
        ),
      ),
    );
  }

  Widget _buildMemoryChart() {
    final memoryData = _performanceReport['memory_usage_mb'];
    if (memoryData == null) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    final current = memoryData['current'] ?? 0.0;
    final average = memoryData['average'] ?? 0.0;
    final max = memoryData['max'] ?? 0.0;

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: max * 1.2,
        barTouchData: BarTouchData(enabled: false),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                switch (value.toInt()) {
                  case 0:
                    return const Text('الحالي');
                  case 1:
                    return const Text('المتوسط');
                  case 2:
                    return const Text('الأقصى');
                  default:
                    return const Text('');
                }
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text('${value.toInt()} MB');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: [
          BarChartGroupData(
            x: 0,
            barRods: [BarChartRodData(toY: current, color: Colors.blue)],
          ),
          BarChartGroupData(
            x: 1,
            barRods: [BarChartRodData(toY: average, color: Colors.green)],
          ),
          BarChartGroupData(
            x: 2,
            barRods: [BarChartRodData(toY: max, color: Colors.red)],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedMetrics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('مقاييس مفصلة', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),

            ..._performanceReport.entries.map((entry) {
              if (entry.value is Map) {
                final data = entry.value as Map<String, dynamic>;
                return _buildMetricRow(
                  entry.key,
                  '${data['current']?.toStringAsFixed(2) ?? 'N/A'}',
                  '${data['average']?.toStringAsFixed(2) ?? 'N/A'}',
                );
              }
              return _buildMetricRow(entry.key, entry.value.toString(), '');
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String name, String current, String average) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(flex: 2, child: Text(name.replaceAll('_', ' '))),
          Expanded(child: Text(current)),
          if (average.isNotEmpty) Expanded(child: Text('متوسط: $average')),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'ممتاز':
      case 'جيد':
      case 'سليم':
        return Colors.green;
      case 'متوسط':
        return Colors.orange;
      case 'مرتفع':
      case 'بطيء':
      case 'يحتاج تحسين':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
