import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/invoice.dart';
import '../../models/customer.dart';
import '../../models/supplier.dart';
import '../../models/item.dart';
import '../../services/invoice_service.dart';
import '../../services/customer_service.dart';
import '../../services/supplier_service.dart';
import '../../services/item_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_inputs.dart';

class AddInvoiceScreen extends StatefulWidget {
  final Invoice? invoice;
  final InvoiceType invoiceType;

  const AddInvoiceScreen({super.key, this.invoice, required this.invoiceType});

  @override
  State<AddInvoiceScreen> createState() => _AddInvoiceScreenState();
}

class _AddInvoiceScreenState extends State<AddInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final InvoiceService _invoiceService = InvoiceService();
  final CustomerService _customerService = CustomerService();
  final SupplierService _supplierService = SupplierService();
  final ItemService _itemService = ItemService();

  // Controllers
  final TextEditingController _invoiceNumberController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _discountController = TextEditingController();

  // Form data
  DateTime _selectedDate = DateTime.now();
  DateTime? _dueDate;
  Customer? _selectedCustomer;
  Supplier? _selectedSupplier;
  List<InvoiceLine> _invoiceLines = [];
  double _discountAmount = 0.0;
  InvoiceStatus _status = InvoiceStatus.draft;

  // Lists
  List<Customer> _customers = [];
  List<Supplier> _suppliers = [];
  List<Item> _items = [];

  bool _isLoading = false;
  bool _isLoadingData = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _initializeForm();
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    setState(() => _isLoadingData = true);

    try {
      // Load customers/suppliers and items
      final customersResult = await _customerService.getAllCustomers();
      final suppliersResult = await _supplierService.getAllSuppliers();
      final itemsResult = await _itemService.getAllItems();

      if (customersResult.isSuccess && customersResult.data != null) {
        _customers = customersResult.data!;
      }

      if (suppliersResult.isSuccess && suppliersResult.data != null) {
        _suppliers = suppliersResult.data!;
      }

      if (itemsResult.isSuccess && itemsResult.data != null) {
        _items = itemsResult.data!;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() => _isLoadingData = false);
  }

  void _initializeForm() {
    if (widget.invoice != null) {
      // Edit mode
      final invoice = widget.invoice!;
      _invoiceNumberController.text = invoice.invoiceNumber;
      _selectedDate = invoice.date;
      _dueDate = invoice.dueDate;
      _notesController.text = invoice.notes ?? '';
      _discountAmount = invoice.discountAmount;
      _discountController.text = _discountAmount.toString();
      _status = invoice.status;
      _invoiceLines = List.from(invoice.lines);

      // Set customer or supplier based on invoice type
      if (widget.invoiceType == InvoiceType.sales &&
          invoice.customerId != null) {
        _selectedCustomer = _customers.firstWhere(
          (c) => c.id == invoice.customerId,
          orElse: () => Customer(
            id: invoice.customerId,
            code: 'DELETED',
            name: 'عميل محذوف',
            email: '',
            phone: '',
            address: '',
            isActive: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
      } else if (widget.invoiceType == InvoiceType.purchase &&
          invoice.supplierId != null) {
        _selectedSupplier = _suppliers.firstWhere(
          (s) => s.id == invoice.supplierId,
          orElse: () => Supplier(
            id: invoice.supplierId,
            code: 'DELETED',
            name: 'مورد محذوف',
            email: '',
            phone: '',
            address: '',
            isActive: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
      }
    } else {
      // Add mode - generate invoice number
      _generateInvoiceNumber();
    }
  }

  void _generateInvoiceNumber() {
    final prefix = widget.invoiceType == InvoiceType.sales ? 'S' : 'P';
    final year = DateTime.now().year;
    final timestamp = DateTime.now().millisecondsSinceEpoch
        .toString()
        .substring(8);
    _invoiceNumberController.text = '$prefix-$year-$timestamp';
  }

  Future<void> _selectDate(BuildContext context, bool isDueDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isDueDate ? (_dueDate ?? DateTime.now()) : _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        if (isDueDate) {
          _dueDate = picked;
        } else {
          _selectedDate = picked;
        }
      });
    }
  }

  void _addInvoiceLine() {
    _showItemSelectionDialog();
  }

  void _showItemSelectionDialog() {
    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد عناصر متاحة. يرجى إضافة عناصر أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _ItemSelectionDialog(
        items: _items,
        onItemSelected: (item, quantity, unitPrice) {
          final line = InvoiceLine(
            invoiceId: widget.invoice?.id ?? 0,
            itemId: item.id!,
            description: item.name,
            quantity: quantity,
            unitPrice: unitPrice,
            lineTotal: quantity * unitPrice,
            lineOrder: _invoiceLines.length,
          );

          setState(() {
            _invoiceLines.add(line);
          });
        },
      ),
    );
  }

  void _editInvoiceLine(int index) {
    final line = _invoiceLines[index];

    showDialog(
      context: context,
      builder: (context) => _EditInvoiceLineDialog(
        line: line,
        items: _items,
        onLineUpdated: (updatedLine) {
          setState(() {
            _invoiceLines[index] = updatedLine;
          });
        },
      ),
    );
  }

  void _removeInvoiceLine(int index) {
    setState(() {
      _invoiceLines.removeAt(index);
      // Reorder remaining lines
      for (int i = 0; i < _invoiceLines.length; i++) {
        _invoiceLines[i].lineOrder = i;
      }
    });
  }

  Map<String, double> _calculateTotals() {
    double subtotal = 0.0;
    for (InvoiceLine line in _invoiceLines) {
      subtotal += line.lineTotal;
    }

    // ملاحظة: يجب استخدام TaxService لحساب الضرائب الفعلية
    // هذا حساب مؤقت للتوافق مع النظام الحالي
    double taxAmount =
        subtotal * 0.15; // 15% VAT - سيتم استبداله بنظام الضرائب المرن
    double total = subtotal + taxAmount - _discountAmount;

    return {
      'subtotal': subtotal,
      'tax': taxAmount,
      'discount': _discountAmount,
      'total': total,
    };
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_invoiceLines.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة عنصر واحد على الأقل للفاتورة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (widget.invoiceType == InvoiceType.sales && _selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب اختيار عميل لفاتورة البيع'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (widget.invoiceType == InvoiceType.purchase &&
        _selectedSupplier == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب اختيار مورد لفاتورة الشراء'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final totals = _calculateTotals();

      final invoice = Invoice(
        id: widget.invoice?.id,
        invoiceNumber: _invoiceNumberController.text,
        invoiceType: widget.invoiceType,
        date: _selectedDate,
        dueDate: _dueDate,
        customerId: widget.invoiceType == InvoiceType.sales
            ? _selectedCustomer?.id
            : null,
        supplierId: widget.invoiceType == InvoiceType.purchase
            ? _selectedSupplier?.id
            : null,
        subtotal: totals['subtotal']!,
        taxAmount: totals['tax']!,
        discountAmount: totals['discount']!,
        totalAmount: totals['total']!,
        paidAmount: widget.invoice?.paidAmount ?? 0.0,
        status: _status,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        lines: _invoiceLines,
      );

      final result = widget.invoice == null
          ? await _invoiceService.createInvoice(invoice)
          : await _invoiceService.updateInvoice(invoice);

      if (mounted) {
        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.invoice == null
                    ? 'تم إنشاء الفاتورة بنجاح'
                    : 'تم تحديث الفاتورة بنجاح',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'خطأ في حفظ الفاتورة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingData) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          title: Text(widget.invoice == null ? 'إضافة فاتورة' : 'تعديل فاتورة'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    final totals = _calculateTotals();

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(widget.invoice == null ? 'إضافة فاتورة' : 'تعديل فاتورة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(icon: const Icon(Icons.save), onPressed: _saveInvoice),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الفاتورة الأساسية
                    _buildBasicInfoSection(),
                    const SizedBox(height: AppTheme.spacingLarge),

                    // اختيار العميل/المورد
                    _buildCustomerSupplierSection(),
                    const SizedBox(height: AppTheme.spacingLarge),

                    // عناصر الفاتورة
                    _buildInvoiceLinesSection(),
                    const SizedBox(height: AppTheme.spacingLarge),

                    // الخصم والملاحظات
                    _buildDiscountNotesSection(),
                    const SizedBox(height: AppTheme.spacingLarge),

                    // ملخص المبالغ
                    _buildTotalsSection(totals),
                  ],
                ),
              ),
            ),

            // أزرار الحفظ والإلغاء
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingMedium),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveInvoice,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : Text(widget.invoice == null ? 'إنشاء' : 'تحديث'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            Row(
              children: [
                Expanded(
                  child: BeautifulTextFormField(
                    controller: _invoiceNumberController,
                    labelText: 'رقم الفاتورة',
                    prefixIcon: Icons.receipt,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'رقم الفاتورة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: DropdownButtonFormField<InvoiceStatus>(
                    value: _status,
                    decoration: const InputDecoration(
                      labelText: 'حالة الفاتورة',
                      prefixIcon: Icon(Icons.flag),
                    ),
                    items: InvoiceStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusText(status)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _status = value);
                      }
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, false),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الفاتورة',
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(_formatDate(_selectedDate)),
                    ),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, true),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الاستحقاق',
                        prefixIcon: Icon(Icons.event),
                      ),
                      child: Text(
                        _dueDate != null ? _formatDate(_dueDate!) : 'غير محدد',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSupplierSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.invoiceType == InvoiceType.sales ? 'العميل' : 'المورد',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            if (widget.invoiceType == InvoiceType.sales)
              DropdownButtonFormField<Customer>(
                value: _selectedCustomer,
                decoration: const InputDecoration(
                  labelText: 'اختر العميل',
                  prefixIcon: Icon(Icons.person),
                ),
                items: _customers.map((customer) {
                  return DropdownMenuItem(
                    value: customer,
                    child: Text(customer.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() => _selectedCustomer = value);
                },
                validator: (value) {
                  if (value == null) {
                    return 'يجب اختيار عميل';
                  }
                  return null;
                },
              )
            else
              DropdownButtonFormField<Supplier>(
                value: _selectedSupplier,
                decoration: const InputDecoration(
                  labelText: 'اختر المورد',
                  prefixIcon: Icon(Icons.business),
                ),
                items: _suppliers.map((supplier) {
                  return DropdownMenuItem(
                    value: supplier,
                    child: Text(supplier.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() => _selectedSupplier = value);
                },
                validator: (value) {
                  if (value == null) {
                    return 'يجب اختيار مورد';
                  }
                  return null;
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceLinesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عناصر الفاتورة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addInvoiceLine,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة عنصر'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            if (_invoiceLines.isEmpty)
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingLarge),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: const Center(
                  child: Text(
                    'لم يتم إضافة أي عناصر بعد',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _invoiceLines.length,
                itemBuilder: (context, index) {
                  final line = _invoiceLines[index];
                  return Card(
                    margin: const EdgeInsets.only(
                      bottom: AppTheme.spacingSmall,
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: AppTheme.primaryColor,
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      title: Text(line.description ?? 'عنصر غير محدد'),
                      subtitle: Text(
                        'الكمية: ${line.quantity} × ${line.unitPrice.toStringAsFixed(2)} ر.س',
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '${line.lineTotal.toStringAsFixed(2)} ر.س',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          PopupMenuButton<String>(
                            onSelected: (value) {
                              if (value == 'edit') {
                                _editInvoiceLine(index);
                              } else if (value == 'delete') {
                                _removeInvoiceLine(index);
                              }
                            },
                            itemBuilder: (context) => [
                              const PopupMenuItem(
                                value: 'edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit, size: 20),
                                    SizedBox(width: 8),
                                    Text('تعديل'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.delete,
                                      size: 20,
                                      color: Colors.red,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'حذف',
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الخصم والملاحظات',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            BeautifulTextFormField(
              controller: _discountController,
              labelText: 'مبلغ الخصم (ر.س)',
              prefixIcon: Icons.discount,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
              onChanged: (value) {
                setState(() {
                  _discountAmount = double.tryParse(value) ?? 0.0;
                });
              },
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            BeautifulTextFormField(
              controller: _notesController,
              labelText: 'ملاحظات',
              prefixIcon: Icons.note,
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSection(Map<String, double> totals) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص المبالغ',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingMedium),

            _buildTotalRow('المجموع الفرعي', totals['subtotal']!, false),
            _buildTotalRow('الضريبة (15%)', totals['tax']!, false),
            _buildTotalRow('الخصم', totals['discount']!, false),
            const Divider(),
            _buildTotalRow('الإجمالي', totals['total']!, true),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, bool isTotal) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ر.س',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppTheme.primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(InvoiceStatus status) {
    return status.nameAr;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class _ItemSelectionDialog extends StatefulWidget {
  final List<Item> items;
  final Function(Item item, double quantity, double unitPrice) onItemSelected;

  const _ItemSelectionDialog({
    required this.items,
    required this.onItemSelected,
  });

  @override
  State<_ItemSelectionDialog> createState() => _ItemSelectionDialogState();
}

class _ItemSelectionDialogState extends State<_ItemSelectionDialog> {
  final TextEditingController _quantityController = TextEditingController(
    text: '1',
  );
  final TextEditingController _unitPriceController = TextEditingController();
  Item? _selectedItem;

  @override
  void dispose() {
    _quantityController.dispose();
    _unitPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('اختيار عنصر'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<Item>(
              value: _selectedItem,
              decoration: const InputDecoration(
                labelText: 'اختر العنصر',
                prefixIcon: Icon(Icons.inventory),
              ),
              items: widget.items.map((item) {
                return DropdownMenuItem(value: item, child: Text(item.name));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedItem = value;
                  if (value != null) {
                    _unitPriceController.text = value.sellingPrice.toString();
                  }
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _unitPriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر الوحدة',
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _selectedItem != null ? _addItem : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('إضافة'),
        ),
      ],
    );
  }

  void _addItem() {
    if (_selectedItem == null) return;

    final quantity = double.tryParse(_quantityController.text) ?? 1.0;
    final unitPrice = double.tryParse(_unitPriceController.text) ?? 0.0;

    if (quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب أن تكون الكمية أكبر من صفر'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (unitPrice <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب أن يكون سعر الوحدة أكبر من صفر'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    widget.onItemSelected(_selectedItem!, quantity, unitPrice);
    Navigator.pop(context);
  }
}

class _EditInvoiceLineDialog extends StatefulWidget {
  final InvoiceLine line;
  final List<Item> items;
  final Function(InvoiceLine updatedLine) onLineUpdated;

  const _EditInvoiceLineDialog({
    required this.line,
    required this.items,
    required this.onLineUpdated,
  });

  @override
  State<_EditInvoiceLineDialog> createState() => _EditInvoiceLineDialogState();
}

class _EditInvoiceLineDialogState extends State<_EditInvoiceLineDialog> {
  late TextEditingController _quantityController;
  late TextEditingController _unitPriceController;
  Item? _selectedItem;

  @override
  void initState() {
    super.initState();
    _quantityController = TextEditingController(
      text: widget.line.quantity.toString(),
    );
    _unitPriceController = TextEditingController(
      text: widget.line.unitPrice.toString(),
    );

    // Find the selected item
    _selectedItem = widget.items.firstWhere(
      (item) => item.id == widget.line.itemId,
      orElse: () => Item(
        id: widget.line.itemId,
        code: 'UNKNOWN',
        name: widget.line.description ?? 'عنصر غير معروف',
        description: '',
        category: '',
        unit: 'قطعة',
        costPrice: 0.0,
        sellingPrice: widget.line.unitPrice,
        currentStock: 0.0,
        minStockLevel: 0.0,
        maxStockLevel: 0.0,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _unitPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل العنصر'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<Item>(
              value: _selectedItem,
              decoration: const InputDecoration(
                labelText: 'العنصر',
                prefixIcon: Icon(Icons.inventory),
              ),
              items: widget.items.map((item) {
                return DropdownMenuItem(value: item, child: Text(item.name));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedItem = value;
                  if (value != null) {
                    _unitPriceController.text = value.sellingPrice.toString();
                  }
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _unitPriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر الوحدة',
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _selectedItem != null ? _updateItem : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('تحديث'),
        ),
      ],
    );
  }

  void _updateItem() {
    if (_selectedItem == null) return;

    final quantity = double.tryParse(_quantityController.text) ?? 1.0;
    final unitPrice = double.tryParse(_unitPriceController.text) ?? 0.0;

    if (quantity <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب أن تكون الكمية أكبر من صفر'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (unitPrice <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب أن يكون سعر الوحدة أكبر من صفر'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final updatedLine = InvoiceLine(
      id: widget.line.id,
      invoiceId: widget.line.invoiceId,
      itemId: _selectedItem!.id!,
      description: _selectedItem!.name,
      quantity: quantity,
      unitPrice: unitPrice,
      lineTotal: quantity * unitPrice,
      lineOrder: widget.line.lineOrder,
    );

    widget.onLineUpdated(updatedLine);
    Navigator.pop(context);
  }
}
