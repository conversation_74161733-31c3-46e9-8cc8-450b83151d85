/// نظام أنماط النصوص للتطبيق
/// Application Text Styles System for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/app_theme.dart';

/// فئة أنماط النصوص الرئيسية للتطبيق
/// Main application text styles class that provides easy access to theme text styles
class AppTextStyles {
  // منع إنشاء كائن من هذه الفئة
  AppTextStyles._();

  // Display Styles - أنماط العناوين الكبيرة
  static TextStyle get displayLarge => GoogleFonts.cairo(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: AppTheme.textPrimaryColor,
        height: 1.2,
      );

  static TextStyle get displayMedium => GoogleFonts.cairo(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: AppTheme.textPrimaryColor,
        height: 1.3,
      );

  static TextStyle get displaySmall => GoogleFonts.cairo(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimaryColor,
        height: 1.3,
      );

  // Headline Styles - أنماط العناوين
  static TextStyle get headlineLarge => GoogleFonts.cairo(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimaryColor,
        height: 1.4,
      );

  static TextStyle get headlineMedium => GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimaryColor,
        height: 1.4,
      );

  static TextStyle get headlineSmall => GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimaryColor,
        height: 1.4,
      );

  // Title Styles - أنماط العناوين الفرعية
  static TextStyle get titleLarge => GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimaryColor,
        height: 1.5,
      );

  static TextStyle get titleMedium => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppTheme.textPrimaryColor,
        height: 1.5,
      );

  static TextStyle get titleSmall => GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppTheme.textSecondaryColor,
        height: 1.5,
      );

  // Body Styles - أنماط النصوص الأساسية
  static TextStyle get bodyLarge => GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.normal,
        color: AppTheme.textPrimaryColor,
        height: 1.6,
      );

  static TextStyle get bodyMedium => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: AppTheme.textPrimaryColor,
        height: 1.6,
      );

  static TextStyle get bodySmall => GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        color: AppTheme.textSecondaryColor,
        height: 1.6,
      );

  // Label Styles - أنماط التسميات
  static TextStyle get labelLarge => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppTheme.textPrimaryColor,
        height: 1.4,
      );

  static TextStyle get labelMedium => GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppTheme.textSecondaryColor,
        height: 1.4,
      );

  static TextStyle get labelSmall => GoogleFonts.cairo(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: AppTheme.textLightColor,
        height: 1.4,
      );

  // Special Styles - أنماط خاصة
  
  /// نمط النص للأزرار
  static TextStyle get button => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        height: 1.4,
      );

  /// نمط النص للتسعير والمبالغ المالية
  static TextStyle get price => GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: AppTheme.primaryColor,
        height: 1.2,
      );

  /// نمط النص للحالات (نجاح، خطأ، تحذير)
  static TextStyle statusText(Color color) => GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        color: color,
        height: 1.2,
      );

  /// نمط النص للعناوين في البطاقات
  static TextStyle get cardTitle => GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimaryColor,
        height: 1.4,
      );

  /// نمط النص للوصف في البطاقات
  static TextStyle get cardSubtitle => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: AppTheme.textSecondaryColor,
        height: 1.5,
      );

  /// نمط النص للتواريخ
  static TextStyle get dateText => GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        color: AppTheme.textLightColor,
        height: 1.3,
      );

  /// نمط النص للأرقام والإحصائيات
  static TextStyle get numberText => GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppTheme.primaryColor,
        height: 1.2,
      );

  /// نمط النص للتنبيهات والرسائل
  static TextStyle get alertText => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        height: 1.5,
      );

  /// نمط النص للروابط
  static TextStyle get linkText => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppTheme.primaryColor,
        height: 1.4,
        decoration: TextDecoration.underline,
      );

  /// نمط النص للعناوين في شريط التطبيق
  static TextStyle get appBarTitle => GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimaryColor,
        height: 1.2,
      );

  /// نمط النص للتبويبات
  static TextStyle get tabText => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        height: 1.2,
      );

  /// نمط النص للحقول النصية
  static TextStyle get inputText => GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.normal,
        color: AppTheme.textPrimaryColor,
        height: 1.4,
      );

  /// نمط النص للتسميات في الحقول النصية
  static TextStyle get inputLabel => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: AppTheme.textSecondaryColor,
        height: 1.2,
      );

  /// نمط النص للنصائح في الحقول النصية
  static TextStyle get inputHint => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: AppTheme.textLightColor,
        height: 1.2,
      );

  /// نمط النص لرسائل الخطأ
  static TextStyle get errorText => GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppTheme.errorColor,
        height: 1.3,
      );

  /// نمط النص للنصوص المساعدة
  static TextStyle get helperText => GoogleFonts.cairo(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        color: AppTheme.textLightColor,
        height: 1.3,
      );
}
