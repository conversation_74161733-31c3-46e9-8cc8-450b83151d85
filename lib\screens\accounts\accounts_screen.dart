import 'package:flutter/material.dart';
import '../../models/account.dart';
import '../../services/account_service.dart';
import '../../widgets/account_tree_widget.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import '../../widgets/beautiful_buttons.dart';
import '../../widgets/beautiful_inputs.dart' as beautiful;

import '../../animations/page_transitions.dart';
import 'add_account_screen.dart';
import 'account_details_screen.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  final AccountService _accountService = AccountService();
  List<Account> _accounts = [];
  List<Account> _filteredAccounts = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _searchQuery = '';
  AccountType? _selectedType;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _accountService.getAllAccounts();
      if (result.isSuccess) {
        setState(() {
          _accounts = result.data!;
          _filteredAccounts = _accounts;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = result.error;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل الحسابات: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _filterAccounts() {
    setState(() {
      _filteredAccounts = _accounts.where((account) {
        bool matchesSearch =
            _searchQuery.isEmpty ||
            account.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            account.code.toLowerCase().contains(_searchQuery.toLowerCase());

        bool matchesType =
            _selectedType == null || account.accountType == _selectedType;

        return matchesSearch && matchesType;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _filterAccounts();
  }

  void _onTypeFilterChanged(AccountType? type) {
    setState(() {
      _selectedType = type;
    });
    _filterAccounts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'دليل الحسابات',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButtonBeautiful(
            icon: Icons.add_rounded,
            tooltip: 'إضافة حساب جديد',
            onPressed: () async {
              final result = await Navigator.push(
                context,
                CustomPageTransitions.slideFromRight(const AddAccountScreen()),
              );
              if (result == true) {
                _loadAccounts();
              }
            },
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          IconButtonBeautiful(
            icon: Icons.refresh_rounded,
            tooltip: 'تحديث',
            onPressed: _loadAccounts,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          BeautifulCard(
            margin: const EdgeInsets.all(AppTheme.spacingMedium),
            child: Column(
              children: [
                // Search Field
                beautiful.SearchField(
                  hintText: 'ابحث بالاسم أو الرمز...',
                  onChanged: _onSearchChanged,
                ),
                const SizedBox(height: AppTheme.spacingMedium),

                // Type Filter with chips
                Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    'تصفية حسب النوع:',
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSmall),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      beautiful.FilterChip(
                        label: 'الكل',
                        isSelected: _selectedType == null,
                        onTap: () => _onTypeFilterChanged(null),
                        icon: Icons.all_inclusive_rounded,
                      ),
                      ...AccountType.values.map(
                        (type) => beautiful.FilterChip(
                          label: type.displayName,
                          isSelected: _selectedType == type,
                          onTap: () => _onTypeFilterChanged(type),
                          icon: AppTheme.getAccountTypeIcon(type.name),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Content Section
          Expanded(child: _buildContent()),
        ],
      ),
      floatingActionButton: FloatingActionButtonBeautiful(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            CustomPageTransitions.scaleTransition(const AddAccountScreen()),
          );
          if (result == true) {
            _loadAccounts();
          }
        },
        icon: Icons.add_rounded,
        tooltip: 'إضافة حساب جديد',
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(_errorMessage!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAccounts,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty && _selectedType == null
                  ? 'لا توجد حسابات'
                  : 'لا توجد حسابات تطابق البحث',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة حساب جديد',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return AccountTreeWidget(
      accounts: _filteredAccounts,
      onAccountTap: (account) {
        Navigator.push(
          context,
          CustomPageTransitions.slideFromRight(
            AccountDetailsScreen(account: account),
          ),
        ).then((_) => _loadAccounts());
      },
      onAccountEdit: (account) async {
        final result = await Navigator.push(
          context,
          CustomPageTransitions.slideFromRight(
            AddAccountScreen(account: account),
          ),
        );
        if (result == true) {
          _loadAccounts();
        }
      },
      onAccountDelete: _deleteAccount,
    );
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الحساب "${account.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await _accountService.deleteAccount(account.id!);
      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم حذف الحساب بنجاح'),
              backgroundColor: AppTheme.secondaryColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppTheme.borderRadiusMedium,
                ),
              ),
            ),
          );
        }
        _loadAccounts();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الحساب: ${result.error}'),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppTheme.borderRadiusMedium,
                ),
              ),
            ),
          );
        }
      }
    }
  }
}
