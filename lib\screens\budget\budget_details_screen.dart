/// شاشة تفاصيل الموازنة
/// Budget Details Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/budget.dart';
import '../../models/budget_reports.dart';
import '../../services/budget_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';
import '../../theme/app_theme.dart';
import 'budget_form_screen.dart';

class BudgetDetailsScreen extends StatefulWidget {
  final Budget budget;

  const BudgetDetailsScreen({super.key, required this.budget});

  @override
  State<BudgetDetailsScreen> createState() => _BudgetDetailsScreenState();
}

class _BudgetDetailsScreenState extends State<BudgetDetailsScreen>
    with SingleTickerProviderStateMixin {
  final BudgetService _budgetService = BudgetService();

  late TabController _tabController;
  late Budget _budget;
  BudgetVsActualReport? _budgetVsActualReport;
  VarianceAnalysisReport? _varianceAnalysisReport;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _budget = widget.budget;
    _loadBudgetDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBudgetDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث بيانات الموازنة
      final budgetResult = await _budgetService.getBudgetById(_budget.id!);
      if (budgetResult.isSuccess && budgetResult.data != null) {
        _budget = budgetResult.data!;
      }

      // تحديث المبالغ الفعلية
      await _budgetService.updateActualAmountsAutomatically(_budget.id!);

      // جلب تقرير الموازنة مقابل الفعلي
      final budgetVsActualResult = await _budgetService
          .generateBudgetVsActualReport(
            budgetId: _budget.id!,
            updateActualAmounts: false, // تم التحديث بالفعل
          );
      if (budgetVsActualResult.isSuccess) {
        _budgetVsActualReport = budgetVsActualResult.data;
      }

      // جلب تقرير تحليل الانحرافات
      final varianceAnalysisResult = await _budgetService
          .generateVarianceAnalysisReport(
            budgetId: _budget.id!,
            updateActualAmounts: false, // تم التحديث بالفعل
          );
      if (varianceAnalysisResult.isSuccess) {
        _varianceAnalysisReport = varianceAnalysisResult.data;
      }
    } catch (e) {
      if (mounted) {
        showErrorDialog(
          context,
          'خطأ في تحميل تفاصيل الموازنة: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text('تفاصيل الموازنة: ${_budget.name}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _editBudget,
            icon: const Icon(Icons.edit),
            tooltip: 'تعديل الموازنة',
          ),
          IconButton(
            onPressed: _refreshData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'معلومات عامة', icon: Icon(Icons.info_outline)),
            Tab(
              text: 'الموازنة مقابل الفعلي',
              icon: Icon(Icons.compare_arrows),
            ),
            Tab(text: 'تحليل الانحرافات', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildGeneralInfoTab(),
            _buildBudgetVsActualTab(),
            _buildVarianceAnalysisTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildBudgetInfoCard(),
          const SizedBox(height: 16),
          _buildBudgetLinesCard(),
          const SizedBox(height: 16),
          _buildBudgetSummaryCard(),
        ],
      ),
    );
  }

  Widget _buildBudgetInfoCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الموازنة',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('الكود', _budget.code),
            _buildInfoRow('الاسم', _budget.name),
            if (_budget.description != null)
              _buildInfoRow('الوصف', _budget.description!),
            _buildInfoRow('النوع', _budget.type.displayName),
            _buildInfoRow('الفترة', _budget.period.displayName),
            _buildInfoRow('تاريخ البداية', _formatDate(_budget.startDate)),
            _buildInfoRow('تاريخ النهاية', _formatDate(_budget.endDate)),
            _buildInfoRow('الحالة', _getStatusText(_budget.status)),
            if (_budget.approvedBy != null) ...[
              _buildInfoRow('معتمد من', _budget.approvedBy!),
              _buildInfoRow('تاريخ الاعتماد', _formatDate(_budget.approvedAt!)),
            ],
            _buildInfoRow('تاريخ الإنشاء', _formatDate(_budget.createdAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }

  Widget _buildBudgetLinesCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بنود الموازنة',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            if (_budget.lines.isEmpty)
              const Center(
                child: Text(
                  'لا توجد بنود في هذه الموازنة',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _budget.lines.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final line = _budget.lines[index];
                  return _buildBudgetLineItem(line);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetLineItem(BudgetLine line) {
    final account = line.account;
    if (account == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  account.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  account.code,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'مخطط: ${line.budgetAmount.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'فعلي: ${line.actualAmount.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 12,
                    color: line.actualAmount > line.budgetAmount
                        ? Colors.red
                        : Colors.green,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetSummaryCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الموازنة',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المخطط',
                    '${_budget.totalBudgetAmount.toStringAsFixed(2)} ر.س',
                    AppTheme.primaryColor,
                    Icons.account_balance_wallet,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الفعلي',
                    '${_budget.totalActualAmount.toStringAsFixed(2)} ر.س',
                    _budget.totalActualAmount > _budget.totalBudgetAmount
                        ? Colors.red
                        : Colors.green,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'الانحراف',
                    '${_budget.totalVariance.toStringAsFixed(2)} ر.س',
                    _budget.totalVariance > 0 ? Colors.red : Colors.green,
                    Icons.compare_arrows,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'نسبة الانحراف',
                    '${_budget.variancePercentage.toStringAsFixed(1)}%',
                    _budget.variancePercentage > 0 ? Colors.red : Colors.green,
                    Icons.percent,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.all(4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetVsActualTab() {
    if (_budgetVsActualReport == null) {
      return const Center(
        child: Text(
          'لا توجد بيانات للموازنة مقابل الفعلي',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildBudgetVsActualSummaryCard(),
          const SizedBox(height: 16),
          _buildBudgetVsActualItemsCard(),
        ],
      ),
    );
  }

  Widget _buildBudgetVsActualSummaryCard() {
    final summary = _budgetVsActualReport!.summary;

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الموازنة مقابل الفعلي',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المخطط',
                    '${summary.totalBudgetAmount.toStringAsFixed(2)} ر.س',
                    AppTheme.primaryColor,
                    Icons.account_balance_wallet,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الفعلي',
                    '${summary.totalActualAmount.toStringAsFixed(2)} ر.س',
                    summary.totalActualAmount > summary.totalBudgetAmount
                        ? Colors.red
                        : Colors.green,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'الانحراف الإجمالي',
                    '${summary.totalVariance.toStringAsFixed(2)} ر.س',
                    summary.totalVariance > 0 ? Colors.red : Colors.green,
                    Icons.compare_arrows,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'نسبة الانحراف',
                    '${summary.totalVariancePercentage.toStringAsFixed(1)}%',
                    summary.totalVariancePercentage > 0
                        ? Colors.red
                        : Colors.green,
                    Icons.percent,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'انحرافات إيجابية',
                    '${summary.favorableVariances}',
                    Colors.green,
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'انحرافات سلبية',
                    '${summary.unfavorableVariances}',
                    Colors.red,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetVsActualItemsCard() {
    final items = _budgetVsActualReport!.items;

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الموازنة مقابل الفعلي',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            if (items.isEmpty)
              const Center(
                child: Text(
                  'لا توجد بيانات',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: items.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final item = items[index];
                  return _buildBudgetVsActualItem(item);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetVsActualItem(BudgetVsActualItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.accountName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      item.accountCode,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'مخطط: ${item.budgetAmount.toStringAsFixed(2)} ر.س',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  Text(
                    'فعلي: ${item.actualAmount.toStringAsFixed(2)} ر.س',
                    style: TextStyle(
                      fontSize: 12,
                      color: item.actualAmount > item.budgetAmount
                          ? Colors.red
                          : Colors.green,
                    ),
                  ),
                  Text(
                    'انحراف: ${item.variance.toStringAsFixed(2)} ر.س (${item.variancePercentage.toStringAsFixed(1)}%)',
                    style: TextStyle(
                      fontSize: 12,
                      color: item.variance > 0 ? Colors.red : Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVarianceAnalysisTab() {
    if (_varianceAnalysisReport == null) {
      return const Center(
        child: Text(
          'لا توجد بيانات لتحليل الانحرافات',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildVarianceAnalysisSummaryCard(),
          const SizedBox(height: 16),
          _buildVarianceAnalysisItemsCard(),
        ],
      ),
    );
  }

  Widget _buildVarianceAnalysisSummaryCard() {
    final summary = _varianceAnalysisReport!.summary;

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص تحليل الانحرافات',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الانحرافات',
                    '${summary.totalVariances}',
                    Colors.blue,
                    Icons.analytics,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'انحرافات مهمة',
                    '${summary.significantVariances}',
                    Colors.orange,
                    Icons.warning,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'انحرافات إيجابية',
                    '${summary.favorableVariances}',
                    Colors.green,
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'انحرافات سلبية',
                    '${summary.unfavorableVariances}',
                    Colors.red,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVarianceAnalysisItemsCard() {
    final items = _varianceAnalysisReport!.items;

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل تحليل الانحرافات',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            if (items.isEmpty)
              const Center(
                child: Text(
                  'لا توجد انحرافات',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: items.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final item = items[index];
                  return _buildVarianceAnalysisItem(item);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVarianceAnalysisItem(VarianceAnalysisItem item) {
    Color statusColor = Colors.grey;
    IconData statusIcon = Icons.info;

    if (item.isCritical) {
      statusColor = Colors.red;
      statusIcon = Icons.error;
    } else if (item.isSignificant) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning;
    } else if (item.varianceType == VarianceType.favorable) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: statusColor.withValues(alpha: 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(statusIcon, color: statusColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  item.accountName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              Text(
                '${item.variancePercentage.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'الانحراف: ${item.varianceAmount.toStringAsFixed(2)} ر.س',
            style: TextStyle(color: statusColor, fontSize: 12),
          ),
          if (item.recommendation?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Text(
              'التوصية: ${item.recommendation}',
              style: const TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getStatusText(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.draft:
        return 'مسودة';
      case BudgetStatus.submitted:
        return 'مرسلة';
      case BudgetStatus.underReview:
        return 'قيد المراجعة';
      case BudgetStatus.approved:
        return 'معتمدة';
      case BudgetStatus.active:
        return 'نشطة';
      case BudgetStatus.closed:
        return 'مغلقة';
      case BudgetStatus.cancelled:
        return 'ملغاة';
    }
  }

  // Action methods
  Future<void> _editBudget() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => BudgetFormScreen(budget: _budget),
      ),
    );

    if (result == true) {
      _loadBudgetDetails();
    }
  }

  Future<void> _refreshData() async {
    await _loadBudgetDetails();
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم تحديث البيانات بنجاح')));
    }
  }
}
