// شاشة الترحيب (Splash Screen) لتطبيق Smart Ledger
// هذه الشاشة تظهر عند بدء تشغيل التطبيق مع لوغو متحرك جميل
// وتقوم بتحضير البيانات الأساسية قبل الانتقال للشاشة الرئيسية

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_ledger_logo.dart';
import '../animations/page_transitions.dart';
import 'home_screen.dart';

/// شاشة الترحيب الرئيسية مع الحركات الجميلة
/// Main splash screen with beautiful animations
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _textController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _textFadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // إعداد شريط الحالة ليكون شفافاً
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    // إعداد متحكمات الحركة
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // إعداد الحركات
    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOut,
    ));

    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.5, 1.0, curve: Curves.easeIn),
    ));

    // بدء تسلسل الحركات
    _startAnimationSequence();
  }

  /// بدء تسلسل الحركات والانتقال للشاشة الرئيسية
  /// Start animation sequence and navigate to main screen
  void _startAnimationSequence() async {
    // بدء حركة الخلفية
    _backgroundController.forward();
    
    // انتظار قليل ثم بدء حركة النص
    await Future.delayed(const Duration(milliseconds: 800));
    _textController.forward();
    
    // انتظار انتهاء جميع الحركات
    await Future.delayed(const Duration(seconds: 4));
    
    // الانتقال للشاشة الرئيسية
    if (mounted) {
      Navigator.pushReplacement(
        context,
        CustomPageTransitions.slideFromRight(const HomeScreen()),
      );
    }
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _backgroundController,
          _textController,
        ]),
        builder: (context, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryColor.withValues(
                    alpha: 0.8 + (0.2 * _backgroundAnimation.value),
                  ),
                  AppTheme.secondaryColor.withValues(
                    alpha: 0.6 + (0.4 * _backgroundAnimation.value),
                  ),
                  AppTheme.accentColor.withValues(
                    alpha: 0.4 + (0.6 * _backgroundAnimation.value),
                  ),
                ],
              ),
            ),
            child: Stack(
              children: [
                // تأثيرات الخلفية المتحركة
                _buildBackgroundEffects(),
                
                // المحتوى الرئيسي
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // اللوغو المتحرك
                      AnimatedSmartLedgerLogo(
                        size: 180,
                        onAnimationComplete: () {
                          // يمكن إضافة منطق إضافي هنا عند انتهاء حركة اللوغو
                        },
                      ),
                      
                      const SizedBox(height: 40),
                      
                      // النص الترحيبي
                      FadeTransition(
                        opacity: _textFadeAnimation,
                        child: Column(
                          children: [
                            Text(
                              'مرحباً بك في',
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Smart Ledger',
                              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 2.0,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'دفتر الأستاذ الذكي',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: Colors.white.withValues(alpha: 0.8),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'نظام محاسبة متقدم وسهل الاستخدام',
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Colors.white.withValues(alpha: 0.7),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 60),
                      
                      // مؤشر التحميل
                      FadeTransition(
                        opacity: _textFadeAnimation,
                        child: Column(
                          children: [
                            SizedBox(
                              width: 40,
                              height: 40,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white.withValues(alpha: 0.8),
                                ),
                                strokeWidth: 3,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'جاري التحضير...',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.white.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // معلومات الإصدار في الأسفل
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: FadeTransition(
                    opacity: _textFadeAnimation,
                    child: Column(
                      children: [
                        Text(
                          'الإصدار 1.0.0',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: 0.6),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '© 2024 Smart Ledger. جميع الحقوق محفوظة',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: 0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء التأثيرات المتحركة للخلفية
  /// Build animated background effects
  Widget _buildBackgroundEffects() {
    return Stack(
      children: [
        // دوائر متحركة في الخلفية
        for (int i = 0; i < 5; i++)
          Positioned(
            top: (i * 150.0) - (100 * _backgroundAnimation.value),
            left: (i * 80.0) - (50 * _backgroundAnimation.value),
            child: Opacity(
              opacity: 0.1 * _backgroundAnimation.value,
              child: Container(
                width: 100 + (i * 20.0),
                height: 100 + (i * 20.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
              ),
            ),
          ),
        
        // تأثير الإضاءة المتدرجة
        Positioned(
          top: -100,
          right: -100,
          child: Opacity(
            opacity: 0.3 * _backgroundAnimation.value,
            child: Container(
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.2),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
