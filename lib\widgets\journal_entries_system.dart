import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 📝 نظام إدارة القيود المحاسبية المتقدم
/// Advanced Journal Entries Management System
///
/// هذا الملف يحتوي على نظام إدارة القيود المحاسبية المتقدم لا مثيل له في التاريخ
/// This file contains unprecedented advanced journal entries management system in history

/// 🌟 لوحة إدارة القيود المحاسبية المتقدمة
/// Advanced Journal Entries Management Dashboard
class AdvancedJournalEntriesDashboard extends StatefulWidget {
  const AdvancedJournalEntriesDashboard({super.key});

  @override
  State<AdvancedJournalEntriesDashboard> createState() =>
      _AdvancedJournalEntriesDashboardState();
}

class _AdvancedJournalEntriesDashboardState
    extends State<AdvancedJournalEntriesDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _balanceController;
  late AnimationController _entryController;
  late Animation<double> _mainAnimation;
  late Animation<double> _balanceAnimation;
  late Animation<double> _entryAnimation;

  final List<JournalEntry> _journalEntries = [
    JournalEntry(
      id: 'JE-2024-001',
      date: DateTime.now().subtract(const Duration(days: 1)),
      description: 'قيد افتتاحي - رأس المال',
      reference: 'REF-001',
      totalDebit: 500000.0,
      totalCredit: 500000.0,
      status: EntryStatus.posted,
      details: [
        JournalEntryDetail(
          accountCode: '1110',
          accountName: 'النقدية بالصندوق',
          debit: 100000.0,
          credit: 0.0,
        ),
        JournalEntryDetail(
          accountCode: '1120',
          accountName: 'البنك الأهلي',
          debit: 400000.0,
          credit: 0.0,
        ),
        JournalEntryDetail(
          accountCode: '3110',
          accountName: 'رأس المال',
          debit: 0.0,
          credit: 500000.0,
        ),
      ],
    ),
    JournalEntry(
      id: 'JE-2024-002',
      date: DateTime.now().subtract(const Duration(days: 2)),
      description: 'شراء أثاث مكتبي',
      reference: 'INV-1001',
      totalDebit: 25000.0,
      totalCredit: 25000.0,
      status: EntryStatus.draft,
      details: [
        JournalEntryDetail(
          accountCode: '1510',
          accountName: 'الأثاث والمعدات',
          debit: 25000.0,
          credit: 0.0,
        ),
        JournalEntryDetail(
          accountCode: '1120',
          accountName: 'البنك الأهلي',
          debit: 0.0,
          credit: 25000.0,
        ),
      ],
    ),
    JournalEntry(
      id: 'JE-2024-003',
      date: DateTime.now(),
      description: 'مبيعات نقدية',
      reference: 'SALE-001',
      totalDebit: 115000.0,
      totalCredit: 115000.0,
      status: EntryStatus.posted,
      details: [
        JournalEntryDetail(
          accountCode: '1110',
          accountName: 'النقدية بالصندوق',
          debit: 115000.0,
          credit: 0.0,
        ),
        JournalEntryDetail(
          accountCode: '4110',
          accountName: 'إيرادات المبيعات',
          debit: 0.0,
          credit: 100000.0,
        ),
        JournalEntryDetail(
          accountCode: '2310',
          accountName: 'ضريبة القيمة المضافة',
          debit: 0.0,
          credit: 15000.0,
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _balanceController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _entryController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _balanceAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _balanceController, curve: Curves.easeInOut),
    );

    _entryAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(parent: _entryController, curve: Curves.linear));

    _mainController.repeat(reverse: true);
    _balanceController.repeat(reverse: true);
    _entryController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _balanceController.dispose();
    _entryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _balanceAnimation,
        _entryAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.3 + (_mainAnimation.value * 0.7),
          child: QuantumEnergyEffect(
            intensity: 2.1 + (_balanceAnimation.value * 0.4),
            primaryColor: const Color(0xFF795548),
            secondaryColor: const Color(0xFF8D6E63),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF3E2723).withValues(alpha: 0.9),
                    const Color(0xFF5D4037).withValues(alpha: 0.8),
                    const Color(0xFF6D4C41).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF795548).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF795548).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام القيود المحاسبية
                  Row(
                    children: [
                      Transform.scale(
                        scale: _balanceAnimation.value,
                        child: Transform.rotate(
                          angle: _entryAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF795548,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFF8D6E63,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.account_balance_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '📝 القيود المحاسبية المتقدمة',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام متطور لإدارة القيود مع التحقق التلقائي من التوازن',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // إحصائيات القيود
                  _buildJournalStats(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // قائمة القيود المحاسبية
                  Text(
                    '📋 سجل القيود المحاسبية',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),

                  ...List.generate(_journalEntries.length, (index) {
                    return _buildJournalEntryCard(_journalEntries[index]);
                  }),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء إحصائيات القيود
  Widget _buildJournalStats() {
    final totalEntries = _journalEntries.length;
    final postedEntries = _journalEntries
        .where((e) => e.status == EntryStatus.posted)
        .length;
    final draftEntries = _journalEntries
        .where((e) => e.status == EntryStatus.draft)
        .length;
    final totalDebits = _journalEntries.fold(
      0.0,
      (sum, entry) => sum + entry.totalDebit,
    );

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي القيود',
            totalEntries.toString(),
            Icons.receipt_long_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'القيود المرحلة',
            postedEntries.toString(),
            Icons.check_circle_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'المسودات',
            draftEntries.toString(),
            Icons.edit_rounded,
            const Color(0xFFFF9800),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي المبالغ',
            '${totalDebits.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet_rounded,
            const Color(0xFF9C27B0),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 9,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة القيد المحاسبي
  Widget _buildJournalEntryCard(JournalEntry entry) {
    final isBalanced = entry.totalDebit == entry.totalCredit;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getStatusColor(entry.status).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس القيد
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getStatusColor(entry.status).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.receipt_long_rounded,
                  color: _getStatusColor(entry.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          entry.id,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingSmall),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(
                              entry.status,
                            ).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _getStatusText(entry.status),
                            style: TextStyle(
                              color: _getStatusColor(entry.status),
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      entry.description,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // مؤشر التوازن
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isBalanced
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.2)
                      : const Color(0xFFF44336).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  isBalanced ? Icons.check_circle_rounded : Icons.error_rounded,
                  color: isBalanced
                      ? const Color(0xFF4CAF50)
                      : const Color(0xFFF44336),
                  size: 20,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات القيد
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'التاريخ',
                  _formatDate(entry.date),
                  Icons.calendar_today_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'المرجع',
                  entry.reference,
                  Icons.tag_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'إجمالي المدين',
                  '${entry.totalDebit.toStringAsFixed(2)} ر.س',
                  Icons.trending_up_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'إجمالي الدائن',
                  '${entry.totalCredit.toStringAsFixed(2)} ر.س',
                  Icons.trending_down_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // تفاصيل القيد
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingSmall),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تفاصيل القيد:',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXSmall),
                ...entry.details.map((detail) => _buildEntryDetailRow(detail)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.6), size: 12),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 9,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEntryDetailRow(JournalEntryDetail detail) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            detail.accountCode,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 9,
              fontFamily: 'monospace',
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              detail.accountName,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 9,
              ),
            ),
          ),
          if (detail.debit > 0)
            Text(
              '${detail.debit.toStringAsFixed(2)} مدين',
              style: const TextStyle(
                color: Color(0xFF4CAF50),
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
          if (detail.credit > 0)
            Text(
              '${detail.credit.toStringAsFixed(2)} دائن',
              style: const TextStyle(
                color: Color(0xFFF44336),
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  Color _getStatusColor(EntryStatus status) {
    switch (status) {
      case EntryStatus.draft:
        return const Color(0xFFFF9800);
      case EntryStatus.posted:
        return const Color(0xFF4CAF50);
      case EntryStatus.cancelled:
        return const Color(0xFFF44336);
    }
  }

  String _getStatusText(EntryStatus status) {
    switch (status) {
      case EntryStatus.draft:
        return 'مسودة';
      case EntryStatus.posted:
        return 'مرحل';
      case EntryStatus.cancelled:
        return 'ملغي';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// نموذج القيد المحاسبي
class JournalEntry {
  final String id;
  final DateTime date;
  final String description;
  final String reference;
  final double totalDebit;
  final double totalCredit;
  final EntryStatus status;
  final List<JournalEntryDetail> details;

  JournalEntry({
    required this.id,
    required this.date,
    required this.description,
    required this.reference,
    required this.totalDebit,
    required this.totalCredit,
    required this.status,
    required this.details,
  });
}

/// نموذج تفاصيل القيد المحاسبي
class JournalEntryDetail {
  final String accountCode;
  final String accountName;
  final double debit;
  final double credit;

  JournalEntryDetail({
    required this.accountCode,
    required this.accountName,
    required this.debit,
    required this.credit,
  });
}

/// حالة القيد المحاسبي
enum EntryStatus { draft, posted, cancelled }
