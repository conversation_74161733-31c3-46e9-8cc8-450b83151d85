import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 👥 نظام إدارة العلاقات مع العملاء والموردين المتقدم
/// Advanced Customer Relationship Management System
///
/// هذا الملف يحتوي على نظام CRM متقدم لا مثيل له في التاريخ
/// This file contains unprecedented advanced CRM system in history

/// 🌟 لوحة إدارة العملاء والموردين المتقدمة
/// Advanced Customer and Supplier Management Dashboard
class AdvancedCRMDashboard extends StatefulWidget {
  const AdvancedCRMDashboard({super.key});

  @override
  State<AdvancedCRMDashboard> createState() => _AdvancedCRMDashboardState();
}

class _AdvancedCRMDashboardState extends State<AdvancedCRMDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _mainAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  int _selectedTab = 0;

  final List<Customer> _customers = [
    Customer(
      name: 'شركة الأندلس للتجارة',
      email: '<EMAIL>',
      phone: '+966501234567',
      balance: 125000.0,
      creditLimit: 200000.0,
      status: CustomerStatus.active,
      rating: 5,
      lastTransaction: DateTime.now().subtract(const Duration(days: 2)),
    ),
    Customer(
      name: 'مؤسسة النور للمقاولات',
      email: '<EMAIL>',
      phone: '+966507654321',
      balance: 85000.0,
      creditLimit: 150000.0,
      status: CustomerStatus.active,
      rating: 4,
      lastTransaction: DateTime.now().subtract(const Duration(days: 5)),
    ),
    Customer(
      name: 'شركة الفجر الجديد',
      email: '<EMAIL>',
      phone: '+966509876543',
      balance: 45000.0,
      creditLimit: 100000.0,
      status: CustomerStatus.pending,
      rating: 3,
      lastTransaction: DateTime.now().subtract(const Duration(days: 15)),
    ),
  ];

  final List<Supplier> _suppliers = [
    Supplier(
      name: 'شركة المواد الأولية المحدودة',
      email: '<EMAIL>',
      phone: '+966502345678',
      balance: -75000.0,
      paymentTerms: 30,
      status: SupplierStatus.active,
      rating: 5,
      lastOrder: DateTime.now().subtract(const Duration(days: 3)),
    ),
    Supplier(
      name: 'مؤسسة التوريدات الذكية',
      email: '<EMAIL>',
      phone: '+966508765432',
      balance: -120000.0,
      paymentTerms: 45,
      status: SupplierStatus.active,
      rating: 4,
      lastOrder: DateTime.now().subtract(const Duration(days: 7)),
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _mainController.repeat(reverse: true);
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _pulseAnimation,
        _rotationAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.0 + (_mainAnimation.value * 0.5),
          child: QuantumEnergyEffect(
            intensity: 1.8 + (_pulseAnimation.value * 0.2),
            primaryColor: const Color(0xFF00BCD4),
            secondaryColor: const Color(0xFF26C6DA),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF006064).withValues(alpha: 0.9),
                    const Color(0xFF00838F).withValues(alpha: 0.8),
                    const Color(0xFF00ACC1).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF00BCD4).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF00BCD4).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام CRM
                  Row(
                    children: [
                      Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Transform.rotate(
                          angle: _rotationAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF00BCD4,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFF4DD0E1,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.people_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '👥 إدارة العملاء والموردين',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام CRM متطور وذكي',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات العملاء والموردين
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  _selectedTab == 0
                      ? _buildCustomersView()
                      : _buildSuppliersView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 0),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 0
                      ? const Color(0xFF00BCD4).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person_rounded,
                      color: _selectedTab == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'العملاء',
                      style: TextStyle(
                        color: _selectedTab == 0
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 1),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 1
                      ? const Color(0xFF00BCD4).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.business_rounded,
                      color: _selectedTab == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'الموردين',
                      style: TextStyle(
                        color: _selectedTab == 1
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض العملاء
  Widget _buildCustomersView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات العملاء
        _buildCustomerStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة العملاء
        Text(
          '📋 قائمة العملاء',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_customers.length, (index) {
          return _buildCustomerCard(_customers[index]);
        }),
      ],
    );
  }

  /// بناء عرض الموردين
  Widget _buildSuppliersView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات الموردين
        _buildSupplierStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة الموردين
        Text(
          '🏭 قائمة الموردين',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_suppliers.length, (index) {
          return _buildSupplierCard(_suppliers[index]);
        }),
      ],
    );
  }

  /// بناء إحصائيات العملاء
  Widget _buildCustomerStats() {
    final totalCustomers = _customers.length;
    final activeCustomers = _customers
        .where((c) => c.status == CustomerStatus.active)
        .length;
    final totalBalance = _customers.fold(
      0.0,
      (sum, customer) => sum + customer.balance,
    );

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي العملاء',
            totalCustomers.toString(),
            Icons.people_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'العملاء النشطين',
            activeCustomers.toString(),
            Icons.person_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي الأرصدة',
            '${totalBalance.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet_rounded,
            const Color(0xFFFF9800),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات الموردين
  Widget _buildSupplierStats() {
    final totalSuppliers = _suppliers.length;
    final activeSuppliers = _suppliers
        .where((s) => s.status == SupplierStatus.active)
        .length;
    final totalBalance = _suppliers.fold(
      0.0,
      (sum, supplier) => sum + supplier.balance.abs(),
    );

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الموردين',
            totalSuppliers.toString(),
            Icons.business_rounded,
            const Color(0xFF9C27B0),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'الموردين النشطين',
            activeSuppliers.toString(),
            Icons.verified_rounded,
            const Color(0xFF00BCD4),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي المستحقات',
            '${totalBalance.toStringAsFixed(0)} ر.س',
            Icons.payment_rounded,
            const Color(0xFFF44336),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة العميل
  Widget _buildCustomerCard(Customer customer) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getCustomerStatusColor(
            customer.status,
          ).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getCustomerStatusColor(
                    customer.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.person_rounded,
                  color: _getCustomerStatusColor(customer.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      customer.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      customer.email,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              // تقييم العميل
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < customer.rating
                        ? Icons.star_rounded
                        : Icons.star_border_rounded,
                    color: const Color(0xFFFFD700),
                    size: 16,
                  );
                }),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات إضافية
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الرصيد',
                  '${customer.balance.toStringAsFixed(2)} ر.س',
                  Icons.account_balance_wallet_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'الحد الائتماني',
                  '${customer.creditLimit.toStringAsFixed(0)} ر.س',
                  Icons.credit_card_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الهاتف',
                  customer.phone,
                  Icons.phone_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'آخر معاملة',
                  _formatDate(customer.lastTransaction),
                  Icons.access_time_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المورد
  Widget _buildSupplierCard(Supplier supplier) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getSupplierStatusColor(
            supplier.status,
          ).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getSupplierStatusColor(
                    supplier.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.business_rounded,
                  color: _getSupplierStatusColor(supplier.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      supplier.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      supplier.email,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              // تقييم المورد
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < supplier.rating
                        ? Icons.star_rounded
                        : Icons.star_border_rounded,
                    color: const Color(0xFFFFD700),
                    size: 16,
                  );
                }),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات إضافية
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'المستحقات',
                  '${supplier.balance.abs().toStringAsFixed(2)} ر.س',
                  Icons.payment_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'شروط الدفع',
                  '${supplier.paymentTerms} يوم',
                  Icons.schedule_rounded,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الهاتف',
                  supplier.phone,
                  Icons.phone_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'آخر طلبية',
                  _formatDate(supplier.lastOrder),
                  Icons.shopping_cart_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.6), size: 14),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 10,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getCustomerStatusColor(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return const Color(0xFF4CAF50);
      case CustomerStatus.pending:
        return const Color(0xFFFF9800);
      case CustomerStatus.inactive:
        return const Color(0xFFF44336);
    }
  }

  Color _getSupplierStatusColor(SupplierStatus status) {
    switch (status) {
      case SupplierStatus.active:
        return const Color(0xFF2196F3);
      case SupplierStatus.pending:
        return const Color(0xFFFF9800);
      case SupplierStatus.inactive:
        return const Color(0xFFF44336);
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'اليوم';
    } else if (difference == 1) {
      return 'أمس';
    } else {
      return 'منذ $difference أيام';
    }
  }
}

/// نموذج العميل
class Customer {
  final String name;
  final String email;
  final String phone;
  final double balance;
  final double creditLimit;
  final CustomerStatus status;
  final int rating;
  final DateTime lastTransaction;

  Customer({
    required this.name,
    required this.email,
    required this.phone,
    required this.balance,
    required this.creditLimit,
    required this.status,
    required this.rating,
    required this.lastTransaction,
  });
}

/// نموذج المورد
class Supplier {
  final String name;
  final String email;
  final String phone;
  final double balance;
  final int paymentTerms;
  final SupplierStatus status;
  final int rating;
  final DateTime lastOrder;

  Supplier({
    required this.name,
    required this.email,
    required this.phone,
    required this.balance,
    required this.paymentTerms,
    required this.status,
    required this.rating,
    required this.lastOrder,
  });
}

/// حالة العميل
enum CustomerStatus { active, pending, inactive }

/// حالة المورد
enum SupplierStatus { active, pending, inactive }
