/// Data Access Object for Cost Layers
/// Cost Layer DAO for Smart Ledger
library;


import '../models/cost_calculation.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class CostLayerDao {
  static final CostLayerDao _instance = CostLayerDao._internal();
  factory CostLayerDao() => _instance;
  CostLayerDao._internal();

  /// Get all cost layers
  Future<List<CostLayer>> getAllCostLayers() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      orderBy: 'received_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Get cost layer by ID
  Future<CostLayer?> getCostLayerById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return CostLayer.fromMap(maps.first);
    }
    return null;
  }

  /// Get cost layers for specific item
  Future<List<CostLayer>> getCostLayersForItem({
    required int itemId,
    required int warehouseId,
    int? locationId,
    String? batchNumber,
    String? orderBy,
  }) async {
    final db = await DatabaseHelper().database;
    
    String whereClause = 'item_id = ? AND warehouse_id = ? AND quantity > 0';
    List<dynamic> whereArgs = [itemId, warehouseId];

    if (locationId != null) {
      whereClause += ' AND location_id = ?';
      whereArgs.add(locationId);
    }

    if (batchNumber != null) {
      whereClause += ' AND batch_number = ?';
      whereArgs.add(batchNumber);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: orderBy ?? 'received_date ASC, id ASC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Get all cost layers for item (across all warehouses)
  Future<List<CostLayer>> getAllCostLayersForItem(int itemId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: 'item_id = ? AND quantity > 0',
      whereArgs: [itemId],
      orderBy: 'received_date ASC, id ASC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Insert new cost layer
  Future<int> insertCostLayer(CostLayer layer) async {
    final db = await DatabaseHelper().database;
    
    final layerMap = layer.toMap();
    layerMap.remove('id'); // Remove ID for auto-increment
    layerMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableCostLayers,
      layerMap,
    );
  }

  /// Update cost layer
  Future<int> updateCostLayer(CostLayer layer) async {
    final db = await DatabaseHelper().database;
    
    final layerMap = layer.toMap();
    layerMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableCostLayers,
      layerMap,
      where: 'id = ?',
      whereArgs: [layer.id],
    );
  }

  /// Consume quantity from cost layer
  Future<int> consumeFromCostLayer(int layerId, double consumedQuantity) async {
    final db = await DatabaseHelper().database;
    
    final layer = await getCostLayerById(layerId);
    if (layer == null) {
      throw Exception('طبقة التكلفة غير موجودة');
    }

    if (layer.quantity < consumedQuantity) {
      throw Exception('الكمية المطلوبة أكبر من المتاح في الطبقة');
    }

    final newQuantity = layer.quantity - consumedQuantity;
    final newTotalCost = newQuantity * layer.unitCost;

    return await db.update(
      DatabaseSchema.tableCostLayers,
      {
        'quantity': newQuantity,
        'total_cost': newTotalCost,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [layerId],
    );
  }

  /// Delete cost layer
  Future<int> deleteCostLayer(int id) async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tableCostLayers,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Delete empty cost layers (quantity = 0)
  Future<int> deleteEmptyCostLayers() async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tableCostLayers,
      where: 'quantity <= 0',
    );
  }

  /// Get cost layers by batch number
  Future<List<CostLayer>> getCostLayersByBatch(String batchNumber) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: 'batch_number = ? AND quantity > 0',
      whereArgs: [batchNumber],
      orderBy: 'received_date ASC, id ASC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Get cost layers by serial number
  Future<List<CostLayer>> getCostLayersBySerial(String serialNumber) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: 'serial_number = ? AND quantity > 0',
      whereArgs: [serialNumber],
      orderBy: 'received_date ASC, id ASC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Get item costing settings
  Future<ItemCostingSettings?> getItemCostingSettings(int itemId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableItemCostingSettings,
      where: 'item_id = ?',
      whereArgs: [itemId],
    );

    if (maps.isNotEmpty) {
      return ItemCostingSettings.fromMap(maps.first);
    }
    return null;
  }

  /// Insert or update item costing settings
  Future<int> saveItemCostingSettings(ItemCostingSettings settings) async {
    final db = await DatabaseHelper().database;
    
    final existing = await getItemCostingSettings(settings.itemId);
    
    final settingsMap = settings.toMap();
    settingsMap['updated_at'] = DateTime.now().toIso8601String();

    if (existing != null) {
      return await db.update(
        DatabaseSchema.tableItemCostingSettings,
        settingsMap,
        where: 'item_id = ?',
        whereArgs: [settings.itemId],
      );
    } else {
      settingsMap.remove('id');
      return await db.insert(
        DatabaseSchema.tableItemCostingSettings,
        settingsMap,
      );
    }
  }

  /// Get cost summary by warehouse
  Future<Map<String, dynamic>> getCostSummaryByWarehouse(int warehouseId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(DISTINCT item_id) as unique_items,
        SUM(quantity) as total_quantity,
        SUM(total_cost) as total_value,
        AVG(unit_cost) as average_unit_cost
      FROM ${DatabaseSchema.tableCostLayers}
      WHERE warehouse_id = ? AND quantity > 0
    ''', [warehouseId]);

    return result.isNotEmpty ? result.first : {};
  }

  /// Get cost summary by item
  Future<Map<String, dynamic>> getCostSummaryByItem(int itemId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as layer_count,
        SUM(quantity) as total_quantity,
        SUM(total_cost) as total_value,
        AVG(unit_cost) as average_unit_cost,
        MIN(unit_cost) as min_unit_cost,
        MAX(unit_cost) as max_unit_cost,
        MIN(received_date) as oldest_date,
        MAX(received_date) as newest_date
      FROM ${DatabaseSchema.tableCostLayers}
      WHERE item_id = ? AND quantity > 0
    ''', [itemId]);

    return result.isNotEmpty ? result.first : {};
  }

  /// Get cost layers expiring soon
  Future<List<CostLayer>> getCostLayersExpiringSoon(int daysAhead) async {
    final db = await DatabaseHelper().database;
    final expiryDate = DateTime.now().add(Duration(days: daysAhead));
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: 'expiry_date IS NOT NULL AND expiry_date <= ? AND quantity > 0',
      whereArgs: [expiryDate.toIso8601String().split('T')[0]],
      orderBy: 'expiry_date ASC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Get cost layers by date range
  Future<List<CostLayer>> getCostLayersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await DatabaseHelper().database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: 'received_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'received_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Search cost layers
  Future<List<CostLayer>> searchCostLayers(String query) async {
    final db = await DatabaseHelper().database;
    final searchQuery = '%$query%';
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCostLayers,
      where: '''
        batch_number LIKE ? OR 
        serial_number LIKE ? OR 
        reference_document LIKE ?
      ''',
      whereArgs: [searchQuery, searchQuery, searchQuery],
      orderBy: 'received_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CostLayer.fromMap(maps[i]);
    });
  }

  /// Get cost layers with item details
  Future<List<Map<String, dynamic>>> getCostLayersWithItemDetails() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        cl.*,
        i.name as item_name,
        i.code as item_code,
        i.unit as item_unit,
        w.name as warehouse_name,
        w.code as warehouse_code
      FROM ${DatabaseSchema.tableCostLayers} cl
      LEFT JOIN items i ON cl.item_id = i.id
      LEFT JOIN warehouses w ON cl.warehouse_id = w.id
      WHERE cl.quantity > 0
      ORDER BY cl.received_date DESC, cl.id DESC
    ''');

    return result;
  }
}
