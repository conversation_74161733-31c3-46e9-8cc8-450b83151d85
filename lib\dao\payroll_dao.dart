import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/payroll_record.dart';

/// DAO للرواتب
/// Payroll Data Access Object
class PayrollDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج سجل راتب جديد
  Future<int> insertPayrollRecord(PayrollRecord payrollRecord) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tablePayrollRecords,
      payrollRecord.toMap(),
    );
  }

  /// تحديث سجل راتب
  Future<int> updatePayrollRecord(PayrollRecord payrollRecord) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tablePayrollRecords,
      payrollRecord.toMap(),
      where: 'id = ?',
      whereArgs: [payrollRecord.id],
    );
  }

  /// حذف سجل راتب
  Future<int> deletePayrollRecord(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      DatabaseSchema.tablePayrollRecords,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على سجل راتب بالمعرف
  Future<PayrollRecord?> getPayrollRecordById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollRecords,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return PayrollRecord.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على سجلات راتب الموظف
  Future<List<PayrollRecord>> getEmployeePayrollRecords(String employeeCode) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollRecords,
      where: 'employee_code = ?',
      whereArgs: [employeeCode],
      orderBy: 'payroll_date DESC',
    );

    return List.generate(maps.length, (i) => PayrollRecord.fromMap(maps[i]));
  }

  /// الحصول على سجلات راتب فترة معينة
  Future<List<PayrollRecord>> getPayrollRecordsByPeriod(int payrollPeriodId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollRecords,
      where: 'payroll_period_id = ?',
      whereArgs: [payrollPeriodId],
      orderBy: 'employee_code ASC',
    );

    return List.generate(maps.length, (i) => PayrollRecord.fromMap(maps[i]));
  }

  /// الحصول على سجل راتب موظف في فترة معينة
  Future<PayrollRecord?> getEmployeePayrollRecordByPeriod(
      String employeeCode, int payrollPeriodId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollRecords,
      where: 'employee_code = ? AND payroll_period_id = ?',
      whereArgs: [employeeCode, payrollPeriodId],
    );

    if (maps.isNotEmpty) {
      return PayrollRecord.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على سجلات الراتب حسب الحالة
  Future<List<PayrollRecord>> getPayrollRecordsByStatus(PayrollStatus status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollRecords,
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'payroll_date DESC',
    );

    return List.generate(maps.length, (i) => PayrollRecord.fromMap(maps[i]));
  }

  /// إدراج فترة راتب جديدة
  Future<int> insertPayrollPeriod(PayrollPeriod payrollPeriod) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tablePayrollPeriods,
      payrollPeriod.toMap(),
    );
  }

  /// تحديث فترة راتب
  Future<int> updatePayrollPeriod(PayrollPeriod payrollPeriod) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tablePayrollPeriods,
      payrollPeriod.toMap(),
      where: 'id = ?',
      whereArgs: [payrollPeriod.id],
    );
  }

  /// الحصول على فترة راتب بالمعرف
  Future<PayrollPeriod?> getPayrollPeriodById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollPeriods,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return PayrollPeriod.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع فترات الراتب
  Future<List<PayrollPeriod>> getAllPayrollPeriods() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollPeriods,
      orderBy: 'start_date DESC',
    );

    return List.generate(maps.length, (i) => PayrollPeriod.fromMap(maps[i]));
  }

  /// الحصول على فترات الراتب حسب الحالة
  Future<List<PayrollPeriod>> getPayrollPeriodsByStatus(PayrollPeriodStatus status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollPeriods,
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'start_date DESC',
    );

    return List.generate(maps.length, (i) => PayrollPeriod.fromMap(maps[i]));
  }

  /// الحصول على الفترة المفتوحة الحالية
  Future<PayrollPeriod?> getCurrentOpenPeriod() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollPeriods,
      where: 'status = ?',
      whereArgs: [PayrollPeriodStatus.open.name],
      orderBy: 'start_date DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return PayrollPeriod.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على إحصائيات الراتب لفترة معينة
  Future<Map<String, dynamic>> getPayrollPeriodStatistics(int payrollPeriodId) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_employees,
        SUM(basic_salary) as total_basic_salary,
        SUM(total_allowances) as total_allowances,
        SUM(total_deductions) as total_deductions,
        SUM(net_salary) as total_net_salary,
        AVG(net_salary) as average_net_salary,
        MIN(net_salary) as min_net_salary,
        MAX(net_salary) as max_net_salary
      FROM ${DatabaseSchema.tablePayrollRecords}
      WHERE payroll_period_id = ?
    ''', [payrollPeriodId]);

    final statusResult = await db.rawQuery('''
      SELECT status, COUNT(*) as count
      FROM ${DatabaseSchema.tablePayrollRecords}
      WHERE payroll_period_id = ?
      GROUP BY status
    ''', [payrollPeriodId]);

    return {
      'total_employees': result.first['total_employees'] ?? 0,
      'total_basic_salary': (result.first['total_basic_salary'] as num?)?.toDouble() ?? 0.0,
      'total_allowances': (result.first['total_allowances'] as num?)?.toDouble() ?? 0.0,
      'total_deductions': (result.first['total_deductions'] as num?)?.toDouble() ?? 0.0,
      'total_net_salary': (result.first['total_net_salary'] as num?)?.toDouble() ?? 0.0,
      'average_net_salary': (result.first['average_net_salary'] as num?)?.toDouble() ?? 0.0,
      'min_net_salary': (result.first['min_net_salary'] as num?)?.toDouble() ?? 0.0,
      'max_net_salary': (result.first['max_net_salary'] as num?)?.toDouble() ?? 0.0,
      'by_status': {
        for (var row in statusResult)
          row['status']: row['count']
      },
    };
  }

  /// الحصول على سجلات الراتب مع تفاصيل الموظف
  Future<List<Map<String, dynamic>>> getPayrollRecordsWithEmployeeDetails(int payrollPeriodId) async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        pr.*,
        e.first_name,
        e.last_name,
        e.middle_name,
        d.name as department_name,
        p.title as position_title
      FROM ${DatabaseSchema.tablePayrollRecords} pr
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON pr.employee_code = e.employee_code
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      LEFT JOIN ${DatabaseSchema.tablePositions} p ON e.position_id = p.id
      WHERE pr.payroll_period_id = ?
      ORDER BY e.first_name ASC, e.last_name ASC
    ''', [payrollPeriodId]);
  }

  /// البحث في سجلات الراتب
  Future<List<Map<String, dynamic>>> searchPayrollRecords(
      int payrollPeriodId, String query) async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        pr.*,
        e.first_name,
        e.last_name,
        e.middle_name,
        d.name as department_name,
        p.title as position_title
      FROM ${DatabaseSchema.tablePayrollRecords} pr
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON pr.employee_code = e.employee_code
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      LEFT JOIN ${DatabaseSchema.tablePositions} p ON e.position_id = p.id
      WHERE pr.payroll_period_id = ? AND (
        LOWER(e.first_name) LIKE LOWER(?) OR
        LOWER(e.last_name) LIKE LOWER(?) OR
        LOWER(e.middle_name) LIKE LOWER(?) OR
        LOWER(pr.employee_code) LIKE LOWER(?) OR
        LOWER(d.name) LIKE LOWER(?) OR
        LOWER(p.title) LIKE LOWER(?)
      )
      ORDER BY e.first_name ASC, e.last_name ASC
    ''', [payrollPeriodId, '%$query%', '%$query%', '%$query%', '%$query%', '%$query%', '%$query%']);
  }

  /// تحديث حالة سجل راتب
  Future<int> updatePayrollRecordStatus(int id, PayrollStatus status) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tablePayrollRecords,
      {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// تحديث حالة فترة راتب
  Future<int> updatePayrollPeriodStatus(int id, PayrollPeriodStatus status) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tablePayrollPeriods,
      {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على إجمالي الرواتب لفترة معينة
  Future<double> getTotalPayrollAmount(int payrollPeriodId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT SUM(net_salary) as total
      FROM ${DatabaseSchema.tablePayrollRecords}
      WHERE payroll_period_id = ?
    ''', [payrollPeriodId]);

    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  /// التحقق من وجود سجل راتب للموظف في الفترة
  Future<bool> hasPayrollRecordForPeriod(String employeeCode, int payrollPeriodId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePayrollRecords,
      where: 'employee_code = ? AND payroll_period_id = ?',
      whereArgs: [employeeCode, payrollPeriodId],
    );

    return maps.isNotEmpty;
  }
}
