import 'package:flutter/material.dart';
import '../../models/invoice.dart';
import '../../models/customer.dart';
import '../../models/supplier.dart';
import '../../models/item.dart';
import '../../services/advanced_invoice_service.dart';
import '../../services/customer_service.dart' as customer_service;
import '../../services/supplier_service.dart' as supplier_service;
import '../../services/item_service.dart' as item_service;
import '../../services/tax_service.dart';
import '../../services/pdf_service.dart';
import '../../utils/result.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';

/// شاشة الفواتير المتقدمة
class AdvancedInvoiceScreen extends StatefulWidget {
  final Invoice? invoice;
  final InvoiceType invoiceType;

  const AdvancedInvoiceScreen({
    super.key,
    this.invoice,
    required this.invoiceType,
  });

  @override
  State<AdvancedInvoiceScreen> createState() => _AdvancedInvoiceScreenState();
}

class _AdvancedInvoiceScreenState extends State<AdvancedInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceNumberController = TextEditingController();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController(text: '0.0');

  final AdvancedInvoiceService _invoiceService = AdvancedInvoiceService();
  final customer_service.CustomerService _customerService =
      customer_service.CustomerService();
  final supplier_service.SupplierService _supplierService =
      supplier_service.SupplierService();
  final item_service.ItemService _itemService = item_service.ItemService();
  final TaxService _taxService = TaxService();
  final PdfService _pdfService = PdfService();

  DateTime _selectedDate = DateTime.now();
  DateTime? _dueDate;
  Customer? _selectedCustomer;
  Supplier? _selectedSupplier;

  List<InvoiceLine> _invoiceLines = [];
  List<Customer> _customers = [];
  List<Supplier> _suppliers = [];
  List<Item> _items = [];

  bool _isLoading = false;
  bool _autoCalculateTax = true;
  bool _isInclusive = false;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    if (widget.invoice != null) {
      _loadInvoiceData();
    }
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    super.dispose();
  }

  /// تحميل البيانات الأولية
  Future<void> _loadInitialData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل العملاء والموردين والأصناف
      final futures = await Future.wait([
        _customerService.getAllCustomers(),
        _supplierService.getAllSuppliers(),
        _itemService.getAllItems(),
      ]);

      final customersResult = futures[0] as Result<List<Customer>>;
      final suppliersResult = futures[1] as Result<List<Supplier>>;
      final itemsResult = futures[2] as Result<List<Item>>;

      if (customersResult.isSuccess) {
        _customers = customersResult.data!;
      }
      if (suppliersResult.isSuccess) {
        _suppliers = suppliersResult.data!;
      }
      if (itemsResult.isSuccess) {
        _items = itemsResult.data!;
      }

      // توليد رقم فاتورة جديد إذا لم تكن موجودة
      if (widget.invoice == null) {
        _generateInvoiceNumber();
      }
    } catch (e) {
      _showErrorDialog('خطأ في تحميل البيانات: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// تحميل بيانات الفاتورة للتعديل
  void _loadInvoiceData() {
    final invoice = widget.invoice!;
    _invoiceNumberController.text = invoice.invoiceNumber;
    _notesController.text = invoice.notes ?? '';
    _discountController.text = invoice.discountAmount.toString();
    _selectedDate = invoice.date;
    _dueDate = invoice.dueDate;
    _selectedCustomer = invoice.customer;
    _selectedSupplier = invoice.supplier;
    _invoiceLines = List.from(invoice.lines);
  }

  /// توليد رقم فاتورة جديد
  void _generateInvoiceNumber() {
    final prefix = widget.invoiceType == InvoiceType.sales ? 'S' : 'P';
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    _invoiceNumberController.text = '$prefix${timestamp % 100000}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.invoiceType == InvoiceType.sales
              ? 'فاتورة مبيعات متقدمة'
              : 'فاتورة مشتريات متقدمة',
        ),
        actions: [
          IconButton(icon: const Icon(Icons.save), onPressed: _saveInvoice),
          if (widget.invoice != null) ...[
            IconButton(
              icon: const Icon(Icons.picture_as_pdf),
              onPressed: _generatePdf,
            ),
            IconButton(icon: const Icon(Icons.print), onPressed: _printInvoice),
          ],
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInvoiceHeader(),
                      const SizedBox(height: 20),
                      _buildCustomerSupplierSection(),
                      const SizedBox(height: 20),
                      _buildInvoiceLinesSection(),
                      const SizedBox(height: 20),
                      _buildTotalsSection(),
                      const SizedBox(height: 20),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء رأس الفاتورة
  Widget _buildInvoiceHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _invoiceNumberController,
                    decoration: const InputDecoration(
                      labelText: 'رقم الفاتورة',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'رقم الفاتورة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الفاتورة',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDueDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الاستحقاق (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        _dueDate != null
                            ? '${_dueDate!.day}/${_dueDate!.month}/${_dueDate!.year}'
                            : 'لم يتم التحديد',
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Row(
                    children: [
                      Checkbox(
                        value: _autoCalculateTax,
                        onChanged: (value) {
                          setState(() {
                            _autoCalculateTax = value ?? true;
                          });
                        },
                      ),
                      const Text('حساب الضريبة تلقائياً'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم العميل/المورد
  Widget _buildCustomerSupplierSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.invoiceType == InvoiceType.sales
                  ? 'معلومات العميل'
                  : 'معلومات المورد',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (widget.invoiceType == InvoiceType.sales)
              DropdownButtonFormField<Customer>(
                value: _selectedCustomer,
                decoration: const InputDecoration(
                  labelText: 'اختر العميل',
                  border: OutlineInputBorder(),
                ),
                items: _customers.map((customer) {
                  return DropdownMenuItem(
                    value: customer,
                    child: Text(customer.name),
                  );
                }).toList(),
                onChanged: (customer) {
                  setState(() {
                    _selectedCustomer = customer;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يجب اختيار العميل';
                  }
                  return null;
                },
              )
            else
              DropdownButtonFormField<Supplier>(
                value: _selectedSupplier,
                decoration: const InputDecoration(
                  labelText: 'اختر المورد',
                  border: OutlineInputBorder(),
                ),
                items: _suppliers.map((supplier) {
                  return DropdownMenuItem(
                    value: supplier,
                    child: Text(supplier.name),
                  );
                }).toList(),
                onChanged: (supplier) {
                  setState(() {
                    _selectedSupplier = supplier;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يجب اختيار المورد';
                  }
                  return null;
                },
              ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم بنود الفاتورة
  Widget _buildInvoiceLinesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'بنود الفاتورة',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                ElevatedButton.icon(
                  onPressed: _addInvoiceLine,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة بند'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_invoiceLines.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('لا توجد بنود في الفاتورة'),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _invoiceLines.length,
                itemBuilder: (context, index) {
                  return _buildInvoiceLineItem(index);
                },
              ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر بند الفاتورة
  Widget _buildInvoiceLineItem(int index) {
    final line = _invoiceLines[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: DropdownButtonFormField<Item>(
                    value: line.item,
                    decoration: const InputDecoration(
                      labelText: 'الصنف',
                      border: OutlineInputBorder(),
                    ),
                    items: _items.map((item) {
                      return DropdownMenuItem(
                        value: item,
                        child: Text(item.name),
                      );
                    }).toList(),
                    onChanged: (item) {
                      setState(() {
                        _invoiceLines[index] = line.copyWith(
                          item: item,
                          itemId: item?.id,
                          unitPrice: item?.sellingPrice ?? 0.0,
                        );
                        _calculateLineTotal(index);
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    initialValue: line.quantity.toString(),
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final quantity = double.tryParse(value) ?? 0.0;
                      setState(() {
                        _invoiceLines[index] = line.copyWith(
                          quantity: quantity,
                        );
                        _calculateLineTotal(index);
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    initialValue: line.unitPrice.toString(),
                    decoration: const InputDecoration(
                      labelText: 'سعر الوحدة',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final price = double.tryParse(value) ?? 0.0;
                      setState(() {
                        _invoiceLines[index] = line.copyWith(unitPrice: price);
                        _calculateLineTotal(index);
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 80,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    line.lineTotal.toStringAsFixed(2),
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                IconButton(
                  onPressed: () => _removeInvoiceLine(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
              ],
            ),
            if (line.description != null && line.description!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: TextFormField(
                  initialValue: line.description,
                  decoration: const InputDecoration(
                    labelText: 'وصف إضافي',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _invoiceLines[index] = line.copyWith(description: value);
                    });
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الإجماليات
  Widget _buildTotalsSection() {
    final subtotal = _calculateSubtotal();
    final discount = double.tryParse(_discountController.text) ?? 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الإجماليات', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _discountController,
                    decoration: const InputDecoration(
                      labelText: 'الخصم',
                      border: OutlineInputBorder(),
                      suffixText: 'ر.س',
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {});
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Row(
                    children: [
                      Checkbox(
                        value: _isInclusive,
                        onChanged: (value) {
                          setState(() {
                            _isInclusive = value ?? false;
                          });
                        },
                      ),
                      const Text('الضريبة شاملة'),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            _buildTotalRow('المجموع الفرعي:', subtotal),
            if (discount > 0) _buildTotalRow('الخصم:', -discount),
            FutureBuilder<double>(
              future: _calculateTax(subtotal - discount),
              builder: (context, snapshot) {
                final tax = snapshot.data ?? 0.0;
                final total = subtotal - discount + tax;
                return Column(
                  children: [
                    _buildTotalRow('الضريبة:', tax),
                    const Divider(thickness: 2),
                    _buildTotalRow('الإجمالي النهائي:', total, isTotal: true),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف الإجمالي
  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ر.س',
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: amount < 0 ? Colors.red : null,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الملاحظات
  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ملاحظات', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الإجراءات السفلية
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveInvoice,
              child: const Text('حفظ الفاتورة'),
            ),
          ),
        ],
      ),
    );
  }

  // ==================== وظائف المساعدة ====================

  /// إضافة بند جديد للفاتورة
  void _addInvoiceLine() {
    setState(() {
      _invoiceLines.add(
        InvoiceLine(
          id: DateTime.now().millisecondsSinceEpoch,
          invoiceId: 0,
          itemId: 0,
          quantity: 1.0,
          unitPrice: 0.0,
          lineTotal: 0.0,
          description: '',
        ),
      );
    });
  }

  /// حذف بند من الفاتورة
  void _removeInvoiceLine(int index) {
    setState(() {
      _invoiceLines.removeAt(index);
    });
  }

  /// حساب إجمالي البند
  void _calculateLineTotal(int index) {
    final line = _invoiceLines[index];
    final total = line.quantity * line.unitPrice;
    setState(() {
      _invoiceLines[index] = line.copyWith(lineTotal: total);
    });
  }

  /// حساب المجموع الفرعي
  double _calculateSubtotal() {
    return _invoiceLines.fold(0.0, (sum, line) => sum + line.lineTotal);
  }

  /// حساب الضريبة
  Future<double> _calculateTax(double amount) async {
    if (!_autoCalculateTax || amount <= 0) return 0.0;

    try {
      // استخدام خدمة الضرائب لحساب ضريبة القيمة المضافة 15%
      final result = await _taxService.calculateVATAdvanced(
        amount: amount,
        vatRate: 15.0, // ضريبة القيمة المضافة 15%
        isInclusive: _isInclusive,
      );

      if (result.isSuccess) {
        return result.data!['vatAmount'] ?? 0.0;
      } else {
        // في حالة فشل الخدمة، استخدم الحساب التقليدي
        if (_isInclusive) {
          return amount * 0.15 / 1.15;
        } else {
          return amount * 0.15;
        }
      }
    } catch (e) {
      // في حالة حدوث خطأ، استخدم الحساب التقليدي
      if (_isInclusive) {
        return amount * 0.15 / 1.15;
      } else {
        return amount * 0.15;
      }
    }
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  /// اختيار تاريخ الاستحقاق
  Future<void> _selectDueDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? _selectedDate.add(const Duration(days: 30)),
      firstDate: _selectedDate,
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        _dueDate = picked;
      });
    }
  }

  /// حفظ الفاتورة
  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_invoiceLines.isEmpty) {
      _showErrorDialog('يجب إضافة بند واحد على الأقل للفاتورة');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final subtotal = _calculateSubtotal();
      final discount = double.tryParse(_discountController.text) ?? 0.0;
      final taxableAmount = subtotal - discount;
      final taxAmount = await _calculateTax(taxableAmount);
      final totalAmount = subtotal - discount + taxAmount;

      final invoice = Invoice(
        id: widget.invoice?.id ?? 0,
        invoiceNumber: _invoiceNumberController.text,
        invoiceType: widget.invoiceType,
        date: _selectedDate,
        dueDate: _dueDate,
        customerId: _selectedCustomer?.id,
        supplierId: _selectedSupplier?.id,
        subtotal: subtotal,
        discountAmount: discount,
        taxAmount: taxAmount,
        totalAmount: totalAmount,
        paidAmount: 0.0,
        status: InvoiceStatus.draft,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        lines: _invoiceLines,
        customer: _selectedCustomer,
        supplier: _selectedSupplier,
      );

      final result = await _invoiceService.createInvoiceWithIntegration(
        invoice,
      );

      if (result.isSuccess) {
        if (mounted) {
          _showSuccessDialog('تم حفظ الفاتورة بنجاح');
          Navigator.of(context).pop(result.data);
        }
      } else {
        if (mounted) {
          _showErrorDialog('خطأ في حفظ الفاتورة: ${result.error}');
        }
      }
    } catch (e) {
      _showErrorDialog('خطأ غير متوقع: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// توليد PDF
  Future<void> _generatePdf() async {
    if (widget.invoice == null) {
      _showErrorDialog('يجب حفظ الفاتورة أولاً');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await _pdfService.generateAdvancedInvoicePdf(
        invoice: widget.invoice!,
        includeQRCode: true,
        includeSignature: true,
      );

      if (result.isSuccess) {
        _showSuccessDialog('تم توليد PDF بنجاح');
        // يمكن إضافة منطق لحفظ أو مشاركة الملف هنا
      } else {
        _showErrorDialog('خطأ في توليد PDF: ${result.error}');
      }
    } catch (e) {
      _showErrorDialog('خطأ في توليد PDF: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// طباعة الفاتورة
  Future<void> _printInvoice() async {
    if (widget.invoice == null) {
      _showErrorDialog('يجب حفظ الفاتورة أولاً');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await _pdfService.printInvoice(widget.invoice!);

      if (result.isSuccess) {
        _showSuccessDialog('تم إرسال الفاتورة للطباعة');
      } else {
        _showErrorDialog('خطأ في الطباعة: ${result.error}');
      }
    } catch (e) {
      _showErrorDialog('خطأ في الطباعة: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// عرض رسالة خطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نجح'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
