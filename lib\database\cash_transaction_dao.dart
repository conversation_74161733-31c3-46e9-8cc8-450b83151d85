/// Data Access Object for Cash Transactions
/// Cash Transaction DAO for Smart Ledger
library;


import '../models/cash_vault.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class CashTransactionDao {
  static final CashTransactionDao _instance = CashTransactionDao._internal();
  factory CashTransactionDao() => _instance;
  CashTransactionDao._internal();

  /// Get all cash transactions
  Future<List<CashTransaction>> getAllCashTransactions() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashTransactions,
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CashTransaction.fromMap(maps[i]);
    });
  }

  /// Get cash transaction by ID
  Future<CashTransaction?> getCashTransactionById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashTransactions,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return CashTransaction.fromMap(maps.first);
    }
    return null;
  }

  /// Get cash transactions by vault ID
  Future<List<CashTransaction>> getCashTransactionsByVaultId(int vaultId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashTransactions,
      where: 'cash_vault_id = ?',
      whereArgs: [vaultId],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CashTransaction.fromMap(maps[i]);
    });
  }

  /// Get cash transactions by date range
  Future<List<CashTransaction>> getCashTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashTransactions,
      where: 'transaction_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CashTransaction.fromMap(maps[i]);
    });
  }

  /// Get cash transactions by type
  Future<List<CashTransaction>> getCashTransactionsByType(CashTransactionType type) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashTransactions,
      where: 'type = ?',
      whereArgs: [type.value],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CashTransaction.fromMap(maps[i]);
    });
  }

  /// Get pending cash transactions
  Future<List<CashTransaction>> getPendingCashTransactions() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashTransactions,
      where: 'status = ?',
      whereArgs: ['pending'],
      orderBy: 'transaction_date ASC, id ASC',
    );

    return List.generate(maps.length, (i) {
      return CashTransaction.fromMap(maps[i]);
    });
  }

  /// Insert new cash transaction
  Future<int> insertCashTransaction(CashTransaction transaction) async {
    final db = await DatabaseHelper().database;
    
    final transactionMap = transaction.toMap();
    transactionMap.remove('id'); // Remove ID for auto-increment
    transactionMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableCashTransactions,
      transactionMap,
    );
  }

  /// Update cash transaction
  Future<int> updateCashTransaction(CashTransaction transaction) async {
    final db = await DatabaseHelper().database;
    
    final transactionMap = transaction.toMap();
    transactionMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableCashTransactions,
      transactionMap,
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  /// Update transaction status
  Future<int> updateTransactionStatus(int transactionId, CashTransactionStatus status) async {
    final db = await DatabaseHelper().database;
    
    return await db.update(
      DatabaseSchema.tableCashTransactions,
      {
        'status': status.value,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [transactionId],
    );
  }

  /// Delete cash transaction
  Future<int> deleteCashTransaction(int id) async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tableCashTransactions,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get cash transaction summary by vault
  Future<Map<String, dynamic>> getCashTransactionSummaryByVault(int vaultId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        type,
        COUNT(*) as transaction_count,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount
      FROM ${DatabaseSchema.tableCashTransactions}
      WHERE cash_vault_id = ? AND status = 'completed'
      GROUP BY type
    ''', [vaultId]);

    final totalResult = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_transactions,
        SUM(CASE WHEN type IN ('receipt', 'withdrawal') THEN amount ELSE 0 END) as total_receipts,
        SUM(CASE WHEN type IN ('payment', 'transfer', 'deposit') THEN amount ELSE 0 END) as total_payments
      FROM ${DatabaseSchema.tableCashTransactions}
      WHERE cash_vault_id = ? AND status = 'completed'
    ''', [vaultId]);

    return {
      'by_type': result,
      'totals': totalResult.isNotEmpty ? totalResult.first : {},
    };
  }

  /// Get daily cash summary
  Future<List<Map<String, dynamic>>> getDailyCashSummary(DateTime date) async {
    final db = await DatabaseHelper().database;
    final dateStr = date.toIso8601String().split('T')[0];
    
    final result = await db.rawQuery('''
      SELECT 
        cv.name as vault_name,
        cv.code as vault_code,
        COUNT(ct.id) as transaction_count,
        SUM(CASE WHEN ct.type IN ('receipt', 'withdrawal') THEN ct.amount ELSE 0 END) as total_receipts,
        SUM(CASE WHEN ct.type IN ('payment', 'transfer', 'deposit') THEN ct.amount ELSE 0 END) as total_payments,
        (SUM(CASE WHEN ct.type IN ('receipt', 'withdrawal') THEN ct.amount ELSE 0 END) - 
         SUM(CASE WHEN ct.type IN ('payment', 'transfer', 'deposit') THEN ct.amount ELSE 0 END)) as net_change
      FROM ${DatabaseSchema.tableCashVaults} cv
      LEFT JOIN ${DatabaseSchema.tableCashTransactions} ct ON cv.id = ct.cash_vault_id 
        AND DATE(ct.transaction_date) = ? 
        AND ct.status = 'completed'
      WHERE cv.status = 'active'
      GROUP BY cv.id, cv.name, cv.code
      ORDER BY cv.name
    ''', [dateStr]);

    return result;
  }

  /// Search cash transactions
  Future<List<CashTransaction>> searchCashTransactions(String query) async {
    final db = await DatabaseHelper().database;
    final searchQuery = '%$query%';
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashTransactions,
      where: '''
        transaction_number LIKE ? OR 
        description LIKE ? OR 
        reference LIKE ? OR
        received_from LIKE ? OR
        paid_to LIKE ?
      ''',
      whereArgs: [searchQuery, searchQuery, searchQuery, searchQuery, searchQuery],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return CashTransaction.fromMap(maps[i]);
    });
  }

  /// Get cash transactions with vault details
  Future<List<Map<String, dynamic>>> getCashTransactionsWithVaultDetails() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        ct.*,
        cv.name as vault_name,
        cv.code as vault_code,
        tcv.name as to_vault_name,
        tcv.code as to_vault_code
      FROM ${DatabaseSchema.tableCashTransactions} ct
      LEFT JOIN ${DatabaseSchema.tableCashVaults} cv ON ct.cash_vault_id = cv.id
      LEFT JOIN ${DatabaseSchema.tableCashVaults} tcv ON ct.to_cash_vault_id = tcv.id
      ORDER BY ct.transaction_date DESC, ct.id DESC
    ''');

    return result;
  }

  /// Get vault balance at specific date
  Future<double> getVaultBalanceAtDate(int vaultId, DateTime date) async {
    final db = await DatabaseHelper().database;
    
    // Get opening balance
    final vaultResult = await db.query(
      DatabaseSchema.tableCashVaults,
      columns: ['opening_balance'],
      where: 'id = ?',
      whereArgs: [vaultId],
    );
    
    if (vaultResult.isEmpty) return 0.0;
    
    double balance = (vaultResult.first['opening_balance'] as num).toDouble();
    
    // Add completed transactions up to the date
    final transactionResult = await db.rawQuery('''
      SELECT 
        SUM(CASE WHEN type IN ('receipt', 'withdrawal') THEN amount ELSE 0 END) as receipts,
        SUM(CASE WHEN type IN ('payment', 'transfer', 'deposit') THEN amount ELSE 0 END) as payments
      FROM ${DatabaseSchema.tableCashTransactions}
      WHERE cash_vault_id = ? 
        AND status = 'completed'
        AND transaction_date <= ?
    ''', [vaultId, date.toIso8601String().split('T')[0]]);
    
    if (transactionResult.isNotEmpty) {
      final receipts = (transactionResult.first['receipts'] as num?)?.toDouble() ?? 0.0;
      final payments = (transactionResult.first['payments'] as num?)?.toDouble() ?? 0.0;
      balance += receipts - payments;
    }
    
    return balance;
  }

  /// Generate next transaction number
  Future<String> generateTransactionNumber() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM ${DatabaseSchema.tableCashTransactions}
      WHERE transaction_number LIKE 'CT${DateTime.now().year}%'
    ''');
    
    final count = (result.first['count'] as int) + 1;
    return 'CT${DateTime.now().year}${count.toString().padLeft(6, '0')}';
  }

  /// Get monthly cash flow
  Future<List<Map<String, dynamic>>> getMonthlyCashFlow(int year) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        strftime('%m', transaction_date) as month,
        COUNT(*) as transaction_count,
        SUM(CASE WHEN type IN ('receipt', 'withdrawal') THEN amount ELSE 0 END) as total_receipts,
        SUM(CASE WHEN type IN ('payment', 'transfer', 'deposit') THEN amount ELSE 0 END) as total_payments
      FROM ${DatabaseSchema.tableCashTransactions}
      WHERE strftime('%Y', transaction_date) = ? AND status = 'completed'
      GROUP BY strftime('%m', transaction_date)
      ORDER BY month
    ''', [year.toString()]);

    return result;
  }
}
