import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/employee.dart';

/// DAO للموظفين
/// Employee Data Access Object
class EmployeeDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج موظف جديد
  Future<int> insertEmployee(Employee employee) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableEmployees,
      employee.toMap()..remove('id'),
    );
  }

  /// تحديث موظف
  Future<int> updateEmployee(Employee employee) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableEmployees,
      employee.toMap(),
      where: 'id = ?',
      whereArgs: [employee.id],
    );
  }

  /// حذف موظف
  Future<int> deleteEmployee(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      DatabaseSchema.tableEmployees,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على موظف بالمعرف
  Future<Employee?> getEmployeeById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Employee.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على موظف بالرمز
  Future<Employee?> getEmployeeByCode(String employeeCode) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'employee_code = ?',
      whereArgs: [employeeCode],
    );

    if (maps.isNotEmpty) {
      return Employee.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على موظف بالهوية الوطنية
  Future<Employee?> getEmployeeByNationalId(String nationalId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'national_id = ?',
      whereArgs: [nationalId],
    );

    if (maps.isNotEmpty) {
      return Employee.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع الموظفين
  Future<List<Employee>> getAllEmployees() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين النشطين فقط
  Future<List<Employee>> getActiveEmployees() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'is_active = 1 AND status = ?',
      whereArgs: ['active'],
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين حسب القسم
  Future<List<Employee>> getEmployeesByDepartment(int departmentId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'department_id = ? AND is_active = 1',
      whereArgs: [departmentId],
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين حسب المنصب
  Future<List<Employee>> getEmployeesByPosition(int positionId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'position_id = ? AND is_active = 1',
      whereArgs: [positionId],
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين حسب المدير المباشر
  Future<List<Employee>> getEmployeesByManager(String managerId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'direct_manager_id = ? AND is_active = 1',
      whereArgs: [managerId],
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين حسب الحالة
  Future<List<Employee>> getEmployeesByStatus(EmployeeStatus status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين حسب نوع التوظيف
  Future<List<Employee>> getEmployeesByType(EmployeeType type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'employee_type = ? AND is_active = 1',
      whereArgs: [type.name],
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// البحث في الموظفين
  Future<List<Employee>> searchEmployees(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: '''
        first_name LIKE ? OR 
        last_name LIKE ? OR 
        middle_name LIKE ? OR 
        employee_code LIKE ? OR 
        national_id LIKE ? OR 
        phone LIKE ? OR 
        email LIKE ?
      ''',
      whereArgs: [
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
      ],
      orderBy: 'first_name ASC, last_name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين مع تفاصيل القسم والمنصب
  Future<List<Map<String, dynamic>>> getEmployeesWithDetails() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        e.*,
        d.name as department_name,
        d.code as department_code,
        p.title as position_title,
        p.code as position_code,
        m.first_name as manager_first_name,
        m.last_name as manager_last_name
      FROM ${DatabaseSchema.tableEmployees} e
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      LEFT JOIN ${DatabaseSchema.tablePositions} p ON e.position_id = p.id
      LEFT JOIN ${DatabaseSchema.tableEmployees} m ON e.direct_manager_id = m.employee_code
      WHERE e.is_active = 1
      ORDER BY e.first_name ASC, e.last_name ASC
    ''');

    return maps;
  }

  /// الحصول على الموظفين حسب نطاق الراتب
  Future<List<Employee>> getEmployeesBySalaryRange(
    double minSalary,
    double maxSalary,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'basic_salary >= ? AND basic_salary <= ? AND is_active = 1',
      whereArgs: [minSalary, maxSalary],
      orderBy: 'basic_salary DESC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين حسب تاريخ التوظيف
  Future<List<Employee>> getEmployeesByHireDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: 'hire_date >= ? AND hire_date <= ? AND is_active = 1',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'hire_date DESC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// الحصول على الموظفين المتقاعدين قريباً
  Future<List<Employee>> getEmployeesNearRetirement(
    int yearsToRetirement,
  ) async {
    final db = await _databaseHelper.database;
    final retirementAge = 60; // سن التقاعد
    final currentYear = DateTime.now().year;
    final targetBirthYear = currentYear - (retirementAge - yearsToRetirement);

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableEmployees,
      where: '''
        birth_date IS NOT NULL AND 
        strftime('%Y', birth_date) <= ? AND 
        is_active = 1 AND 
        status = 'active'
      ''',
      whereArgs: [targetBirthYear.toString()],
      orderBy: 'birth_date ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  /// تحديث حالة الموظف
  Future<int> updateEmployeeStatus(
    String employeeCode,
    EmployeeStatus status,
  ) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableEmployees,
      {'status': status.name, 'updated_at': DateTime.now().toIso8601String()},
      where: 'employee_code = ?',
      whereArgs: [employeeCode],
    );
  }

  /// تحديث راتب الموظف
  Future<int> updateEmployeeSalary(
    String employeeCode,
    double newSalary,
  ) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableEmployees,
      {
        'basic_salary': newSalary,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'employee_code = ?',
      whereArgs: [employeeCode],
    );
  }

  /// إنهاء خدمة الموظف
  Future<int> terminateEmployee(
    String employeeCode,
    DateTime terminationDate,
  ) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableEmployees,
      {
        'status': EmployeeStatus.terminated.name,
        'termination_date': terminationDate.toIso8601String(),
        'is_active': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'employee_code = ?',
      whereArgs: [employeeCode],
    );
  }

  /// الحصول على إحصائيات الموظفين
  Future<Map<String, dynamic>> getEmployeeStatistics() async {
    final db = await _databaseHelper.database;

    final totalEmployees = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableEmployees} WHERE is_active = 1',
    );

    final employeesByStatus = await db.rawQuery('''
      SELECT 
        status,
        COUNT(*) as count
      FROM ${DatabaseSchema.tableEmployees}
      WHERE is_active = 1
      GROUP BY status
    ''');

    final employeesByType = await db.rawQuery('''
      SELECT 
        employee_type,
        COUNT(*) as count
      FROM ${DatabaseSchema.tableEmployees}
      WHERE is_active = 1
      GROUP BY employee_type
    ''');

    final employeesByGender = await db.rawQuery('''
      SELECT 
        gender,
        COUNT(*) as count
      FROM ${DatabaseSchema.tableEmployees}
      WHERE is_active = 1
      GROUP BY gender
    ''');

    final averageSalary = await db.rawQuery(
      'SELECT AVG(basic_salary) as average FROM ${DatabaseSchema.tableEmployees} WHERE is_active = 1',
    );

    final totalSalaries = await db.rawQuery(
      'SELECT SUM(basic_salary) as total FROM ${DatabaseSchema.tableEmployees} WHERE is_active = 1',
    );

    return {
      'total_employees': totalEmployees.first['count'] as int,
      'employees_by_status': employeesByStatus,
      'employees_by_type': employeesByType,
      'employees_by_gender': employeesByGender,
      'average_salary':
          (averageSalary.first['average'] as num?)?.toDouble() ?? 0.0,
      'total_salaries':
          (totalSalaries.first['total'] as num?)?.toDouble() ?? 0.0,
    };
  }

  /// التحقق من تفرد رمز الموظف
  Future<bool> isEmployeeCodeUnique(
    String employeeCode, {
    int? excludeId,
  }) async {
    final db = await _databaseHelper.database;
    String whereClause = 'employee_code = ?';
    List<dynamic> whereArgs = [employeeCode];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final result = await db.query(
      DatabaseSchema.tableEmployees,
      where: whereClause,
      whereArgs: whereArgs,
    );

    return result.isEmpty;
  }

  /// التحقق من تفرد الهوية الوطنية
  Future<bool> isNationalIdUnique(String nationalId, {int? excludeId}) async {
    final db = await _databaseHelper.database;
    String whereClause = 'national_id = ?';
    List<dynamic> whereArgs = [nationalId];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final result = await db.query(
      DatabaseSchema.tableEmployees,
      where: whereClause,
      whereArgs: whereArgs,
    );

    return result.isEmpty;
  }
}
