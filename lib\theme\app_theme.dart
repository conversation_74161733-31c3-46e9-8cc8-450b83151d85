import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Primary Colors - نظام ألوان ثوري وجذاب
  static const Color primaryColor = Color(0xFF6366F1); // بنفسجي عصري
  static const Color primaryLightColor = Color(0xFF8B5CF6); // بنفسجي فاتح
  static const Color primaryDarkColor = Color(0xFF4F46E5); // بنفسجي غامق
  static const Color primaryAccent = Color(0xFFA855F7); // بنفسجي مميز

  // Secondary Colors - ألوان متدرجة جذابة
  static const Color secondaryColor = Color(0xFF10B981); // أخضر زمردي
  static const Color accentColor = Color(0xFFF59E0B); // ذهبي برتقالي
  static const Color errorColor = Color(0xFFEF4444); // أحمر حيوي
  static const Color warningColor = Color(0xFFF97316); // برتقالي تحذيري
  static const Color infoColor = Color(0xFF06B6D4); // سماوي معلوماتي

  // Background Colors - خلفيات متدرجة
  static const Color backgroundColor = Color(0xFFF8FAFC);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color glassMorphismColor = Color(0x1AFFFFFF); // تأثير الزجاج

  // Text Colors
  static const Color textPrimaryColor = Color(0xFF1F2937);
  static const Color textSecondaryColor = Color(0xFF6B7280);
  static const Color textLightColor = Color(0xFF9CA3AF);

  // Account Type Colors - ألوان مميزة لأنواع الحسابات
  static const Color assetColor = Color(0xFF10B981); // أخضر للأصول
  static const Color liabilityColor = Color(0xFFEF4444); // أحمر للخصوم
  static const Color equityColor = Color(0xFF8B5CF6); // بنفسجي لحقوق الملكية
  static const Color revenueColor = Color(0xFF06B6D4); // سماوي للإيرادات
  static const Color expenseColor = Color(0xFFF97316); // برتقالي للمصروفات

  // Gradients - تدرجات لونية ثورية
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryColor, primaryLightColor, primaryAccent],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF10B981), Color(0xFF059669), Color(0xFF047857)],
    stops: [0.0, 0.6, 1.0],
  );

  static const LinearGradient holographicGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF8B5CF6),
      Color(0xFF06B6D4),
      Color(0xFF10B981),
      Color(0xFFF59E0B),
    ],
    stops: [0.0, 0.33, 0.66, 1.0],
  );

  static const LinearGradient glassMorphismGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0x40FFFFFF), Color(0x20FFFFFF), Color(0x10FFFFFF)],
  );

  static const RadialGradient cosmicGradient = RadialGradient(
    center: Alignment.center,
    radius: 1.5,
    colors: [
      Color(0xFF8B5CF6),
      Color(0xFF6366F1),
      Color(0xFF4F46E5),
      Color(0xFF1E1B4B),
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
  );

  // Shadows - ظلال متقدمة وجذابة
  static const BoxShadow cardShadow = BoxShadow(
    color: Color(0x0F000000),
    blurRadius: 15,
    offset: Offset(0, 5),
    spreadRadius: 0,
  );

  static const BoxShadow elevatedShadow = BoxShadow(
    color: Color(0x20000000),
    blurRadius: 25,
    offset: Offset(0, 10),
    spreadRadius: 0,
  );

  static const BoxShadow glowShadow = BoxShadow(
    color: Color(0x40A855F7),
    blurRadius: 20,
    offset: Offset(0, 0),
    spreadRadius: 2,
  );

  static const BoxShadow holographicShadow = BoxShadow(
    color: Color(0x3006B6D4),
    blurRadius: 30,
    offset: Offset(0, 8),
    spreadRadius: 0,
  );

  static const List<BoxShadow> glassMorphismShadows = [
    BoxShadow(color: Color(0x1A000000), blurRadius: 20, offset: Offset(0, 8)),
    BoxShadow(
      color: Color(0x0DFFFFFF),
      blurRadius: 1,
      offset: Offset(0, 1),
      spreadRadius: 0,
    ),
  ];

  // Border Radius
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 24.0;

  // Spacing
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;
  static const double spacingXXLarge = 48.0;

  // Animation Durations - مدد حركات متقدمة
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  static const Duration animationExtraSlow = Duration(milliseconds: 800);
  static const Duration holographicAnimation = Duration(milliseconds: 1200);
  static const Duration cosmicAnimation = Duration(milliseconds: 2000);

  // Animation Curves - منحنيات حركة متقدمة
  static const Curve elasticCurve = Curves.elasticOut;
  static const Curve bounceCurve = Curves.bounceOut;
  static const Curve smoothCurve = Curves.easeInOutCubic;
  static const Curve holographicCurve = Curves.easeInOutQuart;

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        // background: backgroundColor, // deprecated - using surface instead
        error: errorColor,
      ),

      // Typography - خطوط عربية جميلة
      textTheme: GoogleFonts.cairoTextTheme().copyWith(
        displayLarge: GoogleFonts.cairo(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: textPrimaryColor,
          height: 1.2,
        ),
        displayMedium: GoogleFonts.cairo(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: textPrimaryColor,
          height: 1.3,
        ),
        displaySmall: GoogleFonts.cairo(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
          height: 1.3,
        ),
        headlineLarge: GoogleFonts.cairo(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
          height: 1.4,
        ),
        headlineMedium: GoogleFonts.cairo(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
          height: 1.4,
        ),
        headlineSmall: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
          height: 1.4,
        ),
        titleLarge: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
          height: 1.5,
        ),
        titleMedium: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
          height: 1.5,
        ),
        titleSmall: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: textSecondaryColor,
          height: 1.5,
        ),
        bodyLarge: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.normal,
          color: textPrimaryColor,
          height: 1.6,
        ),
        bodyMedium: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: textPrimaryColor,
          height: 1.6,
        ),
        bodySmall: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.normal,
          color: textSecondaryColor,
          height: 1.6,
        ),
        labelLarge: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
          height: 1.4,
        ),
        labelMedium: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: textSecondaryColor,
          height: 1.4,
        ),
        labelSmall: GoogleFonts.cairo(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: textLightColor,
          height: 1.4,
        ),
      ),

      // AppBar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: surfaceColor,
        foregroundColor: textPrimaryColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.cairo(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        iconTheme: const IconThemeData(color: textPrimaryColor, size: 24),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: cardColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        shadowColor: Colors.black.withValues(alpha: 0.1),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingLarge,
            vertical: spacingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          textStyle: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: backgroundColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingMedium,
        ),
        labelStyle: GoogleFonts.cairo(color: textSecondaryColor, fontSize: 14),
        hintStyle: GoogleFonts.cairo(color: textLightColor, fontSize: 14),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: surfaceColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textLightColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.normal,
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 4,
        shape: CircleBorder(),
      ),
    );
  }

  // Helper methods for account type colors
  static Color getAccountTypeColor(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'asset':
        return assetColor;
      case 'liability':
        return liabilityColor;
      case 'equity':
        return equityColor;
      case 'revenue':
        return revenueColor;
      case 'expense':
        return expenseColor;
      default:
        return textSecondaryColor;
    }
  }

  static IconData getAccountTypeIcon(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'asset':
        return Icons.account_balance_wallet;
      case 'liability':
        return Icons.credit_card;
      case 'equity':
        return Icons.business_center;
      case 'revenue':
        return Icons.trending_up;
      case 'expense':
        return Icons.trending_down;
      default:
        return Icons.account_circle;
    }
  }

  // Helper methods for advanced visual effects
  static BoxDecoration getGlassMorphismDecoration({
    double borderRadius = borderRadiusMedium,
    double opacity = 0.1,
  }) {
    return BoxDecoration(
      gradient: glassMorphismGradient,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1),
      boxShadow: glassMorphismShadows,
    );
  }

  static BoxDecoration getHolographicDecoration({
    double borderRadius = borderRadiusMedium,
  }) {
    return BoxDecoration(
      gradient: holographicGradient,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: [holographicShadow],
    );
  }

  static BoxDecoration getCosmicDecoration({
    double borderRadius = borderRadiusMedium,
  }) {
    return BoxDecoration(
      gradient: cosmicGradient,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: [glowShadow],
    );
  }

  static BoxDecoration getElevatedCardDecoration({
    double borderRadius = borderRadiusMedium,
    Color? color,
  }) {
    return BoxDecoration(
      color: color ?? cardColor,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: [elevatedShadow],
    );
  }
}
