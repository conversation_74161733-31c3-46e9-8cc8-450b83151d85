/// نماذج الجرد الدوري
/// Periodic Inventory Models for Smart Ledger
library;

import 'warehouse.dart';
import 'item.dart';

enum InventoryStatus {
  planned('planned', 'مخطط'),
  inProgress('in_progress', 'قيد التنفيذ'),
  completed('completed', 'مكتمل'),
  cancelled('cancelled', 'ملغي');

  const InventoryStatus(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum InventoryType {
  full('full', 'جرد شامل'),
  partial('partial', 'جرد جزئي'),
  cycle('cycle', 'جرد دوري'),
  spot('spot', 'جرد فوري');

  const InventoryType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// نموذج الجرد الدوري
/// Periodic Inventory Model
class PeriodicInventory {
  final int? id;
  final String inventoryNumber;
  final String name;
  final String? description;
  final InventoryType type;
  final InventoryStatus status;
  final int? warehouseId;
  final int? locationId;
  final DateTime plannedDate;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? supervisorId;
  final List<int>? teamMemberIds;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Warehouse? warehouse;
  WarehouseLocation? location;
  List<InventoryItem>? items;

  PeriodicInventory({
    this.id,
    required this.inventoryNumber,
    required this.name,
    this.description,
    this.type = InventoryType.full,
    this.status = InventoryStatus.planned,
    this.warehouseId,
    this.locationId,
    DateTime? plannedDate,
    this.startDate,
    this.endDate,
    this.supervisorId,
    this.teamMemberIds,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.warehouse,
    this.location,
    this.items,
  }) : plannedDate = plannedDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory PeriodicInventory.fromMap(Map<String, dynamic> map) {
    return PeriodicInventory(
      id: map['id'] as int?,
      inventoryNumber: map['inventory_number'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      type: InventoryType.values.firstWhere(
        (e) => e.value == map['type'],
        orElse: () => InventoryType.full,
      ),
      status: InventoryStatus.values.firstWhere(
        (e) => e.value == map['status'],
        orElse: () => InventoryStatus.planned,
      ),
      warehouseId: map['warehouse_id'] as int?,
      locationId: map['location_id'] as int?,
      plannedDate: DateTime.parse(map['planned_date'] as String),
      startDate: map['start_date'] != null
          ? DateTime.parse(map['start_date'] as String)
          : null,
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      supervisorId: map['supervisor_id'] as int?,
      teamMemberIds: map['team_member_ids'] != null
          ? (map['team_member_ids'] as String)
                .split(',')
                .map(int.parse)
                .toList()
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'inventory_number': inventoryNumber,
      'name': name,
      'description': description,
      'type': type.value,
      'status': status.value,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'planned_date': plannedDate.toIso8601String(),
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'supervisor_id': supervisorId,
      'team_member_ids': teamMemberIds?.join(','),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  PeriodicInventory copyWith({
    int? id,
    String? inventoryNumber,
    String? name,
    String? description,
    InventoryType? type,
    InventoryStatus? status,
    int? warehouseId,
    int? locationId,
    DateTime? plannedDate,
    DateTime? startDate,
    DateTime? endDate,
    int? supervisorId,
    List<int>? teamMemberIds,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PeriodicInventory(
      id: id ?? this.id,
      inventoryNumber: inventoryNumber ?? this.inventoryNumber,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      plannedDate: plannedDate ?? this.plannedDate,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      supervisorId: supervisorId ?? this.supervisorId,
      teamMemberIds: teamMemberIds ?? this.teamMemberIds,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'PeriodicInventory{inventoryNumber: $inventoryNumber, name: $name, status: ${status.arabicName}}';
  }
}

/// نموذج صنف الجرد
/// Inventory Item Model
class InventoryItem {
  final int? id;
  final int inventoryId;
  final int itemId;
  final int warehouseId;
  final int? locationId;
  final String? batchNumber;
  final String? serialNumber;
  final double systemQuantity;
  final double? countedQuantity;
  final double? variance;
  final double? unitCost;
  final double? totalVarianceCost;
  final String? notes;
  final int? countedBy;
  final DateTime? countedAt;
  final bool isReconciled;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Item? item;
  Warehouse? warehouse;
  WarehouseLocation? location;

  InventoryItem({
    this.id,
    required this.inventoryId,
    required this.itemId,
    required this.warehouseId,
    this.locationId,
    this.batchNumber,
    this.serialNumber,
    required this.systemQuantity,
    this.countedQuantity,
    this.variance,
    this.unitCost,
    this.totalVarianceCost,
    this.notes,
    this.countedBy,
    this.countedAt,
    this.isReconciled = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.item,
    this.warehouse,
    this.location,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory InventoryItem.fromMap(Map<String, dynamic> map) {
    return InventoryItem(
      id: map['id'] as int?,
      inventoryId: map['inventory_id'] as int,
      itemId: map['item_id'] as int,
      warehouseId: map['warehouse_id'] as int,
      locationId: map['location_id'] as int?,
      batchNumber: map['batch_number'] as String?,
      serialNumber: map['serial_number'] as String?,
      systemQuantity: (map['system_quantity'] as num).toDouble(),
      countedQuantity: (map['counted_quantity'] as num?)?.toDouble(),
      variance: (map['variance'] as num?)?.toDouble(),
      unitCost: (map['unit_cost'] as num?)?.toDouble(),
      totalVarianceCost: (map['total_variance_cost'] as num?)?.toDouble(),
      notes: map['notes'] as String?,
      countedBy: map['counted_by'] as int?,
      countedAt: map['counted_at'] != null
          ? DateTime.parse(map['counted_at'] as String)
          : null,
      isReconciled: (map['is_reconciled'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'inventory_id': inventoryId,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'batch_number': batchNumber,
      'serial_number': serialNumber,
      'system_quantity': systemQuantity,
      'counted_quantity': countedQuantity,
      'variance': variance,
      'unit_cost': unitCost,
      'total_variance_cost': totalVarianceCost,
      'notes': notes,
      'counted_by': countedBy,
      'counted_at': countedAt?.toIso8601String(),
      'is_reconciled': isReconciled ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Calculate variance
  double get calculatedVariance {
    if (countedQuantity == null) return 0.0;
    return countedQuantity! - systemQuantity;
  }

  /// Calculate variance cost
  double get calculatedVarianceCost {
    if (countedQuantity == null || unitCost == null) return 0.0;
    return calculatedVariance * unitCost!;
  }

  /// Check if item has variance
  bool get hasVariance {
    return calculatedVariance.abs() >
        0.001; // Allow for small rounding differences
  }

  /// Create a copy with updated fields
  InventoryItem copyWith({
    int? id,
    int? inventoryId,
    int? itemId,
    int? warehouseId,
    int? locationId,
    String? batchNumber,
    String? serialNumber,
    double? systemQuantity,
    double? countedQuantity,
    double? variance,
    double? unitCost,
    double? totalVarianceCost,
    String? notes,
    int? countedBy,
    DateTime? countedAt,
    bool? isReconciled,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InventoryItem(
      id: id ?? this.id,
      inventoryId: inventoryId ?? this.inventoryId,
      itemId: itemId ?? this.itemId,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      batchNumber: batchNumber ?? this.batchNumber,
      serialNumber: serialNumber ?? this.serialNumber,
      systemQuantity: systemQuantity ?? this.systemQuantity,
      countedQuantity: countedQuantity ?? this.countedQuantity,
      variance: variance ?? this.variance,
      unitCost: unitCost ?? this.unitCost,
      totalVarianceCost: totalVarianceCost ?? this.totalVarianceCost,
      notes: notes ?? this.notes,
      countedBy: countedBy ?? this.countedBy,
      countedAt: countedAt ?? this.countedAt,
      isReconciled: isReconciled ?? this.isReconciled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'InventoryItem{itemId: $itemId, systemQuantity: $systemQuantity, countedQuantity: $countedQuantity}';
  }
}
