import '../models/company_settings.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class CompanySettingsDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get company settings (there should be only one record)
  Future<CompanySettings?> getCompanySettings() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCompanySettings,
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return CompanySettings.fromMap(maps.first);
    }
    return null;
  }

  // Create initial company settings
  Future<int> insertCompanySettings(CompanySettings settings) async {
    Map<String, dynamic> settingsMap = settings.toMap();
    settingsMap.remove('id'); // Remove id for insert
    return await _dbHelper.insert(
      DatabaseSchema.tableCompanySettings,
      settingsMap,
    );
  }

  // Update company settings
  Future<int> updateCompanySettings(CompanySettings settings) async {
    if (settings.id == null) {
      // If no ID, this is a new record
      return await insertCompanySettings(settings);
    }

    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      settings.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [settings.id],
    );
  }

  // Update specific setting fields
  Future<int> updateCompanyName(
    String companyName, {
    String? companyNameEn,
  }) async {
    Map<String, dynamic> updates = {
      'company_name': companyName,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (companyNameEn != null) {
      updates['company_name_en'] = companyNameEn;
    }

    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      updates,
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }

  // Update company contact information
  Future<int> updateCompanyContact({
    String? address,
    String? phone,
    String? email,
    String? taxNumber,
  }) async {
    Map<String, dynamic> updates = {
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (address != null) updates['address'] = address;
    if (phone != null) updates['phone'] = phone;
    if (email != null) updates['email'] = email;
    if (taxNumber != null) updates['tax_number'] = taxNumber;

    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      updates,
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }

  // Update currency settings
  Future<int> updateCurrencySettings({
    String? currencyCode,
    String? currencySymbol,
    int? decimalPlaces,
  }) async {
    Map<String, dynamic> updates = {
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (currencyCode != null) updates['currency_code'] = currencyCode;
    if (currencySymbol != null) updates['currency_symbol'] = currencySymbol;
    if (decimalPlaces != null) updates['decimal_places'] = decimalPlaces;

    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      updates,
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }

  // Update fiscal year settings
  Future<int> updateFiscalYearSettings(String fiscalYearStart) async {
    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      {
        'fiscal_year_start': fiscalYearStart,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }

  // Update date format
  Future<int> updateDateFormat(String dateFormat) async {
    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      {
        'date_format': dateFormat,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }

  // Update company logo path
  Future<int> updateCompanyLogo(String? logoPath) async {
    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      {'logo_path': logoPath, 'updated_at': DateTime.now().toIso8601String()},
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }

  // Check if company settings exist
  Future<bool> hasCompanySettings() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCompanySettings,
      columns: ['id'],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  // Initialize default company settings if none exist
  Future<CompanySettings> initializeDefaultSettings() async {
    bool hasSettings = await hasCompanySettings();

    if (!hasSettings) {
      CompanySettings defaultSettings = CompanySettings(
        companyName: 'شركتي',
        companyNameEn: 'My Company',
        address: '',
        phone: '',
        email: '',
        taxNumber: '',
        currencyCode: 'SAR',
        currencySymbol: 'ر.س',
        fiscalYearStart: '01-01',
        decimalPlaces: 2,
        dateFormat: 'dd/MM/yyyy',
      );

      int id = await insertCompanySettings(defaultSettings);
      return defaultSettings.copyWith(id: id);
    }

    return (await getCompanySettings())!;
  }

  // Get formatted currency string
  Future<String> formatCurrency(double amount) async {
    CompanySettings? settings = await getCompanySettings();
    if (settings != null) {
      return settings.formatCurrency(amount);
    }
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  // Get formatted date string
  Future<String> formatDate(DateTime date) async {
    CompanySettings? settings = await getCompanySettings();
    if (settings != null) {
      return settings.formatDate(date);
    }
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Get current fiscal year dates
  Future<Map<String, DateTime>> getCurrentFiscalYear() async {
    CompanySettings? settings = await getCompanySettings();
    if (settings != null) {
      return {
        'start': settings.getCurrentFiscalYearStart(),
        'end': settings.getCurrentFiscalYearEnd(),
      };
    }

    // Default fiscal year (January 1 to December 31)
    DateTime now = DateTime.now();
    return {
      'start': DateTime(now.year, 1, 1),
      'end': DateTime(now.year, 12, 31),
    };
  }

  // Validate company settings
  Future<List<String>> validateSettings() async {
    CompanySettings? settings = await getCompanySettings();
    if (settings == null) {
      return ['إعدادات الشركة غير موجودة'];
    }

    return settings.validate();
  }

  // Export company settings for backup
  Future<Map<String, dynamic>?> exportSettings() async {
    CompanySettings? settings = await getCompanySettings();
    return settings?.toMap();
  }

  // Import company settings from backup
  Future<bool> importSettings(Map<String, dynamic> settingsData) async {
    try {
      // Remove id to avoid conflicts
      settingsData.remove('id');

      // Clear existing settings
      await _dbHelper.delete(DatabaseSchema.tableCompanySettings);

      // Insert imported settings
      CompanySettings settings = CompanySettings.fromMap(settingsData);
      await insertCompanySettings(settings);

      return true;
    } catch (e) {
      return false;
    }
  }

  // Reset to default settings
  Future<bool> resetToDefaults() async {
    try {
      // Clear existing settings
      await _dbHelper.delete(DatabaseSchema.tableCompanySettings);

      // Initialize default settings
      await initializeDefaultSettings();

      return true;
    } catch (e) {
      return false;
    }
  }

  // Get company display name (Arabic or English based on preference)
  Future<String> getCompanyDisplayName({bool useEnglish = false}) async {
    CompanySettings? settings = await getCompanySettings();
    if (settings == null) return 'شركتي';

    if (useEnglish &&
        settings.companyNameEn != null &&
        settings.companyNameEn!.isNotEmpty) {
      return settings.companyNameEn!;
    }

    return settings.companyName;
  }

  // Get company information for reports
  Future<Map<String, String>> getCompanyInfoForReports() async {
    CompanySettings? settings = await getCompanySettings();
    if (settings == null) {
      return {
        'name': 'شركتي',
        'address': '',
        'phone': '',
        'email': '',
        'tax_number': '',
      };
    }

    return {
      'name': settings.companyName,
      'name_en': settings.companyNameEn ?? '',
      'address': settings.address ?? '',
      'phone': settings.phone ?? '',
      'email': settings.email ?? '',
      'tax_number': settings.taxNumber ?? '',
      'currency': settings.currencySymbol,
    };
  }

  // Check if fiscal year has changed and needs updating
  Future<bool> needsFiscalYearUpdate() async {
    CompanySettings? settings = await getCompanySettings();
    if (settings == null) return false;

    DateTime currentFiscalStart = settings.getCurrentFiscalYearStart();
    DateTime now = DateTime.now();

    // Check if we're in a new fiscal year
    DateTime nextFiscalStart = settings.parseFiscalYearStart(
      currentFiscalStart.year + 1,
    );

    return now.isAfter(nextFiscalStart) ||
        now.isAtSameMomentAs(nextFiscalStart);
  }

  // Alias methods for service compatibility
  Future<int> updateContactInfo({
    String? phone,
    String? email,
    String? address,
  }) async {
    Map<String, dynamic> updates = {
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (phone != null) updates['phone'] = phone;
    if (email != null) updates['email'] = email;
    if (address != null) updates['address'] = address;

    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      updates,
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }

  Future<int> updateLogoPath(String? logoPath) async {
    return await updateCompanyLogo(logoPath);
  }

  Future<int> updateFiscalYearSettingsWithFormat({
    required String fiscalYearStart,
    String? dateFormat,
  }) async {
    Map<String, dynamic> updates = {
      'fiscal_year_start': fiscalYearStart,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (dateFormat != null) {
      updates['date_format'] = dateFormat;
    }

    return await _dbHelper.update(
      DatabaseSchema.tableCompanySettings,
      updates,
      where:
          'id = (SELECT MIN(id) FROM ${DatabaseSchema.tableCompanySettings})',
    );
  }
}
