/// نموذج المشروع وإدارة التكاليف
/// Project and Cost Management Model for Smart Ledger
library;

import 'customer.dart';
import 'project_cost.dart';

/// نموذج المشروع
class Project {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final ProjectType type;
  final ProjectStatus status;
  final int? customerId;
  final int? managerId;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime? actualEndDate;
  final double budgetAmount;
  final double actualCost;
  final double billedAmount;
  final double profitMargin;
  final ProjectPriority priority;
  final String? location;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Customer? customer;
  List<ProjectPhase> phases = [];
  List<ProjectCost> costs = [];
  List<ProjectResource> resources = [];
  List<ProjectTimeEntry> timeEntries = [];

  Project({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.type = ProjectType.internal,
    this.status = ProjectStatus.planning,
    this.customerId,
    this.managerId,
    required this.startDate,
    this.endDate,
    this.actualEndDate,
    this.budgetAmount = 0.0,
    this.actualCost = 0.0,
    this.billedAmount = 0.0,
    this.profitMargin = 0.0,
    this.priority = ProjectPriority.medium,
    this.location,
    this.notes,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.customer,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory Project.fromMap(Map<String, dynamic> map) {
    return Project(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      type: ProjectType.fromString(map['type'] as String),
      status: ProjectStatus.fromString(map['status'] as String),
      customerId: map['customer_id'] as int?,
      managerId: map['manager_id'] as int?,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      actualEndDate: map['actual_end_date'] != null
          ? DateTime.parse(map['actual_end_date'] as String)
          : null,
      budgetAmount: (map['budget_amount'] as num?)?.toDouble() ?? 0.0,
      actualCost: (map['actual_cost'] as num?)?.toDouble() ?? 0.0,
      billedAmount: (map['billed_amount'] as num?)?.toDouble() ?? 0.0,
      profitMargin: (map['profit_margin'] as num?)?.toDouble() ?? 0.0,
      priority: ProjectPriority.fromString(map['priority'] as String),
      location: map['location'] as String?,
      notes: map['notes'] as String?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'type': type.value,
      'status': status.value,
      'customer_id': customerId,
      'manager_id': managerId,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'actual_end_date': actualEndDate?.toIso8601String().split('T')[0],
      'budget_amount': budgetAmount,
      'actual_cost': actualCost,
      'billed_amount': billedAmount,
      'profit_margin': profitMargin,
      'priority': priority.value,
      'location': location,
      'notes': notes,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Project copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    ProjectType? type,
    ProjectStatus? status,
    int? customerId,
    int? managerId,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? actualEndDate,
    double? budgetAmount,
    double? actualCost,
    double? billedAmount,
    double? profitMargin,
    ProjectPriority? priority,
    String? location,
    String? notes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Project(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      customerId: customerId ?? this.customerId,
      managerId: managerId ?? this.managerId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      actualEndDate: actualEndDate ?? this.actualEndDate,
      budgetAmount: budgetAmount ?? this.budgetAmount,
      actualCost: actualCost ?? this.actualCost,
      billedAmount: billedAmount ?? this.billedAmount,
      profitMargin: profitMargin ?? this.profitMargin,
      priority: priority ?? this.priority,
      location: location ?? this.location,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب نسبة الإنجاز
  double get completionPercentage {
    if (phases.isEmpty) return 0.0;
    double totalWeight = phases.fold(0.0, (sum, phase) => sum + phase.weight);
    if (totalWeight == 0) return 0.0;
    double completedWeight = phases.fold(
      0.0,
      (sum, phase) => sum + (phase.weight * phase.completionPercentage / 100),
    );
    return (completedWeight / totalWeight) * 100;
  }

  /// حساب الربح المتوقع
  double get expectedProfit => budgetAmount - actualCost;

  /// حساب نسبة الربح
  double get profitPercentage {
    if (budgetAmount == 0) return 0.0;
    return (expectedProfit / budgetAmount) * 100;
  }

  /// حساب التكلفة المتبقية
  double get remainingBudget => budgetAmount - actualCost;

  /// حساب عدد الأيام المتبقية
  int? get remainingDays {
    if (endDate == null) return null;
    final now = DateTime.now();
    if (endDate!.isBefore(now)) return 0;
    return endDate!.difference(now).inDays;
  }

  /// حساب مدة المشروع بالأيام
  int get projectDuration {
    final end = actualEndDate ?? endDate ?? DateTime.now();
    return end.difference(startDate).inDays;
  }
}

/// مراحل المشروع
class ProjectPhase {
  final int? id;
  final int projectId;
  final String name;
  final String? description;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime? actualEndDate;
  final double budgetAmount;
  final double actualCost;
  final double weight;
  final double completionPercentage;
  final ProjectPhaseStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  List<ProjectTask> tasks = [];

  ProjectPhase({
    this.id,
    required this.projectId,
    required this.name,
    this.description,
    required this.startDate,
    this.endDate,
    this.actualEndDate,
    this.budgetAmount = 0.0,
    this.actualCost = 0.0,
    this.weight = 1.0,
    this.completionPercentage = 0.0,
    this.status = ProjectPhaseStatus.notStarted,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory ProjectPhase.fromMap(Map<String, dynamic> map) {
    return ProjectPhase(
      id: map['id'] as int?,
      projectId: map['project_id'] as int,
      name: map['name'] as String,
      description: map['description'] as String?,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      actualEndDate: map['actual_end_date'] != null
          ? DateTime.parse(map['actual_end_date'] as String)
          : null,
      budgetAmount: (map['budget_amount'] as num?)?.toDouble() ?? 0.0,
      actualCost: (map['actual_cost'] as num?)?.toDouble() ?? 0.0,
      weight: (map['weight'] as num?)?.toDouble() ?? 1.0,
      completionPercentage:
          (map['completion_percentage'] as num?)?.toDouble() ?? 0.0,
      status: ProjectPhaseStatus.fromString(map['status'] as String),
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'project_id': projectId,
      'name': name,
      'description': description,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'actual_end_date': actualEndDate?.toIso8601String().split('T')[0],
      'budget_amount': budgetAmount,
      'actual_cost': actualCost,
      'weight': weight,
      'completion_percentage': completionPercentage,
      'status': status.value,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// أنواع المشاريع
enum ProjectType {
  internal('internal', 'داخلي'),
  external('external', 'خارجي'),
  maintenance('maintenance', 'صيانة'),
  development('development', 'تطوير'),
  consulting('consulting', 'استشاري');

  const ProjectType(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static ProjectType fromString(String value) {
    return ProjectType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ProjectType.internal,
    );
  }

  String get displayName => nameAr;
}

/// حالات المشروع
enum ProjectStatus {
  planning('planning', 'تخطيط'),
  active('active', 'نشط'),
  onHold('on_hold', 'معلق'),
  completed('completed', 'مكتمل'),
  cancelled('cancelled', 'ملغي');

  const ProjectStatus(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static ProjectStatus fromString(String value) {
    return ProjectStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ProjectStatus.planning,
    );
  }

  String get displayName => nameAr;
}

/// أولويات المشروع
enum ProjectPriority {
  low('low', 'منخفضة'),
  medium('medium', 'متوسطة'),
  high('high', 'عالية'),
  critical('critical', 'حرجة');

  const ProjectPriority(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static ProjectPriority fromString(String value) {
    return ProjectPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => ProjectPriority.medium,
    );
  }

  String get displayName => nameAr;
}

/// حالات مراحل المشروع
enum ProjectPhaseStatus {
  notStarted('not_started', 'لم تبدأ'),
  inProgress('in_progress', 'قيد التنفيذ'),
  completed('completed', 'مكتملة'),
  delayed('delayed', 'متأخرة');

  const ProjectPhaseStatus(this.value, this.nameAr);
  final String value;
  final String nameAr;

  static ProjectPhaseStatus fromString(String value) {
    return ProjectPhaseStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ProjectPhaseStatus.notStarted,
    );
  }
}
