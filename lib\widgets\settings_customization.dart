import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// ⚙️ نظام الإعدادات والتخصيص
/// Settings and Customization System
///
/// هذا الملف يحتوي على نظام الإعدادات والتخصيص لا مثيل له في التاريخ
/// This file contains unprecedented settings and customization system in history

/// 🌟 لوحة الإعدادات والتخصيص
/// Settings and Customization Dashboard
class SettingsCustomizationDashboard extends StatefulWidget {
  const SettingsCustomizationDashboard({super.key});

  @override
  State<SettingsCustomizationDashboard> createState() =>
      _SettingsCustomizationDashboardState();
}

class _SettingsCustomizationDashboardState
    extends State<SettingsCustomizationDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _settingsController;
  late AnimationController _rotationController;
  late Animation<double> _mainAnimation;
  late Animation<double> _settingsAnimation;
  late Animation<double> _rotationAnimation;

  int _selectedTab =
      0; // 0 for company, 1 for system, 2 for appearance, 3 for advanced

  // Company Settings
  String _companyName = 'Smart Ledger';
  String _companyAddress = 'الرياض، المملكة العربية السعودية';
  String _companyPhone = '+966 11 123 4567';
  String _companyEmail = '<EMAIL>';
  String _companyTaxNumber = '123456789012345';
  String _companyCommercialRegister = '**********';

  // System Settings
  String _defaultCurrency = 'SAR';
  String _language = 'ar';
  bool _enableNotifications = true;
  bool _enableAutoBackup = true;
  bool _enableSoundEffects = true;
  bool _enableHapticFeedback = true;
  int _autoBackupInterval = 24; // hours

  // Appearance Settings
  String _theme = 'dark';
  double _fontSize = 14.0;
  bool _enableAnimations = true;
  bool _enableHolographicEffects = true;
  bool _enableQuantumEffects = true;
  double _effectsIntensity = 1.0;

  // Advanced Settings
  bool _enableDeveloperMode = false;
  bool _enableDebugMode = false;
  bool _enablePerformanceMonitoring = false;
  int _maxCacheSize = 100; // MB
  int _sessionTimeout = 30; // minutes

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _settingsController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _settingsAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _settingsController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _mainController.repeat(reverse: true);
    _settingsController.repeat(reverse: true);
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _settingsController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _settingsAnimation,
        _rotationAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.8 + (_mainAnimation.value * 0.8),
          child: QuantumEnergyEffect(
            intensity: 2.4 + (_settingsAnimation.value * 0.6),
            primaryColor: const Color(0xFFFF5722),
            secondaryColor: const Color(0xFFFF8A65),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFBF360C).withValues(alpha: 0.9),
                    const Color(0xFFD84315).withValues(alpha: 0.8),
                    const Color(0xFFFF5722).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFFFF5722).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFF5722).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام الإعدادات
                  Row(
                    children: [
                      Transform.scale(
                        scale: _settingsAnimation.value,
                        child: Transform.rotate(
                          angle: _rotationAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFFFF5722,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFFFF8A65,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.settings_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '⚙️ الإعدادات والتخصيص',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'تخصيص النظام وإعدادات الشركة والمظهر',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات الإعدادات
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  if (_selectedTab == 0) _buildCompanySettingsView(),
                  if (_selectedTab == 1) _buildSystemSettingsView(),
                  if (_selectedTab == 2) _buildAppearanceSettingsView(),
                  if (_selectedTab == 3) _buildAdvancedSettingsView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          _buildTabItem(0, 'الشركة', Icons.business_rounded),
          _buildTabItem(1, 'النظام', Icons.computer_rounded),
          _buildTabItem(2, 'المظهر', Icons.palette_rounded),
          _buildTabItem(3, 'متقدم', Icons.engineering_rounded),
        ],
      ),
    );
  }

  Widget _buildTabItem(int index, String title, IconData icon) {
    bool isSelected = _selectedTab == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTab = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFFFF5722).withValues(alpha: 0.3)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.6),
                size: 16,
              ),
              const SizedBox(height: 2),
              Text(
                title,
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.6),
                  fontWeight: FontWeight.bold,
                  fontSize: 9,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عرض إعدادات الشركة
  Widget _buildCompanySettingsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🏢 معلومات الشركة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        _buildSettingField('اسم الشركة', _companyName, Icons.business_rounded, (
          value,
        ) {
          setState(() => _companyName = value);
        }),

        _buildSettingField(
          'العنوان',
          _companyAddress,
          Icons.location_on_rounded,
          (value) {
            setState(() => _companyAddress = value);
          },
        ),

        _buildSettingField('رقم الهاتف', _companyPhone, Icons.phone_rounded, (
          value,
        ) {
          setState(() => _companyPhone = value);
        }),

        _buildSettingField(
          'البريد الإلكتروني',
          _companyEmail,
          Icons.email_rounded,
          (value) {
            setState(() => _companyEmail = value);
          },
        ),

        _buildSettingField(
          'الرقم الضريبي',
          _companyTaxNumber,
          Icons.receipt_rounded,
          (value) {
            setState(() => _companyTaxNumber = value);
          },
        ),

        _buildSettingField(
          'السجل التجاري',
          _companyCommercialRegister,
          Icons.assignment_rounded,
          (value) {
            setState(() => _companyCommercialRegister = value);
          },
        ),
      ],
    );
  }

  /// بناء عرض إعدادات النظام
  Widget _buildSystemSettingsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '💻 إعدادات النظام',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        _buildDropdownSetting(
          'العملة الافتراضية',
          _defaultCurrency,
          ['SAR', 'USD', 'EUR', 'GBP', 'AED'],
          Icons.attach_money_rounded,
          (value) {
            setState(() => _defaultCurrency = value);
          },
        ),

        _buildDropdownSetting(
          'اللغة',
          _language,
          ['ar', 'en'],
          Icons.language_rounded,
          (value) {
            setState(() => _language = value);
          },
        ),

        _buildSwitchSetting(
          'تفعيل الإشعارات',
          _enableNotifications,
          Icons.notifications_rounded,
          (value) {
            setState(() => _enableNotifications = value);
          },
        ),

        _buildSwitchSetting(
          'النسخ الاحتياطي التلقائي',
          _enableAutoBackup,
          Icons.backup_rounded,
          (value) {
            setState(() => _enableAutoBackup = value);
          },
        ),

        _buildSwitchSetting(
          'المؤثرات الصوتية',
          _enableSoundEffects,
          Icons.volume_up_rounded,
          (value) {
            setState(() => _enableSoundEffects = value);
          },
        ),

        _buildSwitchSetting(
          'الاهتزاز التفاعلي',
          _enableHapticFeedback,
          Icons.vibration_rounded,
          (value) {
            setState(() => _enableHapticFeedback = value);
          },
        ),

        _buildSliderSetting(
          'فترة النسخ الاحتياطي (ساعة)',
          _autoBackupInterval.toDouble(),
          1,
          168,
          Icons.schedule_rounded,
          (value) {
            setState(() => _autoBackupInterval = value.round());
          },
        ),
      ],
    );
  }

  /// بناء عرض إعدادات المظهر
  Widget _buildAppearanceSettingsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🎨 إعدادات المظهر',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        _buildDropdownSetting(
          'السمة',
          _theme,
          ['dark', 'light', 'auto'],
          Icons.palette_rounded,
          (value) {
            setState(() => _theme = value);
          },
        ),

        _buildSliderSetting(
          'حجم الخط',
          _fontSize,
          10,
          20,
          Icons.text_fields_rounded,
          (value) {
            setState(() => _fontSize = value);
          },
        ),

        _buildSwitchSetting(
          'تفعيل الحركات',
          _enableAnimations,
          Icons.animation_rounded,
          (value) {
            setState(() => _enableAnimations = value);
          },
        ),

        _buildSwitchSetting(
          'التأثيرات الهولوجرافية',
          _enableHolographicEffects,
          Icons.auto_awesome_rounded,
          (value) {
            setState(() => _enableHolographicEffects = value);
          },
        ),

        _buildSwitchSetting(
          'التأثيرات الكمية',
          _enableQuantumEffects,
          Icons.blur_on_rounded,
          (value) {
            setState(() => _enableQuantumEffects = value);
          },
        ),

        _buildSliderSetting(
          'شدة التأثيرات',
          _effectsIntensity,
          0.1,
          2.0,
          Icons.tune_rounded,
          (value) {
            setState(() => _effectsIntensity = value);
          },
        ),
      ],
    );
  }

  /// بناء عرض الإعدادات المتقدمة
  Widget _buildAdvancedSettingsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🔧 الإعدادات المتقدمة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        _buildSwitchSetting(
          'وضع المطور',
          _enableDeveloperMode,
          Icons.developer_mode_rounded,
          (value) {
            setState(() => _enableDeveloperMode = value);
          },
        ),

        _buildSwitchSetting(
          'وضع التصحيح',
          _enableDebugMode,
          Icons.bug_report_rounded,
          (value) {
            setState(() => _enableDebugMode = value);
          },
        ),

        _buildSwitchSetting(
          'مراقبة الأداء',
          _enablePerformanceMonitoring,
          Icons.speed_rounded,
          (value) {
            setState(() => _enablePerformanceMonitoring = value);
          },
        ),

        _buildSliderSetting(
          'حجم التخزين المؤقت (MB)',
          _maxCacheSize.toDouble(),
          50,
          500,
          Icons.storage_rounded,
          (value) {
            setState(() => _maxCacheSize = value.round());
          },
        ),

        _buildSliderSetting(
          'انتهاء الجلسة (دقيقة)',
          _sessionTimeout.toDouble(),
          5,
          120,
          Icons.timer_rounded,
          (value) {
            setState(() => _sessionTimeout = value.round());
          },
        ),
      ],
    );
  }

  /// بناء حقل إعداد نصي
  Widget _buildSettingField(
    String label,
    String value,
    IconData icon,
    Function(String) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFFFF5722).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFF5722).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: const Color(0xFFFF5722), size: 16),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                TextFormField(
                  initialValue: value,
                  style: const TextStyle(color: Colors.white, fontSize: 11),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: 'أدخل $label',
                    hintStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.5),
                      fontSize: 10,
                    ),
                  ),
                  onChanged: onChanged,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إعداد قائمة منسدلة
  Widget _buildDropdownSetting(
    String label,
    String value,
    List<String> options,
    IconData icon,
    Function(String) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFFFF5722).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFF5722).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: const Color(0xFFFF5722), size: 16),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                DropdownButton<String>(
                  value: value,
                  dropdownColor: const Color(0xFF1A1A1A),
                  style: const TextStyle(color: Colors.white, fontSize: 11),
                  underline: Container(),
                  items: options.map((String option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      onChanged(newValue);
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إعداد مفتاح
  Widget _buildSwitchSetting(
    String label,
    bool value,
    IconData icon,
    Function(bool) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFFFF5722).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFF5722).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: const Color(0xFFFF5722), size: 16),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFFFF5722),
          ),
        ],
      ),
    );
  }

  /// بناء إعداد شريط التمرير
  Widget _buildSliderSetting(
    String label,
    double value,
    double min,
    double max,
    IconData icon,
    Function(double) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFFFF5722).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF5722).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: const Color(0xFFFF5722), size: 16),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                value.toStringAsFixed(value % 1 == 0 ? 0 : 1),
                style: TextStyle(
                  color: const Color(0xFFFF5722),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Slider(
            value: value,
            min: min,
            max: max,
            activeColor: const Color(0xFFFF5722),
            inactiveColor: Colors.white.withValues(alpha: 0.3),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }
}
