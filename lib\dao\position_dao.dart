import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/department.dart';

/// DAO للمناصب
/// Position Data Access Object
class PositionDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج منصب جديد
  Future<int> insertPosition(Position position) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tablePositions,
      position.toMap()..remove('id'),
    );
  }

  /// تحديث منصب
  Future<int> updatePosition(Position position) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tablePositions,
      position.toMap(),
      where: 'id = ?',
      whereArgs: [position.id],
    );
  }

  /// حذف منصب
  Future<int> deletePosition(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      DatabaseSchema.tablePositions,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على منصب بالمعرف
  Future<Position?> getPositionById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Position.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على منصب بالرمز
  Future<Position?> getPositionByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Position.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع المناصب
  Future<List<Position>> getAllPositions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      orderBy: 'title ASC',
    );

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }

  /// الحصول على المناصب النشطة فقط
  Future<List<Position>> getActivePositions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      where: 'is_active = 1',
      orderBy: 'title ASC',
    );

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }

  /// الحصول على المناصب حسب القسم
  Future<List<Position>> getPositionsByDepartment(int departmentId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      where: 'department_id = ? AND is_active = 1',
      whereArgs: [departmentId],
      orderBy: 'title ASC',
    );

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }

  /// الحصول على المناصب حسب المستوى
  Future<List<Position>> getPositionsByLevel(PositionLevel level) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      where: 'level = ? AND is_active = 1',
      whereArgs: [level.name],
      orderBy: 'title ASC',
    );

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }

  /// البحث في المناصب
  Future<List<Position>> searchPositions(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      where: 'title LIKE ? OR code LIKE ? OR description LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'],
      orderBy: 'title ASC',
    );

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }

  /// الحصول على عدد الموظفين في المنصب
  Future<int> getEmployeeCount(int positionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableEmployees} WHERE position_id = ? AND is_active = 1',
      [positionId],
    );
    return result.first['count'] as int;
  }

  /// الحصول على متوسط الراتب للمنصب
  Future<double> getAverageSalary(int positionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT AVG(basic_salary) as average FROM ${DatabaseSchema.tableEmployees} WHERE position_id = ? AND is_active = 1',
      [positionId],
    );
    return (result.first['average'] as num?)?.toDouble() ?? 0.0;
  }

  /// الحصول على إجمالي رواتب المنصب
  Future<double> getTotalSalaries(int positionId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(basic_salary) as total FROM ${DatabaseSchema.tableEmployees} WHERE position_id = ? AND is_active = 1',
      [positionId],
    );
    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  /// تحديث حالة المنصب (تفعيل/إلغاء تفعيل)
  Future<int> updatePositionStatus(int id, bool isActive) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tablePositions,
      {
        'is_active': isActive ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على المناصب مع تفاصيل القسم
  Future<List<Map<String, dynamic>>> getPositionsWithDepartment() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        p.*,
        d.name as department_name,
        d.code as department_code
      FROM ${DatabaseSchema.tablePositions} p
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON p.department_id = d.id
      WHERE p.is_active = 1
      ORDER BY p.title ASC
    ''');

    return maps;
  }

  /// الحصول على المناصب مع عدد الموظفين
  Future<List<Map<String, dynamic>>> getPositionsWithEmployeeCount() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        p.*,
        d.name as department_name,
        COUNT(e.id) as employee_count,
        AVG(e.basic_salary) as average_salary,
        SUM(e.basic_salary) as total_salaries
      FROM ${DatabaseSchema.tablePositions} p
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON p.department_id = d.id
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON p.id = e.position_id AND e.is_active = 1
      WHERE p.is_active = 1
      GROUP BY p.id
      ORDER BY p.title ASC
    ''');

    return maps;
  }

  /// التحقق من إمكانية حذف المنصب
  Future<bool> canDeletePosition(int id) async {
    final employeeCount = await getEmployeeCount(id);
    return employeeCount == 0;
  }

  /// الحصول على المناصب حسب نطاق الراتب
  Future<List<Position>> getPositionsBySalaryRange(
    double minSalary,
    double maxSalary,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tablePositions,
      where:
          'is_active = 1 AND ((min_salary IS NOT NULL AND min_salary >= ?) OR (max_salary IS NOT NULL AND max_salary <= ?))',
      whereArgs: [minSalary, maxSalary],
      orderBy: 'title ASC',
    );

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }

  /// الحصول على إحصائيات المناصب
  Future<Map<String, dynamic>> getPositionStatistics() async {
    final db = await _databaseHelper.database;

    final totalPositions = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tablePositions} WHERE is_active = 1',
    );

    final positionsByLevel = await db.rawQuery('''
      SELECT 
        level,
        COUNT(*) as count
      FROM ${DatabaseSchema.tablePositions}
      WHERE is_active = 1
      GROUP BY level
      ORDER BY level
    ''');

    final positionsByDepartment = await db.rawQuery('''
      SELECT 
        d.name as department_name,
        COUNT(p.id) as position_count
      FROM ${DatabaseSchema.tablePositions} p
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON p.department_id = d.id
      WHERE p.is_active = 1
      GROUP BY d.id
      ORDER BY position_count DESC
    ''');

    return {
      'total_positions': totalPositions.first['count'] as int,
      'positions_by_level': positionsByLevel,
      'positions_by_department': positionsByDepartment,
    };
  }

  /// الحصول على المناصب الشاغرة (بدون موظفين)
  Future<List<Position>> getVacantPositions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT p.*
      FROM ${DatabaseSchema.tablePositions} p
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON p.id = e.position_id AND e.is_active = 1
      WHERE p.is_active = 1 AND e.id IS NULL
      ORDER BY p.title ASC
    ''');

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }

  /// الحصول على المناصب المشغولة (بها موظفين)
  Future<List<Position>> getOccupiedPositions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT p.*
      FROM ${DatabaseSchema.tablePositions} p
      INNER JOIN ${DatabaseSchema.tableEmployees} e ON p.id = e.position_id AND e.is_active = 1
      WHERE p.is_active = 1
      ORDER BY p.title ASC
    ''');

    return List.generate(maps.length, (i) {
      return Position.fromMap(maps[i]);
    });
  }
}
