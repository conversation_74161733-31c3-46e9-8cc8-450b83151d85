/// نماذج مكونات الراتب - البدلات والاستقطاعات
/// Payroll Components Models - Allowances and Deductions
library;

/// نموذج البدل
/// Allowance Model
class Allowance {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final AllowanceType type;
  final AllowanceCalculationType calculationType;
  final double amount;
  final double? percentage;
  final bool isTaxable;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Allowance({
    this.id,
    required this.code,
    required this.name,
    this.description,
    required this.type,
    required this.calculationType,
    required this.amount,
    this.percentage,
    this.isTaxable = true,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory Allowance.fromMap(Map<String, dynamic> map) {
    return Allowance(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      type: AllowanceType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AllowanceType.basic,
      ),
      calculationType: AllowanceCalculationType.values.firstWhere(
        (e) => e.name == map['calculation_type'],
        orElse: () => AllowanceCalculationType.fixed,
      ),
      amount: (map['amount'] as num).toDouble(),
      percentage: map['percentage'] != null
          ? (map['percentage'] as num).toDouble()
          : null,
      isTaxable: (map['is_taxable'] as int?) == 1,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'type': type.name,
      'calculation_type': calculationType.name,
      'amount': amount,
      'percentage': percentage,
      'is_taxable': isTaxable ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  Allowance copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    AllowanceType? type,
    AllowanceCalculationType? calculationType,
    double? amount,
    double? percentage,
    bool? isTaxable,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Allowance(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      calculationType: calculationType ?? this.calculationType,
      amount: amount ?? this.amount,
      percentage: percentage ?? this.percentage,
      isTaxable: isTaxable ?? this.isTaxable,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج الاستقطاع
/// Deduction Model
class Deduction {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final DeductionType type;
  final DeductionCalculationType calculationType;
  final double amount;
  final double? percentage;
  final double? maxAmount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Deduction({
    this.id,
    required this.code,
    required this.name,
    this.description,
    required this.type,
    required this.calculationType,
    required this.amount,
    this.percentage,
    this.maxAmount,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory Deduction.fromMap(Map<String, dynamic> map) {
    return Deduction(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      type: DeductionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => DeductionType.tax,
      ),
      calculationType: DeductionCalculationType.values.firstWhere(
        (e) => e.name == map['calculation_type'],
        orElse: () => DeductionCalculationType.fixed,
      ),
      amount: (map['amount'] as num).toDouble(),
      percentage: map['percentage'] != null
          ? (map['percentage'] as num).toDouble()
          : null,
      maxAmount: map['max_amount'] != null
          ? (map['max_amount'] as num).toDouble()
          : null,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'type': type.name,
      'calculation_type': calculationType.name,
      'amount': amount,
      'percentage': percentage,
      'max_amount': maxAmount,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  Deduction copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    DeductionType? type,
    DeductionCalculationType? calculationType,
    double? amount,
    double? percentage,
    double? maxAmount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Deduction(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      calculationType: calculationType ?? this.calculationType,
      amount: amount ?? this.amount,
      percentage: percentage ?? this.percentage,
      maxAmount: maxAmount ?? this.maxAmount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج بدل الموظف
/// Employee Allowance Model
class EmployeeAllowance {
  final int? id;
  final String employeeCode;
  final int allowanceId;
  final double amount;
  final DateTime effectiveDate;
  final DateTime? endDate;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Allowance? allowance;

  EmployeeAllowance({
    this.id,
    required this.employeeCode,
    required this.allowanceId,
    required this.amount,
    required this.effectiveDate,
    this.endDate,
    this.isActive = true,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory EmployeeAllowance.fromMap(Map<String, dynamic> map) {
    return EmployeeAllowance(
      id: map['id'] as int?,
      employeeCode: map['employee_code'] as String,
      allowanceId: map['allowance_id'] as int,
      amount: (map['amount'] as num).toDouble(),
      effectiveDate: DateTime.parse(map['effective_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      isActive: (map['is_active'] as int?) == 1,
      notes: map['notes'] as String?,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'allowance_id': allowanceId,
      'amount': amount,
      'effective_date': effectiveDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'is_active': isActive ? 1 : 0,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  EmployeeAllowance copyWith({
    int? id,
    String? employeeCode,
    int? allowanceId,
    double? amount,
    DateTime? effectiveDate,
    DateTime? endDate,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeAllowance(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      allowanceId: allowanceId ?? this.allowanceId,
      amount: amount ?? this.amount,
      effectiveDate: effectiveDate ?? this.effectiveDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج استقطاع الموظف
/// Employee Deduction Model
class EmployeeDeduction {
  final int? id;
  final String employeeCode;
  final int deductionId;
  final double amount;
  final DateTime effectiveDate;
  final DateTime? endDate;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Deduction? deduction;

  EmployeeDeduction({
    this.id,
    required this.employeeCode,
    required this.deductionId,
    required this.amount,
    required this.effectiveDate,
    this.endDate,
    this.isActive = true,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory EmployeeDeduction.fromMap(Map<String, dynamic> map) {
    return EmployeeDeduction(
      id: map['id'] as int?,
      employeeCode: map['employee_code'] as String,
      deductionId: map['deduction_id'] as int,
      amount: (map['amount'] as num).toDouble(),
      effectiveDate: DateTime.parse(map['effective_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      isActive: (map['is_active'] as int?) == 1,
      notes: map['notes'] as String?,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'deduction_id': deductionId,
      'amount': amount,
      'effective_date': effectiveDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'is_active': isActive ? 1 : 0,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  EmployeeDeduction copyWith({
    int? id,
    String? employeeCode,
    int? deductionId,
    double? amount,
    DateTime? effectiveDate,
    DateTime? endDate,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeDeduction(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      deductionId: deductionId ?? this.deductionId,
      amount: amount ?? this.amount,
      effectiveDate: effectiveDate ?? this.effectiveDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// تعدادات البدلات
enum AllowanceType {
  basic('بدل أساسي'),
  housing('بدل سكن'),
  transportation('بدل مواصلات'),
  food('بدل طعام'),
  overtime('بدل إضافي'),
  bonus('مكافأة'),
  commission('عمولة'),
  other('أخرى');

  const AllowanceType(this.displayName);
  final String displayName;
}

enum AllowanceCalculationType {
  fixed('مبلغ ثابت'),
  percentage('نسبة مئوية'),
  hourly('بالساعة'),
  daily('يومي');

  const AllowanceCalculationType(this.displayName);
  final String displayName;
}

/// تعدادات الاستقطاعات
enum DeductionType {
  tax('ضريبة'),
  insurance('تأمين'),
  loan('قرض'),
  advance('سلفة'),
  penalty('غرامة'),
  other('أخرى');

  const DeductionType(this.displayName);
  final String displayName;
}

enum DeductionCalculationType {
  fixed('مبلغ ثابت'),
  percentage('نسبة مئوية'),
  progressive('تصاعدي');

  const DeductionCalculationType(this.displayName);
  final String displayName;
}
