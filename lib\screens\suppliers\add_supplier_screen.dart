import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/supplier.dart';
import '../../services/supplier_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import '../../widgets/beautiful_inputs.dart';
import '../../widgets/smart_notifications.dart';

/// شاشة إضافة/تعديل المورد
class AddSupplierScreen extends StatefulWidget {
  final Supplier? supplier;

  const AddSupplierScreen({super.key, this.supplier});

  @override
  State<AddSupplierScreen> createState() => _AddSupplierScreenState();
}

class _AddSupplierScreenState extends State<AddSupplierScreen> {
  final _formKey = GlobalKey<FormState>();
  final SupplierService _supplierService = SupplierService();

  // Controllers
  final _codeController = TextEditingController();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _categoryController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _creditLimitController = TextEditingController();
  final _paymentTermsController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.supplier != null;
    if (_isEditing) {
      _populateFields();
    } else {
      _generateSupplierCode();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _categoryController.dispose();
    _taxNumberController.dispose();
    _creditLimitController.dispose();
    _paymentTermsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _populateFields() {
    final supplier = widget.supplier!;
    _codeController.text = supplier.code;
    _nameController.text = supplier.name;
    _phoneController.text = supplier.phone ?? '';
    _emailController.text = supplier.email ?? '';
    _addressController.text = supplier.address ?? '';
    _categoryController.text = supplier.category ?? '';
    _taxNumberController.text = supplier.taxNumber ?? '';
    _creditLimitController.text = supplier.creditLimit?.toString() ?? '';
    _paymentTermsController.text = supplier.paymentTerms?.toString() ?? '';
    _notesController.text = supplier.notes ?? '';
  }

  Future<void> _generateSupplierCode() async {
    final result = await _supplierService.getNextSupplierCode();
    if (result.isSuccess) {
      _codeController.text = result.data!;
    }
  }

  Future<void> _saveSupplier() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final supplier = Supplier(
        id: _isEditing ? widget.supplier!.id : null,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        category: _categoryController.text.trim().isEmpty ? null : _categoryController.text.trim(),
        taxNumber: _taxNumberController.text.trim().isEmpty ? null : _taxNumberController.text.trim(),
        creditLimit: _creditLimitController.text.trim().isEmpty 
            ? null 
            : double.tryParse(_creditLimitController.text.trim()),
        paymentTerms: _paymentTermsController.text.trim().isEmpty 
            ? null 
            : int.tryParse(_paymentTermsController.text.trim()),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        isActive: true,
        createdAt: _isEditing ? widget.supplier!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = _isEditing
          ? await _supplierService.updateSupplier(supplier)
          : await _supplierService.createSupplier(supplier);

      if (!mounted) return;

      if (result.isSuccess) {
        SmartNotificationManager.showSuccess(
          context,
          title: _isEditing ? 'تم التحديث' : 'تم الحفظ',
          message: _isEditing ? 'تم تحديث المورد بنجاح' : 'تم إضافة المورد بنجاح',
        );
        Navigator.of(context).pop(true);
      } else {
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: result.error ?? 'فشل في حفظ المورد',
        );
      }
    } catch (e) {
      if (!mounted) return;
      SmartNotificationManager.showError(
        context,
        title: 'خطأ',
        message: 'حدث خطأ أثناء حفظ المورد: ${e.toString()}',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل المورد' : 'إضافة مورد جديد'),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _saveSupplier,
              child: Text(
                _isEditing ? 'تحديث' : 'حفظ',
                style: const TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          children: [
            BeautifulCard(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المعلومات الأساسية',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // رمز المورد
                    BeautifulTextFormField(
                      controller: _codeController,
                      labelText: 'رمز المورد *',
                      prefixIcon: Icons.tag,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'رمز المورد مطلوب';
                        }
                        return null;
                      },
                      readOnly: _isEditing,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // اسم المورد
                    BeautifulTextFormField(
                      controller: _nameController,
                      labelText: 'اسم المورد *',
                      prefixIcon: Icons.business,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'اسم المورد مطلوب';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // الفئة
                    BeautifulTextFormField(
                      controller: _categoryController,
                      labelText: 'فئة المورد',
                      prefixIcon: Icons.category,
                      hintText: 'مثال: مواد خام، خدمات، معدات',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            BeautifulCard(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات الاتصال',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // رقم الهاتف
                    BeautifulTextFormField(
                      controller: _phoneController,
                      labelText: 'رقم الهاتف',
                      prefixIcon: Icons.phone,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s()]')),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // البريد الإلكتروني
                    BeautifulTextFormField(
                      controller: _emailController,
                      labelText: 'البريد الإلكتروني',
                      prefixIcon: Icons.email,
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'البريد الإلكتروني غير صحيح';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // العنوان
                    BeautifulTextFormField(
                      controller: _addressController,
                      labelText: 'العنوان',
                      prefixIcon: Icons.location_on,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            BeautifulCard(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المعلومات المالية والضريبية',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // الرقم الضريبي
                    BeautifulTextFormField(
                      controller: _taxNumberController,
                      labelText: 'الرقم الضريبي',
                      prefixIcon: Icons.receipt_long,
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // حد الائتمان
                    BeautifulTextFormField(
                      controller: _creditLimitController,
                      labelText: 'حد الائتمان',
                      prefixIcon: Icons.credit_card,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                      ],
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'يجب أن يكون رقماً صحيحاً';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // شروط الدفع
                    BeautifulTextFormField(
                      controller: _paymentTermsController,
                      labelText: 'شروط الدفع (بالأيام)',
                      prefixIcon: Icons.schedule,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (int.tryParse(value) == null) {
                            return 'يجب أن يكون رقماً صحيحاً';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            BeautifulCard(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ملاحظات إضافية',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // الملاحظات
                    BeautifulTextFormField(
                      controller: _notesController,
                      labelText: 'ملاحظات',
                      prefixIcon: Icons.note,
                      maxLines: 3,
                      hintText: 'أي ملاحظات إضافية حول المورد...',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingLarge),
          ],
        ),
      ),
    );
  }
}
