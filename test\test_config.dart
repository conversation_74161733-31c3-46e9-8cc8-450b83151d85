
import 'package:smart_ledger/models/customer.dart';
import 'package:smart_ledger/models/supplier.dart';
import 'package:smart_ledger/models/item.dart';
import 'package:smart_ledger/models/invoice.dart';

/// إعدادات الاختبار للنظام المتقدم للفواتير
class TestConfig {
  /// إنشاء عميل تجريبي للاختبارات
  static Customer createTestCustomer({int id = 1}) {
    return Customer(
      id: id,
      code: 'CUST${id.toString().padLeft(3, '0')}',
      name: 'عميل تجريبي $id',
      email: 'customer$<EMAIL>',
      phone: '05${id.toString().padLeft(8, '0')}',
      address: 'عنوان العميل التجريبي $id',
      taxNumber: 'TAX$id',
      creditLimit: 10000.0,
      currentBalance: 0.0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء مورد تجريبي للاختبارات
  static Supplier createTestSupplier({int id = 1}) {
    return Supplier(
      id: id,
      code: 'SUPP${id.toString().padLeft(3, '0')}',
      name: 'مورد تجريبي $id',
      email: 'supplier$<EMAIL>',
      phone: '05${(id + 100).toString().padLeft(8, '0')}',
      address: 'عنوان المورد التجريبي $id',
      category: 'موردين عامين',
      taxNumber: 'STAX$id',
      creditLimit: 50000.0,
      currentBalance: 0.0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء صنف تجريبي للاختبارات
  static Item createTestItem({int id = 1, String name = 'منتج تجريبي'}) {
    return Item(
      id: id,
      code: 'ITEM${id.toString().padLeft(3, '0')}',
      name: '$name $id',
      description: 'وصف $name $id',
      unit: 'قطعة',
      barcode: '${1234567890 + id}',
      category: 'منتجات عامة',
      costPrice: 50.0 + id,
      sellingPrice: 100.0 + id,
      minStockLevel: 10.0,
      maxStockLevel: 100.0,
      currentStock: 50.0,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء بند فاتورة تجريبي
  static InvoiceLine createTestInvoiceLine({
    int id = 1,
    int itemId = 1,
    double quantity = 1.0,
    double unitPrice = 100.0,
    Item? item,
  }) {
    final lineTotal = quantity * unitPrice;
    return InvoiceLine(
      id: id,
      invoiceId: 0,
      itemId: itemId,
      description: 'بند تجريبي $id',
      quantity: quantity,
      unitPrice: unitPrice,
      discountPercentage: 0.0,
      lineTotal: lineTotal,
      lineOrder: id,
      createdAt: DateTime.now(),
      item: item ?? createTestItem(id: itemId),
    );
  }

  /// إنشاء فاتورة مبيعات تجريبية
  static Invoice createTestSalesInvoice({
    int id = 1,
    Customer? customer,
    List<InvoiceLine>? lines,
  }) {
    final testCustomer = customer ?? createTestCustomer(id: id);
    final testLines =
        lines ??
        [
          createTestInvoiceLine(id: 1, quantity: 2.0, unitPrice: 100.0),
          createTestInvoiceLine(id: 2, quantity: 1.0, unitPrice: 50.0),
        ];

    final subtotal = testLines.fold(0.0, (sum, line) => sum + line.lineTotal);
    final taxAmount = subtotal * 0.15; // 15% ضريبة
    final totalAmount = subtotal + taxAmount;

    return Invoice(
      id: id,
      invoiceNumber: 'S${id.toString().padLeft(3, '0')}',
      invoiceType: InvoiceType.sales,
      date: DateTime.now(),
      dueDate: DateTime.now().add(const Duration(days: 30)),
      customerId: testCustomer.id,
      subtotal: subtotal,
      taxAmount: taxAmount,
      discountAmount: 0.0,
      totalAmount: totalAmount,
      paidAmount: 0.0,
      status: InvoiceStatus.draft,
      notes: 'فاتورة مبيعات تجريبية $id',
      lines: testLines,
      customer: testCustomer,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء فاتورة مشتريات تجريبية
  static Invoice createTestPurchaseInvoice({
    int id = 1,
    Supplier? supplier,
    List<InvoiceLine>? lines,
  }) {
    final testSupplier = supplier ?? createTestSupplier(id: id);
    final testLines =
        lines ??
        [
          createTestInvoiceLine(id: 1, quantity: 5.0, unitPrice: 25.0),
          createTestInvoiceLine(id: 2, quantity: 3.0, unitPrice: 40.0),
        ];

    final subtotal = testLines.fold(0.0, (sum, line) => sum + line.lineTotal);
    final taxAmount = subtotal * 0.15; // 15% ضريبة
    final totalAmount = subtotal + taxAmount;

    return Invoice(
      id: id,
      invoiceNumber: 'P${id.toString().padLeft(3, '0')}',
      invoiceType: InvoiceType.purchase,
      date: DateTime.now(),
      dueDate: DateTime.now().add(const Duration(days: 15)),
      supplierId: testSupplier.id,
      subtotal: subtotal,
      taxAmount: taxAmount,
      discountAmount: 0.0,
      totalAmount: totalAmount,
      paidAmount: 0.0,
      status: InvoiceStatus.draft,
      notes: 'فاتورة مشتريات تجريبية $id',
      lines: testLines,
      supplier: testSupplier,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء فاتورة مع خصم
  static Invoice createTestInvoiceWithDiscount({
    int id = 1,
    double discountAmount = 50.0,
    InvoiceType type = InvoiceType.sales,
  }) {
    final baseInvoice = type == InvoiceType.sales
        ? createTestSalesInvoice(id: id)
        : createTestPurchaseInvoice(id: id);

    final subtotal = baseInvoice.subtotal;
    final discountedAmount = subtotal - discountAmount;
    final taxAmount = discountedAmount * 0.15;
    final totalAmount = discountedAmount + taxAmount;

    return baseInvoice.copyWith(
      discountAmount: discountAmount,
      taxAmount: taxAmount,
      totalAmount: totalAmount,
    );
  }

  /// إنشاء فاتورة مع ضرائب متعددة
  static Invoice createTestInvoiceWithMultipleTaxes({
    int id = 1,
    InvoiceType type = InvoiceType.sales,
  }) {
    final baseInvoice = type == InvoiceType.sales
        ? createTestSalesInvoice(id: id)
        : createTestPurchaseInvoice(id: id);

    final subtotal = baseInvoice.subtotal;
    final vatAmount = subtotal * 0.15; // 15% ضريبة القيمة المضافة
    final withholdingAmount = subtotal * 0.05; // 5% ضريبة استقطاع
    final totalTaxAmount = vatAmount + withholdingAmount;
    final totalAmount = subtotal + totalTaxAmount;

    return baseInvoice.copyWith(
      taxAmount: totalTaxAmount,
      totalAmount: totalAmount,
      notes: '${baseInvoice.notes} - مع ضرائب متعددة',
    );
  }

  /// بيانات اختبار للعملات
  static const Map<String, double> testExchangeRates = {
    'USD': 3.75,
    'EUR': 4.10,
    'GBP': 4.65,
    'AED': 1.02,
    'KWD': 12.25,
  };

  /// بيانات اختبار للضرائب
  static const Map<String, double> testTaxRates = {
    'VAT': 15.0,
    'WITHHOLDING': 5.0,
    'INCOME': 20.0,
    'CORPORATE': 25.0,
  };

  /// إعدادات اختبار PDF
  static const Map<String, dynamic> testPdfSettings = {
    'includeQRCode': true,
    'includeWatermark': true,
    'watermarkText': 'نسخة تجريبية',
    'includeSignature': true,
    'logoPath': 'assets/images/test_logo.png',
    'fontSize': 12,
    'fontFamily': 'Arial',
  };

  /// تنظيف بيانات الاختبار
  static void cleanup() {
    // يمكن إضافة منطق تنظيف قاعدة البيانات هنا
    // تم تنظيف بيانات الاختبار
  }

  /// التحقق من صحة البيانات التجريبية
  static bool validateTestData(dynamic data) {
    if (data == null) return false;

    if (data is Invoice) {
      return data.invoiceNumber.isNotEmpty &&
          data.lines.isNotEmpty &&
          data.totalAmount > 0;
    }

    if (data is Customer) {
      return data.name.isNotEmpty && (data.email?.isNotEmpty ?? true);
    }

    if (data is Supplier) {
      return data.name.isNotEmpty && (data.email?.isNotEmpty ?? true);
    }

    if (data is Item) {
      return data.name.isNotEmpty &&
          data.code.isNotEmpty &&
          data.sellingPrice > 0;
    }

    return true;
  }
}
