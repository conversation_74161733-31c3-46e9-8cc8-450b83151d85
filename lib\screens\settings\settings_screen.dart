import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/company_settings.dart';
import '../../services/company_settings_service.dart';
import 'company_settings_screen.dart';
import 'system_settings_screen.dart';
import 'backup_restore_screen.dart';
import 'user_settings_screen.dart';
import 'icon_generator_screen.dart';
import 'performance_monitor_screen.dart';

/// شاشة الإعدادات الرئيسية
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final CompanySettingsService _settingsService = CompanySettingsService();
  CompanySettings? _companySettings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadSettings();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    final result = await _settingsService.getCompanySettings();
    if (mounted) {
      setState(() {
        if (result.isSuccess) {
          _companySettings = result.data;
        }
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildSettingsContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الإعدادات',
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'إدارة إعدادات التطبيق والشركة',
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.settings,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsContent() {
    return AnimationLimiter(
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            _buildCompanyInfoCard(),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'إعدادات المستخدم',
              'إدارة بيانات المستخدم والمنصب الوظيفي',
              Icons.person,
              Colors.purple,
              () => _navigateToUserSettings(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'إعدادات الشركة',
              'إدارة معلومات الشركة والإعدادات المالية',
              Icons.business,
              Colors.blue,
              () => _navigateToCompanySettings(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'إعدادات النظام',
              'إعدادات التطبيق والواجهة والإشعارات',
              Icons.settings_applications,
              Colors.green,
              () => _navigateToSystemSettings(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'النسخ الاحتياطي والاستعادة',
              'إدارة النسخ الاحتياطية واستعادة البيانات',
              Icons.backup,
              Colors.orange,
              () => _navigateToBackupRestore(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'مولد أيقونات التطبيق',
              'إنشاء وتخصيص أيقونات التطبيق',
              Icons.app_settings_alt,
              Colors.teal,
              () => _navigateToIconGenerator(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'مراقب الأداء',
              'مراقبة وتحسين أداء التطبيق',
              Icons.speed,
              Colors.indigo,
              () => _navigateToPerformanceMonitor(),
            ),
            const SizedBox(height: 16),
            _buildSettingsSection(
              'حول التطبيق',
              'معلومات التطبيق والإصدار والدعم',
              Icons.info,
              Colors.purple,
              () => _showAboutDialog(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfoCard() {
    if (_companySettings == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.business,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _companySettings!.companyName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_companySettings!.companyNameEn != null &&
                          _companySettings!.companyNameEn!.isNotEmpty)
                        Text(
                          _companySettings!.companyNameEn!,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'العملة',
                    _companySettings!.currencySymbol,
                    Icons.monetization_on,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'السنة المالية',
                    _companySettings!.fiscalYearStart,
                    Icons.calendar_today,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSettingsSection(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCompanySettings() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const CompanySettingsScreen(),
          ),
        )
        .then((_) => _loadSettings());
  }

  void _navigateToUserSettings() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const UserSettingsScreen()));
  }

  void _navigateToSystemSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SystemSettingsScreen()),
    );
  }

  void _navigateToBackupRestore() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const BackupRestoreScreen()),
    );
  }

  void _navigateToIconGenerator() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const IconGeneratorScreen()),
    );
  }

  void _navigateToPerformanceMonitor() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PerformanceMonitorScreen()),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول Smart Ledger'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('نظام محاسبة ذكي ومتطور'),
            SizedBox(height: 8),
            Text('الإصدار: 1.0.0'),
            SizedBox(height: 8),
            Text('تطوير: فريق Smart Ledger'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
