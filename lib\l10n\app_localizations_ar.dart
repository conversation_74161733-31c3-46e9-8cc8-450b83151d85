// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'دفتر الحسابات الذكي';

  @override
  String get home => 'الرئيسية';

  @override
  String get accounts => 'الحسابات';

  @override
  String get entries => 'القيود';

  @override
  String get customers => 'العملاء';

  @override
  String get suppliers => 'الموردين';

  @override
  String get inventory => 'المخزون';

  @override
  String get invoices => 'الفواتير';

  @override
  String get reports => 'التقارير';

  @override
  String get settings => 'الإعدادات';

  @override
  String get add => 'إضافة';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get date => 'التاريخ';

  @override
  String get amount => 'المبلغ';

  @override
  String get description => 'الوصف';

  @override
  String get balance => 'الرصيد';

  @override
  String get debit => 'مدين';

  @override
  String get credit => 'دائن';

  @override
  String get total => 'الإجمالي';

  @override
  String get accountName => 'اسم الحساب';

  @override
  String get accountCode => 'رمز الحساب';

  @override
  String get accountType => 'نوع الحساب';

  @override
  String get parentAccount => 'الحساب الأب';

  @override
  String get journalEntry => 'قيد يومية';

  @override
  String get reference => 'المرجع';

  @override
  String get customerName => 'اسم العميل';

  @override
  String get supplierName => 'اسم المورد';

  @override
  String get phone => 'الهاتف';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get address => 'العنوان';

  @override
  String get itemName => 'اسم الصنف';

  @override
  String get quantity => 'الكمية';

  @override
  String get price => 'السعر';

  @override
  String get unit => 'الوحدة';

  @override
  String get invoiceNumber => 'رقم الفاتورة';

  @override
  String get salesInvoice => 'فاتورة بيع';

  @override
  String get purchaseInvoice => 'فاتورة شراء';

  @override
  String get trialBalance => 'ميزان المراجعة';

  @override
  String get balanceSheet => 'الميزانية العمومية';

  @override
  String get incomeStatement => 'قائمة الدخل';

  @override
  String get accountStatement => 'كشف حساب';

  @override
  String get backup => 'نسخ احتياطي';

  @override
  String get restore => 'استعادة';

  @override
  String get companyInfo => 'بيانات الشركة';

  @override
  String get currency => 'العملة';

  @override
  String get fiscalYear => 'السنة المالية';
}
