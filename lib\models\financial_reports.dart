import 'account.dart';

// Trial Balance Report
class TrialBalanceReport {
  final DateTime reportDate;
  final List<TrialBalanceItem> items;
  final double totalDebits;
  final double totalCredits;
  final bool isBalanced;

  TrialBalanceReport({
    required this.reportDate,
    required this.items,
    required this.totalDebits,
    required this.totalCredits,
    required this.isBalanced,
  });

  double get difference => totalDebits - totalCredits;
}

class TrialBalanceItem {
  final String accountCode;
  final String accountName;
  final double debitBalance;
  final double creditBalance;

  TrialBalanceItem({
    required this.accountCode,
    required this.accountName,
    required this.debitBalance,
    required this.creditBalance,
  });
}

// Balance Sheet Report
class BalanceSheetReport {
  final DateTime reportDate;
  final double currentAssets;
  final double totalAssets;
  final double currentLiabilities;
  final double totalLiabilities;
  final double equity;
  final double retainedEarnings;
  final double totalEquity;
  final bool isBalanced;
  final List<BalanceSheetItem> assets;
  final List<BalanceSheetItem> liabilities;

  BalanceSheetReport({
    required this.reportDate,
    required this.currentAssets,
    required this.totalAssets,
    required this.currentLiabilities,
    required this.totalLiabilities,
    required this.equity,
    required this.retainedEarnings,
    required this.totalEquity,
    required this.isBalanced,
    required this.assets,
    required this.liabilities,
  });

  double get totalLiabilitiesAndEquity => totalLiabilities + totalEquity;
  double get difference => totalAssets - totalLiabilitiesAndEquity;
}

class BalanceSheetItem {
  final String accountCode;
  final String accountName;
  final double amount;

  BalanceSheetItem({
    required this.accountCode,
    required this.accountName,
    required this.amount,
  });
}

// Income Statement Report
class IncomeStatementReport {
  final DateTime startDate;
  final DateTime endDate;
  final double totalRevenue;
  final double totalExpenses;
  final double netIncome;
  final List<Account> revenueAccounts;
  final List<Account> expenseAccounts;
  final List<IncomeStatementItem> revenueItems;
  final List<IncomeStatementItem> expenseItems;

  IncomeStatementReport({
    required this.startDate,
    required this.endDate,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netIncome,
    required this.revenueAccounts,
    required this.expenseAccounts,
    required this.revenueItems,
    required this.expenseItems,
  });

  double get grossProfit => totalRevenue;
  bool get isProfitable => netIncome > 0;
  double get profitMargin =>
      totalRevenue == 0 ? 0 : (netIncome / totalRevenue) * 100;
}

class IncomeStatementItem {
  final String accountCode;
  final String accountName;
  final double amount;

  IncomeStatementItem({
    required this.accountCode,
    required this.accountName,
    required this.amount,
  });
}

// Account Statement Report
class AccountStatementReport {
  final Account account;
  final DateTime startDate;
  final DateTime endDate;
  final double openingBalance;
  final double closingBalance;
  final List<AccountStatementItem> items;
  final List<AccountStatementTransaction> transactions;
  final double totalDebits;
  final double totalCredits;

  AccountStatementReport({
    required this.account,
    required this.startDate,
    required this.endDate,
    required this.openingBalance,
    required this.closingBalance,
    required this.items,
    required this.transactions,
    required this.totalDebits,
    required this.totalCredits,
  });

  double get netMovement => totalDebits - totalCredits;
  int get transactionCount => transactions.length;
}

class AccountStatementTransaction {
  final DateTime date;
  final String description;
  final double debitAmount;
  final double creditAmount;
  final double balance;

  AccountStatementTransaction({
    required this.date,
    required this.description,
    required this.debitAmount,
    required this.creditAmount,
    required this.balance,
  });
}

class AccountStatementItem {
  final DateTime date;
  final String entryNumber;
  final String description;
  final String? reference;
  final double debitAmount;
  final double creditAmount;
  final double balance;

  AccountStatementItem({
    required this.date,
    required this.entryNumber,
    required this.description,
    this.reference,
    required this.debitAmount,
    required this.creditAmount,
    required this.balance,
  });

  double get amount => debitAmount != 0 ? debitAmount : creditAmount;
  bool get isDebit => debitAmount != 0;
  bool get isCredit => creditAmount != 0;
}

// General Ledger Report
class GeneralLedgerReport {
  final DateTime startDate;
  final DateTime endDate;
  final List<AccountStatementReport> accountStatements;

  GeneralLedgerReport({
    required this.startDate,
    required this.endDate,
    required this.accountStatements,
  });

  int get totalAccounts => accountStatements.length;
  int get totalTransactions => accountStatements.fold(
    0,
    (sum, statement) => sum + statement.transactionCount,
  );
  double get totalDebits => accountStatements.fold(
    0.0,
    (sum, statement) => sum + statement.totalDebits,
  );
  double get totalCredits => accountStatements.fold(
    0.0,
    (sum, statement) => sum + statement.totalCredits,
  );
}

// Financial Summary for Dashboard
class FinancialSummary {
  final double totalAssets;
  final double totalLiabilities;
  final double totalEquity;
  final double totalRevenue;
  final double totalExpenses;
  final double netIncome;
  final int totalJournalEntries;
  final int postedEntries;
  final int unpostedEntries;

  FinancialSummary({
    required this.totalAssets,
    required this.totalLiabilities,
    required this.totalEquity,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netIncome,
    required this.totalJournalEntries,
    required this.postedEntries,
    required this.unpostedEntries,
  });

  bool get isBalanced =>
      (totalAssets - (totalLiabilities + totalEquity)).abs() < 0.01;
  bool get isProfitable => netIncome > 0;
  double get profitMargin =>
      totalRevenue == 0 ? 0 : (netIncome / totalRevenue) * 100;
  double get debtToEquityRatio =>
      totalEquity == 0 ? 0 : totalLiabilities / totalEquity;
  double get currentRatio =>
      totalLiabilities == 0 ? 0 : totalAssets / totalLiabilities;
}

// Aging Report for Customers/Suppliers
class AgingReport {
  final DateTime reportDate;
  final List<AgingItem> items;
  final double totalAmount;

  AgingReport({
    required this.reportDate,
    required this.items,
    required this.totalAmount,
  });

  double get current => items.fold(0.0, (sum, item) => sum + item.current);
  double get days30 => items.fold(0.0, (sum, item) => sum + item.days30);
  double get days60 => items.fold(0.0, (sum, item) => sum + item.days60);
  double get days90 => items.fold(0.0, (sum, item) => sum + item.days90);
  double get over90 => items.fold(0.0, (sum, item) => sum + item.over90);
}

class AgingItem {
  final String code;
  final String name;
  final double totalAmount;
  final double current;
  final double days30;
  final double days60;
  final double days90;
  final double over90;

  AgingItem({
    required this.code,
    required this.name,
    required this.totalAmount,
    required this.current,
    required this.days30,
    required this.days60,
    required this.days90,
    required this.over90,
  });

  double get overdueAmount => days30 + days60 + days90 + over90;
  bool get hasOverdue => overdueAmount > 0;
}

// Inventory Report
class InventoryReport {
  final DateTime reportDate;
  final List<InventoryItem> items;
  final double totalCostValue;
  final double totalSellingValue;

  InventoryReport({
    required this.reportDate,
    required this.items,
    required this.totalCostValue,
    required this.totalSellingValue,
  });

  double get potentialProfit => totalSellingValue - totalCostValue;
  double get profitMargin =>
      totalCostValue == 0 ? 0 : (potentialProfit / totalCostValue) * 100;
  int get totalItems => items.length;
  int get lowStockItems => items.where((item) => item.isLowStock).length;
  int get outOfStockItems => items.where((item) => item.isOutOfStock).length;
}

class InventoryItem {
  final String code;
  final String name;
  final String unit;
  final double currentStock;
  final double costPrice;
  final double sellingPrice;
  final double? minStockLevel;
  final double? maxStockLevel;

  InventoryItem({
    required this.code,
    required this.name,
    required this.unit,
    required this.currentStock,
    required this.costPrice,
    required this.sellingPrice,
    this.minStockLevel,
    this.maxStockLevel,
  });

  double get totalCostValue => currentStock * costPrice;
  double get totalSellingValue => currentStock * sellingPrice;
  double get potentialProfit => totalSellingValue - totalCostValue;
  double get profitMargin =>
      costPrice == 0 ? 0 : ((sellingPrice - costPrice) / costPrice) * 100;
  bool get isLowStock =>
      minStockLevel != null && currentStock <= minStockLevel!;
  bool get isOutOfStock => currentStock <= 0;
  bool get isOverStock =>
      maxStockLevel != null && currentStock >= maxStockLevel!;
}

// Sales Report
class SalesReport {
  final DateTime startDate;
  final DateTime endDate;
  final double totalSales;
  final double totalTax;
  final double netSales;
  final int invoiceCount;
  final List<SalesItem> items;

  SalesReport({
    required this.startDate,
    required this.endDate,
    required this.totalSales,
    required this.totalTax,
    required this.netSales,
    required this.invoiceCount,
    required this.items,
  });

  double get averageInvoiceValue =>
      invoiceCount == 0 ? 0 : totalSales / invoiceCount;
  double get taxRate => totalSales == 0 ? 0 : (totalTax / totalSales) * 100;
}

class SalesItem {
  final String itemCode;
  final String itemName;
  final double quantitySold;
  final double totalAmount;
  final double averagePrice;

  SalesItem({
    required this.itemCode,
    required this.itemName,
    required this.quantitySold,
    required this.totalAmount,
    required this.averagePrice,
  });
}

// ==================== التقارير المالية المتقدمة ====================

// Cash Flow Statement Report
class CashFlowReport {
  final DateTime startDate;
  final DateTime endDate;
  final CashFlowOperating operatingActivities;
  final CashFlowInvesting investingActivities;
  final CashFlowFinancing financingActivities;
  final double openingCashBalance;
  final double closingCashBalance;
  final double netCashFlow;

  CashFlowReport({
    required this.startDate,
    required this.endDate,
    required this.operatingActivities,
    required this.investingActivities,
    required this.financingActivities,
    required this.openingCashBalance,
    required this.closingCashBalance,
    required this.netCashFlow,
  });

  double get totalCashInflow =>
      operatingActivities.totalInflow +
      investingActivities.totalInflow +
      financingActivities.totalInflow;

  double get totalCashOutflow =>
      operatingActivities.totalOutflow +
      investingActivities.totalOutflow +
      financingActivities.totalOutflow;
}

// Operating Activities for Cash Flow
class CashFlowOperating {
  final double netIncome;
  final double depreciation;
  final double accountsReceivableChange;
  final double inventoryChange;
  final double accountsPayableChange;
  final double otherOperatingChanges;
  final List<CashFlowItem> items;

  CashFlowOperating({
    required this.netIncome,
    required this.depreciation,
    required this.accountsReceivableChange,
    required this.inventoryChange,
    required this.accountsPayableChange,
    required this.otherOperatingChanges,
    required this.items,
  });

  double get totalInflow => items
      .where((item) => item.amount > 0)
      .fold(0.0, (sum, item) => sum + item.amount);

  double get totalOutflow => items
      .where((item) => item.amount < 0)
      .fold(0.0, (sum, item) => sum + item.amount.abs());

  double get netOperatingCashFlow =>
      netIncome +
      depreciation +
      accountsReceivableChange +
      inventoryChange +
      accountsPayableChange +
      otherOperatingChanges;
}

// Investing Activities for Cash Flow
class CashFlowInvesting {
  final double assetPurchases;
  final double assetSales;
  final double investmentPurchases;
  final double investmentSales;
  final List<CashFlowItem> items;

  CashFlowInvesting({
    required this.assetPurchases,
    required this.assetSales,
    required this.investmentPurchases,
    required this.investmentSales,
    required this.items,
  });

  double get totalInflow => items
      .where((item) => item.amount > 0)
      .fold(0.0, (sum, item) => sum + item.amount);

  double get totalOutflow => items
      .where((item) => item.amount < 0)
      .fold(0.0, (sum, item) => sum + item.amount.abs());

  double get netInvestingCashFlow =>
      assetSales + investmentSales - assetPurchases - investmentPurchases;
}

// Financing Activities for Cash Flow
class CashFlowFinancing {
  final double loanProceeds;
  final double loanRepayments;
  final double equityIssuance;
  final double dividendPayments;
  final List<CashFlowItem> items;

  CashFlowFinancing({
    required this.loanProceeds,
    required this.loanRepayments,
    required this.equityIssuance,
    required this.dividendPayments,
    required this.items,
  });

  double get totalInflow => items
      .where((item) => item.amount > 0)
      .fold(0.0, (sum, item) => sum + item.amount);

  double get totalOutflow => items
      .where((item) => item.amount < 0)
      .fold(0.0, (sum, item) => sum + item.amount.abs());

  double get netFinancingCashFlow =>
      loanProceeds + equityIssuance - loanRepayments - dividendPayments;
}

// Cash Flow Item
class CashFlowItem {
  final String description;
  final double amount;
  final String category;
  final DateTime date;

  CashFlowItem({
    required this.description,
    required this.amount,
    required this.category,
    required this.date,
  });

  bool get isInflow => amount > 0;
  bool get isOutflow => amount < 0;
}

// Profitability Analysis Report
class ProfitabilityReport {
  final DateTime startDate;
  final DateTime endDate;
  final double grossProfit;
  final double operatingProfit;
  final double netProfit;
  final double totalRevenue;
  final double costOfGoodsSold;
  final double operatingExpenses;
  final List<ProfitabilityItem> items;

  ProfitabilityReport({
    required this.startDate,
    required this.endDate,
    required this.grossProfit,
    required this.operatingProfit,
    required this.netProfit,
    required this.totalRevenue,
    required this.costOfGoodsSold,
    required this.operatingExpenses,
    required this.items,
  });

  double get grossProfitMargin =>
      totalRevenue == 0 ? 0 : (grossProfit / totalRevenue) * 100;
  double get operatingProfitMargin =>
      totalRevenue == 0 ? 0 : (operatingProfit / totalRevenue) * 100;
  double get netProfitMargin =>
      totalRevenue == 0 ? 0 : (netProfit / totalRevenue) * 100;
  double get costOfGoodsSoldRatio =>
      totalRevenue == 0 ? 0 : (costOfGoodsSold / totalRevenue) * 100;
  double get operatingExpenseRatio =>
      totalRevenue == 0 ? 0 : (operatingExpenses / totalRevenue) * 100;
}

class ProfitabilityItem {
  final String category;
  final String description;
  final double amount;
  final double percentage;
  final String type; // revenue, cogs, expense

  ProfitabilityItem({
    required this.category,
    required this.description,
    required this.amount,
    required this.percentage,
    required this.type,
  });
}

// Comparative Financial Report
class ComparativeReport {
  final DateTime currentPeriodStart;
  final DateTime currentPeriodEnd;
  final DateTime previousPeriodStart;
  final DateTime previousPeriodEnd;
  final List<ComparativeItem> items;
  final ComparativeSummary summary;

  ComparativeReport({
    required this.currentPeriodStart,
    required this.currentPeriodEnd,
    required this.previousPeriodStart,
    required this.previousPeriodEnd,
    required this.items,
    required this.summary,
  });
}

class ComparativeItem {
  final String accountCode;
  final String accountName;
  final double currentAmount;
  final double previousAmount;
  final double variance;
  final double variancePercentage;

  ComparativeItem({
    required this.accountCode,
    required this.accountName,
    required this.currentAmount,
    required this.previousAmount,
    required this.variance,
    required this.variancePercentage,
  });

  bool get isIncrease => variance > 0;
  bool get isDecrease => variance < 0;
  bool get isSignificantChange => variancePercentage.abs() > 10;
}

class ComparativeSummary {
  final double totalRevenueChange;
  final double totalExpenseChange;
  final double netIncomeChange;
  final double totalAssetChange;
  final double totalLiabilityChange;
  final double equityChange;

  ComparativeSummary({
    required this.totalRevenueChange,
    required this.totalExpenseChange,
    required this.netIncomeChange,
    required this.totalAssetChange,
    required this.totalLiabilityChange,
    required this.equityChange,
  });
}

// Trend Analysis Report
class TrendAnalysisReport {
  final List<DateTime> periods;
  final List<TrendItem> items;
  final TrendSummary summary;

  TrendAnalysisReport({
    required this.periods,
    required this.items,
    required this.summary,
  });

  int get periodCount => periods.length;
}

class TrendItem {
  final String accountCode;
  final String accountName;
  final List<double> values;
  final double averageGrowthRate;
  final String trendDirection; // increasing, decreasing, stable

  TrendItem({
    required this.accountCode,
    required this.accountName,
    required this.values,
    required this.averageGrowthRate,
    required this.trendDirection,
  });

  double get totalChange => values.isNotEmpty ? values.last - values.first : 0;
  double get averageValue =>
      values.isNotEmpty ? values.reduce((a, b) => a + b) / values.length : 0;
}

class TrendSummary {
  final double revenueGrowthRate;
  final double expenseGrowthRate;
  final double profitGrowthRate;
  final double assetGrowthRate;
  final String overallTrend;

  TrendSummary({
    required this.revenueGrowthRate,
    required this.expenseGrowthRate,
    required this.profitGrowthRate,
    required this.assetGrowthRate,
    required this.overallTrend,
  });
}

// Financial Ratios Report
class FinancialRatiosReport {
  final DateTime reportDate;
  final LiquidityRatios liquidityRatios;
  final ProfitabilityRatios profitabilityRatios;
  final EfficiencyRatios efficiencyRatios;
  final LeverageRatios leverageRatios;

  FinancialRatiosReport({
    required this.reportDate,
    required this.liquidityRatios,
    required this.profitabilityRatios,
    required this.efficiencyRatios,
    required this.leverageRatios,
  });
}

class LiquidityRatios {
  final double currentRatio;
  final double quickRatio;
  final double cashRatio;
  final double workingCapital;

  LiquidityRatios({
    required this.currentRatio,
    required this.quickRatio,
    required this.cashRatio,
    required this.workingCapital,
  });
}

class ProfitabilityRatios {
  final double grossProfitMargin;
  final double operatingProfitMargin;
  final double netProfitMargin;
  final double returnOnAssets;
  final double returnOnEquity;

  ProfitabilityRatios({
    required this.grossProfitMargin,
    required this.operatingProfitMargin,
    required this.netProfitMargin,
    required this.returnOnAssets,
    required this.returnOnEquity,
  });
}

class EfficiencyRatios {
  final double assetTurnover;
  final double inventoryTurnover;
  final double receivablesTurnover;
  final double payablesTurnover;

  EfficiencyRatios({
    required this.assetTurnover,
    required this.inventoryTurnover,
    required this.receivablesTurnover,
    required this.payablesTurnover,
  });
}

class LeverageRatios {
  final double debtToEquity;
  final double debtToAssets;
  final double equityMultiplier;
  final double interestCoverage;

  LeverageRatios({
    required this.debtToEquity,
    required this.debtToAssets,
    required this.equityMultiplier,
    required this.interestCoverage,
  });
}

// Budget vs Actual Report
class BudgetVsActualReport {
  final DateTime startDate;
  final DateTime endDate;
  final List<BudgetVsActualItem> items;
  final BudgetVsActualSummary summary;

  BudgetVsActualReport({
    required this.startDate,
    required this.endDate,
    required this.items,
    required this.summary,
  });
}

class BudgetVsActualItem {
  final String accountCode;
  final String accountName;
  final double budgetAmount;
  final double actualAmount;
  final double variance;
  final double variancePercentage;

  BudgetVsActualItem({
    required this.accountCode,
    required this.accountName,
    required this.budgetAmount,
    required this.actualAmount,
    required this.variance,
    required this.variancePercentage,
  });

  bool get isOverBudget => actualAmount > budgetAmount;
  bool get isUnderBudget => actualAmount < budgetAmount;
  bool get isSignificantVariance => variancePercentage.abs() > 10;
}

class BudgetVsActualSummary {
  final double totalBudgetRevenue;
  final double totalActualRevenue;
  final double totalBudgetExpenses;
  final double totalActualExpenses;
  final double budgetedNetIncome;
  final double actualNetIncome;

  BudgetVsActualSummary({
    required this.totalBudgetRevenue,
    required this.totalActualRevenue,
    required this.totalBudgetExpenses,
    required this.totalActualExpenses,
    required this.budgetedNetIncome,
    required this.actualNetIncome,
  });

  double get revenueVariance => totalActualRevenue - totalBudgetRevenue;
  double get expenseVariance => totalActualExpenses - totalBudgetExpenses;
  double get netIncomeVariance => actualNetIncome - budgetedNetIncome;
}
