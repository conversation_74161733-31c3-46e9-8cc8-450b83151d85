/// فئة للتعامل مع النتائج والأخطاء بطريقة آمنة
class Result<T> {
  final T? _data;
  final String? _error;
  final bool _isSuccess;

  const Result._(this._data, this._error, this._isSuccess);

  /// إنشاء نتيجة ناجحة
  factory Result.success(T data) {
    return Result._(data, null, true);
  }

  /// إنشاء نتيجة فاشلة
  factory Result.error(String error) {
    return Result._(null, error, false);
  }

  /// التحقق من نجاح العملية
  bool get isSuccess => _isSuccess;

  /// التحقق من فشل العملية
  bool get isError => !_isSuccess;

  /// الحصول على البيانات (قد تكون null في حالة الفشل)
  T? get data => _data;

  /// الحصول على رسالة الخطأ (قد تكون null في حالة النجاح)
  String? get error => _error;

  /// تحويل النتيجة إلى نوع آخر
  Result<U> map<U>(U Function(T) mapper) {
    if (isSuccess && _data != null) {
      try {
        return Result.success(mapper(_data as T));
      } catch (e) {
        return Result.error('خطأ في تحويل البيانات: ${e.toString()}');
      }
    } else {
      return Result.error(_error ?? 'خطأ غير معروف');
    }
  }

  /// تطبيق دالة على البيانات في حالة النجاح
  Result<U> flatMap<U>(Result<U> Function(T) mapper) {
    if (isSuccess && _data != null) {
      try {
        return mapper(_data as T);
      } catch (e) {
        return Result.error('خطأ في تطبيق العملية: ${e.toString()}');
      }
    } else {
      return Result.error(_error ?? 'خطأ غير معروف');
    }
  }

  /// تطبيق دالة في حالة النجاح أو الفشل
  void fold(void Function(T) onSuccess, void Function(String) onError) {
    if (isSuccess && _data != null) {
      onSuccess(_data as T);
    } else {
      onError(_error ?? 'خطأ غير معروف');
    }
  }

  /// الحصول على البيانات أو قيمة افتراضية
  T getOrElse(T defaultValue) {
    return isSuccess && _data != null ? _data as T : defaultValue;
  }

  /// الحصول على البيانات أو تطبيق دالة لإنشاء قيمة افتراضية
  T getOrElseGet(T Function() defaultValueProvider) {
    return isSuccess && _data != null ? _data as T : defaultValueProvider();
  }

  /// رمي استثناء في حالة الفشل أو إرجاع البيانات
  T getOrThrow() {
    if (isSuccess && _data != null) {
      return _data as T;
    } else {
      throw Exception(_error ?? 'خطأ غير معروف');
    }
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'Result.success($_data)';
    } else {
      return 'Result.error($_error)';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Result<T> &&
        other._isSuccess == _isSuccess &&
        other._data == _data &&
        other._error == _error;
  }

  @override
  int get hashCode {
    return Object.hash(_isSuccess, _data, _error);
  }
}

/// امتدادات مفيدة للعمل مع `Future<Result<T>>`
extension FutureResultExtensions<T> on Future<Result<T>> {
  /// تحويل `Future<Result<T>>` إلى `Future<Result<U>>`
  Future<Result<U>> mapAsync<U>(Future<U> Function(T) mapper) async {
    final result = await this;
    if (result.isSuccess && result.data != null) {
      try {
        final mappedData = await mapper(result.data as T);
        return Result.success(mappedData);
      } catch (e) {
        return Result.error('خطأ في تحويل البيانات: ${e.toString()}');
      }
    } else {
      return Result.error(result.error ?? 'خطأ غير معروف');
    }
  }

  /// تطبيق دالة غير متزامنة على البيانات
  Future<Result<U>> flatMapAsync<U>(
    Future<Result<U>> Function(T) mapper,
  ) async {
    final result = await this;
    if (result.isSuccess && result.data != null) {
      try {
        return await mapper(result.data as T);
      } catch (e) {
        return Result.error('خطأ في تطبيق العملية: ${e.toString()}');
      }
    } else {
      return Result.error(result.error ?? 'خطأ غير معروف');
    }
  }
}

/// دوال مساعدة للعمل مع Result
class ResultUtils {
  /// دمج عدة نتائج في نتيجة واحدة
  static Result<List<T>> combine<T>(List<Result<T>> results) {
    final List<T> successData = [];
    final List<String> errors = [];

    for (final result in results) {
      if (result.isSuccess && result.data != null) {
        successData.add(result.data as T);
      } else {
        errors.add(result.error ?? 'خطأ غير معروف');
      }
    }

    if (errors.isEmpty) {
      return Result.success(successData);
    } else {
      return Result.error('أخطاء متعددة: ${errors.join(', ')}');
    }
  }

  /// تطبيق دالة على قائمة من البيانات وإرجاع أول نجاح
  static Result<T> firstSuccess<T>(List<Result<T>> results) {
    for (final result in results) {
      if (result.isSuccess) {
        return result;
      }
    }

    final errors = results
        .where((r) => r.isError)
        .map((r) => r.error ?? 'خطأ غير معروف')
        .toList();

    return Result.error('جميع المحاولات فشلت: ${errors.join(', ')}');
  }

  /// تحويل استثناء إلى Result
  static Result<T> fromException<T>(Exception exception) {
    return Result.error(exception.toString());
  }

  /// تحويل دالة قد ترمي استثناء إلى Result
  static Result<T> tryCall<T>(T Function() function) {
    try {
      return Result.success(function());
    } catch (e) {
      return Result.error(e.toString());
    }
  }

  /// تحويل دالة غير متزامنة قد ترمي استثناء إلى `Future<Result<T>>`
  static Future<Result<T>> tryCallAsync<T>(
    Future<T> Function() function,
  ) async {
    try {
      final result = await function();
      return Result.success(result);
    } catch (e) {
      return Result.error(e.toString());
    }
  }
}
