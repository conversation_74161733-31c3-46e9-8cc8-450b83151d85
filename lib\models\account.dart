class Account {
  final int? id;
  final String code;
  final String name;
  final String? nameEn;
  final String? description;
  final AccountType accountType;
  final int? parentId;
  final bool isActive;
  final double balance;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Account? parent;
  List<Account> children = [];

  Account({
    this.id,
    required this.code,
    required this.name,
    this.nameEn,
    this.description,
    required this.accountType,
    this.parentId,
    this.isActive = true,
    this.balance = 0.0,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.parent,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from database map
  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      nameEn: map['name_en'] as String?,
      description: map['description'] as String?,
      accountType: AccountType.fromString(map['account_type'] as String),
      parentId: map['parent_id'] as int?,
      isActive: (map['is_active'] as int) == 1,
      balance: (map['balance'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'name_en': nameEn,
      'description': description,
      'account_type': accountType.value,
      'parent_id': parentId,
      'is_active': isActive ? 1 : 0,
      'balance': balance,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  Account copyWith({
    int? id,
    String? code,
    String? name,
    String? nameEn,
    String? description,
    AccountType? accountType,
    int? parentId,
    bool? isActive,
    double? balance,
    DateTime? createdAt,
    DateTime? updatedAt,
    Account? parent,
  }) {
    return Account(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      accountType: accountType ?? this.accountType,
      parentId: parentId ?? this.parentId,
      isActive: isActive ?? this.isActive,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      parent: parent ?? this.parent,
    );
  }

  // Helper methods
  bool get isParent => children.isNotEmpty;
  bool get isChild => parentId != null;
  bool get isRoot => parentId == null;

  String get displayName => name;
  String get fullCode => code;

  // Get account level (0 for root, 1 for first level, etc.)
  int get level {
    if (parent == null) return 0;
    return parent!.level + 1;
  }

  // Get full path from root to this account
  String get fullPath {
    if (parent == null) return name;
    return '${parent!.fullPath} > $name';
  }

  // Check if this account is an ancestor of another account
  bool isAncestorOf(Account other) {
    Account? current = other.parent;
    while (current != null) {
      if (current.id == id) return true;
      current = current.parent;
    }
    return false;
  }

  // Check if this account is a descendant of another account
  bool isDescendantOf(Account other) {
    return other.isAncestorOf(this);
  }

  // Get all descendant accounts (recursive)
  List<Account> getAllDescendants() {
    List<Account> descendants = [];
    for (Account child in children) {
      descendants.add(child);
      descendants.addAll(child.getAllDescendants());
    }
    return descendants;
  }

  // Calculate total balance including children
  double getTotalBalance() {
    double total = balance;
    for (Account child in children) {
      total += child.getTotalBalance();
    }
    return total;
  }

  @override
  String toString() {
    return 'Account{id: $id, code: $code, name: $name, type: ${accountType.value}, balance: $balance}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account && other.id == id && other.code == code;
  }

  @override
  int get hashCode => id.hashCode ^ code.hashCode;

  // Validation method
  List<String> validate() {
    List<String> errors = [];

    if (code.trim().isEmpty) {
      errors.add('رمز الحساب مطلوب');
    }

    if (name.trim().isEmpty) {
      errors.add('اسم الحساب مطلوب');
    }

    if (code.length > 20) {
      errors.add('رمز الحساب يجب أن يكون أقل من 20 حرف');
    }

    if (name.length > 100) {
      errors.add('اسم الحساب يجب أن يكون أقل من 100 حرف');
    }

    // Validate code format (numbers and dots only)
    if (!RegExp(r'^[0-9.]+$').hasMatch(code)) {
      errors.add('رمز الحساب يجب أن يحتوي على أرقام ونقاط فقط');
    }

    return errors;
  }
}

enum AccountType {
  asset('asset', 'أصول', 'Assets'),
  liability('liability', 'خصوم', 'Liabilities'),
  equity('equity', 'حقوق الملكية', 'Equity'),
  revenue('revenue', 'إيرادات', 'Revenue'),
  expense('expense', 'مصروفات', 'Expenses');

  const AccountType(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static AccountType fromString(String value) {
    return AccountType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => AccountType.asset,
    );
  }

  String get displayName => nameAr;

  // Check if this is a debit normal balance account
  bool get isDebitNormal {
    return this == AccountType.asset || this == AccountType.expense;
  }

  // Check if this is a credit normal balance account
  bool get isCreditNormal {
    return this == AccountType.liability ||
        this == AccountType.equity ||
        this == AccountType.revenue;
  }

  // Get the sign for balance calculation
  int get balanceSign {
    return isDebitNormal ? 1 : -1;
  }
}

// Account tree helper class
class AccountTree {
  final List<Account> _accounts = [];
  final Map<int, Account> _accountMap = {};

  void addAccount(Account account) {
    _accounts.add(account);
    if (account.id != null) {
      _accountMap[account.id!] = account;
    }
  }

  void addAccounts(List<Account> accounts) {
    for (Account account in accounts) {
      addAccount(account);
    }
  }

  void buildTree() {
    // Clear existing relationships
    for (Account account in _accounts) {
      account.children.clear();
      account.parent = null;
    }

    // Build parent-child relationships
    for (Account account in _accounts) {
      if (account.parentId != null) {
        Account? parent = _accountMap[account.parentId!];
        if (parent != null) {
          account.parent = parent;
          parent.children.add(account);
        }
      }
    }

    // Sort children by code
    for (Account account in _accounts) {
      account.children.sort((a, b) => a.code.compareTo(b.code));
    }
  }

  List<Account> getRootAccounts() {
    return _accounts.where((account) => account.parentId == null).toList()
      ..sort((a, b) => a.code.compareTo(b.code));
  }

  List<Account> getAllAccounts() {
    return List.from(_accounts);
  }

  Account? getAccountById(int id) {
    return _accountMap[id];
  }

  Account? getAccountByCode(String code) {
    return _accounts.firstWhere(
      (account) => account.code == code,
      orElse: () => throw Exception('Account with code $code not found'),
    );
  }

  List<Account> getAccountsByType(AccountType type) {
    return _accounts.where((account) => account.accountType == type).toList();
  }

  List<Account> searchAccounts(String query) {
    query = query.toLowerCase();
    return _accounts.where((account) {
      return account.name.toLowerCase().contains(query) ||
          account.code.toLowerCase().contains(query) ||
          (account.nameEn?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  void clear() {
    _accounts.clear();
    _accountMap.clear();
  }
}
