import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/company_settings.dart';
import '../../services/company_settings_service.dart';
import '../../widgets/beautiful_text_form_field.dart';

/// شاشة إعدادات الشركة
class CompanySettingsScreen extends StatefulWidget {
  const CompanySettingsScreen({super.key});

  @override
  State<CompanySettingsScreen> createState() => _CompanySettingsScreenState();
}

class _CompanySettingsScreenState extends State<CompanySettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final CompanySettingsService _settingsService = CompanySettingsService();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Controllers
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _companyNameEnController =
      TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _taxNumberController = TextEditingController();

  CompanySettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;

  String _selectedCurrencyCode = 'SAR';
  String _selectedCurrencySymbol = 'ر.س';
  int _selectedDecimalPlaces = 2;
  String _selectedDateFormat = 'dd/MM/yyyy';
  String _selectedFiscalYearStart = '01-01';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadSettings();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _companyNameController.dispose();
    _companyNameEnController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _taxNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    final result = await _settingsService.getCompanySettings();
    if (mounted) {
      setState(() {
        if (result.isSuccess) {
          _currentSettings = result.data;
          _populateFields();
        }
        _isLoading = false;
      });
    }
  }

  void _populateFields() {
    if (_currentSettings != null) {
      _companyNameController.text = _currentSettings!.companyName;
      _companyNameEnController.text = _currentSettings!.companyNameEn ?? '';
      _addressController.text = _currentSettings!.address ?? '';
      _phoneController.text = _currentSettings!.phone ?? '';
      _emailController.text = _currentSettings!.email ?? '';
      _taxNumberController.text = _currentSettings!.taxNumber ?? '';

      _selectedCurrencyCode = _currentSettings!.currencyCode;
      _selectedCurrencySymbol = _currentSettings!.currencySymbol;
      _selectedDecimalPlaces = _currentSettings!.decimalPlaces;
      _selectedDateFormat = _currentSettings!.dateFormat;
      _selectedFiscalYearStart = _currentSettings!.fiscalYearStart;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildForm(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إعدادات الشركة',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'إدارة معلومات الشركة والإعدادات المالية',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.business, color: Colors.white, size: 28),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: AnimationLimiter(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              _buildSection('معلومات الشركة', Icons.business, [
                BeautifulTextFormField(
                  controller: _companyNameController,
                  labelText: 'اسم الشركة (عربي)',
                  prefixIcon: Icons.business,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'اسم الشركة مطلوب';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                BeautifulTextFormField(
                  controller: _companyNameEnController,
                  labelText: 'اسم الشركة (إنجليزي)',
                  prefixIcon: Icons.business_outlined,
                ),
                const SizedBox(height: 16),
                BeautifulTextFormField(
                  controller: _addressController,
                  labelText: 'العنوان',
                  prefixIcon: Icons.location_on,
                  maxLines: 3,
                ),
              ]),
              const SizedBox(height: 24),
              _buildSection('معلومات الاتصال', Icons.contact_phone, [
                BeautifulTextFormField(
                  controller: _phoneController,
                  labelText: 'رقم الهاتف',
                  prefixIcon: Icons.phone,
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                BeautifulTextFormField(
                  controller: _emailController,
                  labelText: 'البريد الإلكتروني',
                  prefixIcon: Icons.email,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return 'البريد الإلكتروني غير صحيح';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                BeautifulTextFormField(
                  controller: _taxNumberController,
                  labelText: 'الرقم الضريبي',
                  prefixIcon: Icons.receipt_long,
                ),
              ]),
              const SizedBox(height: 24),
              _buildSection('إعدادات العملة', Icons.monetization_on, [
                _buildCurrencyDropdown(),
                const SizedBox(height: 16),
                _buildDecimalPlacesDropdown(),
              ]),
              const SizedBox(height: 24),
              _buildSection(
                'إعدادات التاريخ والسنة المالية',
                Icons.calendar_today,
                [
                  _buildDateFormatDropdown(),
                  const SizedBox(height: 16),
                  _buildFiscalYearDropdown(),
                ],
              ),
              const SizedBox(height: 32),
              _buildSaveButton(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.05),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.02),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencyDropdown() {
    final currencies = _settingsService.getSupportedCurrencies();

    return DropdownButtonFormField<String>(
      value: _selectedCurrencyCode,
      decoration: InputDecoration(
        labelText: 'العملة',
        prefixIcon: const Icon(Icons.monetization_on),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items: currencies.map((currency) {
        return DropdownMenuItem<String>(
          value: currency['code'],
          child: Text('${currency['name']} (${currency['symbol']})'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCurrencyCode = value;
            final currency = currencies.firstWhere((c) => c['code'] == value);
            _selectedCurrencySymbol = currency['symbol']!;
          });
        }
      },
    );
  }

  Widget _buildDecimalPlacesDropdown() {
    return DropdownButtonFormField<int>(
      value: _selectedDecimalPlaces,
      decoration: InputDecoration(
        labelText: 'عدد الخانات العشرية',
        prefixIcon: const Icon(Icons.numbers),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items: List.generate(7, (index) {
        return DropdownMenuItem<int>(value: index, child: Text('$index خانات'));
      }),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedDecimalPlaces = value;
          });
        }
      },
    );
  }

  Widget _buildDateFormatDropdown() {
    final formats = _settingsService.getSupportedDateFormats();

    return DropdownButtonFormField<String>(
      value: _selectedDateFormat,
      decoration: InputDecoration(
        labelText: 'تنسيق التاريخ',
        prefixIcon: const Icon(Icons.date_range),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items: formats.map((format) {
        return DropdownMenuItem<String>(
          value: format['format'],
          child: Text('${format['format']} (${format['example']})'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedDateFormat = value;
          });
        }
      },
    );
  }

  Widget _buildFiscalYearDropdown() {
    final months = [
      {'value': '01-01', 'name': 'يناير'},
      {'value': '02-01', 'name': 'فبراير'},
      {'value': '03-01', 'name': 'مارس'},
      {'value': '04-01', 'name': 'أبريل'},
      {'value': '05-01', 'name': 'مايو'},
      {'value': '06-01', 'name': 'يونيو'},
      {'value': '07-01', 'name': 'يوليو'},
      {'value': '08-01', 'name': 'أغسطس'},
      {'value': '09-01', 'name': 'سبتمبر'},
      {'value': '10-01', 'name': 'أكتوبر'},
      {'value': '11-01', 'name': 'نوفمبر'},
      {'value': '12-01', 'name': 'ديسمبر'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedFiscalYearStart,
      decoration: InputDecoration(
        labelText: 'بداية السنة المالية',
        prefixIcon: const Icon(Icons.calendar_month),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items: months.map((month) {
        return DropdownMenuItem<String>(
          value: month['value'],
          child: Text('${month['name']} (${month['value']})'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedFiscalYearStart = value;
          });
        }
      },
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _saveSettings,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
        ),
        child: _isSaving
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'حفظ الإعدادات',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final updatedSettings = CompanySettings(
        id: _currentSettings?.id,
        companyName: _companyNameController.text.trim(),
        companyNameEn: _companyNameEnController.text.trim().isEmpty
            ? null
            : _companyNameEnController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        taxNumber: _taxNumberController.text.trim().isEmpty
            ? null
            : _taxNumberController.text.trim(),
        currencyCode: _selectedCurrencyCode,
        currencySymbol: _selectedCurrencySymbol,
        decimalPlaces: _selectedDecimalPlaces,
        dateFormat: _selectedDateFormat,
        fiscalYearStart: _selectedFiscalYearStart,
        logoPath: _currentSettings?.logoPath,
        createdAt: _currentSettings?.createdAt,
        updatedAt: DateTime.now(),
      );

      final result = await _settingsService.updateCompanySettings(
        updatedSettings,
      );

      if (mounted) {
        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ الإعدادات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'خطأ في حفظ الإعدادات'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
