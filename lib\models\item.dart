class Item {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final String unit;
  final double costPrice;
  final double sellingPrice;
  final double currentStock;
  final double? minStockLevel;
  final double? maxStockLevel;
  final String? category;
  final String? barcode;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Item({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.unit = 'قطعة',
    this.costPrice = 0.0,
    this.sellingPrice = 0.0,
    this.currentStock = 0.0,
    this.minStockLevel,
    this.maxStockLevel,
    this.category,
    this.barcode,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from database map
  factory Item.fromMap(Map<String, dynamic> map) {
    return Item(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      unit: map['unit'] as String? ?? 'قطعة',
      costPrice: (map['cost_price'] as num?)?.toDouble() ?? 0.0,
      sellingPrice: (map['selling_price'] as num?)?.toDouble() ?? 0.0,
      currentStock: (map['current_stock'] as num?)?.toDouble() ?? 0.0,
      minStockLevel: (map['min_stock_level'] as num?)?.toDouble(),
      maxStockLevel: (map['max_stock_level'] as num?)?.toDouble(),
      category: map['category'] as String?,
      barcode: map['barcode'] as String?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'unit': unit,
      'cost_price': costPrice,
      'selling_price': sellingPrice,
      'current_stock': currentStock,
      'min_stock_level': minStockLevel,
      'max_stock_level': maxStockLevel,
      'category': category,
      'barcode': barcode,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  Item copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    String? unit,
    double? costPrice,
    double? sellingPrice,
    double? currentStock,
    double? minStockLevel,
    double? maxStockLevel,
    String? category,
    String? barcode,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Item(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      unit: unit ?? this.unit,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      currentStock: currentStock ?? this.currentStock,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      maxStockLevel: maxStockLevel ?? this.maxStockLevel,
      category: category ?? this.category,
      barcode: barcode ?? this.barcode,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  double get profitMargin {
    if (costPrice == 0) return 0.0;
    return ((sellingPrice - costPrice) / costPrice) * 100;
  }

  double get profitAmount => sellingPrice - costPrice;

  double get stockValue => currentStock * costPrice;

  bool get isLowStock {
    if (minStockLevel == null) return false;
    return currentStock <= minStockLevel!;
  }

  bool get isOverStock {
    if (maxStockLevel == null) return false;
    return currentStock >= maxStockLevel!;
  }

  bool get isOutOfStock => currentStock <= 0;

  StockStatus get stockStatus {
    if (isOutOfStock) return StockStatus.outOfStock;
    if (isLowStock) return StockStatus.lowStock;
    if (isOverStock) return StockStatus.overStock;
    return StockStatus.normal;
  }

  String get stockStatusText {
    switch (stockStatus) {
      case StockStatus.outOfStock:
        return 'نفد المخزون';
      case StockStatus.lowStock:
        return 'مخزون منخفض';
      case StockStatus.overStock:
        return 'مخزون زائد';
      case StockStatus.normal:
        return 'طبيعي';
    }
  }

  // Validation
  List<String> validate() {
    List<String> errors = [];

    if (code.trim().isEmpty) {
      errors.add('رمز الصنف مطلوب');
    }

    if (name.trim().isEmpty) {
      errors.add('اسم الصنف مطلوب');
    }

    if (unit.trim().isEmpty) {
      errors.add('وحدة القياس مطلوبة');
    }

    if (costPrice < 0) {
      errors.add('سعر التكلفة لا يمكن أن يكون سالباً');
    }

    if (sellingPrice < 0) {
      errors.add('سعر البيع لا يمكن أن يكون سالباً');
    }

    if (currentStock < 0) {
      errors.add('الكمية الحالية لا يمكن أن تكون سالبة');
    }

    if (minStockLevel != null && minStockLevel! < 0) {
      errors.add('الحد الأدنى للمخزون لا يمكن أن يكون سالباً');
    }

    if (maxStockLevel != null && maxStockLevel! < 0) {
      errors.add('الحد الأقصى للمخزون لا يمكن أن يكون سالباً');
    }

    if (minStockLevel != null &&
        maxStockLevel != null &&
        minStockLevel! > maxStockLevel!) {
      errors.add('الحد الأدنى للمخزون لا يمكن أن يكون أكبر من الحد الأقصى');
    }

    if (barcode != null && barcode!.isNotEmpty) {
      if (!_isValidBarcode(barcode!)) {
        errors.add('الباركود غير صحيح');
      }
    }

    return errors;
  }

  bool _isValidBarcode(String barcode) {
    // Simple barcode validation - adjust based on your requirements
    return RegExp(r'^[0-9]{8,13}$').hasMatch(barcode);
  }

  @override
  String toString() {
    return 'Item{id: $id, code: $code, name: $name, stock: $currentStock, price: $sellingPrice}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Item && other.id == id && other.code == code;
  }

  @override
  int get hashCode => id.hashCode ^ code.hashCode;
}

enum StockStatus {
  normal,
  lowStock,
  outOfStock,
  overStock;

  String get displayName {
    switch (this) {
      case StockStatus.normal:
        return 'طبيعي';
      case StockStatus.lowStock:
        return 'مخزون منخفض';
      case StockStatus.outOfStock:
        return 'نفد المخزون';
      case StockStatus.overStock:
        return 'مخزون زائد';
    }
  }

  String get value {
    switch (this) {
      case StockStatus.normal:
        return 'normal';
      case StockStatus.lowStock:
        return 'low_stock';
      case StockStatus.outOfStock:
        return 'out_of_stock';
      case StockStatus.overStock:
        return 'over_stock';
    }
  }
}

// Item movement for tracking stock changes
class ItemMovement {
  final int? id;
  final int itemId;
  final DateTime date;
  final String movementType; // 'in', 'out', 'adjustment'
  final double quantity;
  final double unitCost;
  final String reference;
  final String? description;
  final double balanceAfter;
  final DateTime createdAt;

  // Navigation properties
  Item? item;

  ItemMovement({
    this.id,
    required this.itemId,
    required this.date,
    required this.movementType,
    required this.quantity,
    required this.unitCost,
    required this.reference,
    this.description,
    required this.balanceAfter,
    DateTime? createdAt,
    this.item,
  }) : createdAt = createdAt ?? DateTime.now();

  factory ItemMovement.fromMap(Map<String, dynamic> map) {
    return ItemMovement(
      id: map['id'] as int?,
      itemId: map['item_id'] as int,
      date: DateTime.parse(map['date'] as String),
      movementType: map['movement_type'] as String,
      quantity: (map['quantity'] as num).toDouble(),
      unitCost: (map['unit_cost'] as num).toDouble(),
      reference: map['reference'] as String,
      description: map['description'] as String?,
      balanceAfter: (map['balance_after'] as num).toDouble(),
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'date': date.toIso8601String().split('T')[0],
      'movement_type': movementType,
      'quantity': quantity,
      'unit_cost': unitCost,
      'reference': reference,
      'description': description,
      'balance_after': balanceAfter,
      'created_at': createdAt.toIso8601String(),
    };
  }

  double get totalValue => quantity * unitCost;
  bool get isInward => movementType == 'in';
  bool get isOutward => movementType == 'out';
  bool get isAdjustment => movementType == 'adjustment';

  String get movementTypeText {
    switch (movementType) {
      case 'in':
        return 'وارد';
      case 'out':
        return 'صادر';
      case 'adjustment':
        return 'تسوية';
      default:
        return movementType;
    }
  }

  @override
  String toString() {
    return 'ItemMovement{id: $id, itemId: $itemId, type: $movementType, qty: $quantity}';
  }
}

// Item summary for reports
class ItemSummary {
  final int itemId;
  final String itemCode;
  final String itemName;
  final String unit;
  final double openingStock;
  final double totalIn;
  final double totalOut;
  final double closingStock;
  final double averageCost;
  final double stockValue;

  ItemSummary({
    required this.itemId,
    required this.itemCode,
    required this.itemName,
    required this.unit,
    required this.openingStock,
    required this.totalIn,
    required this.totalOut,
    required this.closingStock,
    required this.averageCost,
    required this.stockValue,
  });

  factory ItemSummary.fromMap(Map<String, dynamic> map) {
    return ItemSummary(
      itemId: map['item_id'] as int,
      itemCode: map['item_code'] as String,
      itemName: map['item_name'] as String,
      unit: map['unit'] as String,
      openingStock: (map['opening_stock'] as num?)?.toDouble() ?? 0.0,
      totalIn: (map['total_in'] as num?)?.toDouble() ?? 0.0,
      totalOut: (map['total_out'] as num?)?.toDouble() ?? 0.0,
      closingStock: (map['closing_stock'] as num?)?.toDouble() ?? 0.0,
      averageCost: (map['average_cost'] as num?)?.toDouble() ?? 0.0,
      stockValue: (map['stock_value'] as num?)?.toDouble() ?? 0.0,
    );
  }

  double get netMovement => totalIn - totalOut;

  @override
  String toString() {
    return 'ItemSummary{id: $itemId, name: $itemName, stock: $closingStock}';
  }
}
