# Smart Ledger - دفتر الأستاذ الذكي

## نظام محاسبة متقدم وسهل الاستخدام

### 📋 نظرة عامة

Smart Ledger هو نظام محاسبة شامل مطور باستخدام Flutter، مصمم ليعمل على أنظمة Windows و Android. يوفر النظام واجهة مستخدم جميلة وسهلة الاستخدام مع دعم كامل للغة العربية والعمل بدون اتصال بالإنترنت.

### ✨ المميزات الرئيسية

#### 🎨 التصميم والواجهة
- **واجهة مستخدم جميلة**: تصميم عصري مع ألوان متدرجة وتأثيرات بصرية جذابة
- **حركات متقدمة**: نظام حركات شامل مع انتقالات سلسة بين الشاشات
- **لوغو احترافي**: نظام لوغو متعدد الأشكال مع تأثيرات الإضاءة والحركة
- **دعم اللغة العربية**: واجهة مصممة خصيصاً للغة العربية مع دعم RTL
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع أحجام الشاشات

#### 💼 الوظائف المحاسبية
- **إدارة الحسابات**: نظام شجرة الحسابات مع التصنيفات الرئيسية والفرعية
- **القيود المحاسبية**: إدارة شاملة للقيود مع التحقق من التوازن
- **التقارير المالية**: تقارير متقدمة شاملة جميع البيانات المالية
- **إدارة العملاء والموردين**: نظام شامل لإدارة العلاقات التجارية
- **المخزون**: إدارة متقدمة للمخزون مع طرق التكلفة المختلفة
- **الفواتير**: نظام فواتير البيع والشراء مع الحسابات التلقائية

#### 🔧 التقنيات المستخدمة
- **Flutter**: إطار العمل الرئيسي للتطوير متعدد المنصات
- **SQLite**: قاعدة بيانات محلية للعمل بدون اتصال
- **Material Design 3**: نظام التصميم الحديث من Google
- **Google Fonts**: خطوط Cairo العربية الجميلة

### 📁 هيكل المشروع

```
lib/
├── animations/          # نظام الحركات والانتقالات
│   ├── app_animations.dart      # الحركات الأساسية
│   └── page_transitions.dart    # انتقالات الصفحات
├── models/             # نماذج البيانات
│   ├── account.dart           # نموذج الحساب
│   ├── journal_entry.dart     # نموذج القيد المحاسبي
│   └── financial_summary.dart # ملخص مالي
├── services/           # خدمات الأعمال
│   ├── database_service.dart  # خدمة قاعدة البيانات
│   └── account_service.dart   # خدمة إدارة الحسابات
├── screens/            # شاشات التطبيق
│   ├── splash_screen.dart     # شاشة الترحيب
│   ├── home_screen.dart       # الشاشة الرئيسية
│   └── accounts/              # شاشات إدارة الحسابات
├── widgets/            # المكونات المخصصة
│   ├── smart_ledger_logo.dart # نظام اللوغو
│   ├── beautiful_card.dart    # البطاقات الجميلة
│   └── beautiful_buttons.dart # الأزرار المخصصة
└── theme/              # نظام الألوان والتصميم
    └── app_theme.dart         # إعدادات التصميم
```

### 🎯 شرح المكونات الرئيسية

#### 1. نظام الحركات (Animation System)

```dart
// ملف: lib/animations/app_animations.dart
// يحتوي على جميع الحركات الأساسية المستخدمة في التطبيق

class StaggeredAnimation extends StatefulWidget {
  // حركة متدرجة تظهر العناصر واحداً تلو الآخر
  // مفيدة لإظهار قوائم البيانات بشكل جميل
}

class PulseAnimation extends StatefulWidget {
  // حركة نبضة مستمرة للعناصر المهمة
  // تلفت انتباه المستخدم للعناصر الحيوية
}
```

#### 2. نظام اللوغو (Logo System)

```dart
// ملف: lib/widgets/smart_ledger_logo.dart
// نظام لوغو متكامل مع أشكال متعددة

class SmartLedgerLogo extends StatelessWidget {
  // اللوغو الرئيسي مع خلفية متدرجة وأيقونة
  // يمكن تخصيص الحجم والحركة وإظهار النص
}

class AnimatedSmartLedgerLogo extends StatefulWidget {
  // نسخة متحركة من اللوغو لشاشة الترحيب
  // تتضمن حركات التكبير والدوران والتلاشي
}
```

#### 3. نظام قاعدة البيانات

```dart
// ملف: lib/services/database_service.dart
// إدارة شاملة لقاعدة البيانات المحلية

class DatabaseService {
  // إنشاء وإدارة جداول قاعدة البيانات
  // تنفيذ العمليات الأساسية (CRUD)
  // ضمان سلامة البيانات والعلاقات
}
```

#### 4. نماذج البيانات

```dart
// ملف: lib/models/account.dart
// نموذج الحساب المحاسبي

class Account {
  final int? id;              // معرف الحساب
  final String code;          // رمز الحساب
  final String name;          // اسم الحساب
  final AccountType type;     // نوع الحساب (أصول، خصوم، إلخ)
  final int? parentId;        // معرف الحساب الأب
  final double balance;       // رصيد الحساب
  final bool isActive;        // حالة الحساب
  
  // دوال مساعدة لتحويل البيانات
  factory Account.fromMap(Map<String, dynamic> map);
  Map<String, dynamic> toMap();
}
```

### 🎨 نظام التصميم

#### الألوان الرئيسية
```dart
// ملف: lib/theme/app_theme.dart

class AppTheme {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFF2E7D32);      // أخضر أساسي
  static const Color secondaryColor = Color(0xFF1976D2);    // أزرق ثانوي
  static const Color accentColor = Color(0xFFFF6F00);       // برتقالي مميز
  
  // ألوان الحسابات المحاسبية
  static const Color assetColor = Color(0xFF4CAF50);        // أخضر للأصول
  static const Color liabilityColor = Color(0xFFFF5722);    // أحمر للخصوم
  static const Color equityColor = Color(0xFF9C27B0);       // بنفسجي لحقوق الملكية
  static const Color revenueColor = Color(0xFF2196F3);      // أزرق للإيرادات
  static const Color expenseColor = Color(0xFFF44336);      // أحمر للمصروفات
}
```

#### المسافات والأحجام
```dart
// نظام مسافات متسق
static const double spacingSmall = 8.0;
static const double spacingMedium = 16.0;
static const double spacingLarge = 24.0;
static const double spacingXLarge = 32.0;

// أحجام الحدود المنحنية
static const double borderRadiusSmall = 8.0;
static const double borderRadiusMedium = 12.0;
static const double borderRadiusLarge = 16.0;
```

### 🚀 كيفية تشغيل التطبيق

#### متطلبات النظام
- Flutter SDK (الإصدار 3.0 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- محاكي Android أو جهاز Android للاختبار
- Windows 10/11 للتشغيل على Windows

#### خطوات التشغيل

1. **تحميل المشروع**
```bash
git clone [repository-url]
cd smart_ledger
```

2. **تحميل التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
# للتشغيل على Android
flutter run

# للتشغيل على Windows
flutter run -d windows
```

### 📱 لقطات الشاشة

#### شاشة الترحيب
- لوغو متحرك مع تأثيرات بصرية جميلة
- خلفية متدرجة مع دوائر متحركة
- نص ترحيبي بالعربية والإنجليزية

#### الشاشة الرئيسية
- بطاقات إحصائية مع الأرقام المالية الرئيسية
- قائمة إجراءات سريعة للوظائف الأساسية
- شريط تنقل سفلي مع أيقونات جميلة

#### شاشة إدارة الحسابات
- عرض شجرة الحسابات بشكل هرمي
- إمكانية إضافة وتعديل وحذف الحسابات
- بحث سريع في الحسابات

### 🔧 التخصيص والتطوير

#### إضافة حركات جديدة
```dart
// في ملف lib/animations/app_animations.dart
class CustomAnimation extends StatefulWidget {
  // إضافة حركة مخصصة جديدة
  // اتباع نفس النمط المستخدم في الحركات الموجودة
}
```

#### إضافة شاشات جديدة
```dart
// إنشاء ملف جديد في lib/screens/
class NewScreen extends StatefulWidget {
  // استخدام نفس النمط والتصميم
  // إضافة الحركات والانتقالات المناسبة
}
```

### 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- قراءة هذا الدليل بعناية
- فحص ملفات الكود للأمثلة العملية
- التأكد من تحديث Flutter SDK

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**Smart Ledger** - نظام محاسبة ذكي وجميل لإدارة أعمالك بكفاءة ✨
