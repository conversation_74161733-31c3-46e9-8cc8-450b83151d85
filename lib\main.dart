import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'theme/app_theme.dart';
import 'screens/customers/customers_screen.dart';
import 'screens/suppliers/suppliers_screen.dart';
import 'screens/inventory/items_screen.dart';
import 'screens/invoices/invoices_screen.dart';
import 'screens/journal_entries/journal_entries_screen.dart';
import 'screens/reports/financial_reports_screen.dart';
import 'screens/settings/settings_screen.dart';
import 'screens/fixed_assets/fixed_assets_dashboard_screen.dart';
import 'screens/fixed_assets/fixed_assets_list_screen.dart';
import 'screens/projects/projects_screen.dart';
import 'services/user_service.dart';
import 'providers/language_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize user service
  await UserService.instance.initialize();

  // Initialize language provider
  final languageProvider = LanguageProvider();
  await languageProvider.initialize();

  runApp(SmartLedgerApp(languageProvider: languageProvider));
}

class SmartLedgerApp extends StatelessWidget {
  final LanguageProvider languageProvider;

  const SmartLedgerApp({super.key, required this.languageProvider});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<LanguageProvider>.value(
      value: languageProvider,
      child: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return MaterialApp(
            title: 'Smart Ledger',
            debugShowCheckedModeBanner: false,

            // Localization settings
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar', ''), // Arabic
              Locale('en', ''), // English
            ],
            locale: languageProvider.currentLocale,
            // استخدام النظام الجديد للتصميم
            theme: AppTheme.lightTheme,

            home: const SplashScreen(),
            routes: {
              '/customers': (context) => const CustomersScreen(),
              '/suppliers': (context) => const SuppliersScreen(),
              '/items': (context) => const ItemsScreen(),
              '/invoices': (context) => const InvoicesScreen(),
              '/journal-entries': (context) => const JournalEntriesScreen(),
              '/financial-reports': (context) => const FinancialReportsScreen(),
              '/settings': (context) => const SettingsScreen(),
              '/fixed-assets-dashboard': (context) =>
                  const FixedAssetsDashboardScreen(),
              '/fixed-assets': (context) => const FixedAssetsListScreen(),
              '/projects': (context) => const ProjectsScreen(),
            },
          );
        },
      ),
    );
  }
}
