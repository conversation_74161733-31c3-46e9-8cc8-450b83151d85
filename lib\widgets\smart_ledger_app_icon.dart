import 'dart:math' as math;
import 'package:flutter/material.dart';

/// 🎨 أيقونة تطبيق Smart Ledger الاحترافية
/// Professional Smart Ledger App Icon
class SmartLedgerAppIcon extends StatefulWidget {
  final double size;
  final bool animated;
  final Color? primaryColor;
  final Color? secondaryColor;

  const SmartLedgerAppIcon({
    super.key,
    this.size = 120,
    this.animated = true,
    this.primaryColor,
    this.secondaryColor,
  });

  @override
  State<SmartLedgerAppIcon> createState() => _SmartLedgerAppIconState();
}

class _SmartLedgerAppIconState extends State<SmartLedgerAppIcon>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _glowController;

  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    if (widget.animated) {
      _rotationController = AnimationController(
        duration: const Duration(seconds: 8),
        vsync: this,
      );

      _pulseController = AnimationController(
        duration: const Duration(seconds: 2),
        vsync: this,
      );

      _glowController = AnimationController(
        duration: const Duration(seconds: 3),
        vsync: this,
      );

      _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
        CurvedAnimation(parent: _rotationController, curve: Curves.linear),
      );

      _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
        CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
      );

      _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
        CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
      );

      _rotationController.repeat();
      _pulseController.repeat(reverse: true);
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    if (widget.animated) {
      _rotationController.dispose();
      _pulseController.dispose();
      _glowController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.primaryColor ?? const Color(0xFF1565C0);
    final secondaryColor = widget.secondaryColor ?? const Color(0xFF42A5F5);

    if (!widget.animated) {
      return _buildStaticIcon(primaryColor, secondaryColor);
    }

    return AnimatedBuilder(
      animation: Listenable.merge([
        _rotationAnimation,
        _pulseAnimation,
        _glowAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withValues(
                    alpha: _glowAnimation.value * 0.6,
                  ),
                  blurRadius: 20 * _glowAnimation.value,
                  spreadRadius: 5 * _glowAnimation.value,
                ),
              ],
            ),
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: _buildIconContent(primaryColor, secondaryColor),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStaticIcon(Color primaryColor, Color secondaryColor) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: _buildIconContent(primaryColor, secondaryColor),
    );
  }

  Widget _buildIconContent(Color primaryColor, Color secondaryColor) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            secondaryColor,
            primaryColor,
            primaryColor.withValues(alpha: 0.8),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الخلفية المتدرجة
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor.withValues(alpha: 0.9),
                  secondaryColor.withValues(alpha: 0.7),
                ],
              ),
            ),
          ),

          // رمز الدفتر المحاسبي
          Icon(
            Icons.account_balance_wallet_rounded,
            size: widget.size * 0.4,
            color: Colors.white,
          ),

          // رمز الذكاء (دائرة صغيرة)
          Positioned(
            top: widget.size * 0.15,
            right: widget.size * 0.15,
            child: Container(
              width: widget.size * 0.2,
              height: widget.size * 0.2,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFFFFD700),
                border: Border.all(color: Colors.white, width: 1),
              ),
              child: Icon(
                Icons.auto_awesome,
                size: widget.size * 0.1,
                color: primaryColor,
              ),
            ),
          ),

          // خطوط الطاقة
          ...List.generate(8, (index) {
            final angle = (index * math.pi * 2) / 8;
            return Transform.rotate(
              angle: angle,
              child: Container(
                width: 2,
                height: widget.size * 0.15,
                margin: EdgeInsets.only(bottom: widget.size * 0.35),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(1),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}

/// 🏷️ شعار Smart Ledger النصي
/// Smart Ledger Text Logo
class SmartLedgerTextLogo extends StatelessWidget {
  final double fontSize;
  final Color? textColor;
  final bool showSubtitle;

  const SmartLedgerTextLogo({
    super.key,
    this.fontSize = 24,
    this.textColor,
    this.showSubtitle = true,
  });

  @override
  Widget build(BuildContext context) {
    final color = textColor ?? Theme.of(context).primaryColor;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // العنوان الرئيسي
        Text(
          'Smart Ledger',
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: color,
            letterSpacing: 1.2,
            shadows: [
              Shadow(
                color: color.withValues(alpha: 0.3),
                offset: const Offset(2, 2),
                blurRadius: 4,
              ),
            ],
          ),
        ),

        if (showSubtitle) ...[
          const SizedBox(height: 4),
          Text(
            'دفتر الأستاذ الذكي',
            style: TextStyle(
              fontSize: fontSize * 0.6,
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }
}

/// 🎨 أيقونة مصغرة للتطبيق
/// Mini App Icon for small spaces
class SmartLedgerMiniIcon extends StatelessWidget {
  final double size;
  final Color? color;

  const SmartLedgerMiniIcon({super.key, this.size = 32, this.color});

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? Theme.of(context).primaryColor;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [iconColor, iconColor.withValues(alpha: 0.7)],
        ),
      ),
      child: Icon(
        Icons.account_balance_wallet_rounded,
        size: size * 0.6,
        color: Colors.white,
      ),
    );
  }
}
