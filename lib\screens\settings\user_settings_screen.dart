import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../services/user_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_text_form_field.dart';
import '../../widgets/smart_notifications.dart';

/// شاشة إعدادات المستخدم
/// User Settings Screen
class UserSettingsScreen extends StatefulWidget {
  const UserSettingsScreen({super.key});

  @override
  State<UserSettingsScreen> createState() => _UserSettingsScreenState();
}

class _UserSettingsScreenState extends State<UserSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();

  String _selectedRole = 'محاسب';
  bool _isLoading = false;
  bool _isUserConfigured = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentUser() async {
    setState(() => _isLoading = true);

    try {
      _isUserConfigured = await UserService.instance.isUserConfigured();

      if (_isUserConfigured) {
        final userResult = await UserService.instance.getCurrentUser();
        if (userResult.isSuccess) {
          final user = userResult.data!;
          _nameController.text = user.name;
          _emailController.text = user.email ?? '';
          _selectedRole = user.role ?? 'محاسب';
        }
      }
    } catch (e) {
      if (mounted) {
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: 'فشل في تحميل بيانات المستخدم: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveUserSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final user = User(
        name: _nameController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        role: _selectedRole,
      );

      final result = await UserService.instance.setCurrentUser(user);

      if (mounted) {
        if (result.isSuccess) {
          SmartNotificationManager.showSuccess(
            context,
            title: 'تم الحفظ',
            message: 'تم حفظ إعدادات المستخدم بنجاح',
          );
          Navigator.of(context).pop(true);
        } else {
          SmartNotificationManager.showError(
            context,
            title: 'خطأ',
            message: result.error ?? 'فشل في حفظ إعدادات المستخدم',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: 'حدث خطأ أثناء حفظ البيانات: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('إعدادات المستخدم'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppTheme.primaryColor,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingMedium),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header Card
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingMedium),
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusMedium,
                        ),
                        boxShadow: [AppTheme.cardShadow],
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.person_rounded,
                            size: 48,
                            color: Colors.white,
                          ),
                          const SizedBox(height: AppTheme.spacingSmall),
                          Text(
                            _isUserConfigured
                                ? 'تحديث بيانات المستخدم'
                                : 'إعداد بيانات المستخدم',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacingSmall),
                          Text(
                            'هذه البيانات ستظهر في القيود المحاسبية والتقارير',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: AppTheme.spacingLarge),

                    // User Name Field
                    BeautifulTextFormField(
                      controller: _nameController,
                      labelText: 'اسم المستخدم',
                      hintText: 'أدخل اسم المستخدم',
                      prefixIcon: Icons.person_rounded,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'اسم المستخدم مطلوب';
                        }
                        if (value.trim().length < 2) {
                          return 'اسم المستخدم يجب أن يكون أكثر من حرفين';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: AppTheme.spacingMedium),

                    // Email Field
                    BeautifulTextFormField(
                      controller: _emailController,
                      labelText: 'البريد الإلكتروني (اختياري)',
                      hintText: 'أدخل البريد الإلكتروني',
                      prefixIcon: Icons.email_rounded,
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (!RegExp(
                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                          ).hasMatch(value)) {
                            return 'البريد الإلكتروني غير صحيح';
                          }
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: AppTheme.spacingMedium),

                    // Role Selection
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingMedium),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusMedium,
                        ),
                        boxShadow: [AppTheme.cardShadow],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.work_rounded,
                                color: AppTheme.primaryColor,
                                size: 20,
                              ),
                              const SizedBox(width: AppTheme.spacingSmall),
                              Text(
                                'المنصب الوظيفي',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.textPrimaryColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppTheme.spacingMedium),
                          DropdownButtonFormField<String>(
                            value: _selectedRole,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusSmall,
                                ),
                                borderSide: BorderSide(
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusSmall,
                                ),
                                borderSide: BorderSide(
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusSmall,
                                ),
                                borderSide: BorderSide(
                                  color: AppTheme.primaryColor,
                                  width: 2,
                                ),
                              ),
                            ),
                            items: UserService.instance.getAvailableRoles().map(
                              (role) {
                                return DropdownMenuItem(
                                  value: role,
                                  child: Text(role),
                                );
                              },
                            ).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() => _selectedRole = value);
                              }
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: AppTheme.spacingLarge),

                    // Save Button
                    ElevatedButton(
                      onPressed: _isLoading ? null : _saveUserSettings,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          vertical: AppTheme.spacingMedium,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            AppTheme.borderRadiusMedium,
                          ),
                        ),
                        elevation: 4,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Text(
                              'حفظ الإعدادات',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
