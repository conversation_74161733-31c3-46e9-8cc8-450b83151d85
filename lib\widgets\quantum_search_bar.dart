/// شريط بحث كمي متقدم لتطبيق Smart Ledger
/// Advanced Quantum Search Bar for Smart Ledger Application
library;

import 'package:flutter/material.dart';

/// شريط بحث كمي مع تأثيرات بصرية متقدمة
/// Quantum search bar with advanced visual effects
class QuantumSearchBar extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final void Function()? onClear;
  final bool enabled;
  final bool autofocus;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool enableGlowEffect;
  final bool enableFocusEffect;
  final IconData? prefixIcon;
  final List<Widget>? actions;

  const QuantumSearchBar({
    super.key,
    this.controller,
    this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.enabled = true,
    this.autofocus = false,
    this.borderRadius = 25.0,
    this.padding,
    this.margin,
    this.enableGlowEffect = true,
    this.enableFocusEffect = true,
    this.prefixIcon,
    this.actions,
  });

  @override
  State<QuantumSearchBar> createState() => _QuantumSearchBarState();
}

class _QuantumSearchBarState extends State<QuantumSearchBar>
    with TickerProviderStateMixin {
  late AnimationController _focusController;
  late AnimationController _glowController;
  late Animation<double> _focusAnimation;
  late Animation<double> _glowAnimation;
  late FocusNode _focusNode;
  late TextEditingController _controller;
  bool _isFocused = false;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _focusNode = FocusNode();
    _controller = widget.controller ?? TextEditingController();
    _focusNode.addListener(_onFocusChange);
    _controller.addListener(_onTextChange);
    _hasText = _controller.text.isNotEmpty;
  }

  void _initializeAnimations() {
    _focusController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _focusAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _focusController, curve: Curves.easeInOut),
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    if (widget.enableGlowEffect) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _focusController.dispose();
    _glowController.dispose();
    _focusNode.removeListener(_onFocusChange);
    _controller.removeListener(_onTextChange);
    _focusNode.dispose();
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() => _isFocused = _focusNode.hasFocus);

    if (widget.enableFocusEffect) {
      if (_isFocused) {
        _focusController.forward();
      } else {
        _focusController.reverse();
      }
    }
  }

  void _onTextChange() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() => _hasText = hasText);
    }
  }

  void _clearText() {
    _controller.clear();
    widget.onClear?.call();
    widget.onChanged?.call('');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedBuilder(
      animation: Listenable.merge([_focusAnimation, _glowAnimation]),
      builder: (context, child) {
        return Container(
          margin: widget.margin,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            boxShadow: [
              if (widget.enableGlowEffect && widget.enabled)
                BoxShadow(
                  color: Colors.cyan.withValues(
                    alpha: _glowAnimation.value * 0.2,
                  ),
                  blurRadius: 15,
                  offset: const Offset(0, 0),
                ),
              if (_isFocused && widget.enableFocusEffect)
                BoxShadow(
                  color: colorScheme.primary.withValues(
                    alpha: _focusAnimation.value * 0.4,
                  ),
                  blurRadius: 20,
                  offset: const Offset(0, 0),
                ),
            ],
          ),
          child: Container(
            padding:
                widget.padding ?? const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  colorScheme.surface.withValues(alpha: 0.9),
                  colorScheme.surface.withValues(alpha: 0.7),
                ],
              ),
              border: Border.all(
                color: _isFocused
                    ? colorScheme.primary
                    : colorScheme.outline.withValues(alpha: 0.3),
                width: _isFocused ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 12, right: 8),
                  child: Icon(
                    widget.prefixIcon ?? Icons.search,
                    color: _isFocused
                        ? colorScheme.primary
                        : colorScheme.onSurface.withValues(alpha: 0.6),
                    size: 24,
                  ),
                ),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    enabled: widget.enabled,
                    autofocus: widget.autofocus,
                    onChanged: widget.onChanged,
                    onSubmitted: widget.onSubmitted,
                    style: TextStyle(
                      color: widget.enabled
                          ? colorScheme.onSurface
                          : colorScheme.onSurface.withValues(alpha: 0.6),
                      fontSize: 16,
                    ),
                    decoration: InputDecoration(
                      hintText: widget.hintText ?? 'البحث...',
                      hintStyle: TextStyle(
                        color: colorScheme.onSurface.withValues(alpha: 0.4),
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                if (_hasText)
                  IconButton(
                    onPressed: _clearText,
                    icon: Icon(
                      Icons.clear,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                if (widget.actions != null) ...widget.actions!,
              ],
            ),
          ),
        );
      },
    );
  }
}

/// شريط بحث كمي مبسط للاستخدام السريع
/// Simple quantum search bar for quick usage
class SimpleQuantumSearchBar extends StatelessWidget {
  final TextEditingController? controller;
  final String? hintText;
  final void Function(String)? onChanged;

  const SimpleQuantumSearchBar({
    super.key,
    this.controller,
    this.hintText,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return QuantumSearchBar(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      enableGlowEffect: false,
    );
  }
}
