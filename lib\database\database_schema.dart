// Database schema for Smart Ledger accounting system
// This file contains all table creation scripts and database structure

class DatabaseSchema {
  static const String databaseName = 'smart_ledger.db';
  static const int databaseVersion = 2;

  // Table names
  static const String tableAccounts = 'accounts';
  static const String tableJournalEntries = 'journal_entries';
  static const String tableJournalEntryLines = 'journal_entry_lines';
  static const String tableCustomers = 'customers';
  static const String tableSuppliers = 'suppliers';
  static const String tableItems = 'items';
  static const String tableInvoices = 'invoices';
  static const String tableInvoiceLines = 'invoice_lines';
  static const String tableCompanySettings = 'company_settings';
  static const String tableFixedAssets = 'fixed_assets';
  static const String tableAssetDepreciation = 'asset_depreciation';
  static const String tableWarehouses = 'warehouses';
  static const String tableWarehouseLocations = 'warehouse_locations';
  static const String tableStockMovements = 'stock_movements';
  static const String tableStockBalances = 'stock_balances';
  static const String tableCurrencies = 'currencies';
  static const String tableExchangeRates = 'exchange_rates';
  static const String tableExchangeRateHistory = 'exchange_rate_history';

  // Tax tables
  static const String tableTaxes = 'taxes';
  static const String tableTaxGroups = 'tax_groups';
  static const String tableTaxGroupTaxes = 'tax_group_taxes';
  static const String tableTaxCalculations = 'tax_calculations';
  static const String tableCompanyTaxSettings = 'company_tax_settings';
  static const String tableProjects = 'projects';
  static const String tableProjectPhases = 'project_phases';
  static const String tableProjectCosts = 'project_costs';
  static const String tableProjectResources = 'project_resources';
  static const String tableProjectTimeEntries = 'project_time_entries';
  static const String tableProjectTasks = 'project_tasks';

  // Budget tables
  static const String tableBudgets = 'budgets';
  static const String tableBudgetLines = 'budget_lines';
  static const String tableBudgetRevisions = 'budget_revisions';

  // HR and Payroll tables
  static const String tableDepartments = 'departments';
  static const String tablePositions = 'positions';
  static const String tableEmployees = 'employees';
  static const String tableAllowances = 'allowances';
  static const String tableDeductions = 'deductions';
  static const String tableEmployeeAllowances = 'employee_allowances';
  static const String tableEmployeeDeductions = 'employee_deductions';
  static const String tablePayrollPeriods = 'payroll_periods';
  static const String tablePayrollRecords = 'payroll_records';
  static const String tablePayrollAllowanceDetails =
      'payroll_allowance_details';
  static const String tablePayrollDeductionDetails =
      'payroll_deduction_details';
  static const String tableAttendanceRecords = 'attendance_records';
  static const String tableLeaveTypes = 'leave_types';
  static const String tableLeaveRequests = 'leave_requests';
  static const String tableLeaveBalances = 'leave_balances';

  // Bank and Cash tables
  static const String tableBankAccounts = 'bank_accounts';
  static const String tableBankTransactions = 'bank_transactions';
  static const String tableCashVaults = 'cash_vaults';
  static const String tableCashTransactions = 'cash_transactions';

  // Cost Calculation Tables
  static const String tableCostLayers = 'cost_layers';
  static const String tableItemCostingSettings = 'item_costing_settings';

  // Approval System Tables
  static const String tableApprovalWorkflows = 'approval_workflows';
  static const String tableApprovalSteps = 'approval_steps';
  static const String tableApprovalRequests = 'approval_requests';
  static const String tableApprovalActions = 'approval_actions';
  static const String tablePeriodicInventories = 'periodic_inventories';
  static const String tableInventoryItems = 'inventory_items';

  // Create table scripts
  static const String createAccountsTable =
      '''
    CREATE TABLE $tableAccounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      name_en TEXT,
      description TEXT,
      account_type TEXT NOT NULL CHECK (account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
      parent_id INTEGER,
      is_active INTEGER NOT NULL DEFAULT 1,
      balance REAL NOT NULL DEFAULT 0.0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (parent_id) REFERENCES $tableAccounts (id)
    )
  ''';

  static const String createJournalEntriesTable =
      '''
    CREATE TABLE $tableJournalEntries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      entry_number TEXT NOT NULL UNIQUE,
      date TEXT NOT NULL,
      description TEXT NOT NULL,
      reference TEXT,
      project_id INTEGER,
      total_debit REAL NOT NULL DEFAULT 0.0,
      total_credit REAL NOT NULL DEFAULT 0.0,
      is_balanced INTEGER NOT NULL DEFAULT 0,
      is_posted INTEGER NOT NULL DEFAULT 0,
      created_by TEXT,

      -- Multi-Currency Support Fields
      currency_code TEXT NOT NULL DEFAULT 'SAR',
      exchange_rate REAL NOT NULL DEFAULT 1.0,
      base_currency_code TEXT NOT NULL DEFAULT 'SAR',
      base_currency_total_debit REAL NOT NULL DEFAULT 0.0,
      base_currency_total_credit REAL NOT NULL DEFAULT 0.0,

      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES $tableProjects (id)
    )
  ''';

  static const String createJournalEntryLinesTable =
      '''
    CREATE TABLE $tableJournalEntryLines (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      journal_entry_id INTEGER NOT NULL,
      account_id INTEGER NOT NULL,
      description TEXT,
      debit_amount REAL NOT NULL DEFAULT 0.0,
      credit_amount REAL NOT NULL DEFAULT 0.0,
      line_order INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (journal_entry_id) REFERENCES $tableJournalEntries (id) ON DELETE CASCADE,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id)
    )
  ''';

  static const String createCustomersTable =
      '''
    CREATE TABLE $tableCustomers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      phone TEXT,
      email TEXT,
      address TEXT,
      tax_number TEXT,
      credit_limit REAL DEFAULT 0.0,
      current_balance REAL NOT NULL DEFAULT 0.0,
      account_id INTEGER,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id)
    )
  ''';

  static const String createSuppliersTable =
      '''
    CREATE TABLE $tableSuppliers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      phone TEXT,
      email TEXT,
      address TEXT,
      category TEXT,
      tax_number TEXT,
      credit_limit REAL,
      payment_terms INTEGER,
      notes TEXT,
      current_balance REAL NOT NULL DEFAULT 0.0,
      account_id INTEGER,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id)
    )
  ''';

  static const String createItemsTable =
      '''
    CREATE TABLE $tableItems (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      unit TEXT NOT NULL DEFAULT 'قطعة',
      cost_price REAL NOT NULL DEFAULT 0.0,
      selling_price REAL NOT NULL DEFAULT 0.0,
      current_stock REAL NOT NULL DEFAULT 0.0,
      min_stock_level REAL DEFAULT 0.0,
      max_stock_level REAL DEFAULT 0.0,
      category TEXT,
      barcode TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  static const String createInvoicesTable =
      '''
    CREATE TABLE $tableInvoices (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      invoice_number TEXT NOT NULL UNIQUE,
      invoice_type TEXT NOT NULL CHECK (invoice_type IN ('sales', 'purchase')),
      date TEXT NOT NULL,
      due_date TEXT,
      customer_id INTEGER,
      supplier_id INTEGER,
      project_id INTEGER,
      subtotal REAL NOT NULL DEFAULT 0.0,
      tax_amount REAL NOT NULL DEFAULT 0.0,
      discount_amount REAL NOT NULL DEFAULT 0.0,
      total_amount REAL NOT NULL DEFAULT 0.0,
      paid_amount REAL NOT NULL DEFAULT 0.0,
      status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'posted', 'paid', 'cancelled')),
      notes TEXT,
      journal_entry_id INTEGER,

      -- Multi-Currency Support Fields
      currency_code TEXT NOT NULL DEFAULT 'SAR',
      exchange_rate REAL NOT NULL DEFAULT 1.0,
      base_currency_code TEXT NOT NULL DEFAULT 'SAR',
      base_currency_subtotal REAL NOT NULL DEFAULT 0.0,
      base_currency_tax_amount REAL NOT NULL DEFAULT 0.0,
      base_currency_discount_amount REAL NOT NULL DEFAULT 0.0,
      base_currency_total_amount REAL NOT NULL DEFAULT 0.0,
      base_currency_paid_amount REAL NOT NULL DEFAULT 0.0,

      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES $tableCustomers (id),
      FOREIGN KEY (supplier_id) REFERENCES $tableSuppliers (id),
      FOREIGN KEY (project_id) REFERENCES $tableProjects (id),
      FOREIGN KEY (journal_entry_id) REFERENCES $tableJournalEntries (id)
    )
  ''';

  static const String createInvoiceLinesTable =
      '''
    CREATE TABLE $tableInvoiceLines (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      invoice_id INTEGER NOT NULL,
      item_id INTEGER NOT NULL,
      description TEXT,
      quantity REAL NOT NULL DEFAULT 1.0,
      unit_price REAL NOT NULL DEFAULT 0.0,
      discount_percentage REAL DEFAULT 0.0,
      line_total REAL NOT NULL DEFAULT 0.0,
      line_order INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (invoice_id) REFERENCES $tableInvoices (id) ON DELETE CASCADE,
      FOREIGN KEY (item_id) REFERENCES $tableItems (id)
    )
  ''';

  static const String createCompanySettingsTable =
      '''
    CREATE TABLE $tableCompanySettings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_name TEXT NOT NULL,
      company_name_en TEXT,
      address TEXT,
      phone TEXT,
      email TEXT,
      tax_number TEXT,
      currency_code TEXT NOT NULL DEFAULT 'SAR',
      currency_symbol TEXT NOT NULL DEFAULT 'ر.س',
      fiscal_year_start TEXT NOT NULL DEFAULT '01-01',
      decimal_places INTEGER NOT NULL DEFAULT 2,
      date_format TEXT NOT NULL DEFAULT 'dd/MM/yyyy',
      logo_path TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  static const String createFixedAssetsTable =
      '''
    CREATE TABLE $tableFixedAssets (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      category TEXT NOT NULL CHECK (category IN ('building', 'machinery', 'vehicle', 'furniture', 'computer', 'other')),
      location TEXT,
      purchase_date TEXT NOT NULL,
      purchase_price REAL NOT NULL DEFAULT 0.0,
      salvage_value REAL DEFAULT 0.0,
      useful_life_years INTEGER NOT NULL DEFAULT 1,
      useful_life_units INTEGER,
      depreciation_method TEXT NOT NULL DEFAULT 'straightLine' CHECK (depreciation_method IN ('straightLine', 'decliningBalance', 'unitsOfProduction', 'sumOfYears')),
      status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'disposed', 'underMaintenance')),
      account_id INTEGER,
      depreciation_account_id INTEGER,
      expense_account_id INTEGER,
      serial_number TEXT,
      model TEXT,
      manufacturer TEXT,
      warranty_info TEXT,
      warranty_expiry TEXT,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id),
      FOREIGN KEY (depreciation_account_id) REFERENCES $tableAccounts (id),
      FOREIGN KEY (expense_account_id) REFERENCES $tableAccounts (id)
    )
  ''';

  static const String createAssetDepreciationTable =
      '''
    CREATE TABLE $tableAssetDepreciation (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      asset_id INTEGER NOT NULL,
      year INTEGER NOT NULL,
      month INTEGER NOT NULL,
      depreciation_date TEXT NOT NULL,
      depreciation_amount REAL NOT NULL DEFAULT 0.0,
      accumulated_depreciation REAL NOT NULL DEFAULT 0.0,
      book_value REAL NOT NULL DEFAULT 0.0,
      notes TEXT,
      journal_entry_id INTEGER,
      is_posted INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (asset_id) REFERENCES $tableFixedAssets (id) ON DELETE CASCADE,
      FOREIGN KEY (journal_entry_id) REFERENCES $tableJournalEntries (id),
      UNIQUE(asset_id, year, month)
    )
  ''';

  static const String createWarehousesTable =
      '''
    CREATE TABLE $tableWarehouses (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL DEFAULT 'main' CHECK (type IN ('main', 'branch', 'retail', 'wholesale', 'transit', 'damaged', 'quarantine')),
      status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance', 'closed')),
      address TEXT,
      city TEXT,
      country TEXT,
      phone TEXT,
      email TEXT,
      manager_name TEXT,
      area REAL,
      capacity REAL,
      is_main_warehouse INTEGER NOT NULL DEFAULT 0,
      parent_warehouse_id INTEGER,
      allow_negative_stock INTEGER NOT NULL DEFAULT 0,
      require_approval INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (parent_warehouse_id) REFERENCES $tableWarehouses (id)
    )
  ''';

  static const String createWarehouseLocationsTable =
      '''
    CREATE TABLE $tableWarehouseLocations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      warehouse_id INTEGER NOT NULL,
      code TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      zone TEXT,
      aisle TEXT,
      rack TEXT,
      shelf TEXT,
      bin TEXT,
      level INTEGER,
      max_weight REAL,
      max_volume REAL,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (warehouse_id) REFERENCES $tableWarehouses (id) ON DELETE CASCADE,
      UNIQUE(warehouse_id, code)
    )
  ''';

  static const String createStockMovementsTable =
      '''
    CREATE TABLE $tableStockMovements (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      document_number TEXT NOT NULL UNIQUE,
      type TEXT NOT NULL CHECK (type IN ('receipt', 'issue', 'transfer', 'adjustment', 'return_', 'damage', 'loss', 'found')),
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed', 'cancelled')),
      reason TEXT NOT NULL CHECK (reason IN ('purchase', 'sale', 'transfer', 'adjustment', 'return_', 'damage', 'loss', 'found', 'production', 'consumption')),
      item_id INTEGER NOT NULL,
      warehouse_id INTEGER NOT NULL,
      location_id INTEGER,
      to_warehouse_id INTEGER,
      to_location_id INTEGER,
      quantity REAL NOT NULL,
      unit_cost REAL NOT NULL DEFAULT 0.0,
      total_cost REAL NOT NULL DEFAULT 0.0,
      batch_number TEXT,
      serial_number TEXT,
      expiry_date TEXT,
      movement_date TEXT NOT NULL,
      reference_document TEXT,
      notes TEXT,
      user_id INTEGER NOT NULL,
      approved_by INTEGER,
      approved_at TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (item_id) REFERENCES $tableItems (id),
      FOREIGN KEY (warehouse_id) REFERENCES $tableWarehouses (id),
      FOREIGN KEY (location_id) REFERENCES $tableWarehouseLocations (id),
      FOREIGN KEY (to_warehouse_id) REFERENCES $tableWarehouses (id),
      FOREIGN KEY (to_location_id) REFERENCES $tableWarehouseLocations (id),
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (approved_by) REFERENCES users (id)
    )
  ''';

  static const String createStockBalancesTable =
      '''
    CREATE TABLE $tableStockBalances (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      item_id INTEGER NOT NULL,
      warehouse_id INTEGER NOT NULL,
      location_id INTEGER,
      quantity REAL NOT NULL DEFAULT 0.0,
      average_cost REAL NOT NULL DEFAULT 0.0,
      total_value REAL NOT NULL DEFAULT 0.0,
      batch_number TEXT,
      serial_number TEXT,
      expiry_date TEXT,
      last_movement_date TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (item_id) REFERENCES $tableItems (id),
      FOREIGN KEY (warehouse_id) REFERENCES $tableWarehouses (id),
      FOREIGN KEY (location_id) REFERENCES $tableWarehouseLocations (id),
      UNIQUE(item_id, warehouse_id, location_id, batch_number, serial_number)
    )
  ''';

  static const String createCurrenciesTable =
      '''
    CREATE TABLE $tableCurrencies (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      symbol TEXT NOT NULL,
      name_ar TEXT NOT NULL,
      name_en TEXT NOT NULL,
      is_active INTEGER NOT NULL DEFAULT 1,
      is_base_currency INTEGER NOT NULL DEFAULT 0,
      decimal_places INTEGER NOT NULL DEFAULT 2,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  static const String createExchangeRatesTable =
      '''
    CREATE TABLE $tableExchangeRates (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      from_currency_code TEXT NOT NULL,
      to_currency_code TEXT NOT NULL,
      rate REAL NOT NULL,
      effective_date TEXT NOT NULL,
      expiry_date TEXT,
      source TEXT NOT NULL DEFAULT 'manual' CHECK (source IN ('manual', 'central_bank', 'commercial_bank', 'api', 'market')),
      is_active INTEGER NOT NULL DEFAULT 1,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (from_currency_code) REFERENCES $tableCurrencies (code),
      FOREIGN KEY (to_currency_code) REFERENCES $tableCurrencies (code),
      UNIQUE(from_currency_code, to_currency_code, effective_date)
    )
  ''';

  static const String createExchangeRateHistoryTable =
      '''
    CREATE TABLE $tableExchangeRateHistory (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      from_currency_code TEXT NOT NULL,
      to_currency_code TEXT NOT NULL,
      rate REAL NOT NULL,
      date TEXT NOT NULL,
      source TEXT NOT NULL DEFAULT 'manual' CHECK (source IN ('manual', 'central_bank', 'commercial_bank', 'api', 'market')),
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (from_currency_code) REFERENCES $tableCurrencies (code),
      FOREIGN KEY (to_currency_code) REFERENCES $tableCurrencies (code)
    )
  ''';

  // Tax tables creation scripts
  static const String createTaxesTable =
      '''
    CREATE TABLE $tableTaxes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name_ar TEXT NOT NULL,
      name_en TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL CHECK (type IN ('vat', 'income_tax', 'corporate_tax', 'withholding_tax', 'customs_duty', 'excise_tax', 'other')),
      calculation_method TEXT NOT NULL DEFAULT 'percentage' CHECK (calculation_method IN ('percentage', 'fixed_amount', 'tiered', 'compound')),
      rate REAL NOT NULL DEFAULT 0.0,
      minimum_amount REAL,
      maximum_amount REAL,
      is_inclusive INTEGER NOT NULL DEFAULT 0,
      is_compound INTEGER NOT NULL DEFAULT 0,
      account_id INTEGER,
      status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'archived')),
      effective_date TEXT,
      expiry_date TEXT,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id)
    )
  ''';

  static const String createTaxGroupsTable =
      '''
    CREATE TABLE $tableTaxGroups (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name_ar TEXT NOT NULL,
      name_en TEXT NOT NULL,
      description TEXT,
      is_default INTEGER NOT NULL DEFAULT 0,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  static const String createTaxGroupTaxesTable =
      '''
    CREATE TABLE $tableTaxGroupTaxes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tax_group_id INTEGER NOT NULL,
      tax_id INTEGER NOT NULL,
      sort_order INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (tax_group_id) REFERENCES $tableTaxGroups (id) ON DELETE CASCADE,
      FOREIGN KEY (tax_id) REFERENCES $tableTaxes (id) ON DELETE CASCADE,
      UNIQUE(tax_group_id, tax_id)
    )
  ''';

  static const String createTaxCalculationsTable =
      '''
    CREATE TABLE $tableTaxCalculations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      document_type TEXT NOT NULL CHECK (document_type IN ('invoice', 'journal_entry', 'payment')),
      document_id INTEGER NOT NULL,
      tax_id INTEGER NOT NULL,
      base_amount REAL NOT NULL DEFAULT 0.0,
      tax_amount REAL NOT NULL DEFAULT 0.0,
      tax_rate REAL NOT NULL DEFAULT 0.0,
      is_inclusive INTEGER NOT NULL DEFAULT 0,
      notes TEXT,
      calculated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (tax_id) REFERENCES $tableTaxes (id)
    )
  ''';

  static const String createCompanyTaxSettingsTable =
      '''
    CREATE TABLE $tableCompanyTaxSettings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tax_number TEXT NOT NULL,
      vat_number TEXT,
      tax_authority TEXT NOT NULL,
      tax_period TEXT NOT NULL DEFAULT 'monthly' CHECK (tax_period IN ('monthly', 'quarterly', 'annually')),
      tax_year_start TEXT,
      tax_year_end TEXT,
      is_vat_registered INTEGER NOT NULL DEFAULT 0,
      is_withholding_agent INTEGER NOT NULL DEFAULT 0,
      default_tax_group_id INTEGER,
      additional_settings TEXT, -- JSON string
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (default_tax_group_id) REFERENCES $tableTaxGroups (id)
    )
  ''';

  static const String createProjectsTable =
      '''
    CREATE TABLE $tableProjects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL DEFAULT 'internal' CHECK (type IN ('internal', 'external', 'maintenance', 'development', 'consulting')),
      status TEXT NOT NULL DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled')),
      customer_id INTEGER,
      manager_id INTEGER,
      start_date TEXT NOT NULL,
      end_date TEXT,
      actual_end_date TEXT,
      budget_amount REAL NOT NULL DEFAULT 0.0,
      actual_cost REAL NOT NULL DEFAULT 0.0,
      billed_amount REAL NOT NULL DEFAULT 0.0,
      profit_margin REAL NOT NULL DEFAULT 0.0,
      priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
      location TEXT,
      notes TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES $tableCustomers (id)
    )
  ''';

  static const String createProjectPhasesTable =
      '''
    CREATE TABLE $tableProjectPhases (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      start_date TEXT NOT NULL,
      end_date TEXT,
      actual_end_date TEXT,
      budget_amount REAL NOT NULL DEFAULT 0.0,
      actual_cost REAL NOT NULL DEFAULT 0.0,
      weight REAL NOT NULL DEFAULT 1.0,
      completion_percentage REAL NOT NULL DEFAULT 0.0,
      status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'delayed')),
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES $tableProjects (id) ON DELETE CASCADE
    )
  ''';

  static const String createProjectCostsTable =
      '''
    CREATE TABLE $tableProjectCosts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      phase_id INTEGER,
      description TEXT NOT NULL,
      cost_type TEXT NOT NULL CHECK (cost_type IN ('labor', 'material', 'equipment', 'overhead', 'subcontractor', 'travel', 'other')),
      category TEXT NOT NULL CHECK (category IN ('direct', 'indirect', 'fixed', 'variable')),
      amount REAL NOT NULL,
      quantity REAL NOT NULL DEFAULT 1.0,
      unit_cost REAL NOT NULL DEFAULT 0.0,
      date TEXT NOT NULL,
      account_id INTEGER,
      item_id INTEGER,
      supplier_id INTEGER,
      reference TEXT,
      is_billable INTEGER NOT NULL DEFAULT 1,
      is_approved INTEGER NOT NULL DEFAULT 0,
      approved_by TEXT,
      approved_at TEXT,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES $tableProjects (id) ON DELETE CASCADE,
      FOREIGN KEY (phase_id) REFERENCES $tableProjectPhases (id) ON DELETE SET NULL,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id),
      FOREIGN KEY (item_id) REFERENCES $tableItems (id),
      FOREIGN KEY (supplier_id) REFERENCES $tableSuppliers (id)
    )
  ''';

  static const String createProjectResourcesTable =
      '''
    CREATE TABLE $tableProjectResources (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      resource_type TEXT NOT NULL CHECK (resource_type IN ('human', 'equipment', 'material', 'facility')),
      cost_per_hour REAL NOT NULL DEFAULT 0.0,
      cost_per_day REAL NOT NULL DEFAULT 0.0,
      allocated_hours REAL NOT NULL DEFAULT 0.0,
      actual_hours REAL NOT NULL DEFAULT 0.0,
      start_date TEXT NOT NULL,
      end_date TEXT,
      skills TEXT,
      notes TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES $tableProjects (id) ON DELETE CASCADE
    )
  ''';

  static const String createProjectTimeEntriesTable =
      '''
    CREATE TABLE $tableProjectTimeEntries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      phase_id INTEGER,
      task_id INTEGER,
      resource_id INTEGER NOT NULL,
      date TEXT NOT NULL,
      hours REAL NOT NULL,
      description TEXT NOT NULL,
      entry_type TEXT NOT NULL DEFAULT 'regular' CHECK (entry_type IN ('regular', 'overtime', 'holiday', 'sick')),
      is_billable INTEGER NOT NULL DEFAULT 1,
      hourly_rate REAL,
      notes TEXT,
      is_approved INTEGER NOT NULL DEFAULT 0,
      approved_by TEXT,
      approved_at TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES $tableProjects (id) ON DELETE CASCADE,
      FOREIGN KEY (phase_id) REFERENCES $tableProjectPhases (id) ON DELETE SET NULL,
      FOREIGN KEY (resource_id) REFERENCES $tableProjectResources (id) ON DELETE CASCADE
    )
  ''';

  static const String createProjectTasksTable =
      '''
    CREATE TABLE $tableProjectTasks (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      phase_id INTEGER,
      name TEXT NOT NULL,
      description TEXT,
      status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'on_hold', 'cancelled')),
      priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
      start_date TEXT,
      end_date TEXT,
      actual_end_date TEXT,
      estimated_hours REAL NOT NULL DEFAULT 0.0,
      actual_hours REAL NOT NULL DEFAULT 0.0,
      assigned_to INTEGER,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES $tableProjects (id) ON DELETE CASCADE,
      FOREIGN KEY (phase_id) REFERENCES $tableProjectPhases (id) ON DELETE SET NULL,
      FOREIGN KEY (assigned_to) REFERENCES $tableProjectResources (id) ON DELETE SET NULL
    )
  ''';

  // Indexes for better performance
  static const List<String> createIndexes = [
    'CREATE INDEX idx_accounts_code ON $tableAccounts (code)',
    'CREATE INDEX idx_accounts_parent ON $tableAccounts (parent_id)',
    'CREATE INDEX idx_journal_entries_date ON $tableJournalEntries (date)',
    'CREATE INDEX idx_journal_entries_number ON $tableJournalEntries (entry_number)',
    'CREATE INDEX idx_journal_entry_lines_entry ON $tableJournalEntryLines (journal_entry_id)',
    'CREATE INDEX idx_journal_entry_lines_account ON $tableJournalEntryLines (account_id)',
    'CREATE INDEX idx_customers_code ON $tableCustomers (code)',
    'CREATE INDEX idx_suppliers_code ON $tableSuppliers (code)',
    'CREATE INDEX idx_items_code ON $tableItems (code)',
    'CREATE INDEX idx_invoices_number ON $tableInvoices (invoice_number)',
    'CREATE INDEX idx_invoices_date ON $tableInvoices (date)',
    'CREATE INDEX idx_invoices_customer ON $tableInvoices (customer_id)',
    'CREATE INDEX idx_invoices_supplier ON $tableInvoices (supplier_id)',
    'CREATE INDEX idx_invoice_lines_invoice ON $tableInvoiceLines (invoice_id)',
    'CREATE INDEX idx_invoice_lines_item ON $tableInvoiceLines (item_id)',
    'CREATE INDEX idx_fixed_assets_code ON $tableFixedAssets (code)',
    'CREATE INDEX idx_fixed_assets_category ON $tableFixedAssets (category)',
    'CREATE INDEX idx_fixed_assets_status ON $tableFixedAssets (status)',
    'CREATE INDEX idx_asset_depreciation_asset ON $tableAssetDepreciation (asset_id)',
    'CREATE INDEX idx_asset_depreciation_date ON $tableAssetDepreciation (depreciation_date)',
    'CREATE INDEX idx_asset_depreciation_year_month ON $tableAssetDepreciation (year, month)',
    // Warehouse indexes
    'CREATE INDEX idx_warehouses_code ON $tableWarehouses (code)',
    'CREATE INDEX idx_warehouses_type ON $tableWarehouses (type)',
    'CREATE INDEX idx_warehouses_status ON $tableWarehouses (status)',
    'CREATE INDEX idx_warehouse_locations_warehouse ON $tableWarehouseLocations (warehouse_id)',
    'CREATE INDEX idx_warehouse_locations_code ON $tableWarehouseLocations (warehouse_id, code)',
    'CREATE INDEX idx_stock_movements_item ON $tableStockMovements (item_id)',
    'CREATE INDEX idx_stock_movements_warehouse ON $tableStockMovements (warehouse_id)',
    'CREATE INDEX idx_stock_movements_date ON $tableStockMovements (movement_date)',
    'CREATE INDEX idx_stock_movements_type ON $tableStockMovements (type)',
    'CREATE INDEX idx_stock_movements_status ON $tableStockMovements (status)',
    'CREATE INDEX idx_stock_movements_document ON $tableStockMovements (document_number)',
    'CREATE INDEX idx_stock_balances_item ON $tableStockBalances (item_id)',
    'CREATE INDEX idx_stock_balances_warehouse ON $tableStockBalances (warehouse_id)',
    'CREATE INDEX idx_stock_balances_location ON $tableStockBalances (location_id)',
    'CREATE INDEX idx_stock_balances_quantity ON $tableStockBalances (quantity)',
    // Currency indexes
    'CREATE INDEX idx_currencies_code ON $tableCurrencies (code)',
    'CREATE INDEX idx_currencies_active ON $tableCurrencies (is_active)',
    'CREATE INDEX idx_currencies_base ON $tableCurrencies (is_base_currency)',
    // Exchange rate indexes
    'CREATE INDEX idx_exchange_rates_from_to ON $tableExchangeRates (from_currency_code, to_currency_code)',
    'CREATE INDEX idx_exchange_rates_effective_date ON $tableExchangeRates (effective_date)',
    'CREATE INDEX idx_exchange_rates_active ON $tableExchangeRates (is_active)',
    'CREATE INDEX idx_exchange_rates_source ON $tableExchangeRates (source)',
    // Exchange rate history indexes
    'CREATE INDEX idx_exchange_rate_history_from_to ON $tableExchangeRateHistory (from_currency_code, to_currency_code)',
    'CREATE INDEX idx_exchange_rate_history_date ON $tableExchangeRateHistory (date)',
    'CREATE INDEX idx_exchange_rate_history_source ON $tableExchangeRateHistory (source)',

    // Tax table indexes
    'CREATE INDEX idx_taxes_code ON $tableTaxes (code)',
    'CREATE INDEX idx_taxes_type ON $tableTaxes (type)',
    'CREATE INDEX idx_taxes_status ON $tableTaxes (status)',
    'CREATE INDEX idx_taxes_effective_date ON $tableTaxes (effective_date)',
    'CREATE INDEX idx_taxes_expiry_date ON $tableTaxes (expiry_date)',
    'CREATE INDEX idx_tax_groups_code ON $tableTaxGroups (code)',
    'CREATE INDEX idx_tax_groups_is_default ON $tableTaxGroups (is_default)',
    'CREATE INDEX idx_tax_group_taxes_group_id ON $tableTaxGroupTaxes (tax_group_id)',
    'CREATE INDEX idx_tax_group_taxes_tax_id ON $tableTaxGroupTaxes (tax_id)',
    'CREATE INDEX idx_tax_calculations_document ON $tableTaxCalculations (document_type, document_id)',
    'CREATE INDEX idx_tax_calculations_tax_id ON $tableTaxCalculations (tax_id)',
    'CREATE INDEX idx_tax_calculations_calculated_at ON $tableTaxCalculations (calculated_at)',
    'CREATE INDEX idx_company_tax_settings_tax_number ON $tableCompanyTaxSettings (tax_number)',
    'CREATE INDEX idx_company_tax_settings_vat_number ON $tableCompanyTaxSettings (vat_number)',

    // Project table indexes
    'CREATE INDEX idx_projects_code ON $tableProjects (code)',
    'CREATE INDEX idx_projects_status ON $tableProjects (status)',
    'CREATE INDEX idx_projects_customer_id ON $tableProjects (customer_id)',
    'CREATE INDEX idx_projects_start_date ON $tableProjects (start_date)',
    'CREATE INDEX idx_projects_end_date ON $tableProjects (end_date)',
    'CREATE INDEX idx_projects_is_active ON $tableProjects (is_active)',
    'CREATE INDEX idx_project_phases_project_id ON $tableProjectPhases (project_id)',
    'CREATE INDEX idx_project_phases_status ON $tableProjectPhases (status)',
    'CREATE INDEX idx_project_costs_project_id ON $tableProjectCosts (project_id)',
    'CREATE INDEX idx_project_costs_phase_id ON $tableProjectCosts (phase_id)',
    'CREATE INDEX idx_project_costs_date ON $tableProjectCosts (date)',
    'CREATE INDEX idx_project_costs_cost_type ON $tableProjectCosts (cost_type)',
    'CREATE INDEX idx_project_costs_category ON $tableProjectCosts (category)',
    'CREATE INDEX idx_project_resources_project_id ON $tableProjectResources (project_id)',
    'CREATE INDEX idx_project_resources_type ON $tableProjectResources (resource_type)',
    'CREATE INDEX idx_project_time_entries_project_id ON $tableProjectTimeEntries (project_id)',
    'CREATE INDEX idx_project_time_entries_resource_id ON $tableProjectTimeEntries (resource_id)',
    'CREATE INDEX idx_project_time_entries_date ON $tableProjectTimeEntries (date)',
    'CREATE INDEX idx_project_tasks_project_id ON $tableProjectTasks (project_id)',
    'CREATE INDEX idx_project_tasks_phase_id ON $tableProjectTasks (phase_id)',
    'CREATE INDEX idx_project_tasks_status ON $tableProjectTasks (status)',
    'CREATE INDEX idx_project_tasks_assigned_to ON $tableProjectTasks (assigned_to)',

    // Budget indexes
    'CREATE INDEX idx_budgets_code ON $tableBudgets (code)',
    'CREATE INDEX idx_budgets_type ON $tableBudgets (type)',
    'CREATE INDEX idx_budgets_period ON $tableBudgets (period)',
    'CREATE INDEX idx_budgets_status ON $tableBudgets (status)',
    'CREATE INDEX idx_budgets_start_date ON $tableBudgets (start_date)',
    'CREATE INDEX idx_budgets_end_date ON $tableBudgets (end_date)',
    'CREATE INDEX idx_budgets_created_by ON $tableBudgets (created_by)',
    'CREATE INDEX idx_budget_lines_budget_id ON $tableBudgetLines (budget_id)',
    'CREATE INDEX idx_budget_lines_account_id ON $tableBudgetLines (account_id)',
    'CREATE INDEX idx_budget_revisions_budget_id ON $tableBudgetRevisions (budget_id)',
    'CREATE INDEX idx_budget_revisions_revision_date ON $tableBudgetRevisions (revision_date)',
  ];

  // ==================== جداول الموازنات والتخطيط المالي ====================

  /// جدول الموازنات الرئيسية
  static const String createBudgetsTable =
      '''
    CREATE TABLE $tableBudgets (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL CHECK (type IN ('operational', 'capital', 'cash', 'master', 'flexible', 'static', 'rolling')),
      period TEXT NOT NULL CHECK (period IN ('monthly', 'quarterly', 'semi_annual', 'annual', 'biennial', 'custom')),
      start_date TEXT NOT NULL,
      end_date TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'active', 'closed', 'cancelled')),
      approved_by TEXT,
      approved_at TEXT,
      created_by TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (approved_by) REFERENCES users(username),
      FOREIGN KEY (created_by) REFERENCES users(username)
    )
  ''';

  /// جدول بنود الموازنة
  static const String createBudgetLinesTable =
      '''
    CREATE TABLE $tableBudgetLines (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      budget_id INTEGER NOT NULL,
      account_id INTEGER NOT NULL,
      budget_amount REAL NOT NULL DEFAULT 0.0,
      actual_amount REAL NOT NULL DEFAULT 0.0,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (budget_id) REFERENCES $tableBudgets(id) ON DELETE CASCADE,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts(id) ON DELETE RESTRICT,
      UNIQUE(budget_id, account_id)
    )
  ''';

  /// جدول مراجعات الموازنة
  static const String createBudgetRevisionsTable =
      '''
    CREATE TABLE $tableBudgetRevisions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      budget_id INTEGER NOT NULL,
      revision_number INTEGER NOT NULL,
      description TEXT NOT NULL,
      revised_by TEXT,
      revision_date TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (budget_id) REFERENCES $tableBudgets(id) ON DELETE CASCADE,
      FOREIGN KEY (revised_by) REFERENCES users(username),
      UNIQUE(budget_id, revision_number)
    )
  ''';

  // Get all table creation scripts
  static List<String> get createTableScripts => [
    createAccountsTable,
    createJournalEntriesTable,
    createJournalEntryLinesTable,
    createCustomersTable,
    createSuppliersTable,
    createItemsTable,
    createInvoicesTable,
    createInvoiceLinesTable,
    createCompanySettingsTable,
    createFixedAssetsTable,
    createAssetDepreciationTable,
    createWarehousesTable,
    createWarehouseLocationsTable,
    createStockMovementsTable,
    createStockBalancesTable,
    createCurrenciesTable,
    createExchangeRatesTable,
    createExchangeRateHistoryTable,
    createTaxesTable,
    createTaxGroupsTable,
    createTaxGroupTaxesTable,
    createTaxCalculationsTable,
    createCompanyTaxSettingsTable,
    createProjectsTable,
    createProjectPhasesTable,
    createProjectCostsTable,
    createProjectResourcesTable,
    createBudgetsTable,
    createBudgetLinesTable,
    createBudgetRevisionsTable,
    createProjectTimeEntriesTable,
    createProjectTasksTable,
    // HR and Payroll tables
    createDepartmentsTable,
    createPositionsTable,
    createEmployeesTable,
    createAllowancesTable,
    createDeductionsTable,
    createEmployeeAllowancesTable,
    createEmployeeDeductionsTable,
    createPayrollPeriodsTable,
    createPayrollRecordsTable,
    createPayrollAllowanceDetailsTable,
    createPayrollDeductionDetailsTable,
    createAttendanceRecordsTable,
    createLeaveTypesTable,
    createLeaveRequestsTable,
    createBankAccountsTable,
    createBankTransactionsTable,
    createCashVaultsTable,
    createCashTransactionsTable,
    createCostLayersTable,
    createItemCostingSettingsTable,
    createApprovalWorkflowsTable,
    createApprovalStepsTable,
    createApprovalRequestsTable,
    createApprovalActionsTable,
    createPeriodicInventoriesTable,
    createInventoryItemsTable,
  ];

  // Default chart of accounts (Arabic)
  static const List<Map<String, dynamic>> defaultAccounts = [
    // الأصول (Assets)
    {
      'code': '1000',
      'name': 'الأصول',
      'name_en': 'Assets',
      'account_type': 'asset',
      'parent_id': null,
    },
    {
      'code': '1100',
      'name': 'الأصول المتداولة',
      'name_en': 'Current Assets',
      'account_type': 'asset',
      'parent_code': '1000',
    },
    {
      'code': '1110',
      'name': 'النقدية والبنوك',
      'name_en': 'Cash and Banks',
      'account_type': 'asset',
      'parent_code': '1100',
    },
    {
      'code': '1111',
      'name': 'الصندوق',
      'name_en': 'Cash',
      'account_type': 'asset',
      'parent_code': '1110',
    },
    {
      'code': '1112',
      'name': 'البنك الأهلي',
      'name_en': 'National Bank',
      'account_type': 'asset',
      'parent_code': '1110',
    },
    {
      'code': '1120',
      'name': 'العملاء',
      'name_en': 'Accounts Receivable',
      'account_type': 'asset',
      'parent_code': '1100',
    },
    {
      'code': '1130',
      'name': 'المخزون',
      'name_en': 'Inventory',
      'account_type': 'asset',
      'parent_code': '1100',
    },

    // الخصوم (Liabilities)
    {
      'code': '2000',
      'name': 'الخصوم',
      'name_en': 'Liabilities',
      'account_type': 'liability',
      'parent_id': null,
    },
    {
      'code': '2100',
      'name': 'الخصوم المتداولة',
      'name_en': 'Current Liabilities',
      'account_type': 'liability',
      'parent_code': '2000',
    },
    {
      'code': '2110',
      'name': 'الموردين',
      'name_en': 'Accounts Payable',
      'account_type': 'liability',
      'parent_code': '2100',
    },
    {
      'code': '2120',
      'name': 'ضريبة القيمة المضافة',
      'name_en': 'VAT Payable',
      'account_type': 'liability',
      'parent_code': '2100',
    },

    // حقوق الملكية (Equity)
    {
      'code': '3000',
      'name': 'حقوق الملكية',
      'name_en': 'Equity',
      'account_type': 'equity',
      'parent_id': null,
    },
    {
      'code': '3100',
      'name': 'رأس المال',
      'name_en': 'Capital',
      'account_type': 'equity',
      'parent_code': '3000',
    },
    {
      'code': '3200',
      'name': 'الأرباح المحتجزة',
      'name_en': 'Retained Earnings',
      'account_type': 'equity',
      'parent_code': '3000',
    },

    // الإيرادات (Revenue)
    {
      'code': '4000',
      'name': 'الإيرادات',
      'name_en': 'Revenue',
      'account_type': 'revenue',
      'parent_id': null,
    },
    {
      'code': '4100',
      'name': 'إيرادات المبيعات',
      'name_en': 'Sales Revenue',
      'account_type': 'revenue',
      'parent_code': '4000',
    },

    // المصروفات (Expenses)
    {
      'code': '5000',
      'name': 'المصروفات',
      'name_en': 'Expenses',
      'account_type': 'expense',
      'parent_id': null,
    },
    {
      'code': '5100',
      'name': 'تكلفة البضاعة المباعة',
      'name_en': 'Cost of Goods Sold',
      'account_type': 'expense',
      'parent_code': '5000',
    },
    {
      'code': '5200',
      'name': 'المصروفات التشغيلية',
      'name_en': 'Operating Expenses',
      'account_type': 'expense',
      'parent_code': '5000',
    },
    {
      'code': '5210',
      'name': 'مصروفات الإيجار',
      'name_en': 'Rent Expense',
      'account_type': 'expense',
      'parent_code': '5200',
    },
    {
      'code': '5220',
      'name': 'مصروفات الكهرباء',
      'name_en': 'Electricity Expense',
      'account_type': 'expense',
      'parent_code': '5200',
    },
    {
      'code': '5230',
      'name': 'مصروفات الرواتب',
      'name_en': 'Salaries Expense',
      'account_type': 'expense',
      'parent_code': '5200',
    },
  ];

  // Default warehouses
  static const List<Map<String, dynamic>> defaultWarehouses = [
    {
      'code': 'WH001',
      'name': 'المخزن الرئيسي',
      'description': 'المخزن الرئيسي للشركة',
      'type': 'main',
      'status': 'active',
      'is_main_warehouse': 1,
      'allow_negative_stock': 0,
      'require_approval': 0,
    },
    {
      'code': 'WH002',
      'name': 'مخزن الفرع الأول',
      'description': 'مخزن فرع المبيعات الأول',
      'type': 'branch',
      'status': 'active',
      'is_main_warehouse': 0,
      'allow_negative_stock': 0,
      'require_approval': 1,
    },
    {
      'code': 'WH003',
      'name': 'مخزن التجزئة',
      'description': 'مخزن مبيعات التجزئة',
      'type': 'retail',
      'status': 'active',
      'is_main_warehouse': 0,
      'allow_negative_stock': 0,
      'require_approval': 0,
    },
    {
      'code': 'WH004',
      'name': 'مخزن الجملة',
      'description': 'مخزن مبيعات الجملة',
      'type': 'wholesale',
      'status': 'active',
      'is_main_warehouse': 0,
      'allow_negative_stock': 0,
      'require_approval': 1,
    },
    {
      'code': 'WH005',
      'name': 'مخزن العبور',
      'description': 'مخزن مؤقت للبضائع في العبور',
      'type': 'transit',
      'status': 'active',
      'is_main_warehouse': 0,
      'allow_negative_stock': 1,
      'require_approval': 1,
    },
    {
      'code': 'WH006',
      'name': 'مخزن البضائع التالفة',
      'description': 'مخزن للبضائع التالفة والمرتجعة',
      'type': 'damaged',
      'status': 'active',
      'is_main_warehouse': 0,
      'allow_negative_stock': 0,
      'require_approval': 1,
    },
  ];

  // Default warehouse locations for main warehouse
  static const List<Map<String, dynamic>> defaultWarehouseLocations = [
    {
      'warehouse_id': 1,
      'code': 'A01-01-01',
      'name': 'المنطقة أ - ممر 1 - رف 1',
      'description': 'موقع في المنطقة الأولى',
      'zone': 'A',
      'aisle': '01',
      'rack': '01',
      'shelf': '01',
      'is_active': 1,
    },
    {
      'warehouse_id': 1,
      'code': 'A01-01-02',
      'name': 'المنطقة أ - ممر 1 - رف 2',
      'description': 'موقع في المنطقة الأولى',
      'zone': 'A',
      'aisle': '01',
      'rack': '01',
      'shelf': '02',
      'is_active': 1,
    },
    {
      'warehouse_id': 1,
      'code': 'A01-02-01',
      'name': 'المنطقة أ - ممر 2 - رف 1',
      'description': 'موقع في المنطقة الأولى',
      'zone': 'A',
      'aisle': '01',
      'rack': '02',
      'shelf': '01',
      'is_active': 1,
    },
    {
      'warehouse_id': 1,
      'code': 'B01-01-01',
      'name': 'المنطقة ب - ممر 1 - رف 1',
      'description': 'موقع في المنطقة الثانية',
      'zone': 'B',
      'aisle': '01',
      'rack': '01',
      'shelf': '01',
      'is_active': 1,
    },
    {
      'warehouse_id': 1,
      'code': 'B01-01-02',
      'name': 'المنطقة ب - ممر 1 - رف 2',
      'description': 'موقع في المنطقة الثانية',
      'zone': 'B',
      'aisle': '01',
      'rack': '01',
      'shelf': '02',
      'is_active': 1,
    },
    {
      'warehouse_id': 1,
      'code': 'C01-01-01',
      'name': 'المنطقة ج - ممر 1 - رف 1',
      'description': 'موقع في المنطقة الثالثة',
      'zone': 'C',
      'aisle': '01',
      'rack': '01',
      'shelf': '01',
      'is_active': 1,
    },
  ];

  /// جدول الأقسام
  static const String createDepartmentsTable =
      '''
    CREATE TABLE $tableDepartments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      parent_department_id INTEGER,
      manager_id TEXT,
      budget REAL,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (parent_department_id) REFERENCES $tableDepartments(id),
      FOREIGN KEY (manager_id) REFERENCES $tableEmployees(employee_code)
    )
  ''';

  /// جدول المناصب
  static const String createPositionsTable =
      '''
    CREATE TABLE $tablePositions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      title TEXT NOT NULL,
      description TEXT,
      department_id INTEGER NOT NULL,
      level TEXT NOT NULL DEFAULT 'junior' CHECK (level IN ('intern', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'executive')),
      min_salary REAL,
      max_salary REAL,
      responsibilities TEXT,
      requirements TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (department_id) REFERENCES $tableDepartments(id)
    )
  ''';

  /// جدول الموظفين
  static const String createEmployeesTable =
      '''
    CREATE TABLE $tableEmployees (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_code TEXT NOT NULL UNIQUE,
      first_name TEXT NOT NULL,
      last_name TEXT NOT NULL,
      middle_name TEXT,
      national_id TEXT UNIQUE,
      passport_number TEXT UNIQUE,
      birth_date TEXT,
      gender TEXT NOT NULL DEFAULT 'male' CHECK (gender IN ('male', 'female')),
      marital_status TEXT NOT NULL DEFAULT 'single' CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed')),
      phone TEXT,
      email TEXT,
      address TEXT,
      emergency_contact TEXT,
      emergency_phone TEXT,
      department_id INTEGER NOT NULL,
      position_id INTEGER NOT NULL,
      hire_date TEXT NOT NULL,
      termination_date TEXT,
      status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'terminated', 'suspended')),
      employee_type TEXT NOT NULL DEFAULT 'fullTime' CHECK (employee_type IN ('fullTime', 'partTime', 'contract', 'intern', 'consultant')),
      direct_manager_id TEXT,
      basic_salary REAL NOT NULL DEFAULT 0.0,
      bank_account TEXT,
      bank_name TEXT,
      iban TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      notes TEXT,
      profile_image_path TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (department_id) REFERENCES $tableDepartments(id),
      FOREIGN KEY (position_id) REFERENCES $tablePositions(id),
      FOREIGN KEY (direct_manager_id) REFERENCES $tableEmployees(employee_code)
    )
  ''';

  /// جدول البدلات
  static const String createAllowancesTable =
      '''
    CREATE TABLE $tableAllowances (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL DEFAULT 'basic' CHECK (type IN ('basic', 'housing', 'transportation', 'food', 'overtime', 'bonus', 'commission', 'other')),
      calculation_type TEXT NOT NULL DEFAULT 'fixed' CHECK (calculation_type IN ('fixed', 'percentage', 'hourly', 'daily')),
      amount REAL NOT NULL DEFAULT 0.0,
      percentage REAL,
      is_taxable INTEGER NOT NULL DEFAULT 1,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  /// جدول الاستقطاعات
  static const String createDeductionsTable =
      '''
    CREATE TABLE $tableDeductions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL DEFAULT 'tax' CHECK (type IN ('tax', 'insurance', 'loan', 'advance', 'penalty', 'other')),
      calculation_type TEXT NOT NULL DEFAULT 'fixed' CHECK (calculation_type IN ('fixed', 'percentage', 'progressive')),
      amount REAL NOT NULL DEFAULT 0.0,
      percentage REAL,
      max_amount REAL,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  /// جدول بدلات الموظفين
  static const String createEmployeeAllowancesTable =
      '''
    CREATE TABLE $tableEmployeeAllowances (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_code TEXT NOT NULL,
      allowance_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      effective_date TEXT NOT NULL,
      end_date TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_code) REFERENCES $tableEmployees(employee_code),
      FOREIGN KEY (allowance_id) REFERENCES $tableAllowances(id),
      UNIQUE(employee_code, allowance_id, effective_date)
    )
  ''';

  /// جدول استقطاعات الموظفين
  static const String createEmployeeDeductionsTable =
      '''
    CREATE TABLE $tableEmployeeDeductions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_code TEXT NOT NULL,
      deduction_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      effective_date TEXT NOT NULL,
      end_date TEXT,
      is_active INTEGER NOT NULL DEFAULT 1,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_code) REFERENCES $tableEmployees(employee_code),
      FOREIGN KEY (deduction_id) REFERENCES $tableDeductions(id),
      UNIQUE(employee_code, deduction_id, effective_date)
    )
  ''';

  /// جدول فترات الرواتب
  static const String createPayrollPeriodsTable =
      '''
    CREATE TABLE $tablePayrollPeriods (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      start_date TEXT NOT NULL,
      end_date TEXT NOT NULL,
      working_days INTEGER NOT NULL,
      status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'processing', 'processed', 'closed')),
      processed_date TEXT,
      processed_by TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(start_date, end_date)
    )
  ''';

  /// جدول سجلات الرواتب
  static const String createPayrollRecordsTable =
      '''
    CREATE TABLE $tablePayrollRecords (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_code TEXT NOT NULL,
      payroll_period_id INTEGER NOT NULL,
      payroll_date TEXT NOT NULL,
      basic_salary REAL NOT NULL DEFAULT 0.0,
      total_allowances REAL NOT NULL DEFAULT 0.0,
      total_deductions REAL NOT NULL DEFAULT 0.0,
      gross_salary REAL NOT NULL DEFAULT 0.0,
      net_salary REAL NOT NULL DEFAULT 0.0,
      tax_amount REAL NOT NULL DEFAULT 0.0,
      insurance_amount REAL NOT NULL DEFAULT 0.0,
      working_days INTEGER NOT NULL,
      actual_working_days INTEGER NOT NULL,
      overtime_hours REAL NOT NULL DEFAULT 0.0,
      overtime_amount REAL NOT NULL DEFAULT 0.0,
      status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'calculated', 'approved', 'paid', 'cancelled')),
      notes TEXT,
      paid_date TEXT,
      paid_by TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_code) REFERENCES $tableEmployees(employee_code),
      FOREIGN KEY (payroll_period_id) REFERENCES $tablePayrollPeriods(id),
      UNIQUE(employee_code, payroll_period_id)
    )
  ''';

  /// جدول تفاصيل بدلات الرواتب
  static const String createPayrollAllowanceDetailsTable =
      '''
    CREATE TABLE $tablePayrollAllowanceDetails (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      payroll_record_id INTEGER NOT NULL,
      allowance_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      notes TEXT,
      FOREIGN KEY (payroll_record_id) REFERENCES $tablePayrollRecords(id) ON DELETE CASCADE,
      FOREIGN KEY (allowance_id) REFERENCES $tableAllowances(id)
    )
  ''';

  /// جدول تفاصيل استقطاعات الرواتب
  static const String createPayrollDeductionDetailsTable =
      '''
    CREATE TABLE $tablePayrollDeductionDetails (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      payroll_record_id INTEGER NOT NULL,
      deduction_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      notes TEXT,
      FOREIGN KEY (payroll_record_id) REFERENCES $tablePayrollRecords(id) ON DELETE CASCADE,
      FOREIGN KEY (deduction_id) REFERENCES $tableDeductions(id)
    )
  ''';

  /// جدول سجلات الحضور
  static const String createAttendanceRecordsTable =
      '''
    CREATE TABLE $tableAttendanceRecords (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_code TEXT NOT NULL,
      date TEXT NOT NULL,
      check_in_time TEXT,
      check_out_time TEXT,
      status TEXT NOT NULL DEFAULT 'absent' CHECK (status IN ('present', 'absent', 'late', 'halfDay', 'leave', 'holiday', 'sick')),
      working_hours REAL,
      overtime_hours REAL,
      late_minutes REAL,
      early_leave_minutes REAL,
      notes TEXT,
      location TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_code) REFERENCES $tableEmployees(employee_code),
      UNIQUE(employee_code, date)
    )
  ''';

  /// جدول أنواع الإجازات
  static const String createLeaveTypesTable =
      '''
    CREATE TABLE $tableLeaveTypes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      max_days_per_year INTEGER NOT NULL,
      is_paid INTEGER NOT NULL DEFAULT 1,
      requires_approval INTEGER NOT NULL DEFAULT 1,
      carry_forward INTEGER NOT NULL DEFAULT 0,
      max_carry_forward_days INTEGER,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  /// جدول طلبات الإجازات
  static const String createLeaveRequestsTable =
      '''
    CREATE TABLE $tableLeaveRequests (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_code TEXT NOT NULL,
      leave_type_id INTEGER NOT NULL,
      start_date TEXT NOT NULL,
      end_date TEXT NOT NULL,
      total_days INTEGER NOT NULL,
      reason TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
      approved_by TEXT,
      approved_date TEXT,
      rejection_reason TEXT,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_code) REFERENCES $tableEmployees(employee_code),
      FOREIGN KEY (leave_type_id) REFERENCES $tableLeaveTypes(id),
      FOREIGN KEY (approved_by) REFERENCES $tableEmployees(employee_code)
    )
  ''';

  /// جدول أرصدة الإجازات
  static const String createLeaveBalancesTable =
      '''
    CREATE TABLE $tableLeaveBalances (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_code TEXT NOT NULL,
      leave_type_id INTEGER NOT NULL,
      year INTEGER NOT NULL,
      allocated_days INTEGER NOT NULL DEFAULT 0,
      used_days INTEGER NOT NULL DEFAULT 0,
      remaining_days INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_code) REFERENCES $tableEmployees(employee_code),
      FOREIGN KEY (leave_type_id) REFERENCES $tableLeaveTypes(id),
      UNIQUE(employee_code, leave_type_id, year)
    )
  ''';

  // Bank Accounts table
  static const String createBankAccountsTable =
      '''
    CREATE TABLE $tableBankAccounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      account_number TEXT NOT NULL UNIQUE,
      account_name TEXT NOT NULL,
      bank_name TEXT NOT NULL,
      bank_code TEXT NOT NULL,
      branch_name TEXT,
      branch_code TEXT,
      iban TEXT,
      swift_code TEXT,
      account_type TEXT NOT NULL DEFAULT 'current' CHECK (account_type IN ('current', 'savings', 'fixed_deposit', 'credit_card', 'loan')),
      status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'closed', 'frozen')),
      currency TEXT NOT NULL DEFAULT 'SAR',
      opening_balance REAL NOT NULL DEFAULT 0.0,
      current_balance REAL NOT NULL DEFAULT 0.0,
      credit_limit REAL,
      notes TEXT,
      account_id INTEGER,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id)
    )
  ''';

  // Bank Transactions table
  static const String createBankTransactionsTable =
      '''
    CREATE TABLE $tableBankTransactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      bank_account_id INTEGER NOT NULL,
      transaction_number TEXT NOT NULL UNIQUE,
      type TEXT NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'transfer', 'fee', 'interest', 'check', 'direct_debit', 'standing_order')),
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled', 'failed')),
      amount REAL NOT NULL,
      currency TEXT NOT NULL DEFAULT 'SAR',
      description TEXT NOT NULL,
      reference TEXT,
      check_number TEXT,
      to_bank_account_id INTEGER,
      journal_entry_id INTEGER,
      transaction_date TEXT NOT NULL,
      value_date TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (bank_account_id) REFERENCES $tableBankAccounts (id),
      FOREIGN KEY (to_bank_account_id) REFERENCES $tableBankAccounts (id),
      FOREIGN KEY (journal_entry_id) REFERENCES $tableJournalEntries (id)
    )
  ''';

  // Cash Vaults table
  static const String createCashVaultsTable =
      '''
    CREATE TABLE $tableCashVaults (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL DEFAULT 'main' CHECK (type IN ('main', 'branch', 'petty', 'foreign')),
      status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'closed')),
      currency TEXT NOT NULL DEFAULT 'SAR',
      opening_balance REAL NOT NULL DEFAULT 0.0,
      current_balance REAL NOT NULL DEFAULT 0.0,
      max_limit REAL,
      location TEXT,
      responsible_person TEXT,
      account_id INTEGER,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (account_id) REFERENCES $tableAccounts (id)
    )
  ''';

  // Cash Transactions table
  static const String createCashTransactionsTable =
      '''
    CREATE TABLE $tableCashTransactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      cash_vault_id INTEGER NOT NULL,
      transaction_number TEXT NOT NULL UNIQUE,
      type TEXT NOT NULL CHECK (type IN ('receipt', 'payment', 'transfer', 'deposit', 'withdrawal', 'exchange', 'adjustment')),
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
      amount REAL NOT NULL,
      currency TEXT NOT NULL DEFAULT 'SAR',
      description TEXT NOT NULL,
      reference TEXT,
      received_from TEXT,
      paid_to TEXT,
      to_cash_vault_id INTEGER,
      bank_account_id INTEGER,
      journal_entry_id INTEGER,
      transaction_date TEXT NOT NULL,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (cash_vault_id) REFERENCES $tableCashVaults (id),
      FOREIGN KEY (to_cash_vault_id) REFERENCES $tableCashVaults (id),
      FOREIGN KEY (bank_account_id) REFERENCES $tableBankAccounts (id),
      FOREIGN KEY (journal_entry_id) REFERENCES $tableJournalEntries (id)
    )
  ''';

  // Cost Layers table
  static const String createCostLayersTable =
      '''
    CREATE TABLE $tableCostLayers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      item_id INTEGER NOT NULL,
      warehouse_id INTEGER NOT NULL,
      location_id INTEGER,
      batch_number TEXT,
      serial_number TEXT,
      quantity REAL NOT NULL DEFAULT 0.0,
      unit_cost REAL NOT NULL DEFAULT 0.0,
      total_cost REAL NOT NULL DEFAULT 0.0,
      received_date TEXT NOT NULL,
      expiry_date TEXT,
      reference_document TEXT,
      movement_id INTEGER,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (item_id) REFERENCES $tableItems (id),
      FOREIGN KEY (warehouse_id) REFERENCES $tableWarehouses (id),
      FOREIGN KEY (location_id) REFERENCES $tableWarehouseLocations (id),
      FOREIGN KEY (movement_id) REFERENCES $tableStockMovements (id)
    )
  ''';

  // Item Costing Settings table
  static const String createItemCostingSettingsTable =
      '''
    CREATE TABLE $tableItemCostingSettings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      item_id INTEGER NOT NULL UNIQUE,
      costing_method TEXT NOT NULL DEFAULT 'weighted_average' CHECK (costing_method IN ('fifo', 'lifo', 'weighted_average', 'standard_cost', 'specific_identification')),
      standard_cost REAL,
      track_batches INTEGER NOT NULL DEFAULT 0,
      track_serial_numbers INTEGER NOT NULL DEFAULT 0,
      allow_negative_stock INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (item_id) REFERENCES $tableItems (id) ON DELETE CASCADE
    )
  ''';

  // Approval Workflows table
  static const String createApprovalWorkflowsTable =
      '''
    CREATE TABLE $tableApprovalWorkflows (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      document_type TEXT NOT NULL CHECK (document_type IN ('journal_entry', 'invoice', 'expense', 'stock_movement', 'bank_transaction', 'cash_transaction', 'purchase', 'sale')),
      min_amount REAL,
      max_amount REAL,
      is_active INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  // Approval Steps table
  static const String createApprovalStepsTable =
      '''
    CREATE TABLE $tableApprovalSteps (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      workflow_id INTEGER NOT NULL,
      step_order INTEGER NOT NULL,
      name TEXT NOT NULL,
      level TEXT NOT NULL CHECK (level IN ('level1', 'level2', 'level3', 'final')),
      approver_id INTEGER,
      role_id INTEGER,
      is_required INTEGER NOT NULL DEFAULT 1,
      timeout_hours INTEGER,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (workflow_id) REFERENCES $tableApprovalWorkflows (id) ON DELETE CASCADE,
      UNIQUE(workflow_id, step_order)
    )
  ''';

  // Approval Requests table
  static const String createApprovalRequestsTable =
      '''
    CREATE TABLE $tableApprovalRequests (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      workflow_id INTEGER NOT NULL,
      document_type TEXT NOT NULL CHECK (document_type IN ('journal_entry', 'invoice', 'expense', 'stock_movement', 'bank_transaction', 'cash_transaction', 'purchase', 'sale')),
      document_id INTEGER NOT NULL,
      document_number TEXT NOT NULL,
      amount REAL,
      description TEXT,
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
      requested_by INTEGER NOT NULL,
      requested_at TEXT NOT NULL,
      current_step_id INTEGER,
      rejection_reason TEXT,
      completed_at TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (workflow_id) REFERENCES $tableApprovalWorkflows (id),
      FOREIGN KEY (current_step_id) REFERENCES $tableApprovalSteps (id),
      UNIQUE(document_type, document_id)
    )
  ''';

  // Approval Actions table
  static const String createApprovalActionsTable =
      '''
    CREATE TABLE $tableApprovalActions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      request_id INTEGER NOT NULL,
      step_id INTEGER NOT NULL,
      action TEXT NOT NULL CHECK (action IN ('approved', 'rejected')),
      action_by INTEGER NOT NULL,
      action_at TEXT NOT NULL,
      comments TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (request_id) REFERENCES $tableApprovalRequests (id) ON DELETE CASCADE,
      FOREIGN KEY (step_id) REFERENCES $tableApprovalSteps (id)
    )
  ''';

  // Periodic Inventories table
  static const String createPeriodicInventoriesTable =
      '''
    CREATE TABLE $tablePeriodicInventories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      inventory_number TEXT NOT NULL UNIQUE,
      name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL DEFAULT 'full' CHECK (type IN ('full', 'partial', 'cycle', 'spot')),
      status TEXT NOT NULL DEFAULT 'planned' CHECK (status IN ('planned', 'in_progress', 'completed', 'cancelled')),
      warehouse_id INTEGER,
      location_id INTEGER,
      planned_date TEXT NOT NULL,
      start_date TEXT,
      end_date TEXT,
      supervisor_id INTEGER,
      team_member_ids TEXT,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (warehouse_id) REFERENCES $tableWarehouses (id),
      FOREIGN KEY (location_id) REFERENCES $tableWarehouseLocations (id)
    )
  ''';

  // Inventory Items table
  static const String createInventoryItemsTable =
      '''
    CREATE TABLE $tableInventoryItems (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      inventory_id INTEGER NOT NULL,
      item_id INTEGER NOT NULL,
      warehouse_id INTEGER NOT NULL,
      location_id INTEGER,
      batch_number TEXT,
      serial_number TEXT,
      system_quantity REAL NOT NULL DEFAULT 0.0,
      counted_quantity REAL,
      variance REAL,
      unit_cost REAL,
      total_variance_cost REAL,
      notes TEXT,
      counted_by INTEGER,
      counted_at TEXT,
      is_reconciled INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (inventory_id) REFERENCES $tablePeriodicInventories (id) ON DELETE CASCADE,
      FOREIGN KEY (item_id) REFERENCES $tableItems (id),
      FOREIGN KEY (warehouse_id) REFERENCES $tableWarehouses (id),
      FOREIGN KEY (location_id) REFERENCES $tableWarehouseLocations (id),
      UNIQUE(inventory_id, item_id, warehouse_id, location_id, batch_number, serial_number)
    )
  ''';

  // Default company settings
  static const Map<String, dynamic> defaultCompanySettings = {
    'company_name': 'شركتي',
    'company_name_en': 'My Company',
    'currency_code': 'SAR',
    'currency_symbol': 'ر.س',
    'fiscal_year_start': '01-01',
    'decimal_places': 2,
    'date_format': 'dd/MM/yyyy',
  };
}
