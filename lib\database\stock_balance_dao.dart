/// Data Access Object لأرصدة المخزون
/// Stock Balance DAO for Smart Ledger
library;

import 'package:sqflite/sqflite.dart';
import '../models/stock_movement.dart';
import '../models/stock_balance.dart';
import 'database_helper.dart';

class StockBalanceDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع أرصدة المخزون
  Future<List<StockBalance>> getAllBalances() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_balances',
      orderBy: 'warehouse_id ASC, item_id ASC',
    );

    return List.generate(maps.length, (i) {
      return StockBalance.fromMap(maps[i]);
    });
  }

  /// الحصول على أرصدة مخزن معين
  Future<List<StockBalance>> getBalancesByWarehouse(int warehouseId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_balances',
      where: 'warehouse_id = ? AND quantity > 0',
      whereArgs: [warehouseId],
      orderBy: 'item_id ASC',
    );

    return List.generate(maps.length, (i) {
      return StockBalance.fromMap(maps[i]);
    });
  }

  /// الحصول على أرصدة صنف معين
  Future<List<StockBalance>> getBalancesByItem(int itemId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_balances',
      where: 'item_id = ? AND quantity > 0',
      whereArgs: [itemId],
      orderBy: 'warehouse_id ASC',
    );

    return List.generate(maps.length, (i) {
      return StockBalance.fromMap(maps[i]);
    });
  }

  /// الحصول على رصيد صنف في مخزن
  Future<StockBalance?> getBalance(
    int itemId,
    int warehouseId, {
    int? locationId,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = 'item_id = ? AND warehouse_id = ?';
    List<dynamic> whereArgs = [itemId, warehouseId];

    if (locationId != null) {
      whereClause += ' AND location_id = ?';
      whereArgs.add(locationId);
    } else {
      whereClause += ' AND location_id IS NULL';
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'stock_balances',
      where: whereClause,
      whereArgs: whereArgs,
    );

    if (maps.isNotEmpty) {
      return StockBalance.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة أو تحديث رصيد
  Future<int> upsertBalance(StockBalance balance) async {
    final db = await _databaseHelper.database;

    final existing = await getBalance(
      balance.itemId,
      balance.warehouseId,
      locationId: balance.locationId,
    );

    if (existing != null) {
      return await db.update(
        'stock_balances',
        balance.toMap(),
        where: 'id = ?',
        whereArgs: [existing.id],
      );
    } else {
      return await db.insert('stock_balances', balance.toMap());
    }
  }

  /// تحديث رصيد بعد حركة
  Future<void> updateBalanceAfterMovement(StockMovement movement) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // تحديث رصيد المخزن المصدر
      await _updateWarehouseBalance(
        txn,
        movement.itemId,
        movement.warehouseId,
        movement.locationId,
        -movement.signedQuantity,
        movement.unitCost,
        movement.movementDate,
      );

      // تحديث رصيد المخزن المستهدف (في حالة النقل)
      if (movement.toWarehouseId != null) {
        await _updateWarehouseBalance(
          txn,
          movement.itemId,
          movement.toWarehouseId!,
          movement.toLocationId,
          movement.quantity,
          movement.unitCost,
          movement.movementDate,
        );
      }
    });
  }

  /// تحديث رصيد مخزن داخلي
  Future<void> _updateWarehouseBalance(
    Transaction txn,
    int itemId,
    int warehouseId,
    int? locationId,
    double quantityChange,
    double unitCost,
    DateTime movementDate,
  ) async {
    String whereClause = 'item_id = ? AND warehouse_id = ?';
    List<dynamic> whereArgs = [itemId, warehouseId];

    if (locationId != null) {
      whereClause += ' AND location_id = ?';
      whereArgs.add(locationId);
    } else {
      whereClause += ' AND location_id IS NULL';
    }

    final List<Map<String, dynamic>> maps = await txn.query(
      'stock_balances',
      where: whereClause,
      whereArgs: whereArgs,
    );

    if (maps.isNotEmpty) {
      // تحديث رصيد موجود
      final existing = StockBalance.fromMap(maps.first);
      final newQuantity = existing.quantity + quantityChange;

      // حساب المتوسط المرجح للتكلفة
      double newAverageCost = existing.averageCost;
      if (quantityChange > 0 && unitCost > 0) {
        final totalValue =
            (existing.quantity * existing.averageCost) +
            (quantityChange * unitCost);
        newAverageCost = newQuantity > 0 ? totalValue / newQuantity : 0;
      }

      await txn.update(
        'stock_balances',
        {
          'quantity': newQuantity,
          'average_cost': newAverageCost,
          'total_value': newQuantity * newAverageCost,
          'last_movement_date': movementDate.toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [existing.id],
      );
    } else if (quantityChange > 0) {
      // إنشاء رصيد جديد
      final newBalance = StockBalance(
        itemId: itemId,
        warehouseId: warehouseId,
        locationId: locationId,
        quantity: quantityChange,
        averageCost: unitCost,
        totalValue: quantityChange * unitCost,
        lastMovementDate: movementDate,
      );

      await txn.insert('stock_balances', newBalance.toMap());
    }
  }

  /// الحصول على إجمالي رصيد صنف في جميع المخازن
  Future<double> getTotalItemQuantity(int itemId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT SUM(quantity) as total_quantity
      FROM stock_balances
      WHERE item_id = ?
    ''',
      [itemId],
    );

    return (maps.first['total_quantity'] as num?)?.toDouble() ?? 0.0;
  }

  /// الحصول على إجمالي قيمة المخزون في مخزن
  Future<double> getTotalWarehouseValue(int warehouseId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT SUM(total_value) as total_value
      FROM stock_balances
      WHERE warehouse_id = ? AND quantity > 0
    ''',
      [warehouseId],
    );

    return (maps.first['total_value'] as num?)?.toDouble() ?? 0.0;
  }

  /// الحصول على الأصناف منخفضة المخزون
  Future<List<Map<String, dynamic>>> getLowStockItems() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT i.id, i.code, i.name, i.min_stock_level,
             SUM(sb.quantity) as current_stock
      FROM items i
      LEFT JOIN stock_balances sb ON i.id = sb.item_id
      WHERE i.min_stock_level > 0 AND i.is_active = 1
      GROUP BY i.id, i.code, i.name, i.min_stock_level
      HAVING current_stock < i.min_stock_level
      ORDER BY (current_stock / i.min_stock_level) ASC
    ''');

    return maps;
  }

  /// الحصول على الأصناف منتهية الصلاحية
  Future<List<StockBalance>> getExpiredItems({int? daysFromNow}) async {
    final db = await _databaseHelper.database;

    DateTime expiryDate = DateTime.now();
    if (daysFromNow != null) {
      expiryDate = expiryDate.add(Duration(days: daysFromNow));
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'stock_balances',
      where: 'expiry_date IS NOT NULL AND expiry_date <= ? AND quantity > 0',
      whereArgs: [expiryDate.toIso8601String()],
      orderBy: 'expiry_date ASC',
    );

    return List.generate(maps.length, (i) {
      return StockBalance.fromMap(maps[i]);
    });
  }

  /// الحصول على تقرير أعمار المخزون
  Future<List<Map<String, dynamic>>> getStockAgeReport() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT sb.*, i.name as item_name, i.code as item_code,
             w.name as warehouse_name, w.code as warehouse_code,
             julianday('now') - julianday(sb.last_movement_date) as age_days
      FROM stock_balances sb
      INNER JOIN items i ON sb.item_id = i.id
      INNER JOIN warehouses w ON sb.warehouse_id = w.id
      WHERE sb.quantity > 0
      ORDER BY age_days DESC
    ''');

    return maps;
  }

  /// الحصول على إحصائيات المخزون
  Future<Map<String, dynamic>> getStockStatistics() async {
    final db = await _databaseHelper.database;

    // إجمالي قيمة المخزون
    final valueResult = await db.rawQuery('''
      SELECT SUM(total_value) as total_value
      FROM stock_balances
      WHERE quantity > 0
    ''');
    final totalValue =
        (valueResult.first['total_value'] as num?)?.toDouble() ?? 0.0;

    // عدد الأصناف في المخزون
    final itemsResult = await db.rawQuery('''
      SELECT COUNT(DISTINCT item_id) as total_items
      FROM stock_balances
      WHERE quantity > 0
    ''');
    final totalItems = itemsResult.first['total_items'] as int;

    // عدد المخازن المستخدمة
    final warehousesResult = await db.rawQuery('''
      SELECT COUNT(DISTINCT warehouse_id) as total_warehouses
      FROM stock_balances
      WHERE quantity > 0
    ''');
    final totalWarehouses = warehousesResult.first['total_warehouses'] as int;

    // الأصناف منخفضة المخزون
    final lowStockResult = await db.rawQuery('''
      SELECT COUNT(*) as low_stock_count
      FROM (
        SELECT i.id
        FROM items i
        LEFT JOIN stock_balances sb ON i.id = sb.item_id
        WHERE i.min_stock_level > 0 AND i.is_active = 1
        GROUP BY i.id, i.min_stock_level
        HAVING SUM(COALESCE(sb.quantity, 0)) < i.min_stock_level
      )
    ''');
    final lowStockCount = lowStockResult.first['low_stock_count'] as int;

    return {
      'total_value': totalValue,
      'total_items': totalItems,
      'total_warehouses': totalWarehouses,
      'low_stock_count': lowStockCount,
    };
  }

  /// حذف الأرصدة الصفرية
  Future<int> deleteZeroBalances() async {
    final db = await _databaseHelper.database;
    return await db.delete('stock_balances', where: 'quantity <= 0');
  }

  /// إعادة حساب جميع الأرصدة
  Future<void> recalculateAllBalances() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // حذف جميع الأرصدة الحالية
      await txn.delete('stock_balances');

      // إعادة حساب الأرصدة من الحركات
      await txn.execute('''
        INSERT INTO stock_balances (
          item_id, warehouse_id, location_id, quantity, 
          average_cost, total_value, last_movement_date, updated_at
        )
        SELECT 
          item_id,
          warehouse_id,
          location_id,
          SUM(CASE 
            WHEN type IN ('receipt', 'return_', 'found') THEN quantity
            WHEN type IN ('issue', 'damage', 'loss') THEN -quantity
            ELSE 0
          END) as quantity,
          AVG(unit_cost) as average_cost,
          SUM(CASE 
            WHEN type IN ('receipt', 'return_', 'found') THEN total_cost
            WHEN type IN ('issue', 'damage', 'loss') THEN -total_cost
            ELSE 0
          END) as total_value,
          MAX(movement_date) as last_movement_date,
          datetime('now') as updated_at
        FROM stock_movements
        WHERE status = 'completed'
        GROUP BY item_id, warehouse_id, location_id
        HAVING quantity > 0
      ''');
    });
  }
}
