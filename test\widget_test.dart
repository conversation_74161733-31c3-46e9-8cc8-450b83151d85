// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:smart_ledger/main.dart';
import 'package:smart_ledger/providers/language_provider.dart';

void main() {
  testWidgets('Smart Ledger app smoke test', (WidgetTester tester) async {
    // Create a language provider for testing
    final languageProvider = LanguageProvider();

    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ChangeNotifierProvider<LanguageProvider>(
        create: (_) => languageProvider,
        child: SmartLedgerApp(languageProvider: languageProvider),
      ),
    );

    // Verify that the app loads without errors
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
