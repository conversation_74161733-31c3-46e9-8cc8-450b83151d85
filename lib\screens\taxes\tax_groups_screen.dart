/// شاشة مجموعات الضرائب
/// Tax Groups Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/tax.dart';
import '../../services/tax_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';

class TaxGroupsScreen extends StatefulWidget {
  const TaxGroupsScreen({super.key});

  @override
  State<TaxGroupsScreen> createState() => _TaxGroupsScreenState();
}

class _TaxGroupsScreenState extends State<TaxGroupsScreen> {
  final TaxService _taxService = TaxService();
  final TextEditingController _searchController = TextEditingController();

  List<TaxGroup> _taxGroups = [];
  List<TaxGroup> _filteredTaxGroups = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadTaxGroups();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTaxGroups() async {
    setState(() => _isLoading = true);

    try {
      final result = await _taxService.getAllTaxGroups();
      if (result.isSuccess) {
        setState(() {
          _taxGroups = result.data!;
          _applyFilters();
        });
      } else {
        _showError(result.error!);
      }
    } catch (e) {
      _showError('خطأ في تحميل مجموعات الضرائب: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _applyFilters() {
    List<TaxGroup> filtered = _taxGroups;

    // تطبيق البحث النصي
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      filtered = filtered
          .where(
            (group) =>
                group.nameAr.toLowerCase().contains(query) ||
                group.nameEn.toLowerCase().contains(query) ||
                group.code.toLowerCase().contains(query),
          )
          .toList();
    }

    setState(() {
      _filteredTaxGroups = filtered;
    });
  }

  void _showError(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  Future<void> _deleteTaxGroup(TaxGroup group) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مجموعة الضرائب "${group.nameAr}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      final result = await _taxService.deleteTaxGroup(group.id!);
      if (mounted) {
        if (result.isSuccess) {
          _loadTaxGroups();
          scaffoldMessenger.showSnackBar(
            const SnackBar(content: Text('تم حذف مجموعة الضرائب بنجاح')),
          );
        } else {
          _showError(result.error!);
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: _isLoading,
      child: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _searchController,
                    labelText: 'البحث في مجموعات الضرائب',
                    prefixIcon: Icons.search,
                    onChanged: (_) => _applyFilters(),
                  ),
                ),
                const SizedBox(width: 16),
                QuantumButton(
                  onPressed: _showAddTaxGroupDialog,
                  text: 'إضافة مجموعة',
                  icon: Icons.add,
                ),
              ],
            ),
          ),

          // قائمة مجموعات الضرائب
          Expanded(
            child: _filteredTaxGroups.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.group_work_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد مجموعات ضرائب',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'اضغط على "إضافة مجموعة" لإنشاء مجموعة جديدة',
                          style: TextStyle(color: Colors.grey[500]),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredTaxGroups.length,
                    itemBuilder: (context, index) {
                      final group = _filteredTaxGroups[index];
                      return _buildTaxGroupCard(group);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxGroupCard(TaxGroup group) {
    return QuantumCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: group.isDefault
              ? Colors.blue[100]
              : Colors.grey[100],
          child: Icon(
            Icons.group_work,
            color: group.isDefault ? Colors.blue[700] : Colors.grey[700],
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                group.nameAr,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            if (group.isDefault)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'افتراضي',
                  style: TextStyle(color: Colors.blue[700], fontSize: 12),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرمز: ${group.code}'),
            Text('عدد الضرائب: ${group.taxes.length}'),
            if (group.description != null && group.description!.isNotEmpty)
              Text(
                group.description!,
                style: TextStyle(color: Colors.grey[600]),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditTaxGroupDialog(group);
                break;
              case 'delete':
                _deleteTaxGroup(group);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        children: [
          if (group.taxes.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الضرائب المشمولة:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  ...group.taxes.map(
                    (tax) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          Icon(Icons.arrow_right, color: Colors.grey[600]),
                          const SizedBox(width: 8),
                          Expanded(child: Text('${tax.nameAr} (${tax.rate}%)')),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: tax.status == TaxStatus.active
                                  ? Colors.green[100]
                                  : Colors.orange[100],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              tax.status == TaxStatus.active
                                  ? 'نشط'
                                  : 'غير نشط',
                              style: TextStyle(
                                color: tax.status == TaxStatus.active
                                    ? Colors.green[700]
                                    : Colors.orange[700],
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'لا توجد ضرائب في هذه المجموعة',
                style: TextStyle(color: Colors.grey),
              ),
            ),
        ],
      ),
    );
  }

  void _showAddTaxGroupDialog() {
    showDialog(
      context: context,
      builder: (context) => _TaxGroupDialog(
        onSave: (group) async {
          final scaffoldMessenger = ScaffoldMessenger.of(this.context);
          final result = await _taxService.createTaxGroup(group);
          if (result.isSuccess) {
            _loadTaxGroups();
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(content: Text('تم إضافة مجموعة الضرائب بنجاح')),
              );
            }
          } else {
            if (mounted) {
              _showError(result.error!);
            }
          }
        },
      ),
    );
  }

  void _showEditTaxGroupDialog(TaxGroup group) {
    showDialog(
      context: context,
      builder: (context) => _TaxGroupDialog(
        group: group,
        onSave: (updatedGroup) async {
          final scaffoldMessenger = ScaffoldMessenger.of(this.context);
          final result = await _taxService.updateTaxGroup(updatedGroup);
          if (result.isSuccess) {
            _loadTaxGroups();
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(content: Text('تم تحديث مجموعة الضرائب بنجاح')),
              );
            }
          } else {
            if (mounted) {
              _showError(result.error!);
            }
          }
        },
      ),
    );
  }
}

class _TaxGroupDialog extends StatefulWidget {
  final TaxGroup? group;
  final Function(TaxGroup) onSave;

  const _TaxGroupDialog({this.group, required this.onSave});

  @override
  State<_TaxGroupDialog> createState() => _TaxGroupDialogState();
}

class _TaxGroupDialogState extends State<_TaxGroupDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _nameArController = TextEditingController();
  final TextEditingController _nameEnController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  bool _isDefault = false;
  bool get _isEditing => widget.group != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      final group = widget.group!;
      _codeController.text = group.code;
      _nameArController.text = group.nameAr;
      _nameEnController.text = group.nameEn;
      _descriptionController.text = group.description ?? '';
      _isDefault = group.isDefault;
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameArController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _save() {
    if (!_formKey.currentState!.validate()) return;

    final group = TaxGroup(
      id: _isEditing ? widget.group!.id : null,
      code: _codeController.text.trim(),
      nameAr: _nameArController.text.trim(),
      nameEn: _nameEnController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      isDefault: _isDefault,
      createdAt: _isEditing ? widget.group!.createdAt : DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Copy existing taxes if editing
    if (_isEditing && widget.group!.taxes.isNotEmpty) {
      group.taxes = List.from(widget.group!.taxes);
    }

    widget.onSave(group);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_isEditing ? 'تعديل مجموعة الضرائب' : 'إضافة مجموعة ضرائب'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              QuantumTextField(
                controller: _codeController,
                labelText: 'رمز المجموعة *',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'رمز المجموعة مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              QuantumTextField(
                controller: _nameArController,
                labelText: 'الاسم بالعربية *',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الاسم بالعربية مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              QuantumTextField(
                controller: _nameEnController,
                labelText: 'الاسم بالإنجليزية',
              ),
              const SizedBox(height: 16),
              QuantumTextField(
                controller: _descriptionController,
                labelText: 'الوصف',
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('مجموعة افتراضية'),
                subtitle: const Text('ستستخدم تلقائياً للفواتير الجديدة'),
                value: _isDefault,
                onChanged: (value) => setState(() => _isDefault = value!),
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        TextButton(onPressed: _save, child: Text(_isEditing ? 'تحديث' : 'حفظ')),
      ],
    );
  }
}
