import 'dart:convert';
import 'package:http/http.dart' as http;
import '../database/exchange_rate_dao.dart';
import '../models/exchange_rate.dart';
import '../utils/result.dart';
import 'currency_service.dart';

/// خدمة إدارة أسعار الصرف
class ExchangeRateService {
  final ExchangeRateDao _exchangeRateDao = ExchangeRateDao();
  final CurrencyService _currencyService = CurrencyService();

  // API configuration
  static const String _apiBaseUrl =
      'https://api.exchangerate-api.com/v4/latest';
  static const String _fallbackApiUrl = 'https://api.fixer.io/latest';
  static const Duration _apiTimeout = Duration(seconds: 30);

  /// إضافة سعر صرف جديد
  Future<Result<ExchangeRate>> addExchangeRate(
    ExchangeRate exchangeRate,
  ) async {
    try {
      // التحقق من وجود العملات
      final fromCurrency = await _currencyService.getCurrencyByCode(
        exchangeRate.fromCurrencyCode,
      );
      final toCurrency = await _currencyService.getCurrencyByCode(
        exchangeRate.toCurrencyCode,
      );

      if (fromCurrency == null) {
        return Result.error(
          'العملة المصدر ${exchangeRate.fromCurrencyCode} غير موجودة',
        );
      }

      if (toCurrency == null) {
        return Result.error(
          'العملة المستهدفة ${exchangeRate.toCurrencyCode} غير موجودة',
        );
      }

      if (exchangeRate.fromCurrencyCode == exchangeRate.toCurrencyCode) {
        return Result.error('لا يمكن إنشاء سعر صرف للعملة نفسها');
      }

      if (exchangeRate.rate <= 0) {
        return Result.error('سعر الصرف يجب أن يكون أكبر من الصفر');
      }

      // إدراج سعر الصرف
      final result = await _exchangeRateDao.insertExchangeRate(exchangeRate);
      if (result.isSuccess) {
        final newExchangeRate = exchangeRate.copyWith(id: result.data);

        // إضافة سجل في التاريخ
        await _addToHistory(newExchangeRate);

        return Result.success(newExchangeRate);
      } else {
        return Result.error(result.error!);
      }
    } catch (e) {
      return Result.error('خطأ في إضافة سعر الصرف: ${e.toString()}');
    }
  }

  /// تحديث سعر صرف
  Future<Result<ExchangeRate>> updateExchangeRate(
    ExchangeRate exchangeRate,
  ) async {
    try {
      if (exchangeRate.rate <= 0) {
        return Result.error('سعر الصرف يجب أن يكون أكبر من الصفر');
      }

      final result = await _exchangeRateDao.updateExchangeRate(exchangeRate);
      if (result.isSuccess) {
        // إضافة سجل في التاريخ
        await _addToHistory(exchangeRate);

        return Result.success(exchangeRate);
      } else {
        return Result.error(result.error!);
      }
    } catch (e) {
      return Result.error('خطأ في تحديث سعر الصرف: ${e.toString()}');
    }
  }

  /// حذف سعر صرف
  Future<Result<bool>> deleteExchangeRate(int id) async {
    try {
      return await _exchangeRateDao.deleteExchangeRate(id);
    } catch (e) {
      return Result.error('خطأ في حذف سعر الصرف: ${e.toString()}');
    }
  }

  /// الحصول على سعر صرف بالمعرف
  Future<ExchangeRate?> getExchangeRateById(int id) async {
    return await _exchangeRateDao.getExchangeRateById(id);
  }

  /// الحصول على أحدث سعر صرف
  Future<ExchangeRate?> getLatestExchangeRate(
    String fromCurrency,
    String toCurrency, {
    DateTime? asOfDate,
  }) async {
    return await _exchangeRateDao.getLatestExchangeRate(
      fromCurrency,
      toCurrency,
      asOfDate: asOfDate,
    );
  }

  /// الحصول على جميع أسعار الصرف
  Future<List<ExchangeRate>> getAllExchangeRates() async {
    return await _exchangeRateDao.getAllExchangeRates();
  }

  /// الحصول على أسعار الصرف النشطة
  Future<List<ExchangeRate>> getActiveExchangeRates() async {
    return await _exchangeRateDao.getActiveExchangeRates();
  }

  /// الحصول على أسعار الصرف لعملة معينة
  Future<List<ExchangeRate>> getExchangeRatesForCurrency(
    String currencyCode,
  ) async {
    return await _exchangeRateDao.getExchangeRatesForCurrency(currencyCode);
  }

  /// البحث في أسعار الصرف
  Future<List<ExchangeRate>> searchExchangeRates(String query) async {
    return await _exchangeRateDao.searchExchangeRates(query);
  }

  /// تفعيل/إلغاء تفعيل سعر صرف
  Future<Result<bool>> toggleExchangeRateStatus(int id, bool isActive) async {
    return await _exchangeRateDao.toggleExchangeRateStatus(id, isActive);
  }

  /// الحصول على تاريخ أسعار الصرف
  Future<List<ExchangeRateHistory>> getExchangeRateHistory(
    String fromCurrency,
    String toCurrency, {
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    return await _exchangeRateDao.getExchangeRateHistory(
      fromCurrency,
      toCurrency,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
    );
  }

  /// إنشاء أسعار صرف متبادلة
  Future<Result<List<ExchangeRate>>> createCrossRates(
    String baseCurrency,
    Map<String, double> rates, {
    DateTime? effectiveDate,
    ExchangeRateSource source = ExchangeRateSource.manual,
  }) async {
    try {
      final List<ExchangeRate> createdRates = [];
      final date = effectiveDate ?? DateTime.now();

      for (final entry in rates.entries) {
        final currencyCode = entry.key;
        final rate = entry.value;

        if (currencyCode != baseCurrency && rate > 0) {
          // إنشاء سعر الصرف من العملة الأساسية إلى العملة الأخرى
          final exchangeRate = ExchangeRate(
            fromCurrencyCode: baseCurrency,
            toCurrencyCode: currencyCode,
            rate: rate,
            effectiveDate: date,
            source: source,
          );

          final result = await addExchangeRate(exchangeRate);
          if (result.isSuccess) {
            createdRates.add(result.data!);
          }

          // إنشاء سعر الصرف العكسي
          final reverseRate = ExchangeRate(
            fromCurrencyCode: currencyCode,
            toCurrencyCode: baseCurrency,
            rate: 1.0 / rate,
            effectiveDate: date,
            source: source,
          );

          final reverseResult = await addExchangeRate(reverseRate);
          if (reverseResult.isSuccess) {
            createdRates.add(reverseResult.data!);
          }
        }
      }

      return Result.success(createdRates);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء أسعار الصرف المتبادلة: ${e.toString()}',
      );
    }
  }

  /// تحديث أسعار الصرف بشكل دوري
  Future<Result<bool>> updateExchangeRatesFromAPI({
    String baseCurrency = 'USD',
    List<String>? targetCurrencies,
  }) async {
    try {
      // جلب أسعار الصرف من API
      final apiResult = await _fetchRatesFromAPI(baseCurrency);
      if (!apiResult.isSuccess) {
        return Result.error(apiResult.error!);
      }

      final apiRates = apiResult.data!;
      int updatedCount = 0;

      // تحديث أسعار الصرف في قاعدة البيانات
      for (final entry in apiRates.entries) {
        final toCurrency = entry.key;
        final rate = entry.value;

        // تصفية العملات المطلوبة إذا تم تحديدها
        if (targetCurrencies != null &&
            !targetCurrencies.contains(toCurrency)) {
          continue;
        }

        // التحقق من صحة سعر الصرف
        if (!isValidExchangeRate(rate)) {
          continue;
        }

        // إنشاء سعر صرف جديد
        final exchangeRate = ExchangeRate(
          fromCurrencyCode: baseCurrency,
          toCurrencyCode: toCurrency,
          rate: rate,
          effectiveDate: DateTime.now(),
          source: ExchangeRateSource.api,
          notes: 'تم التحديث تلقائياً من API',
        );

        // إضافة أو تحديث سعر الصرف
        final result = await _addOrUpdateExchangeRate(exchangeRate);
        if (result.isSuccess) {
          updatedCount++;
        }
      }

      // إرجاع نجاح العملية بناءً على عدد الأسعار المحدثة
      return Result.success(updatedCount > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث أسعار الصرف من API: ${e.toString()}');
    }
  }

  /// حساب أسعار الصرف المتقاطعة
  Future<Result<double>> calculateCrossRate(
    String fromCurrency,
    String toCurrency,
    String baseCurrency, {
    DateTime? asOfDate,
  }) async {
    try {
      // الحصول على سعر الصرف من العملة المصدر إلى العملة الأساسية
      final fromToBase = await getLatestExchangeRate(
        fromCurrency,
        baseCurrency,
        asOfDate: asOfDate,
      );

      // الحصول على سعر الصرف من العملة الأساسية إلى العملة المستهدفة
      final baseToTarget = await getLatestExchangeRate(
        baseCurrency,
        toCurrency,
        asOfDate: asOfDate,
      );

      if (fromToBase != null && baseToTarget != null) {
        final crossRate = fromToBase.rate * baseToTarget.rate;
        return Result.success(crossRate);
      }

      return Result.error('لا يمكن حساب سعر الصرف المتقاطع');
    } catch (e) {
      return Result.error('خطأ في حساب سعر الصرف المتقاطع: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات أسعار الصرف
  Future<Map<String, dynamic>> getExchangeRateStats() async {
    return await _exchangeRateDao.getExchangeRateStats();
  }

  /// إضافة سجل في تاريخ أسعار الصرف
  Future<void> _addToHistory(ExchangeRate exchangeRate) async {
    try {
      final history = ExchangeRateHistory(
        fromCurrencyCode: exchangeRate.fromCurrencyCode,
        toCurrencyCode: exchangeRate.toCurrencyCode,
        rate: exchangeRate.rate,
        date: exchangeRate.effectiveDate,
        source: exchangeRate.source,
        notes: exchangeRate.notes,
      );

      await _exchangeRateDao.insertExchangeRateHistory(history);
    } catch (e) {
      // تجاهل الأخطاء في التاريخ
    }
  }

  /// التحقق من صحة سعر الصرف
  bool isValidExchangeRate(double rate) {
    return rate > 0 && rate.isFinite;
  }

  /// الحصول على أزواج العملات المتاحة
  Future<List<String>> getAvailableCurrencyPairs() async {
    try {
      final rates = await getActiveExchangeRates();
      final pairs = rates.map((rate) => rate.currencyPair).toSet().toList();
      pairs.sort();
      return pairs;
    } catch (e) {
      return [];
    }
  }

  /// جلب أسعار الصرف من API خارجي
  Future<Result<Map<String, double>>> _fetchRatesFromAPI(
    String baseCurrency,
  ) async {
    try {
      // محاولة استخدام API الأساسي
      final primaryResult = await _fetchFromPrimaryAPI(baseCurrency);
      if (primaryResult.isSuccess) {
        return primaryResult;
      }

      // في حالة فشل API الأساسي، استخدام API احتياطي
      final fallbackResult = await _fetchFromFallbackAPI(baseCurrency);
      if (fallbackResult.isSuccess) {
        return fallbackResult;
      }

      return Result.error('فشل في جلب أسعار الصرف من جميع مصادر API');
    } catch (e) {
      return Result.error('خطأ في الاتصال بـ API: ${e.toString()}');
    }
  }

  /// جلب أسعار الصرف من API الأساسي
  Future<Result<Map<String, double>>> _fetchFromPrimaryAPI(
    String baseCurrency,
  ) async {
    try {
      final url = '$_apiBaseUrl/$baseCurrency';
      final response = await http
          .get(Uri.parse(url), headers: {'Accept': 'application/json'})
          .timeout(_apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        final rates = data['rates'] as Map<String, dynamic>;

        final convertedRates = <String, double>{};
        for (final entry in rates.entries) {
          final rate = entry.value;
          if (rate is num) {
            convertedRates[entry.key] = rate.toDouble();
          }
        }

        return Result.success(convertedRates);
      } else {
        return Result.error('خطأ في API: ${response.statusCode}');
      }
    } catch (e) {
      return Result.error('خطأ في الاتصال بـ API الأساسي: ${e.toString()}');
    }
  }

  /// جلب أسعار الصرف من API احتياطي
  Future<Result<Map<String, double>>> _fetchFromFallbackAPI(
    String baseCurrency,
  ) async {
    try {
      final url = '$_fallbackApiUrl?base=$baseCurrency';
      final response = await http
          .get(Uri.parse(url), headers: {'Accept': 'application/json'})
          .timeout(_apiTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        final rates = data['rates'] as Map<String, dynamic>;

        final convertedRates = <String, double>{};
        for (final entry in rates.entries) {
          final rate = entry.value;
          if (rate is num) {
            convertedRates[entry.key] = rate.toDouble();
          }
        }

        return Result.success(convertedRates);
      } else {
        return Result.error('خطأ في API الاحتياطي: ${response.statusCode}');
      }
    } catch (e) {
      return Result.error('خطأ في الاتصال بـ API الاحتياطي: ${e.toString()}');
    }
  }

  /// إضافة أو تحديث سعر صرف
  Future<Result<ExchangeRate>> _addOrUpdateExchangeRate(
    ExchangeRate exchangeRate,
  ) async {
    try {
      // البحث عن سعر صرف موجود
      final existingRate = await getLatestExchangeRate(
        exchangeRate.fromCurrencyCode,
        exchangeRate.toCurrencyCode,
      );

      if (existingRate != null) {
        // تحديث سعر الصرف الموجود
        final updatedRate = existingRate.copyWith(
          rate: exchangeRate.rate,
          effectiveDate: exchangeRate.effectiveDate,
          source: exchangeRate.source,
          notes: exchangeRate.notes,
          updatedAt: DateTime.now(),
        );
        return await updateExchangeRate(updatedRate);
      } else {
        // إضافة سعر صرف جديد
        return await addExchangeRate(exchangeRate);
      }
    } catch (e) {
      return Result.error('خطأ في إضافة/تحديث سعر الصرف: ${e.toString()}');
    }
  }

  /// جلب سعر صرف محدد من API
  Future<Result<double>> fetchSpecificRateFromAPI(
    String fromCurrency,
    String toCurrency,
  ) async {
    try {
      final apiResult = await _fetchRatesFromAPI(fromCurrency);
      if (!apiResult.isSuccess) {
        return Result.error(apiResult.error!);
      }

      final rates = apiResult.data!;
      if (rates.containsKey(toCurrency)) {
        return Result.success(rates[toCurrency]!);
      } else {
        return Result.error('العملة $toCurrency غير متوفرة في API');
      }
    } catch (e) {
      return Result.error('خطأ في جلب سعر الصرف: ${e.toString()}');
    }
  }

  /// تحديث أسعار الصرف لعملات محددة
  Future<Result<List<ExchangeRate>>> updateSpecificCurrencyRates(
    String baseCurrency,
    List<String> targetCurrencies,
  ) async {
    try {
      final updatedRates = <ExchangeRate>[];

      for (final targetCurrency in targetCurrencies) {
        final rateResult = await fetchSpecificRateFromAPI(
          baseCurrency,
          targetCurrency,
        );
        if (rateResult.isSuccess) {
          final exchangeRate = ExchangeRate(
            fromCurrencyCode: baseCurrency,
            toCurrencyCode: targetCurrency,
            rate: rateResult.data!,
            effectiveDate: DateTime.now(),
            source: ExchangeRateSource.api,
            notes: 'تم التحديث من API',
          );

          final addResult = await _addOrUpdateExchangeRate(exchangeRate);
          if (addResult.isSuccess) {
            updatedRates.add(addResult.data!);
          }
        }
      }

      return Result.success(updatedRates);
    } catch (e) {
      return Result.error(
        'خطأ في تحديث أسعار العملات المحددة: ${e.toString()}',
      );
    }
  }

  /// التحقق من توفر الاتصال بـ API
  Future<Result<bool>> checkAPIConnectivity() async {
    try {
      final testResult = await _fetchFromPrimaryAPI('USD');
      if (testResult.isSuccess) {
        return Result.success(true);
      }

      final fallbackResult = await _fetchFromFallbackAPI('USD');
      return Result.success(fallbackResult.isSuccess);
    } catch (e) {
      return Result.success(false);
    }
  }

  /// الحصول على العملات المدعومة من API
  Future<Result<List<String>>> getSupportedCurrenciesFromAPI() async {
    try {
      final apiResult = await _fetchRatesFromAPI('USD');
      if (!apiResult.isSuccess) {
        return Result.error(apiResult.error!);
      }

      final rates = apiResult.data!;
      final currencies = rates.keys.toList();
      currencies.add('USD'); // إضافة العملة الأساسية
      currencies.sort();

      return Result.success(currencies);
    } catch (e) {
      return Result.error('خطأ في جلب العملات المدعومة: ${e.toString()}');
    }
  }
}
