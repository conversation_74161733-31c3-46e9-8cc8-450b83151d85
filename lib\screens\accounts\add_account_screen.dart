import 'package:flutter/material.dart';
import '../../models/account.dart';
import '../../services/account_service.dart';

class AddAccountScreen extends StatefulWidget {
  final Account? account; // For editing existing account

  const AddAccountScreen({super.key, this.account});

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  final _nameController = TextEditingController();
  final _nameEnController = TextEditingController();
  final _descriptionController = TextEditingController();

  final AccountService _accountService = AccountService();

  AccountType _selectedType = AccountType.asset;
  Account? _selectedParent;
  List<Account> _availableParents = [];
  bool _isLoading = false;
  bool _isLoadingParents = true;

  bool get _isEditing => widget.account != null;

  @override
  void initState() {
    super.initState();
    _loadAvailableParents();

    if (_isEditing) {
      _populateFields();
    }
  }

  void _populateFields() {
    final account = widget.account!;
    _codeController.text = account.code;
    _nameController.text = account.name;
    _nameEnController.text = account.nameEn ?? '';
    _descriptionController.text = account.description ?? '';
    _selectedType = account.accountType;
    // Parent will be set after loading available parents
  }

  Future<void> _loadAvailableParents() async {
    setState(() => _isLoadingParents = true);

    try {
      final result = await _accountService.getAllAccounts();
      if (result.isSuccess) {
        setState(() {
          _availableParents = result.data!;

          // If editing, find and set the parent account
          if (_isEditing && widget.account!.parentId != null) {
            _selectedParent = _availableParents.firstWhere(
              (account) => account.id == widget.account!.parentId,
              orElse: () => _availableParents.first,
            );
          }

          _isLoadingParents = false;
        });
      } else {
        setState(() => _isLoadingParents = false);
        _showErrorSnackBar('خطأ في تحميل الحسابات: ${result.error}');
      }
    } catch (e) {
      setState(() => _isLoadingParents = false);
      _showErrorSnackBar('خطأ في تحميل الحسابات: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل الحساب' : 'إضافة حساب جديد'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveAccount,
              child: Text(_isEditing ? 'حفظ' : 'إضافة'),
            ),
        ],
      ),
      body: _isLoadingParents
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Account Type Selection
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'نوع الحساب',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            DropdownButtonFormField<AccountType>(
                              value: _selectedType,
                              decoration: const InputDecoration(
                                labelText: 'نوع الحساب',
                                border: OutlineInputBorder(),
                              ),
                              items: AccountType.values.map((type) {
                                return DropdownMenuItem(
                                  value: type,
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 12,
                                        height: 12,
                                        decoration: BoxDecoration(
                                          color: _getAccountTypeColor(type),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(type.displayName),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedType = value!;
                                  _selectedParent =
                                      null; // Reset parent when type changes
                                });
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'يرجى اختيار نوع الحساب';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Parent Account Selection
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'الحساب الأب (اختياري)',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            DropdownButtonFormField<Account?>(
                              value: _selectedParent,
                              decoration: const InputDecoration(
                                labelText: 'الحساب الأب',
                                hintText: 'اختر الحساب الأب (اختياري)',
                                border: OutlineInputBorder(),
                              ),
                              items: [
                                const DropdownMenuItem<Account?>(
                                  value: null,
                                  child: Text('لا يوجد (حساب رئيسي)'),
                                ),
                                ..._getFilteredParents().map((account) {
                                  return DropdownMenuItem(
                                    value: account,
                                    child: Text(
                                      '${account.code} - ${account.name}',
                                    ),
                                  );
                                }),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedParent = value;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Account Details
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'بيانات الحساب',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Account Code
                            TextFormField(
                              controller: _codeController,
                              decoration: const InputDecoration(
                                labelText: 'رمز الحساب *',
                                hintText: 'مثال: 1.1.1',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.tag),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'رمز الحساب مطلوب';
                                }
                                if (!RegExp(r'^[0-9.]+$').hasMatch(value)) {
                                  return 'رمز الحساب يجب أن يحتوي على أرقام ونقاط فقط';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // Account Name (Arabic)
                            TextFormField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: 'اسم الحساب (عربي) *',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.account_balance),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'اسم الحساب مطلوب';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // Account Name (English)
                            TextFormField(
                              controller: _nameEnController,
                              decoration: const InputDecoration(
                                labelText: 'اسم الحساب (إنجليزي)',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.language),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Description
                            TextFormField(
                              controller: _descriptionController,
                              decoration: const InputDecoration(
                                labelText: 'الوصف',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.description),
                              ),
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveAccount,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                _isEditing ? 'حفظ التعديلات' : 'إضافة الحساب',
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  List<Account> _getFilteredParents() {
    return _availableParents.where((account) {
      // Filter accounts of the same type
      bool sameType = account.accountType == _selectedType;

      // Exclude the account being edited (to prevent circular reference)
      bool notSelf = !_isEditing || account.id != widget.account!.id;

      return sameType && notSelf;
    }).toList();
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.asset:
        return Colors.green;
      case AccountType.liability:
        return Colors.red;
      case AccountType.equity:
        return Colors.blue;
      case AccountType.revenue:
        return Colors.orange;
      case AccountType.expense:
        return Colors.purple;
    }
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final account = Account(
        id: _isEditing ? widget.account!.id : null,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        nameEn: _nameEnController.text.trim().isEmpty
            ? null
            : _nameEnController.text.trim(),
        accountType: _selectedType,
        parentId: _selectedParent?.id,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        balance: _isEditing ? widget.account!.balance : 0.0,
        isActive: _isEditing ? widget.account!.isActive : true,
        createdAt: _isEditing ? widget.account!.createdAt : null,
        updatedAt: null,
      );

      final result = _isEditing
          ? await _accountService.updateAccount(account)
          : await _accountService.createAccount(account);

      if (!mounted) return;

      setState(() => _isLoading = false);

      if (result.isSuccess) {
        _showSuccessSnackBar(
          _isEditing ? 'تم تحديث الحساب بنجاح' : 'تم إضافة الحساب بنجاح',
        );
        Navigator.pop(context, true);
      } else {
        _showErrorSnackBar(result.error!);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في حفظ الحساب: ${e.toString()}');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
