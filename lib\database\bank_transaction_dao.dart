/// Data Access Object for Bank Transactions
/// Bank Transaction DAO for Smart Ledger
library;

import '../models/bank_account.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class BankTransactionDao {
  static final BankTransactionDao _instance = BankTransactionDao._internal();
  factory BankTransactionDao() => _instance;
  BankTransactionDao._internal();

  /// Get all bank transactions
  Future<List<BankTransaction>> getAllBankTransactions() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankTransactions,
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return BankTransaction.fromMap(maps[i]);
    });
  }

  /// Get bank transaction by ID
  Future<BankTransaction?> getBankTransactionById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankTransactions,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return BankTransaction.fromMap(maps.first);
    }
    return null;
  }

  /// Get bank transactions by account ID
  Future<List<BankTransaction>> getBankTransactionsByAccountId(int accountId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankTransactions,
      where: 'bank_account_id = ?',
      whereArgs: [accountId],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return BankTransaction.fromMap(maps[i]);
    });
  }

  /// Get bank transactions by date range
  Future<List<BankTransaction>> getBankTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankTransactions,
      where: 'transaction_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return BankTransaction.fromMap(maps[i]);
    });
  }

  /// Get bank transactions by type
  Future<List<BankTransaction>> getBankTransactionsByType(BankTransactionType type) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankTransactions,
      where: 'type = ?',
      whereArgs: [type.value],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return BankTransaction.fromMap(maps[i]);
    });
  }

  /// Get pending bank transactions
  Future<List<BankTransaction>> getPendingBankTransactions() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankTransactions,
      where: 'status = ?',
      whereArgs: ['pending'],
      orderBy: 'transaction_date ASC, id ASC',
    );

    return List.generate(maps.length, (i) {
      return BankTransaction.fromMap(maps[i]);
    });
  }

  /// Insert new bank transaction
  Future<int> insertBankTransaction(BankTransaction transaction) async {
    final db = await DatabaseHelper().database;
    
    final transactionMap = transaction.toMap();
    transactionMap.remove('id'); // Remove ID for auto-increment
    transactionMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableBankTransactions,
      transactionMap,
    );
  }

  /// Update bank transaction
  Future<int> updateBankTransaction(BankTransaction transaction) async {
    final db = await DatabaseHelper().database;
    
    final transactionMap = transaction.toMap();
    transactionMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableBankTransactions,
      transactionMap,
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  /// Update transaction status
  Future<int> updateTransactionStatus(int transactionId, BankTransactionStatus status) async {
    final db = await DatabaseHelper().database;
    
    return await db.update(
      DatabaseSchema.tableBankTransactions,
      {
        'status': status.value,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [transactionId],
    );
  }

  /// Delete bank transaction
  Future<int> deleteBankTransaction(int id) async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tableBankTransactions,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get bank transaction summary by account
  Future<Map<String, dynamic>> getBankTransactionSummaryByAccount(int accountId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        type,
        COUNT(*) as transaction_count,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount
      FROM ${DatabaseSchema.tableBankTransactions}
      WHERE bank_account_id = ? AND status = 'completed'
      GROUP BY type
    ''', [accountId]);

    final totalResult = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_transactions,
        SUM(CASE WHEN type IN ('deposit', 'interest') THEN amount ELSE 0 END) as total_credits,
        SUM(CASE WHEN type IN ('withdrawal', 'fee', 'transfer') THEN amount ELSE 0 END) as total_debits
      FROM ${DatabaseSchema.tableBankTransactions}
      WHERE bank_account_id = ? AND status = 'completed'
    ''', [accountId]);

    return {
      'by_type': result,
      'totals': totalResult.isNotEmpty ? totalResult.first : {},
    };
  }

  /// Get monthly transaction summary
  Future<List<Map<String, dynamic>>> getMonthlyTransactionSummary(int year) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        strftime('%m', transaction_date) as month,
        COUNT(*) as transaction_count,
        SUM(CASE WHEN type IN ('deposit', 'interest') THEN amount ELSE 0 END) as total_credits,
        SUM(CASE WHEN type IN ('withdrawal', 'fee', 'transfer') THEN amount ELSE 0 END) as total_debits
      FROM ${DatabaseSchema.tableBankTransactions}
      WHERE strftime('%Y', transaction_date) = ? AND status = 'completed'
      GROUP BY strftime('%m', transaction_date)
      ORDER BY month
    ''', [year.toString()]);

    return result;
  }

  /// Search bank transactions
  Future<List<BankTransaction>> searchBankTransactions(String query) async {
    final db = await DatabaseHelper().database;
    final searchQuery = '%$query%';
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankTransactions,
      where: '''
        transaction_number LIKE ? OR 
        description LIKE ? OR 
        reference LIKE ? OR
        check_number LIKE ?
      ''',
      whereArgs: [searchQuery, searchQuery, searchQuery, searchQuery],
      orderBy: 'transaction_date DESC, id DESC',
    );

    return List.generate(maps.length, (i) {
      return BankTransaction.fromMap(maps[i]);
    });
  }

  /// Get bank transactions with account details
  Future<List<Map<String, dynamic>>> getBankTransactionsWithAccountDetails() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        bt.*,
        ba.account_name,
        ba.account_number,
        ba.bank_name,
        tba.account_name as to_account_name,
        tba.account_number as to_account_number
      FROM ${DatabaseSchema.tableBankTransactions} bt
      LEFT JOIN ${DatabaseSchema.tableBankAccounts} ba ON bt.bank_account_id = ba.id
      LEFT JOIN ${DatabaseSchema.tableBankAccounts} tba ON bt.to_bank_account_id = tba.id
      ORDER BY bt.transaction_date DESC, bt.id DESC
    ''');

    return result;
  }

  /// Get account balance at specific date
  Future<double> getAccountBalanceAtDate(int accountId, DateTime date) async {
    final db = await DatabaseHelper().database;
    
    // Get opening balance
    final accountResult = await db.query(
      DatabaseSchema.tableBankAccounts,
      columns: ['opening_balance'],
      where: 'id = ?',
      whereArgs: [accountId],
    );
    
    if (accountResult.isEmpty) return 0.0;
    
    double balance = (accountResult.first['opening_balance'] as num).toDouble();
    
    // Add completed transactions up to the date
    final transactionResult = await db.rawQuery('''
      SELECT 
        SUM(CASE WHEN type IN ('deposit', 'interest') THEN amount ELSE 0 END) as credits,
        SUM(CASE WHEN type IN ('withdrawal', 'fee', 'transfer') THEN amount ELSE 0 END) as debits
      FROM ${DatabaseSchema.tableBankTransactions}
      WHERE bank_account_id = ? 
        AND status = 'completed'
        AND transaction_date <= ?
    ''', [accountId, date.toIso8601String().split('T')[0]]);
    
    if (transactionResult.isNotEmpty) {
      final credits = (transactionResult.first['credits'] as num?)?.toDouble() ?? 0.0;
      final debits = (transactionResult.first['debits'] as num?)?.toDouble() ?? 0.0;
      balance += credits - debits;
    }
    
    return balance;
  }

  /// Generate next transaction number
  Future<String> generateTransactionNumber() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM ${DatabaseSchema.tableBankTransactions}
      WHERE transaction_number LIKE 'BT${DateTime.now().year}%'
    ''');
    
    final count = (result.first['count'] as int) + 1;
    return 'BT${DateTime.now().year}${count.toString().padLeft(6, '0')}';
  }
}
