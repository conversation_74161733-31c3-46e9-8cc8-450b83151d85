import '../models/account.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class AccountDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Create a new account
  Future<int> insertAccount(Account account) async {
    Map<String, dynamic> accountMap = account.toMap();
    accountMap.remove('id'); // Remove id for insert
    return await _dbHelper.insert(DatabaseSchema.tableAccounts, accountMap);
  }

  // Get all accounts
  Future<List<Account>> getAllAccounts() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      orderBy: 'code ASC',
    );
    return maps.map((map) => Account.fromMap(map)).toList();
  }

  // Get account by ID
  Future<Account?> getAccountById(int id) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Account.fromMap(maps.first);
    }
    return null;
  }

  // Get account by code
  Future<Account?> getAccountByCode(String code) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'code = ?',
      whereArgs: [code],
    );
    if (maps.isNotEmpty) {
      return Account.fromMap(maps.first);
    }
    return null;
  }

  // Get accounts by type
  Future<List<Account>> getAccountsByType(AccountType type) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'account_type = ?',
      whereArgs: [type.value],
      orderBy: 'code ASC',
    );
    return maps.map((map) => Account.fromMap(map)).toList();
  }

  // Get root accounts (no parent)
  Future<List<Account>> getRootAccounts() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'parent_id IS NULL',
      orderBy: 'code ASC',
    );
    return maps.map((map) => Account.fromMap(map)).toList();
  }

  // Get child accounts of a parent
  Future<List<Account>> getChildAccounts(int parentId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'parent_id = ?',
      whereArgs: [parentId],
      orderBy: 'code ASC',
    );
    return maps.map((map) => Account.fromMap(map)).toList();
  }

  // Get active accounts only
  Future<List<Account>> getActiveAccounts() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'is_active = 1',
      orderBy: 'code ASC',
    );
    return maps.map((map) => Account.fromMap(map)).toList();
  }

  // Search accounts by name or code
  Future<List<Account>> searchAccounts(String query) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'name LIKE ? OR code LIKE ? OR name_en LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'code ASC',
    );
    return maps.map((map) => Account.fromMap(map)).toList();
  }

  // Update account
  Future<int> updateAccount(Account account) async {
    return await _dbHelper.update(
      DatabaseSchema.tableAccounts,
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  // Update account balance
  Future<int> updateAccountBalance(int accountId, double newBalance) async {
    return await _dbHelper.update(
      DatabaseSchema.tableAccounts,
      {
        'balance': newBalance,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [accountId],
    );
  }

  // Delete account (soft delete by setting inactive)
  Future<int> deactivateAccount(int accountId) async {
    return await _dbHelper.update(
      DatabaseSchema.tableAccounts,
      {
        'is_active': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [accountId],
    );
  }

  // Hard delete account (use with caution)
  Future<int> deleteAccount(int accountId) async {
    return await _dbHelper.delete(
      DatabaseSchema.tableAccounts,
      where: 'id = ?',
      whereArgs: [accountId],
    );
  }

  // Check if account code exists
  Future<bool> isCodeExists(String code, {int? excludeId}) async {
    String whereClause = 'code = ?';
    List<dynamic> whereArgs = [code];
    
    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.isNotEmpty;
  }

  // Check if account has children
  Future<bool> hasChildren(int accountId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'parent_id = ?',
      whereArgs: [accountId],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  // Check if account has transactions
  Future<bool> hasTransactions(int accountId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableJournalEntryLines,
      where: 'account_id = ?',
      whereArgs: [accountId],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  // Get account tree with parent-child relationships
  Future<AccountTree> getAccountTree() async {
    List<Account> allAccounts = await getAllAccounts();
    AccountTree tree = AccountTree();
    tree.addAccounts(allAccounts);
    tree.buildTree();
    return tree;
  }

  // Get account balance summary by type
  Future<Map<AccountType, double>> getBalanceSummaryByType() async {
    Map<AccountType, double> summary = {};
    
    for (AccountType type in AccountType.values) {
      final result = await _dbHelper.rawQuery(
        'SELECT SUM(balance) as total FROM ${DatabaseSchema.tableAccounts} WHERE account_type = ? AND is_active = 1',
        [type.value],
      );
      
      double total = 0.0;
      if (result.isNotEmpty && result.first['total'] != null) {
        total = (result.first['total'] as num).toDouble();
      }
      summary[type] = total;
    }
    
    return summary;
  }

  // Get accounts with non-zero balances
  Future<List<Account>> getAccountsWithBalance() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'balance != 0 AND is_active = 1',
      orderBy: 'code ASC',
    );
    return maps.map((map) => Account.fromMap(map)).toList();
  }

  // Get next available account code for a parent
  Future<String> getNextAccountCode(String parentCode) async {
    // Find accounts with codes starting with parentCode
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableAccounts,
      where: 'code LIKE ?',
      whereArgs: ['$parentCode%'],
      orderBy: 'code DESC',
      limit: 1,
    );
    
    if (maps.isEmpty) {
      return '${parentCode}01';
    }
    
    String lastCode = maps.first['code'] as String;
    if (lastCode.length > parentCode.length) {
      String suffix = lastCode.substring(parentCode.length);
      int? lastNumber = int.tryParse(suffix);
      if (lastNumber != null) {
        int nextNumber = lastNumber + 1;
        return '$parentCode${nextNumber.toString().padLeft(suffix.length, '0')}';
      }
    }
    
    return '${parentCode}01';
  }

  // Batch insert accounts (for initial setup)
  Future<void> insertAccountsBatch(List<Account> accounts) async {
    await _dbHelper.transaction((txn) async {
      for (Account account in accounts) {
        Map<String, dynamic> accountMap = account.toMap();
        accountMap.remove('id');
        await txn.insert(DatabaseSchema.tableAccounts, accountMap);
      }
    });
  }

  // Get account statistics
  Future<Map<String, dynamic>> getAccountStatistics() async {
    final totalResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as total FROM ${DatabaseSchema.tableAccounts}',
    );
    
    final activeResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as active FROM ${DatabaseSchema.tableAccounts} WHERE is_active = 1',
    );
    
    final withBalanceResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as with_balance FROM ${DatabaseSchema.tableAccounts} WHERE balance != 0',
    );
    
    return {
      'total': totalResult.first['total'] as int,
      'active': activeResult.first['active'] as int,
      'with_balance': withBalanceResult.first['with_balance'] as int,
    };
  }
}
