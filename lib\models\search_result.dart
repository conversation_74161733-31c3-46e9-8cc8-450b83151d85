/// Model representing a search result from global search
class SearchResult {
  final String id;
  final String title;
  final String? subtitle;
  final String category;
  final String? additionalInfo;
  final Map<String, dynamic>? data;
  final DateTime? date;

  const SearchResult({
    required this.id,
    required this.title,
    this.subtitle,
    required this.category,
    this.additionalInfo,
    this.data,
    this.date,
  });

  factory SearchResult.fromAccount(Map<String, dynamic> account) {
    return SearchResult(
      id: account['id'].toString(),
      title: account['name'] ?? '',
      subtitle: account['name_en'],
      category: 'الحسابات',
      additionalInfo: 'رمز: ${account['code'] ?? ''}',
      data: account,
    );
  }

  factory SearchResult.fromJournalEntry(Map<String, dynamic> entry) {
    return SearchResult(
      id: entry['id'].toString(),
      title: 'قيد رقم ${entry['entry_number'] ?? ''}',
      subtitle: entry['description'],
      category: 'القيود',
      additionalInfo: 'المبلغ: ${entry['total_amount'] ?? 0} ر.س',
      data: entry,
      date: entry['date'] != null ? DateTime.parse(entry['date']) : null,
    );
  }

  factory SearchResult.fromCustomer(Map<String, dynamic> customer) {
    return SearchResult(
      id: customer['id'].toString(),
      title: customer['name'] ?? '',
      subtitle: customer['email'],
      category: 'العملاء',
      additionalInfo: 'رمز: ${customer['code'] ?? ''}',
      data: customer,
    );
  }

  factory SearchResult.fromSupplier(Map<String, dynamic> supplier) {
    return SearchResult(
      id: supplier['id'].toString(),
      title: supplier['name'] ?? '',
      subtitle: supplier['email'],
      category: 'الموردين',
      additionalInfo: 'رمز: ${supplier['code'] ?? ''}',
      data: supplier,
    );
  }

  factory SearchResult.fromItem(Map<String, dynamic> item) {
    return SearchResult(
      id: item['id'].toString(),
      title: item['name'] ?? '',
      subtitle: item['description'],
      category: 'الأصناف',
      additionalInfo: 'رمز: ${item['code'] ?? ''}',
      data: item,
    );
  }

  factory SearchResult.fromInvoice(Map<String, dynamic> invoice) {
    return SearchResult(
      id: invoice['id'].toString(),
      title: 'فاتورة رقم ${invoice['invoice_number'] ?? ''}',
      subtitle: invoice['notes'],
      category: 'الفواتير',
      additionalInfo: 'المبلغ: ${invoice['total_amount'] ?? 0} ر.س',
      data: invoice,
      date: invoice['date'] != null ? DateTime.parse(invoice['date']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'category': category,
      'additionalInfo': additionalInfo,
      'data': data,
      'date': date?.toIso8601String(),
    };
  }

  factory SearchResult.fromMap(Map<String, dynamic> map) {
    return SearchResult(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      subtitle: map['subtitle'],
      category: map['category'] ?? '',
      additionalInfo: map['additionalInfo'],
      data: map['data'],
      date: map['date'] != null ? DateTime.parse(map['date']) : null,
    );
  }

  @override
  String toString() {
    return 'SearchResult{id: $id, title: $title, category: $category}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchResult &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          category == other.category;

  @override
  int get hashCode => id.hashCode ^ category.hashCode;
}
