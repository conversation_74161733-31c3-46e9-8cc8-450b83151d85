import 'customer.dart';
import 'supplier.dart';
import 'item.dart';
import 'journal_entry.dart';

class Invoice {
  final int? id;
  final String invoiceNumber;
  final InvoiceType invoiceType;
  final DateTime date;
  final DateTime? dueDate;
  final int? customerId;
  final int? supplierId;
  final int? projectId; // ربط الفاتورة بالمشروع
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final double paidAmount;
  final InvoiceStatus status;
  final String? notes;
  final int? journalEntryId;

  // Multi-Currency Support Fields
  final String currencyCode; // رمز العملة (SAR, USD, EUR, etc.)
  final double exchangeRate; // سعر الصرف مقابل العملة الأساسية
  final String baseCurrencyCode; // رمز العملة الأساسية
  final double baseCurrencySubtotal; // المجموع الفرعي بالعملة الأساسية
  final double baseCurrencyTaxAmount; // مبلغ الضريبة بالعملة الأساسية
  final double baseCurrencyDiscountAmount; // مبلغ الخصم بالعملة الأساسية
  final double baseCurrencyTotalAmount; // المجموع الكلي بالعملة الأساسية
  final double baseCurrencyPaidAmount; // المبلغ المدفوع بالعملة الأساسية

  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Customer? customer;
  Supplier? supplier;
  JournalEntry? journalEntry;
  List<InvoiceLine> lines = [];

  Invoice({
    this.id,
    required this.invoiceNumber,
    required this.invoiceType,
    required this.date,
    this.dueDate,
    this.customerId,
    this.supplierId,
    this.projectId,
    this.subtotal = 0.0,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    this.totalAmount = 0.0,
    this.paidAmount = 0.0,
    this.status = InvoiceStatus.draft,
    this.notes,
    this.journalEntryId,

    // Multi-Currency Support Parameters
    this.currencyCode = 'SAR', // العملة الافتراضية
    this.exchangeRate = 1.0, // سعر الصرف الافتراضي
    this.baseCurrencyCode = 'SAR', // العملة الأساسية الافتراضية
    this.baseCurrencySubtotal = 0.0,
    this.baseCurrencyTaxAmount = 0.0,
    this.baseCurrencyDiscountAmount = 0.0,
    this.baseCurrencyTotalAmount = 0.0,
    this.baseCurrencyPaidAmount = 0.0,

    DateTime? createdAt,
    DateTime? updatedAt,
    this.customer,
    this.supplier,
    this.journalEntry,
    List<InvoiceLine>? lines,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now() {
    if (lines != null) {
      this.lines = lines;
    }
  }

  // Factory constructor from database map
  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id'] as int?,
      invoiceNumber: map['invoice_number'] as String,
      invoiceType: InvoiceType.fromString(map['invoice_type'] as String),
      date: DateTime.parse(map['date'] as String),
      dueDate: map['due_date'] != null
          ? DateTime.parse(map['due_date'] as String)
          : null,
      customerId: map['customer_id'] as int?,
      supplierId: map['supplier_id'] as int?,
      projectId: map['project_id'] as int?,
      subtotal: (map['subtotal'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (map['tax_amount'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (map['discount_amount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (map['total_amount'] as num?)?.toDouble() ?? 0.0,
      paidAmount: (map['paid_amount'] as num?)?.toDouble() ?? 0.0,
      status: InvoiceStatus.fromString(map['status'] as String),
      notes: map['notes'] as String?,
      journalEntryId: map['journal_entry_id'] as int?,

      // Multi-Currency Support Fields
      currencyCode: map['currency_code'] as String? ?? 'SAR',
      exchangeRate: (map['exchange_rate'] as num?)?.toDouble() ?? 1.0,
      baseCurrencyCode: map['base_currency_code'] as String? ?? 'SAR',
      baseCurrencySubtotal:
          (map['base_currency_subtotal'] as num?)?.toDouble() ?? 0.0,
      baseCurrencyTaxAmount:
          (map['base_currency_tax_amount'] as num?)?.toDouble() ?? 0.0,
      baseCurrencyDiscountAmount:
          (map['base_currency_discount_amount'] as num?)?.toDouble() ?? 0.0,
      baseCurrencyTotalAmount:
          (map['base_currency_total_amount'] as num?)?.toDouble() ?? 0.0,
      baseCurrencyPaidAmount:
          (map['base_currency_paid_amount'] as num?)?.toDouble() ?? 0.0,

      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'invoice_type': invoiceType.value,
      'date': date.toIso8601String().split('T')[0],
      'due_date': dueDate?.toIso8601String().split('T')[0],
      'customer_id': customerId,
      'supplier_id': supplierId,
      'project_id': projectId,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'status': status.value,
      'notes': notes,
      'journal_entry_id': journalEntryId,

      // Multi-Currency Support Fields
      'currency_code': currencyCode,
      'exchange_rate': exchangeRate,
      'base_currency_code': baseCurrencyCode,
      'base_currency_subtotal': baseCurrencySubtotal,
      'base_currency_tax_amount': baseCurrencyTaxAmount,
      'base_currency_discount_amount': baseCurrencyDiscountAmount,
      'base_currency_total_amount': baseCurrencyTotalAmount,
      'base_currency_paid_amount': baseCurrencyPaidAmount,

      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  Invoice copyWith({
    int? id,
    String? invoiceNumber,
    InvoiceType? invoiceType,
    DateTime? date,
    DateTime? dueDate,
    int? customerId,
    int? supplierId,
    int? projectId,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    double? paidAmount,
    InvoiceStatus? status,
    String? notes,
    int? journalEntryId,

    // Multi-Currency Support Parameters
    String? currencyCode,
    double? exchangeRate,
    String? baseCurrencyCode,
    double? baseCurrencySubtotal,
    double? baseCurrencyTaxAmount,
    double? baseCurrencyDiscountAmount,
    double? baseCurrencyTotalAmount,
    double? baseCurrencyPaidAmount,

    DateTime? createdAt,
    DateTime? updatedAt,
    Customer? customer,
    Supplier? supplier,
    JournalEntry? journalEntry,
    List<InvoiceLine>? lines,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      invoiceType: invoiceType ?? this.invoiceType,
      date: date ?? this.date,
      dueDate: dueDate ?? this.dueDate,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      projectId: projectId ?? this.projectId,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      journalEntryId: journalEntryId ?? this.journalEntryId,

      // Multi-Currency Support Fields
      currencyCode: currencyCode ?? this.currencyCode,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      baseCurrencyCode: baseCurrencyCode ?? this.baseCurrencyCode,
      baseCurrencySubtotal: baseCurrencySubtotal ?? this.baseCurrencySubtotal,
      baseCurrencyTaxAmount:
          baseCurrencyTaxAmount ?? this.baseCurrencyTaxAmount,
      baseCurrencyDiscountAmount:
          baseCurrencyDiscountAmount ?? this.baseCurrencyDiscountAmount,
      baseCurrencyTotalAmount:
          baseCurrencyTotalAmount ?? this.baseCurrencyTotalAmount,
      baseCurrencyPaidAmount:
          baseCurrencyPaidAmount ?? this.baseCurrencyPaidAmount,

      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      customer: customer ?? this.customer,
      supplier: supplier ?? this.supplier,
      journalEntry: journalEntry ?? this.journalEntry,
      lines: lines ?? this.lines,
    );
  }

  // Helper methods
  String get number => invoiceNumber; // Compatibility getter
  double get remainingAmount => totalAmount - paidAmount;
  bool get isFullyPaid => remainingAmount <= 0.01;
  bool get isPartiallyPaid => paidAmount > 0 && !isFullyPaid;
  bool get isOverdue =>
      dueDate != null && DateTime.now().isAfter(dueDate!) && !isFullyPaid;

  // Multi-Currency Helper Methods
  /// التحقق من كون الفاتورة بعملة مختلفة عن العملة الأساسية
  bool get isMultiCurrency => currencyCode != baseCurrencyCode;

  /// الحصول على المبلغ المتبقي بالعملة الأساسية
  double get baseCurrencyRemainingAmount =>
      baseCurrencyTotalAmount - baseCurrencyPaidAmount;

  /// تحويل مبلغ من عملة الفاتورة إلى العملة الأساسية
  double convertToBaseCurrency(double amount) {
    return amount * exchangeRate;
  }

  /// تحويل مبلغ من العملة الأساسية إلى عملة الفاتورة
  double convertFromBaseCurrency(double baseCurrencyAmount) {
    return exchangeRate != 0 ? baseCurrencyAmount / exchangeRate : 0.0;
  }

  /// تحديث المبالغ بالعملة الأساسية بناءً على سعر الصرف الحالي
  Invoice updateBaseCurrencyAmounts() {
    return copyWith(
      baseCurrencySubtotal: convertToBaseCurrency(subtotal),
      baseCurrencyTaxAmount: convertToBaseCurrency(taxAmount),
      baseCurrencyDiscountAmount: convertToBaseCurrency(discountAmount),
      baseCurrencyTotalAmount: convertToBaseCurrency(totalAmount),
      baseCurrencyPaidAmount: convertToBaseCurrency(paidAmount),
      updatedAt: DateTime.now(),
    );
  }

  String get clientName {
    if (invoiceType == InvoiceType.sales) {
      return customer?.name ?? 'عميل غير محدد';
    } else {
      return supplier?.name ?? 'مورد غير محدد';
    }
  }

  // Calculate totals from lines
  void calculateTotals() {
    // Calculate subtotal from all lines
    for (InvoiceLine line in lines) {
      // Process line totals
      line.lineTotal; // Access line total for calculation
    }
    // Note: This creates new values but doesn't update the instance
    // In practice, you'd need to handle this differently or return new instance
  }

  Map<String, double> getCalculatedTotals({int? taxGroupId}) {
    double lineSubtotal = 0.0;
    for (InvoiceLine line in lines) {
      lineSubtotal += line.lineTotal;
    }

    // ملاحظة: يجب استخدام TaxService لحساب الضرائب الفعلية
    // هذا حساب مؤقت للتوافق مع النظام الحالي
    double calculatedTaxAmount = taxAmount > 0
        ? taxAmount
        : lineSubtotal * 0.15;
    double calculatedTotal =
        lineSubtotal + calculatedTaxAmount - discountAmount;

    return {
      'subtotal': lineSubtotal,
      'tax': calculatedTaxAmount,
      'discount': discountAmount,
      'total': calculatedTotal,
    };
  }

  // Add line to invoice
  void addLine(InvoiceLine line) {
    line.lineOrder = lines.length;
    lines.add(line);
  }

  // Remove line from invoice
  void removeLine(int index) {
    if (index >= 0 && index < lines.length) {
      lines.removeAt(index);
      // Reorder remaining lines
      for (int i = 0; i < lines.length; i++) {
        lines[i].lineOrder = i;
      }
    }
  }

  // Validation
  List<String> validate() {
    List<String> errors = [];

    if (invoiceNumber.trim().isEmpty) {
      errors.add('رقم الفاتورة مطلوب');
    }

    if (invoiceType == InvoiceType.sales && customerId == null) {
      errors.add('العميل مطلوب لفاتورة المبيعات');
    }

    if (invoiceType == InvoiceType.purchase && supplierId == null) {
      errors.add('المورد مطلوب لفاتورة المشتريات');
    }

    if (lines.isEmpty) {
      errors.add('يجب إضافة صنف واحد على الأقل');
    }

    if (totalAmount <= 0) {
      errors.add('إجمالي الفاتورة يجب أن يكون أكبر من صفر');
    }

    if (paidAmount < 0) {
      errors.add('المبلغ المدفوع لا يمكن أن يكون سالباً');
    }

    if (paidAmount > totalAmount) {
      errors.add('المبلغ المدفوع لا يمكن أن يكون أكبر من إجمالي الفاتورة');
    }

    // Validate lines
    for (int i = 0; i < lines.length; i++) {
      List<String> lineErrors = lines[i].validate();
      for (String error in lineErrors) {
        errors.add('السطر ${i + 1}: $error');
      }
    }

    return errors;
  }

  @override
  String toString() {
    return 'Invoice{id: $id, number: $invoiceNumber, type: ${invoiceType.value}, total: $totalAmount}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Invoice &&
        other.id == id &&
        other.invoiceNumber == invoiceNumber;
  }

  @override
  int get hashCode => id.hashCode ^ invoiceNumber.hashCode;
}

enum InvoiceType {
  sales('sales', 'مبيعات', 'Sales'),
  purchase('purchase', 'مشتريات', 'Purchase');

  const InvoiceType(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static InvoiceType fromString(String value) {
    return InvoiceType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => InvoiceType.sales,
    );
  }

  String get displayName => nameAr;
}

enum InvoiceStatus {
  draft('draft', 'مسودة', 'Draft'),
  posted('posted', 'مرحلة', 'Posted'),
  sent('sent', 'مرسلة', 'Sent'),
  partiallyPaid('partially_paid', 'مدفوعة جزئياً', 'Partially Paid'),
  paid('paid', 'مدفوعة', 'Paid'),
  overdue('overdue', 'متأخرة', 'Overdue'),
  cancelled('cancelled', 'ملغية', 'Cancelled');

  const InvoiceStatus(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static InvoiceStatus fromString(String value) {
    return InvoiceStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => InvoiceStatus.draft,
    );
  }

  String get displayName => nameAr;
}

class InvoiceLine {
  final int? id;
  final int invoiceId;
  final int itemId;
  final String? description;
  final double quantity;
  final double unitPrice;
  final double discountPercentage;
  final double lineTotal;
  int lineOrder;
  final DateTime createdAt;

  // Navigation properties
  Item? item;

  InvoiceLine({
    this.id,
    required this.invoiceId,
    required this.itemId,
    this.description,
    required this.quantity,
    required this.unitPrice,
    this.discountPercentage = 0.0,
    required this.lineTotal,
    this.lineOrder = 0,
    DateTime? createdAt,
    this.item,
  }) : createdAt = createdAt ?? DateTime.now();

  // Factory constructor from database map
  factory InvoiceLine.fromMap(Map<String, dynamic> map) {
    return InvoiceLine(
      id: map['id'] as int?,
      invoiceId: map['invoice_id'] as int,
      itemId: map['item_id'] as int,
      description: map['description'] as String?,
      quantity: (map['quantity'] as num).toDouble(),
      unitPrice: (map['unit_price'] as num).toDouble(),
      discountPercentage:
          (map['discount_percentage'] as num?)?.toDouble() ?? 0.0,
      lineTotal: (map['line_total'] as num).toDouble(),
      lineOrder: map['line_order'] as int? ?? 0,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'item_id': itemId,
      'description': description,
      'quantity': quantity,
      'unit_price': unitPrice,
      'discount_percentage': discountPercentage,
      'line_total': lineTotal,
      'line_order': lineOrder,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  InvoiceLine copyWith({
    int? id,
    int? invoiceId,
    int? itemId,
    String? description,
    double? quantity,
    double? unitPrice,
    double? discountPercentage,
    double? lineTotal,
    int? lineOrder,
    DateTime? createdAt,
    Item? item,
  }) {
    return InvoiceLine(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      itemId: itemId ?? this.itemId,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      lineTotal: lineTotal ?? this.lineTotal,
      lineOrder: lineOrder ?? this.lineOrder,
      createdAt: createdAt ?? this.createdAt,
      item: item ?? this.item,
    );
  }

  // Calculate line total
  double calculateLineTotal() {
    double subtotal = quantity * unitPrice;
    double discountAmount = subtotal * (discountPercentage / 100);
    return subtotal - discountAmount;
  }

  // Helper methods
  double get discountAmount =>
      (quantity * unitPrice) * (discountPercentage / 100);
  double get subtotalBeforeDiscount => quantity * unitPrice;
  String get itemName => item?.name ?? 'صنف غير محدد';
  String get itemCode => item?.code ?? '';
  String get itemUnit => item?.unit ?? 'قطعة';

  // Validation
  List<String> validate() {
    List<String> errors = [];

    if (quantity <= 0) {
      errors.add('الكمية يجب أن تكون أكبر من صفر');
    }

    if (unitPrice < 0) {
      errors.add('سعر الوحدة لا يمكن أن يكون سالباً');
    }

    if (discountPercentage < 0 || discountPercentage > 100) {
      errors.add('نسبة الخصم يجب أن تكون بين 0 و 100');
    }

    if (lineTotal < 0) {
      errors.add('إجمالي السطر لا يمكن أن يكون سالباً');
    }

    return errors;
  }

  @override
  String toString() {
    return 'InvoiceLine{id: $id, itemId: $itemId, qty: $quantity, price: $unitPrice, total: $lineTotal}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceLine &&
        other.id == id &&
        other.invoiceId == invoiceId &&
        other.itemId == itemId;
  }

  @override
  int get hashCode => id.hashCode ^ invoiceId.hashCode ^ itemId.hashCode;
}
