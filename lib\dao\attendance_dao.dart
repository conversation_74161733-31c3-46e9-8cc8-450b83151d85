import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/attendance.dart';

/// DAO للحضور والغياب
/// Attendance Data Access Object
class AttendanceDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج سجل حضور جديد
  Future<int> insertAttendanceRecord(AttendanceRecord attendanceRecord) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableAttendanceRecords,
      attendanceRecord.toMap(),
    );
  }

  /// تحديث سجل حضور
  Future<int> updateAttendanceRecord(AttendanceRecord attendanceRecord) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableAttendanceRecords,
      attendanceRecord.toMap(),
      where: 'id = ?',
      whereArgs: [attendanceRecord.id],
    );
  }

  /// حذف سجل حضور
  Future<int> deleteAttendanceRecord(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      DatabaseSchema.tableAttendanceRecords,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على سجل حضور بالمعرف
  Future<AttendanceRecord?> getAttendanceRecordById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAttendanceRecords,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return AttendanceRecord.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على سجل حضور موظف في تاريخ معين
  Future<AttendanceRecord?> getEmployeeAttendanceByDate(
      String employeeCode, DateTime date) async {
    final db = await _databaseHelper.database;
    final dateStr = date.toIso8601String().split('T')[0];
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAttendanceRecords,
      where: 'employee_code = ? AND DATE(date) = ?',
      whereArgs: [employeeCode, dateStr],
    );

    if (maps.isNotEmpty) {
      return AttendanceRecord.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على سجلات حضور الموظف
  Future<List<AttendanceRecord>> getEmployeeAttendanceRecords(
      String employeeCode, {DateTime? startDate, DateTime? endDate}) async {
    final db = await _databaseHelper.database;
    
    String whereClause = 'employee_code = ?';
    List<dynamic> whereArgs = [employeeCode];

    if (startDate != null) {
      whereClause += ' AND DATE(date) >= ?';
      whereArgs.add(startDate.toIso8601String().split('T')[0]);
    }

    if (endDate != null) {
      whereClause += ' AND DATE(date) <= ?';
      whereArgs.add(endDate.toIso8601String().split('T')[0]);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAttendanceRecords,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) => AttendanceRecord.fromMap(maps[i]));
  }

  /// الحصول على سجلات الحضور لتاريخ معين
  Future<List<AttendanceRecord>> getAttendanceRecordsByDate(DateTime date) async {
    final db = await _databaseHelper.database;
    final dateStr = date.toIso8601String().split('T')[0];
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAttendanceRecords,
      where: 'DATE(date) = ?',
      whereArgs: [dateStr],
      orderBy: 'employee_code ASC',
    );

    return List.generate(maps.length, (i) => AttendanceRecord.fromMap(maps[i]));
  }

  /// الحصول على سجلات الحضور حسب الحالة
  Future<List<AttendanceRecord>> getAttendanceRecordsByStatus(
      AttendanceStatus status, {DateTime? startDate, DateTime? endDate}) async {
    final db = await _databaseHelper.database;
    
    String whereClause = 'status = ?';
    List<dynamic> whereArgs = [status.name];

    if (startDate != null) {
      whereClause += ' AND DATE(date) >= ?';
      whereArgs.add(startDate.toIso8601String().split('T')[0]);
    }

    if (endDate != null) {
      whereClause += ' AND DATE(date) <= ?';
      whereArgs.add(endDate.toIso8601String().split('T')[0]);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAttendanceRecords,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'date DESC, employee_code ASC',
    );

    return List.generate(maps.length, (i) => AttendanceRecord.fromMap(maps[i]));
  }

  /// تسجيل دخول الموظف
  Future<int> checkIn(String employeeCode, DateTime checkInTime, {String? location}) async {
    final db = await _databaseHelper.database;
    final date = DateTime(checkInTime.year, checkInTime.month, checkInTime.day);
    
    // التحقق من وجود سجل لهذا اليوم
    final existingRecord = await getEmployeeAttendanceByDate(employeeCode, date);
    
    if (existingRecord != null) {
      // تحديث وقت الدخول
      return await db.update(
        DatabaseSchema.tableAttendanceRecords,
        {
          'check_in_time': checkInTime.toIso8601String(),
          'location': location,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [existingRecord.id],
      );
    } else {
      // إنشاء سجل جديد
      final attendanceRecord = AttendanceRecord(
        employeeCode: employeeCode,
        date: date,
        checkInTime: checkInTime,
        status: AttendanceStatus.present,
        location: location,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await insertAttendanceRecord(attendanceRecord);
    }
  }

  /// تسجيل خروج الموظف
  Future<int> checkOut(String employeeCode, DateTime checkOutTime, {String? location}) async {
    final db = await _databaseHelper.database;
    final date = DateTime(checkOutTime.year, checkOutTime.month, checkOutTime.day);
    
    final existingRecord = await getEmployeeAttendanceByDate(employeeCode, date);
    
    if (existingRecord != null) {
      // حساب ساعات العمل
      double? workedHours;
      if (existingRecord.checkInTime != null) {
        final duration = checkOutTime.difference(existingRecord.checkInTime!);
        workedHours = duration.inMinutes / 60.0;
      }

      return await db.update(
        DatabaseSchema.tableAttendanceRecords,
        {
          'check_out_time': checkOutTime.toIso8601String(),
          'worked_hours': workedHours,
          'location': location,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [existingRecord.id],
      );
    } else {
      throw Exception('لا يوجد سجل دخول لهذا اليوم');
    }
  }

  /// الحصول على إحصائيات الحضور للموظف
  Future<Map<String, dynamic>> getEmployeeAttendanceStatistics(
      String employeeCode, DateTime startDate, DateTime endDate) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN status = 'halfDay' THEN 1 ELSE 0 END) as half_days,
        SUM(CASE WHEN status = 'leave' THEN 1 ELSE 0 END) as leave_days,
        AVG(worked_hours) as average_worked_hours,
        SUM(worked_hours) as total_worked_hours,
        SUM(overtime_hours) as total_overtime_hours
      FROM ${DatabaseSchema.tableAttendanceRecords}
      WHERE employee_code = ? AND DATE(date) BETWEEN ? AND ?
    ''', [
      employeeCode,
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
    ]);

    final row = result.first;
    return {
      'total_days': row['total_days'] ?? 0,
      'present_days': row['present_days'] ?? 0,
      'absent_days': row['absent_days'] ?? 0,
      'late_days': row['late_days'] ?? 0,
      'half_days': row['half_days'] ?? 0,
      'leave_days': row['leave_days'] ?? 0,
      'average_worked_hours': (row['average_worked_hours'] as num?)?.toDouble() ?? 0.0,
      'total_worked_hours': (row['total_worked_hours'] as num?)?.toDouble() ?? 0.0,
      'total_overtime_hours': (row['total_overtime_hours'] as num?)?.toDouble() ?? 0.0,
    };
  }

  /// الحصول على إحصائيات الحضور العامة
  Future<Map<String, dynamic>> getGeneralAttendanceStatistics(
      DateTime startDate, DateTime endDate) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(DISTINCT employee_code) as total_employees,
        COUNT(*) as total_records,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,
        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
        AVG(worked_hours) as average_worked_hours
      FROM ${DatabaseSchema.tableAttendanceRecords}
      WHERE DATE(date) BETWEEN ? AND ?
    ''', [
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
    ]);

    final row = result.first;
    return {
      'total_employees': row['total_employees'] ?? 0,
      'total_records': row['total_records'] ?? 0,
      'present_count': row['present_count'] ?? 0,
      'absent_count': row['absent_count'] ?? 0,
      'late_count': row['late_count'] ?? 0,
      'average_worked_hours': (row['average_worked_hours'] as num?)?.toDouble() ?? 0.0,
    };
  }

  /// الحصول على سجلات الحضور مع تفاصيل الموظف
  Future<List<Map<String, dynamic>>> getAttendanceRecordsWithEmployeeDetails(
      DateTime startDate, DateTime endDate) async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        ar.*,
        e.first_name,
        e.last_name,
        e.middle_name,
        d.name as department_name,
        p.title as position_title
      FROM ${DatabaseSchema.tableAttendanceRecords} ar
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON ar.employee_code = e.employee_code
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      LEFT JOIN ${DatabaseSchema.tablePositions} p ON e.position_id = p.id
      WHERE DATE(ar.date) BETWEEN ? AND ?
      ORDER BY ar.date DESC, e.first_name ASC
    ''', [
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
    ]);
  }

  /// البحث في سجلات الحضور
  Future<List<Map<String, dynamic>>> searchAttendanceRecords(
      DateTime startDate, DateTime endDate, String query) async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        ar.*,
        e.first_name,
        e.last_name,
        e.middle_name,
        d.name as department_name,
        p.title as position_title
      FROM ${DatabaseSchema.tableAttendanceRecords} ar
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON ar.employee_code = e.employee_code
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      LEFT JOIN ${DatabaseSchema.tablePositions} p ON e.position_id = p.id
      WHERE DATE(ar.date) BETWEEN ? AND ? AND (
        LOWER(e.first_name) LIKE LOWER(?) OR
        LOWER(e.last_name) LIKE LOWER(?) OR
        LOWER(ar.employee_code) LIKE LOWER(?) OR
        LOWER(d.name) LIKE LOWER(?)
      )
      ORDER BY ar.date DESC, e.first_name ASC
    ''', [
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
      '%$query%', '%$query%', '%$query%', '%$query%'
    ]);
  }

  /// الحصول على الموظفين الغائبين اليوم
  Future<List<String>> getTodayAbsentEmployees() async {
    final db = await _databaseHelper.database;
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    final result = await db.rawQuery('''
      SELECT DISTINCT e.employee_code
      FROM ${DatabaseSchema.tableEmployees} e
      LEFT JOIN ${DatabaseSchema.tableAttendanceRecords} ar 
        ON e.employee_code = ar.employee_code AND DATE(ar.date) = ?
      WHERE e.is_active = 1 AND ar.employee_code IS NULL
    ''', [today]);

    return result.map((row) => row['employee_code'] as String).toList();
  }

  /// تحديث حالة الحضور
  Future<int> updateAttendanceStatus(int id, AttendanceStatus status) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableAttendanceRecords,
      {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
