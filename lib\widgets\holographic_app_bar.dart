/// شريط تطبيق هولوجرافي متقدم لتطبيق Smart Ledger
/// Advanced Holographic App Bar for Smart Ledger Application
library;

import 'package:flutter/material.dart';

/// شريط تطبيق هولوجرافي مع تأثيرات بصرية متقدمة
/// Holographic app bar with advanced visual effects
class HolographicAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final bool enableGradient;
  final bool enableGlowEffect;

  const HolographicAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0.0,
    this.enableGradient = true,
    this.enableGlowEffect = true,
  });

  @override
  State<HolographicAppBar> createState() => _HolographicAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 20);
}

class _HolographicAppBarState extends State<HolographicAppBar>
    with TickerProviderStateMixin {
  late AnimationController _glowController;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    if (widget.enableGlowEffect) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor =
        widget.backgroundColor ?? theme.primaryColor.withValues(alpha: 0.1);
    final foregroundColor =
        widget.foregroundColor ??
        theme.textTheme.titleLarge?.color ??
        Colors.white;

    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: widget.enableGradient
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      backgroundColor,
                      backgroundColor.withValues(alpha: 0.8),
                      backgroundColor.withValues(alpha: 0.6),
                    ],
                  )
                : null,
            color: widget.enableGradient ? null : backgroundColor,
            boxShadow: [
              if (widget.elevation > 0)
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: widget.elevation,
                  offset: const Offset(0, 2),
                ),
              if (widget.enableGlowEffect)
                BoxShadow(
                  color: Colors.cyan.withValues(
                    alpha: _glowAnimation.value * 0.3,
                  ),
                  blurRadius: 20,
                  offset: const Offset(0, 0),
                ),
            ],
          ),
          child: AppBar(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    color: foregroundColor,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    shadows: widget.enableGlowEffect
                        ? [
                            Shadow(
                              color: Colors.cyan.withValues(
                                alpha: _glowAnimation.value * 0.5,
                              ),
                              blurRadius: 10,
                            ),
                          ]
                        : null,
                  ),
                ),
                if (widget.subtitle != null)
                  Text(
                    widget.subtitle!,
                    style: TextStyle(
                      color: foregroundColor.withValues(alpha: 0.7),
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
              ],
            ),
            leading: widget.leading,
            automaticallyImplyLeading: widget.automaticallyImplyLeading,
            actions: widget.actions,
            backgroundColor: Colors.transparent,
            elevation: 0,
            foregroundColor: foregroundColor,
            iconTheme: IconThemeData(
              color: foregroundColor,
              shadows: widget.enableGlowEffect
                  ? [
                      Shadow(
                        color: Colors.cyan.withValues(
                          alpha: _glowAnimation.value * 0.3,
                        ),
                        blurRadius: 5,
                      ),
                    ]
                  : null,
            ),
          ),
        );
      },
    );
  }
}

/// شريط تطبيق هولوجرافي مبسط للاستخدام السريع
/// Simple holographic app bar for quick usage
class SimpleHolographicAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;

  const SimpleHolographicAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return HolographicAppBar(
      title: title,
      subtitle: subtitle,
      actions: actions,
      enableGlowEffect: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 20);
}
