import '../models/search_result.dart';
import '../services/account_service.dart';
import '../services/journal_entry_service.dart';
import '../database/customer_dao.dart';
import '../database/supplier_dao.dart';
import '../database/item_dao.dart';
import '../database/invoice_dao.dart';

/// Service for performing global search across all modules
class SearchService {
  final AccountService _accountService = AccountService();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final CustomerDao _customerDao = CustomerDao();
  final SupplierDao _supplierDao = SupplierDao();
  final ItemDao _itemDao = ItemDao();
  final InvoiceDao _invoiceDao = InvoiceDao();

  /// Perform global search across all modules
  Future<Result<List<SearchResult>>> globalSearch(
    String query, {
    String? category,
    int? limit = 50,
  }) async {
    try {
      List<SearchResult> allResults = [];

      // Search in different categories based on selection
      if (category == null || category == 'الحسابات') {
        final accountResults = await _searchAccounts(query);
        if (accountResults.isSuccess) {
          allResults.addAll(accountResults.data ?? []);
        }
      }

      if (category == null || category == 'القيود') {
        final entryResults = await _searchJournalEntries(query);
        if (entryResults.isSuccess) {
          allResults.addAll(entryResults.data ?? []);
        }
      }

      if (category == null || category == 'العملاء') {
        final customerResults = await _searchCustomers(query);
        if (customerResults.isSuccess) {
          allResults.addAll(customerResults.data ?? []);
        }
      }

      if (category == null || category == 'الموردين') {
        final supplierResults = await _searchSuppliers(query);
        if (supplierResults.isSuccess) {
          allResults.addAll(supplierResults.data ?? []);
        }
      }

      if (category == null || category == 'الأصناف') {
        final itemResults = await _searchItems(query);
        if (itemResults.isSuccess) {
          allResults.addAll(itemResults.data ?? []);
        }
      }

      if (category == null || category == 'الفواتير') {
        final invoiceResults = await _searchInvoices(query);
        if (invoiceResults.isSuccess) {
          allResults.addAll(invoiceResults.data ?? []);
        }
      }

      // Sort results by relevance (exact matches first, then partial matches)
      allResults.sort((a, b) {
        // Exact title matches first
        bool aExact = a.title.toLowerCase().contains(query.toLowerCase());
        bool bExact = b.title.toLowerCase().contains(query.toLowerCase());

        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;

        // Then by date (newest first) if available
        if (a.date != null && b.date != null) {
          return b.date!.compareTo(a.date!);
        }

        // Finally alphabetically
        return a.title.compareTo(b.title);
      });

      // Apply limit if specified
      if (limit != null && allResults.length > limit) {
        allResults = allResults.take(limit).toList();
      }

      return Result.success(allResults);
    } catch (e) {
      return Result.error('خطأ في البحث الشامل: ${e.toString()}');
    }
  }

  /// Search in accounts
  Future<Result<List<SearchResult>>> _searchAccounts(String query) async {
    try {
      final result = await _accountService.searchAccounts(query);
      if (result.isSuccess) {
        final searchResults = result.data!
            .map((account) => SearchResult.fromAccount(account.toMap()))
            .toList();
        return Result.success(searchResults);
      }
      return Result.error(result.error ?? 'خطأ في البحث في الحسابات');
    } catch (e) {
      return Result.error('خطأ في البحث في الحسابات: ${e.toString()}');
    }
  }

  /// Search in journal entries
  Future<Result<List<SearchResult>>> _searchJournalEntries(String query) async {
    try {
      final result = await _journalEntryService.searchJournalEntries(query);
      if (result.isSuccess) {
        final searchResults = result.data!
            .map((entry) => SearchResult.fromJournalEntry(entry.toMap()))
            .toList();
        return Result.success(searchResults);
      }
      return Result.error(result.error ?? 'خطأ في البحث في القيود');
    } catch (e) {
      return Result.error('خطأ في البحث في القيود: ${e.toString()}');
    }
  }

  /// Search in customers
  Future<Result<List<SearchResult>>> _searchCustomers(String query) async {
    try {
      final customers = await _customerDao.searchCustomers(query);
      final searchResults = customers
          .map((customer) => SearchResult.fromCustomer(customer.toMap()))
          .toList();
      return Result.success(searchResults);
    } catch (e) {
      return Result.error('خطأ في البحث في العملاء: ${e.toString()}');
    }
  }

  /// Search in suppliers
  Future<Result<List<SearchResult>>> _searchSuppliers(String query) async {
    try {
      final suppliers = await _supplierDao.searchSuppliers(query);
      final searchResults = suppliers
          .map((supplier) => SearchResult.fromSupplier(supplier.toMap()))
          .toList();
      return Result.success(searchResults);
    } catch (e) {
      return Result.error('خطأ في البحث في الموردين: ${e.toString()}');
    }
  }

  /// Search in items
  Future<Result<List<SearchResult>>> _searchItems(String query) async {
    try {
      final items = await _itemDao.searchItems(query);
      final searchResults = items
          .map((item) => SearchResult.fromItem(item.toMap()))
          .toList();
      return Result.success(searchResults);
    } catch (e) {
      return Result.error('خطأ في البحث في الأصناف: ${e.toString()}');
    }
  }

  /// Search in invoices
  Future<Result<List<SearchResult>>> _searchInvoices(String query) async {
    try {
      final invoices = await _invoiceDao.searchInvoices(query);
      final searchResults = invoices
          .map((invoice) => SearchResult.fromInvoice(invoice.toMap()))
          .toList();
      return Result.success(searchResults);
    } catch (e) {
      return Result.error('خطأ في البحث في الفواتير: ${e.toString()}');
    }
  }

  /// Get search suggestions based on recent searches or popular items
  Future<Result<List<String>>> getSearchSuggestions({
    String? category,
    int limit = 10,
  }) async {
    try {
      List<String> suggestions = [];

      // Add some common search terms based on category
      if (category == null || category == 'الحسابات') {
        suggestions.addAll([
          'النقدية',
          'البنك',
          'العملاء',
          'الموردين',
          'المبيعات',
          'المشتريات',
        ]);
      }

      if (category == null || category == 'القيود') {
        suggestions.addAll([
          'قيد افتتاحي',
          'قيد إقفال',
          'قيد تسوية',
          'قيد يومي',
        ]);
      }

      // Limit suggestions
      if (suggestions.length > limit) {
        suggestions = suggestions.take(limit).toList();
      }

      return Result.success(suggestions);
    } catch (e) {
      return Result.error('خطأ في جلب اقتراحات البحث: ${e.toString()}');
    }
  }

  /// Get recent searches (this would typically be stored in local storage)
  Future<Result<List<String>>> getRecentSearches({int limit = 5}) async {
    try {
      // For now, return empty list
      return Result.success(<String>[]);
    } catch (e) {
      return Result.error('خطأ في جلب عمليات البحث الأخيرة: ${e.toString()}');
    }
  }

  /// Save search query to recent searches
  Future<Result<void>> saveRecentSearch(String query) async {
    try {
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حفظ البحث: ${e.toString()}');
    }
  }
}
