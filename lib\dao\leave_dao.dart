import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/leave.dart';

/// DAO للإجازات
/// Leave Data Access Object
class LeaveDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج طلب إجازة جديد
  Future<int> insertLeaveRequest(LeaveRequest leaveRequest) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableLeaveRequests,
      leaveRequest.toMap(),
    );
  }

  /// تحديث طلب إجازة
  Future<int> updateLeaveRequest(LeaveRequest leaveRequest) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableLeaveRequests,
      leaveRequest.toMap(),
      where: 'id = ?',
      whereArgs: [leaveRequest.id],
    );
  }

  /// حذف طلب إجازة
  Future<int> deleteLeaveRequest(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      DatabaseSchema.tableLeaveRequests,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على طلب إجازة بالمعرف
  Future<LeaveRequest?> getLeaveRequestById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveRequests,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return LeaveRequest.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على طلبات إجازة الموظف
  Future<List<LeaveRequest>> getEmployeeLeaveRequests(String employeeCode) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveRequests,
      where: 'employee_code = ?',
      whereArgs: [employeeCode],
      orderBy: 'start_date DESC',
    );

    return List.generate(maps.length, (i) => LeaveRequest.fromMap(maps[i]));
  }

  /// الحصول على طلبات الإجازة حسب الحالة
  Future<List<LeaveRequest>> getLeaveRequestsByStatus(LeaveStatus status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveRequests,
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) => LeaveRequest.fromMap(maps[i]));
  }

  /// الحصول على طلبات الإجازة حسب النوع
  Future<List<LeaveRequest>> getLeaveRequestsByType(int leaveTypeId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveRequests,
      where: 'leave_type_id = ?',
      whereArgs: [leaveTypeId],
      orderBy: 'start_date DESC',
    );

    return List.generate(maps.length, (i) => LeaveRequest.fromMap(maps[i]));
  }

  /// الحصول على طلبات الإجازة في فترة معينة
  Future<List<LeaveRequest>> getLeaveRequestsByDateRange(
      DateTime startDate, DateTime endDate) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveRequests,
      where: 'start_date <= ? AND end_date >= ?',
      whereArgs: [
        endDate.toIso8601String().split('T')[0],
        startDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'start_date ASC',
    );

    return List.generate(maps.length, (i) => LeaveRequest.fromMap(maps[i]));
  }

  /// إدراج نوع إجازة جديد
  Future<int> insertLeaveType(LeaveType leaveType) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableLeaveTypes,
      leaveType.toMap(),
    );
  }

  /// تحديث نوع إجازة
  Future<int> updateLeaveType(LeaveType leaveType) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableLeaveTypes,
      leaveType.toMap(),
      where: 'id = ?',
      whereArgs: [leaveType.id],
    );
  }

  /// حذف نوع إجازة
  Future<int> deleteLeaveType(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableLeaveTypes,
      {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على نوع إجازة بالمعرف
  Future<LeaveType?> getLeaveTypeById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveTypes,
      where: 'id = ? AND is_active = 1',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return LeaveType.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع أنواع الإجازات
  Future<List<LeaveType>> getAllLeaveTypes() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveTypes,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => LeaveType.fromMap(maps[i]));
  }

  /// إدراج رصيد إجازة جديد
  Future<int> insertLeaveBalance(LeaveBalance leaveBalance) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableLeaveBalances,
      leaveBalance.toMap(),
    );
  }

  /// تحديث رصيد إجازة
  Future<int> updateLeaveBalance(LeaveBalance leaveBalance) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableLeaveBalances,
      leaveBalance.toMap(),
      where: 'id = ?',
      whereArgs: [leaveBalance.id],
    );
  }

  /// الحصول على رصيد إجازة الموظف
  Future<LeaveBalance?> getEmployeeLeaveBalance(
      String employeeCode, int leaveTypeId, int year) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveBalances,
      where: 'employee_code = ? AND leave_type_id = ? AND year = ?',
      whereArgs: [employeeCode, leaveTypeId, year],
    );

    if (maps.isNotEmpty) {
      return LeaveBalance.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع أرصدة إجازات الموظف
  Future<List<LeaveBalance>> getEmployeeLeaveBalances(
      String employeeCode, int year) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveBalances,
      where: 'employee_code = ? AND year = ?',
      whereArgs: [employeeCode, year],
      orderBy: 'leave_type_id ASC',
    );

    return List.generate(maps.length, (i) => LeaveBalance.fromMap(maps[i]));
  }

  /// تحديث حالة طلب الإجازة
  Future<int> updateLeaveRequestStatus(int id, LeaveStatus status, 
      {String? approvedBy, String? rejectionReason}) async {
    final db = await _databaseHelper.database;
    
    Map<String, dynamic> updateData = {
      'status': status.name,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (approvedBy != null) {
      updateData['approved_by'] = approvedBy;
    }

    if (rejectionReason != null) {
      updateData['rejection_reason'] = rejectionReason;
    }

    return await db.update(
      DatabaseSchema.tableLeaveRequests,
      updateData,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على إحصائيات الإجازات للموظف
  Future<Map<String, dynamic>> getEmployeeLeaveStatistics(
      String employeeCode, int year) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT 
        lt.name as leave_type_name,
        lb.allocated_days,
        lb.used_days,
        lb.remaining_days,
        COUNT(lr.id) as total_requests,
        SUM(CASE WHEN lr.status = 'approved' THEN lr.days_count ELSE 0 END) as approved_days,
        SUM(CASE WHEN lr.status = 'pending' THEN lr.days_count ELSE 0 END) as pending_days
      FROM ${DatabaseSchema.tableLeaveBalances} lb
      LEFT JOIN ${DatabaseSchema.tableLeaveTypes} lt ON lb.leave_type_id = lt.id
      LEFT JOIN ${DatabaseSchema.tableLeaveRequests} lr ON lb.employee_code = lr.employee_code 
        AND lb.leave_type_id = lr.leave_type_id 
        AND CAST(strftime('%Y', lr.start_date) AS INTEGER) = lb.year
      WHERE lb.employee_code = ? AND lb.year = ?
      GROUP BY lb.id, lt.name
      ORDER BY lt.name ASC
    ''', [employeeCode, year]);

    return {
      'leave_types': result,
      'total_allocated': result.fold(0, (sum, row) => sum + (row['allocated_days'] as int? ?? 0)),
      'total_used': result.fold(0, (sum, row) => sum + (row['used_days'] as int? ?? 0)),
      'total_remaining': result.fold(0, (sum, row) => sum + (row['remaining_days'] as int? ?? 0)),
    };
  }

  /// الحصول على طلبات الإجازة مع تفاصيل الموظف ونوع الإجازة
  Future<List<Map<String, dynamic>>> getLeaveRequestsWithDetails() async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        lr.*,
        e.first_name,
        e.last_name,
        e.middle_name,
        lt.name as leave_type_name,
        lt.is_paid,
        d.name as department_name,
        p.title as position_title
      FROM ${DatabaseSchema.tableLeaveRequests} lr
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON lr.employee_code = e.employee_code
      LEFT JOIN ${DatabaseSchema.tableLeaveTypes} lt ON lr.leave_type_id = lt.id
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      LEFT JOIN ${DatabaseSchema.tablePositions} p ON e.position_id = p.id
      ORDER BY lr.created_at DESC
    ''');
  }

  /// البحث في طلبات الإجازة
  Future<List<Map<String, dynamic>>> searchLeaveRequests(String query) async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        lr.*,
        e.first_name,
        e.last_name,
        e.middle_name,
        lt.name as leave_type_name,
        d.name as department_name
      FROM ${DatabaseSchema.tableLeaveRequests} lr
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON lr.employee_code = e.employee_code
      LEFT JOIN ${DatabaseSchema.tableLeaveTypes} lt ON lr.leave_type_id = lt.id
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      WHERE 
        LOWER(e.first_name) LIKE LOWER(?) OR
        LOWER(e.last_name) LIKE LOWER(?) OR
        LOWER(lr.employee_code) LIKE LOWER(?) OR
        LOWER(lt.name) LIKE LOWER(?) OR
        LOWER(d.name) LIKE LOWER(?)
      ORDER BY lr.created_at DESC
    ''', ['%$query%', '%$query%', '%$query%', '%$query%', '%$query%']);
  }

  /// الحصول على الطلبات المعلقة للموافقة
  Future<List<Map<String, dynamic>>> getPendingLeaveRequests() async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        lr.*,
        e.first_name,
        e.last_name,
        e.middle_name,
        lt.name as leave_type_name,
        d.name as department_name
      FROM ${DatabaseSchema.tableLeaveRequests} lr
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON lr.employee_code = e.employee_code
      LEFT JOIN ${DatabaseSchema.tableLeaveTypes} lt ON lr.leave_type_id = lt.id
      LEFT JOIN ${DatabaseSchema.tableDepartments} d ON e.department_id = d.id
      WHERE lr.status = 'pending'
      ORDER BY lr.created_at ASC
    ''');
  }

  /// التحقق من تضارب الإجازات
  Future<List<LeaveRequest>> getConflictingLeaveRequests(
      String employeeCode, DateTime startDate, DateTime endDate, {int? excludeId}) async {
    final db = await _databaseHelper.database;
    
    String whereClause = '''
      employee_code = ? AND 
      status = 'approved' AND
      ((start_date <= ? AND end_date >= ?) OR
       (start_date <= ? AND end_date >= ?) OR
       (start_date >= ? AND end_date <= ?))
    ''';
    
    List<dynamic> whereArgs = [
      employeeCode,
      startDate.toIso8601String().split('T')[0],
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
    ];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableLeaveRequests,
      where: whereClause,
      whereArgs: whereArgs,
    );

    return List.generate(maps.length, (i) => LeaveRequest.fromMap(maps[i]));
  }

  /// تحديث رصيد الإجازة بعد الموافقة
  Future<void> updateLeaveBalanceAfterApproval(
      String employeeCode, int leaveTypeId, int year, int daysUsed) async {
    final db = await _databaseHelper.database;
    
    await db.rawUpdate('''
      UPDATE ${DatabaseSchema.tableLeaveBalances}
      SET 
        used_days = used_days + ?,
        remaining_days = remaining_days - ?,
        updated_at = ?
      WHERE employee_code = ? AND leave_type_id = ? AND year = ?
    ''', [
      daysUsed,
      daysUsed,
      DateTime.now().toIso8601String(),
      employeeCode,
      leaveTypeId,
      year,
    ]);
  }

  /// استرداد رصيد الإجازة بعد الإلغاء
  Future<void> restoreLeaveBalanceAfterCancellation(
      String employeeCode, int leaveTypeId, int year, int daysToRestore) async {
    final db = await _databaseHelper.database;
    
    await db.rawUpdate('''
      UPDATE ${DatabaseSchema.tableLeaveBalances}
      SET 
        used_days = used_days - ?,
        remaining_days = remaining_days + ?,
        updated_at = ?
      WHERE employee_code = ? AND leave_type_id = ? AND year = ?
    ''', [
      daysToRestore,
      daysToRestore,
      DateTime.now().toIso8601String(),
      employeeCode,
      leaveTypeId,
      year,
    ]);
  }
}
