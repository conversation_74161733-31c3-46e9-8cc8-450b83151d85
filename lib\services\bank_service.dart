/// خدمة إدارة البنوك
/// Bank Management Service for Smart Ledger
library;

import '../models/bank_account.dart';
import '../database/bank_account_dao.dart';
import '../database/bank_transaction_dao.dart';
import '../utils/result.dart';

class BankService {
  static final BankService _instance = BankService._internal();
  factory BankService() => _instance;
  BankService._internal();

  final BankAccountDao _bankAccountDao = BankAccountDao();
  final BankTransactionDao _bankTransactionDao = BankTransactionDao();

  /// Get all bank accounts
  Future<Result<List<BankAccount>>> getAllBankAccounts() async {
    try {
      final accounts = await _bankAccountDao.getAllBankAccounts();
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في جلب الحسابات البنكية: ${e.toString()}');
    }
  }

  /// Get active bank accounts
  Future<Result<List<BankAccount>>> getActiveBankAccounts() async {
    try {
      final accounts = await _bankAccountDao.getActiveBankAccounts();
      return Result.success(accounts);
    } catch (e) {
      return Result.error(
        'خطأ في جلب الحسابات البنكية النشطة: ${e.toString()}',
      );
    }
  }

  /// Get bank account by ID
  Future<Result<BankAccount?>> getBankAccountById(int id) async {
    try {
      final account = await _bankAccountDao.getBankAccountById(id);
      return Result.success(account);
    } catch (e) {
      return Result.error('خطأ في جلب الحساب البنكي: ${e.toString()}');
    }
  }

  /// Create new bank account
  Future<Result<int>> createBankAccount(BankAccount account) async {
    try {
      // Validate account data
      final validation = _validateBankAccount(account);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      final id = await _bankAccountDao.insertBankAccount(account);
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إنشاء الحساب البنكي: ${e.toString()}');
    }
  }

  /// Update bank account
  Future<Result<bool>> updateBankAccount(BankAccount account) async {
    try {
      // Validate account data
      final validation = _validateBankAccount(account);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      final rowsAffected = await _bankAccountDao.updateBankAccount(account);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث الحساب البنكي: ${e.toString()}');
    }
  }

  /// Delete bank account
  Future<Result<bool>> deleteBankAccount(int id) async {
    try {
      final rowsAffected = await _bankAccountDao.deleteBankAccount(id);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في حذف الحساب البنكي: ${e.toString()}');
    }
  }

  /// Create bank transaction
  Future<Result<int>> createBankTransaction(BankTransaction transaction) async {
    try {
      // Validate transaction data
      final validation = _validateBankTransaction(transaction);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      // Generate transaction number if not provided
      if (transaction.transactionNumber.isEmpty) {
        final transactionNumber = await _bankTransactionDao
            .generateTransactionNumber();
        transaction = BankTransaction(
          bankAccountId: transaction.bankAccountId,
          transactionNumber: transactionNumber,
          type: transaction.type,
          status: transaction.status,
          amount: transaction.amount,
          currency: transaction.currency,
          description: transaction.description,
          reference: transaction.reference,
          checkNumber: transaction.checkNumber,
          toBankAccountId: transaction.toBankAccountId,
          journalEntryId: transaction.journalEntryId,
          transactionDate: transaction.transactionDate,
          valueDate: transaction.valueDate,
        );
      }

      final id = await _bankTransactionDao.insertBankTransaction(transaction);

      // Update account balance if transaction is completed
      if (transaction.status == BankTransactionStatus.completed) {
        await _updateAccountBalanceAfterTransaction(transaction);
      }

      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إنشاء المعاملة البنكية: ${e.toString()}');
    }
  }

  /// Complete bank transaction
  Future<Result<bool>> completeBankTransaction(int transactionId) async {
    try {
      final transaction = await _bankTransactionDao.getBankTransactionById(
        transactionId,
      );
      if (transaction == null) {
        return Result.error('المعاملة غير موجودة');
      }

      if (transaction.status != BankTransactionStatus.pending) {
        return Result.error('لا يمكن إكمال هذه المعاملة');
      }

      // Update transaction status
      await _bankTransactionDao.updateTransactionStatus(
        transactionId,
        BankTransactionStatus.completed,
      );

      // Update account balance
      await _updateAccountBalanceAfterTransaction(transaction);

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إكمال المعاملة البنكية: ${e.toString()}');
    }
  }

  /// Cancel bank transaction
  Future<Result<bool>> cancelBankTransaction(int transactionId) async {
    try {
      final transaction = await _bankTransactionDao.getBankTransactionById(
        transactionId,
      );
      if (transaction == null) {
        return Result.error('المعاملة غير موجودة');
      }

      if (transaction.status != BankTransactionStatus.pending) {
        return Result.error('لا يمكن إلغاء هذه المعاملة');
      }

      // Update transaction status
      await _bankTransactionDao.updateTransactionStatus(
        transactionId,
        BankTransactionStatus.cancelled,
      );

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إلغاء المعاملة البنكية: ${e.toString()}');
    }
  }

  /// Get bank transactions by account
  Future<Result<List<BankTransaction>>> getBankTransactionsByAccount(
    int accountId,
  ) async {
    try {
      final transactions = await _bankTransactionDao
          .getBankTransactionsByAccountId(accountId);
      return Result.success(transactions);
    } catch (e) {
      return Result.error('خطأ في جلب معاملات الحساب: ${e.toString()}');
    }
  }

  /// Get account balance summary
  Future<Result<Map<String, dynamic>>> getAccountBalanceSummary() async {
    try {
      final summary = await _bankAccountDao.getBankAccountBalanceSummary();
      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب ملخص الأرصدة: ${e.toString()}');
    }
  }

  /// Search bank accounts
  Future<Result<List<BankAccount>>> searchBankAccounts(String query) async {
    try {
      final accounts = await _bankAccountDao.searchBankAccounts(query);
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في البحث عن الحسابات: ${e.toString()}');
    }
  }

  /// Get overdrawn accounts
  Future<Result<List<BankAccount>>> getOverdrawnAccounts() async {
    try {
      final accounts = await _bankAccountDao.getOverdrawnAccounts();
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في جلب الحسابات المكشوفة: ${e.toString()}');
    }
  }

  /// Validate bank account data
  Result<bool> _validateBankAccount(BankAccount account) {
    if (account.accountNumber.trim().isEmpty) {
      return Result.error('رقم الحساب مطلوب');
    }

    if (account.accountName.trim().isEmpty) {
      return Result.error('اسم الحساب مطلوب');
    }

    if (account.bankName.trim().isEmpty) {
      return Result.error('اسم البنك مطلوب');
    }

    if (account.bankCode.trim().isEmpty) {
      return Result.error('رمز البنك مطلوب');
    }

    if (account.currency.trim().isEmpty) {
      return Result.error('العملة مطلوبة');
    }

    // Validate IBAN format if provided
    if (account.iban != null && account.iban!.isNotEmpty) {
      if (!_isValidIBAN(account.iban!)) {
        return Result.error('رقم الآيبان غير صحيح');
      }
    }

    return Result.success(true);
  }

  /// Validate bank transaction data
  Result<bool> _validateBankTransaction(BankTransaction transaction) {
    if (transaction.amount <= 0) {
      return Result.error('المبلغ يجب أن يكون أكبر من صفر');
    }

    if (transaction.description.trim().isEmpty) {
      return Result.error('وصف المعاملة مطلوب');
    }

    if (transaction.currency.trim().isEmpty) {
      return Result.error('العملة مطلوبة');
    }

    // Validate transfer transactions
    if (transaction.type == BankTransactionType.transfer) {
      if (transaction.toBankAccountId == null) {
        return Result.error('حساب الوجهة مطلوب للتحويل');
      }
      if (transaction.toBankAccountId == transaction.bankAccountId) {
        return Result.error('لا يمكن التحويل إلى نفس الحساب');
      }
    }

    return Result.success(true);
  }

  /// Update account balance after transaction
  Future<void> _updateAccountBalanceAfterTransaction(
    BankTransaction transaction,
  ) async {
    final account = await _bankAccountDao.getBankAccountById(
      transaction.bankAccountId,
    );
    if (account != null) {
      final newBalance = account.currentBalance + transaction.signedAmount;
      await _bankAccountDao.updateBankAccountBalance(account.id!, newBalance);
    }

    // Update destination account for transfers
    if (transaction.type == BankTransactionType.transfer &&
        transaction.toBankAccountId != null) {
      final toAccount = await _bankAccountDao.getBankAccountById(
        transaction.toBankAccountId!,
      );
      if (toAccount != null) {
        final newBalance = toAccount.currentBalance + transaction.amount;
        await _bankAccountDao.updateBankAccountBalance(
          toAccount.id!,
          newBalance,
        );
      }
    }
  }

  /// Validate IBAN format (basic validation)
  bool _isValidIBAN(String iban) {
    // Remove spaces and convert to uppercase
    final cleanIban = iban.replaceAll(' ', '').toUpperCase();

    // Basic length check (15-34 characters)
    if (cleanIban.length < 15 || cleanIban.length > 34) {
      return false;
    }

    // Check if starts with 2 letters followed by 2 digits
    final regex = RegExp(r'^[A-Z]{2}[0-9]{2}[A-Z0-9]+$');
    return regex.hasMatch(cleanIban);
  }
}
