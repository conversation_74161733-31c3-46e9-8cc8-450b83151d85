import '../models/invoice.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class InvoiceDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Create a new invoice with lines
  Future<int> insertInvoice(Invoice invoice) async {
    return await _dbHelper.transaction((txn) async {
      // Insert the invoice
      Map<String, dynamic> invoiceMap = invoice.toMap();
      invoiceMap.remove('id');
      int invoiceId = await txn.insert(
        DatabaseSchema.tableInvoices,
        invoiceMap,
      );

      // Insert the invoice lines
      for (InvoiceLine line in invoice.lines) {
        Map<String, dynamic> lineMap = line.toMap();
        lineMap.remove('id');
        lineMap['invoice_id'] = invoiceId;
        await txn.insert(DatabaseSchema.tableInvoiceLines, lineMap);
      }

      return invoiceId;
    });
  }

  // Get all invoices
  Future<List<Invoice>> getAllInvoices() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Get invoice by ID
  Future<Invoice?> getInvoiceById(int id) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      Invoice invoice = Invoice.fromMap(maps.first);
      invoice.lines = await getInvoiceLines(id);
      return invoice;
    }
    return null;
  }

  // Get invoice by number
  Future<Invoice?> getInvoiceByNumber(String invoiceNumber) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'invoice_number = ?',
      whereArgs: [invoiceNumber],
    );

    if (maps.isNotEmpty) {
      Invoice invoice = Invoice.fromMap(maps.first);
      invoice.lines = await getInvoiceLines(invoice.id!);
      return invoice;
    }
    return null;
  }

  // Get invoice lines for a specific invoice
  Future<List<InvoiceLine>> getInvoiceLines(int invoiceId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoiceLines,
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'line_number ASC',
    );
    return maps.map((map) => InvoiceLine.fromMap(map)).toList();
  }

  // Get invoices by type
  Future<List<Invoice>> getInvoicesByType(InvoiceType type) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'invoice_type = ?',
      whereArgs: [type.value],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Get invoices by customer
  Future<List<Invoice>> getInvoicesByCustomer(int customerId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Get invoices by supplier
  Future<List<Invoice>> getInvoicesBySupplier(int supplierId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'supplier_id = ?',
      whereArgs: [supplierId],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Get invoices by status
  Future<List<Invoice>> getInvoicesByStatus(InvoiceStatus status) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'status = ?',
      whereArgs: [status.value],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Get invoices by date range
  Future<List<Invoice>> getInvoicesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'date >= ? AND date <= ?',
      whereArgs: [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Get customer invoices
  Future<List<Invoice>> getCustomerInvoices(int customerId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'customer_id = ?',
      whereArgs: [customerId],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Get supplier invoices
  Future<List<Invoice>> getSupplierInvoices(int supplierId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'supplier_id = ?',
      whereArgs: [supplierId],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Search invoices
  Future<List<Invoice>> searchInvoices(String query) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'invoice_number LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'date DESC, invoice_number DESC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Update invoice
  Future<int> updateInvoice(Invoice invoice) async {
    return await _dbHelper.transaction((txn) async {
      // Update the invoice
      int result = await txn.update(
        DatabaseSchema.tableInvoices,
        invoice.toMap(),
        where: 'id = ?',
        whereArgs: [invoice.id],
      );

      // Delete existing lines
      await txn.delete(
        DatabaseSchema.tableInvoiceLines,
        where: 'invoice_id = ?',
        whereArgs: [invoice.id],
      );

      // Insert updated lines
      for (InvoiceLine line in invoice.lines) {
        Map<String, dynamic> lineMap = line.toMap();
        lineMap.remove('id');
        lineMap['invoice_id'] = invoice.id;
        await txn.insert(DatabaseSchema.tableInvoiceLines, lineMap);
      }

      return result;
    });
  }

  // Update invoice status
  Future<int> updateInvoiceStatus(int invoiceId, InvoiceStatus status) async {
    return await _dbHelper.update(
      DatabaseSchema.tableInvoices,
      {'status': status.value, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  // Delete invoice
  Future<int> deleteInvoice(int invoiceId) async {
    return await _dbHelper.transaction((txn) async {
      // Delete invoice lines first (due to foreign key)
      await txn.delete(
        DatabaseSchema.tableInvoiceLines,
        where: 'invoice_id = ?',
        whereArgs: [invoiceId],
      );

      // Delete invoice
      return await txn.delete(
        DatabaseSchema.tableInvoices,
        where: 'id = ?',
        whereArgs: [invoiceId],
      );
    });
  }

  // Check if invoice number exists
  Future<bool> isInvoiceNumberExists(
    String invoiceNumber, {
    int? excludeId,
  }) async {
    String whereClause = 'invoice_number = ?';
    List<dynamic> whereArgs = [invoiceNumber];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.isNotEmpty;
  }

  // Get next invoice number by type
  Future<String> getNextInvoiceNumber(InvoiceType type) async {
    String prefix = type == InvoiceType.sales ? 'S' : 'P';

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'invoice_type = ?',
      whereArgs: [type.value],
      orderBy: 'invoice_number DESC',
      limit: 1,
    );

    if (maps.isEmpty) {
      return '${prefix}001';
    }

    String lastNumber = maps.first['invoice_number'] as String;
    // Extract number from invoice number (assuming format like S001, P001, etc.)
    RegExp regExp = RegExp(r'(\d+)$');
    Match? match = regExp.firstMatch(lastNumber);

    if (match != null) {
      int lastNum = int.parse(match.group(1)!);
      int nextNum = lastNum + 1;
      return '$prefix${nextNum.toString().padLeft(3, '0')}';
    }

    return '${prefix}001';
  }

  // Get invoice statistics
  Future<Map<String, dynamic>> getInvoiceStatistics() async {
    final totalResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as total FROM ${DatabaseSchema.tableInvoices}',
    );

    final salesResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as sales FROM ${DatabaseSchema.tableInvoices} WHERE invoice_type = "sales"',
    );

    final purchaseResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as purchase FROM ${DatabaseSchema.tableInvoices} WHERE invoice_type = "purchase"',
    );

    final paidResult = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as paid FROM ${DatabaseSchema.tableInvoices} WHERE status = "paid"',
    );

    return {
      'total': totalResult.first['total'] as int,
      'sales': salesResult.first['sales'] as int,
      'purchase': purchaseResult.first['purchase'] as int,
      'paid': paidResult.first['paid'] as int,
    };
  }

  // Get sales summary
  Future<Map<String, double>> getSalesSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    String whereClause = 'invoice_type = "sales"';
    List<dynamic> whereArgs = [];

    if (startDate != null) {
      whereClause += ' AND date >= ?';
      whereArgs.add(startDate.toIso8601String().split('T')[0]);
    }

    if (endDate != null) {
      whereClause += ' AND date <= ?';
      whereArgs.add(endDate.toIso8601String().split('T')[0]);
    }

    final result = await _dbHelper.rawQuery(
      'SELECT SUM(total_amount) as total_sales, SUM(tax_amount) as total_tax, COUNT(*) as invoice_count FROM ${DatabaseSchema.tableInvoices} WHERE $whereClause',
      whereArgs.isNotEmpty ? whereArgs : null,
    );

    double totalSales = 0.0;
    double totalTax = 0.0;
    int invoiceCount = 0;

    if (result.isNotEmpty) {
      totalSales = (result.first['total_sales'] as num?)?.toDouble() ?? 0.0;
      totalTax = (result.first['total_tax'] as num?)?.toDouble() ?? 0.0;
      invoiceCount = result.first['invoice_count'] as int;
    }

    return {
      'total_sales': totalSales,
      'total_tax': totalTax,
      'net_sales': totalSales - totalTax,
      'invoice_count': invoiceCount.toDouble(),
    };
  }

  // Get overdue invoices
  Future<List<Invoice>> getOverdueInvoices() async {
    final now = DateTime.now();
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'due_date < ? AND status != ? AND status != ?',
      whereArgs: [now.toIso8601String(), 'paid', 'cancelled'],
      orderBy: 'due_date ASC',
    );

    List<Invoice> invoices = [];
    for (Map<String, dynamic> map in maps) {
      Invoice invoice = Invoice.fromMap(map);
      invoice.lines = await getInvoiceLines(invoice.id!);
      invoices.add(invoice);
    }

    return invoices;
  }

  // Record payment for invoice
  Future<void> recordPayment(int invoiceId, double amount) async {
    await _dbHelper.transaction((txn) async {
      // Get current invoice
      final invoiceResult = await txn.query(
        DatabaseSchema.tableInvoices,
        where: 'id = ?',
        whereArgs: [invoiceId],
      );

      if (invoiceResult.isNotEmpty) {
        final invoice = Invoice.fromMap(invoiceResult.first);
        final newPaidAmount = invoice.paidAmount + amount;

        // Determine new status
        String newStatus;
        if (newPaidAmount >= invoice.totalAmount) {
          newStatus = 'paid';
        } else if (newPaidAmount > 0) {
          newStatus = 'partially_paid';
        } else {
          newStatus = invoice.status.value;
        }

        // Update invoice
        await txn.update(
          DatabaseSchema.tableInvoices,
          {
            'paid_amount': newPaidAmount,
            'status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [invoiceId],
        );
      }
    });
  }

  // Get total sales for period
  Future<double> getTotalSales({DateTime? startDate, DateTime? endDate}) async {
    String whereClause = 'invoice_type = ? AND status != ?';
    List<dynamic> whereArgs = ['sales', 'cancelled'];

    if (startDate != null) {
      whereClause += ' AND date >= ?';
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      whereClause += ' AND date <= ?';
      whereArgs.add(endDate.toIso8601String());
    }

    final result = await _dbHelper.rawQuery(
      'SELECT SUM(total_amount) as total FROM ${DatabaseSchema.tableInvoices} WHERE $whereClause',
      whereArgs,
    );

    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  // Get total purchases for period
  Future<double> getTotalPurchases({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    String whereClause = 'invoice_type = ? AND status != ?';
    List<dynamic> whereArgs = ['purchase', 'cancelled'];

    if (startDate != null) {
      whereClause += ' AND date >= ?';
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      whereClause += ' AND date <= ?';
      whereArgs.add(endDate.toIso8601String());
    }

    final result = await _dbHelper.rawQuery(
      'SELECT SUM(total_amount) as total FROM ${DatabaseSchema.tableInvoices} WHERE $whereClause',
      whereArgs,
    );

    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  // Get total outstanding amount
  Future<double> getTotalOutstanding() async {
    final result = await _dbHelper.rawQuery(
      'SELECT SUM(total_amount - paid_amount) as outstanding FROM ${DatabaseSchema.tableInvoices} WHERE status != ? AND status != ?',
      ['paid', 'cancelled'],
    );

    return (result.first['outstanding'] as num?)?.toDouble() ?? 0.0;
  }

  // Get invoices count
  Future<int> getInvoicesCount({
    InvoiceType? type,
    InvoiceStatus? status,
  }) async {
    String whereClause = '1=1';
    List<dynamic> whereArgs = [];

    if (type != null) {
      whereClause += ' AND invoice_type = ?';
      whereArgs.add(type.value);
    }

    if (status != null) {
      whereClause += ' AND status = ?';
      whereArgs.add(status.value);
    }

    final result = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableInvoices} WHERE $whereClause',
      whereArgs,
    );

    return result.first['count'] as int;
  }

  // Cancel invoice
  Future<void> cancelInvoice(int invoiceId) async {
    await _dbHelper.update(
      DatabaseSchema.tableInvoices,
      {'status': 'cancelled', 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }
}
