import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import '../../widgets/beautiful_inputs.dart' as beautiful;
import '../../animations/app_animations.dart';
import '../../widgets/smart_notifications.dart';
import '../../services/search_service.dart';
import '../../models/search_result.dart';
import '../../models/account.dart';
import '../accounts/account_details_screen.dart';
import '../../animations/page_transitions.dart';

class GlobalSearchScreen extends StatefulWidget {
  final String? initialQuery;

  const GlobalSearchScreen({super.key, this.initialQuery});

  @override
  State<GlobalSearchScreen> createState() => _GlobalSearchScreenState();
}

class _GlobalSearchScreenState extends State<GlobalSearchScreen>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final SearchService _searchService = SearchService();

  List<SearchResult> _searchResults = [];
  bool _isLoading = false;
  String _selectedCategory = 'الكل';
  late TabController _tabController;

  final List<String> _categories = [
    'الكل',
    'الحسابات',
    'القيود',
    'العملاء',
    'الموردين',
    'الأصناف',
    'الفواتير',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);

    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
      _performSearch(widget.initialQuery!);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final results = await _searchService.globalSearch(
        query,
        category: _selectedCategory == 'الكل' ? null : _selectedCategory,
      );

      if (results.isSuccess) {
        setState(() {
          _searchResults = results.data ?? [];
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          SmartNotificationManager.showError(
            context,
            title: 'خطأ في البحث',
            message: results.error ?? 'حدث خطأ أثناء البحث',
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        SmartNotificationManager.showError(
          context,
          title: 'خطأ في البحث',
          message: 'حدث خطأ غير متوقع: ${e.toString()}',
        );
      }
    }
  }

  void _onCategoryChanged(int index) {
    setState(() {
      _selectedCategory = _categories[index];
    });

    if (_searchController.text.isNotEmpty) {
      _performSearch(_searchController.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث الشامل'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search Header
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor,
                  AppTheme.primaryColor.withValues(alpha: 0.8),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              children: [
                // Search Field
                Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingMedium),
                  child: StaggeredAnimation(
                    index: 0,
                    child: beautiful.SearchField(
                      controller: _searchController,
                      hintText: 'ابحث في جميع البيانات...',
                      onChanged: _performSearch,
                      onSubmitted: _performSearch,
                      onClear: () {
                        _searchController.clear();
                        setState(() {
                          _searchResults = [];
                        });
                      },
                    ),
                  ),
                ),

                // Category Tabs
                Container(
                  height: 50,
                  margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
                  child: TabBar(
                    controller: _tabController,
                    onTap: _onCategoryChanged,
                    isScrollable: true,
                    indicatorColor: Colors.white,
                    indicatorWeight: 3,
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.white70,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontWeight: FontWeight.normal,
                      fontSize: 14,
                    ),
                    tabs: _categories
                        .map((category) => Tab(text: category))
                        .toList(),
                  ),
                ),
              ],
            ),
          ),

          // Search Results
          Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppTheme.spacingMedium),
            Text('جاري البحث...'),
          ],
        ),
      );
    }

    if (_searchController.text.isEmpty) {
      return _buildEmptyState();
    }

    if (_searchResults.isEmpty) {
      return _buildNoResultsState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final result = _searchResults[index];
        return StaggeredAnimation(
          index: index,
          child: _buildSearchResultCard(result),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_rounded,
            size: 80,
            color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'ابدأ البحث',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'اكتب في حقل البحث للعثور على الحسابات، القيود،\nالعملاء، الموردين، الأصناف والفواتير',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: 80,
            color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'لا توجد نتائج',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'لم يتم العثور على نتائج لـ "${_searchController.text}"',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          ElevatedButton(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _searchResults = [];
              });
            },
            child: const Text('مسح البحث'),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResultCard(SearchResult result) {
    return BeautifulCard(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getCategoryColor(result.category),
          child: Icon(
            _getCategoryIcon(result.category),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          result.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (result.subtitle != null) ...[
              Text(result.subtitle!),
              const SizedBox(height: 4),
            ],
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(
                      result.category,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    result.category,
                    style: TextStyle(
                      color: _getCategoryColor(result.category),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (result.additionalInfo != null) ...[
                  const SizedBox(width: 8),
                  Text(
                    result.additionalInfo!,
                    style: TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _onResultTap(result),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'الحسابات':
        return const Color(0xFF2196F3);
      case 'القيود':
        return const Color(0xFF4CAF50);
      case 'العملاء':
        return const Color(0xFFFF9800);
      case 'الموردين':
        return const Color(0xFF9C27B0);
      case 'الأصناف':
        return const Color(0xFFE91E63);
      case 'الفواتير':
        return const Color(0xFF00BCD4);
      default:
        return AppTheme.primaryColor;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'الحسابات':
        return Icons.account_balance;
      case 'القيود':
        return Icons.receipt_long;
      case 'العملاء':
        return Icons.people;
      case 'الموردين':
        return Icons.business;
      case 'الأصناف':
        return Icons.inventory;
      case 'الفواتير':
        return Icons.description;
      default:
        return Icons.search;
    }
  }

  void _onResultTap(SearchResult result) {
    // Navigate to the appropriate screen based on result type
    switch (result.category) {
      case 'الحسابات':
        _navigateToAccountDetails(result);
        break;
      case 'القيود':
        _navigateToJournalEntryDetails(result);
        break;
      case 'العملاء':
        _navigateToCustomerDetails(result);
        break;
      case 'الموردين':
        _navigateToSupplierDetails(result);
        break;
      case 'الأصناف':
        _navigateToItemDetails(result);
        break;
      case 'الفواتير':
        _navigateToInvoiceDetails(result);
        break;
      default:
        SmartNotificationManager.showInfo(
          context,
          title: 'فتح ${result.category}',
          message: 'جاري فتح ${result.title}...',
        );
    }
  }

  /// Navigate to account details screen
  void _navigateToAccountDetails(SearchResult result) async {
    try {
      // Create Account object from search result data
      final accountData = result.data;
      if (accountData != null) {
        final account = Account.fromMap(accountData);

        await Navigator.push(
          context,
          CustomPageTransitions.slideFromRight(
            AccountDetailsScreen(account: account),
          ),
        );
      } else {
        if (!mounted) return;
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: 'لا يمكن فتح تفاصيل الحساب',
        );
      }
    } catch (e) {
      if (!mounted) return;
      SmartNotificationManager.showError(
        context,
        title: 'خطأ',
        message: 'حدث خطأ أثناء فتح تفاصيل الحساب: ${e.toString()}',
      );
    }
  }

  /// Navigate to journal entry details
  void _navigateToJournalEntryDetails(SearchResult result) {
    _showDetailDialog(
      title: 'تفاصيل القيد المحاسبي',
      content: _buildJournalEntryDetails(result),
      icon: Icons.receipt_long,
      color: const Color(0xFF4CAF50),
    );
  }

  /// Navigate to customer details
  void _navigateToCustomerDetails(SearchResult result) {
    _showDetailDialog(
      title: 'تفاصيل العميل',
      content: _buildCustomerDetails(result),
      icon: Icons.people,
      color: const Color(0xFFFF9800),
    );
  }

  /// Navigate to supplier details
  void _navigateToSupplierDetails(SearchResult result) {
    _showDetailDialog(
      title: 'تفاصيل المورد',
      content: _buildSupplierDetails(result),
      icon: Icons.business,
      color: const Color(0xFF9C27B0),
    );
  }

  /// Navigate to item details
  void _navigateToItemDetails(SearchResult result) {
    _showDetailDialog(
      title: 'تفاصيل الصنف',
      content: _buildItemDetails(result),
      icon: Icons.inventory,
      color: const Color(0xFFE91E63),
    );
  }

  /// Navigate to invoice details
  void _navigateToInvoiceDetails(SearchResult result) {
    _showDetailDialog(
      title: 'تفاصيل الفاتورة',
      content: _buildInvoiceDetails(result),
      icon: Icons.description,
      color: const Color(0xFF00BCD4),
    );
  }

  /// Show detail dialog with custom content
  void _showDetailDialog({
    required String title,
    required Widget content,
    required IconData icon,
    required Color color,
  }) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.9),
                color.withValues(alpha: 0.7),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingLarge),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(icon, color: Colors.white, size: 24),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Content
              Flexible(
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.fromLTRB(
                    AppTheme.spacingLarge,
                    0,
                    AppTheme.spacingLarge,
                    AppTheme.spacingLarge,
                  ),
                  padding: const EdgeInsets.all(AppTheme.spacingLarge),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: SingleChildScrollView(child: content),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build customer details widget
  Widget _buildCustomerDetails(SearchResult result) {
    final data = result.data ?? {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('الاسم', data['name']?.toString() ?? 'غير محدد'),
        _buildDetailRow('الرمز', data['code']?.toString() ?? 'غير محدد'),
        _buildDetailRow(
          'البريد الإلكتروني',
          data['email']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('الهاتف', data['phone']?.toString() ?? 'غير محدد'),
        _buildDetailRow('العنوان', data['address']?.toString() ?? 'غير محدد'),
        _buildDetailRow(
          'الرقم الضريبي',
          data['tax_number']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('حد الائتمان', '${data['credit_limit'] ?? 0} ر.س'),
        _buildDetailRow('الرصيد الحالي', '${data['current_balance'] ?? 0} ر.س'),
        _buildDetailRow(
          'الحالة',
          (data['is_active'] == 1 || data['is_active'] == true)
              ? 'نشط'
              : 'غير نشط',
        ),
      ],
    );
  }

  /// Build supplier details widget
  Widget _buildSupplierDetails(SearchResult result) {
    final data = result.data ?? {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('الاسم', data['name']?.toString() ?? 'غير محدد'),
        _buildDetailRow('الرمز', data['code']?.toString() ?? 'غير محدد'),
        _buildDetailRow(
          'البريد الإلكتروني',
          data['email']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('الهاتف', data['phone']?.toString() ?? 'غير محدد'),
        _buildDetailRow('العنوان', data['address']?.toString() ?? 'غير محدد'),
        _buildDetailRow(
          'الرقم الضريبي',
          data['tax_number']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('الرصيد الحالي', '${data['current_balance'] ?? 0} ر.س'),
        _buildDetailRow(
          'الحالة',
          (data['is_active'] == 1 || data['is_active'] == true)
              ? 'نشط'
              : 'غير نشط',
        ),
      ],
    );
  }

  /// Build item details widget
  Widget _buildItemDetails(SearchResult result) {
    final data = result.data ?? {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('الاسم', data['name']?.toString() ?? 'غير محدد'),
        _buildDetailRow('الرمز', data['code']?.toString() ?? 'غير محدد'),
        _buildDetailRow('الوصف', data['description']?.toString() ?? 'غير محدد'),
        _buildDetailRow('الوحدة', data['unit']?.toString() ?? 'غير محدد'),
        _buildDetailRow('سعر البيع', '${data['selling_price'] ?? 0} ر.س'),
        _buildDetailRow('سعر الشراء', '${data['purchase_price'] ?? 0} ر.س'),
        _buildDetailRow(
          'الكمية المتاحة',
          data['quantity_on_hand']?.toString() ?? '0',
        ),
        _buildDetailRow(
          'الحد الأدنى',
          data['minimum_stock']?.toString() ?? '0',
        ),
        _buildDetailRow(
          'الحالة',
          (data['is_active'] == 1 || data['is_active'] == true)
              ? 'نشط'
              : 'غير نشط',
        ),
      ],
    );
  }

  /// Build invoice details widget
  Widget _buildInvoiceDetails(SearchResult result) {
    final data = result.data ?? {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow(
          'رقم الفاتورة',
          data['invoice_number']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('التاريخ', data['date']?.toString() ?? 'غير محدد'),
        _buildDetailRow(
          'العميل/المورد',
          data['customer_name']?.toString() ??
              data['supplier_name']?.toString() ??
              'غير محدد',
        ),
        _buildDetailRow(
          'نوع الفاتورة',
          data['invoice_type']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('المبلغ الإجمالي', '${data['total_amount'] ?? 0} ر.س'),
        _buildDetailRow('المبلغ المدفوع', '${data['paid_amount'] ?? 0} ر.س'),
        _buildDetailRow(
          'المبلغ المتبقي',
          '${(data['total_amount'] ?? 0) - (data['paid_amount'] ?? 0)} ر.س',
        ),
        _buildDetailRow(
          'الملاحظات',
          data['notes']?.toString() ?? 'لا توجد ملاحظات',
        ),
        _buildDetailRow('الحالة', data['status']?.toString() ?? 'غير محدد'),
      ],
    );
  }

  /// Build journal entry details widget
  Widget _buildJournalEntryDetails(SearchResult result) {
    final data = result.data ?? {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow(
          'رقم القيد',
          data['entry_number']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('التاريخ', data['date']?.toString() ?? 'غير محدد'),
        _buildDetailRow('الوصف', data['description']?.toString() ?? 'غير محدد'),
        _buildDetailRow('المبلغ الإجمالي', '${data['total_amount'] ?? 0} ر.س'),
        _buildDetailRow('المرجع', data['reference']?.toString() ?? 'غير محدد'),
        _buildDetailRow(
          'نوع القيد',
          data['entry_type']?.toString() ?? 'غير محدد',
        ),
        _buildDetailRow('الحالة', data['status']?.toString() ?? 'غير محدد'),
      ],
    );
  }

  /// Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: AppTheme.textPrimaryColor),
            ),
          ),
        ],
      ),
    );
  }
}
