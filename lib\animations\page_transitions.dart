// ملف انتقالات الصفحات المخصصة
// هذا الملف يحتوي على انتقالات جميلة ومتقدمة بين الصفحات
// لجعل تجربة المستخدم سلسة وممتعة

import 'package:flutter/material.dart';

/// فئة تحتوي على جميع انتقالات الصفحات المخصصة
/// Custom page transitions class
class CustomPageTransitions {
  /// انتقال الانزلاق من اليمين (مناسب للعربية)
  /// Slide transition from right (suitable for Arabic)
  static PageRouteBuilder slideFromRight(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // تعريف نقطة البداية والنهاية للانزلاق
        const begin = Offset(1.0, 0.0); // من اليمين
        const end = Offset.zero; // إلى المركز
        const curve = Curves.easeInOutCubic;

        // إنشاء حركة الانزلاق
        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        // إنشاء حركة التلاشي
        var fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
          ),
        );

        return SlideTransition(
          position: offsetAnimation,
          child: FadeTransition(opacity: fadeAnimation, child: child),
        );
      },
    );
  }

  /// انتقال التكبير مع التلاشي
  /// Scale transition with fade
  static PageRouteBuilder scaleTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // حركة التكبير
        var scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
          CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
        );

        // حركة التلاشي
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return ScaleTransition(
          scale: scaleAnimation,
          child: FadeTransition(opacity: fadeAnimation, child: child),
        );
      },
    );
  }

  /// انتقال الدوران مع التكبير
  /// Rotation transition with scale
  static PageRouteBuilder rotationTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 600),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // حركة الدوران
        var rotationAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.elasticOut));

        // حركة التكبير
        var scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
        );

        return RotationTransition(
          turns: rotationAnimation,
          child: ScaleTransition(scale: scaleAnimation, child: child),
        );
      },
    );
  }

  /// انتقال الانزلاق من الأسفل
  /// Slide transition from bottom
  static PageRouteBuilder slideFromBottom(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 350),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0); // من الأسفل
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  /// انتقال مخصص للحوارات
  /// Custom transition for dialogs
  static PageRouteBuilder dialogTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      barrierDismissible: true,
      barrierColor: Colors.black54,
      opaque: false,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // حركة التكبير للحوار
        var scaleAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(
          CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
        );

        // حركة التلاشي للخلفية
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(scale: scaleAnimation, child: child),
        );
      },
    );
  }
}

/// ويدجت مخصص لانتقال الصفحات مع تأثيرات متقدمة
/// Custom page transition widget with advanced effects
class AnimatedPageRoute<T> extends PageRoute<T> {
  final Widget child;
  final Duration _transitionDuration;
  final Duration _reverseTransitionDuration;
  final Curve curve;
  final PageTransitionType transitionType;

  AnimatedPageRoute({
    required this.child,
    Duration transitionDuration = const Duration(milliseconds: 350),
    Duration reverseTransitionDuration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.transitionType = PageTransitionType.slideFromRight,
    super.settings,
  }) : _transitionDuration = transitionDuration,
       _reverseTransitionDuration = reverseTransitionDuration;

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => _transitionDuration;

  @override
  Duration get reverseTransitionDuration => _reverseTransitionDuration;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return child;
  }

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    switch (transitionType) {
      case PageTransitionType.slideFromRight:
        return _buildSlideTransition(animation, child, const Offset(1.0, 0.0));
      case PageTransitionType.slideFromLeft:
        return _buildSlideTransition(animation, child, const Offset(-1.0, 0.0));
      case PageTransitionType.slideFromBottom:
        return _buildSlideTransition(animation, child, const Offset(0.0, 1.0));
      case PageTransitionType.slideFromTop:
        return _buildSlideTransition(animation, child, const Offset(0.0, -1.0));
      case PageTransitionType.scale:
        return _buildScaleTransition(animation, child);
      case PageTransitionType.fade:
        return _buildFadeTransition(animation, child);
      case PageTransitionType.rotation:
        return _buildRotationTransition(animation, child);
    }
  }

  Widget _buildSlideTransition(
    Animation<double> animation,
    Widget child,
    Offset begin,
  ) {
    var tween = Tween(
      begin: begin,
      end: Offset.zero,
    ).chain(CurveTween(curve: curve));
    var offsetAnimation = animation.drive(tween);

    return SlideTransition(position: offsetAnimation, child: child);
  }

  Widget _buildScaleTransition(Animation<double> animation, Widget child) {
    var scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animation, curve: curve));

    return ScaleTransition(scale: scaleAnimation, child: child);
  }

  Widget _buildFadeTransition(Animation<double> animation, Widget child) {
    return FadeTransition(opacity: animation, child: child);
  }

  Widget _buildRotationTransition(Animation<double> animation, Widget child) {
    var rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animation, curve: curve));

    return RotationTransition(turns: rotationAnimation, child: child);
  }
}

/// أنواع انتقالات الصفحات
/// Page transition types
enum PageTransitionType {
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  slideFromTop,
  scale,
  fade,
  rotation,
}
