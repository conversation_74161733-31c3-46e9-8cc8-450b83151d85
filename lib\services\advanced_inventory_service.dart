/// خدمة إدارة المخزون المتقدمة
/// Advanced Inventory Management Service for Smart Ledger
library;

import 'dart:developer' as developer;
import '../utils/result.dart';
import '../models/stock_movement.dart';
import '../models/periodic_inventory.dart';
import '../models/cost_calculation.dart';
import '../models/inventory_reports.dart' as reports;
import '../models/journal_entry.dart';
import '../services/warehouse_service.dart';
import '../services/cost_calculation_service.dart';
import '../services/periodic_inventory_service.dart';
import '../services/journal_entry_service.dart';
import '../services/item_service.dart' as item_service;
import '../database/stock_balance_dao.dart';
import '../database/stock_movement_dao.dart';

/// خدمة إدارة المخزون المتقدمة
class AdvancedInventoryService {
  static final AdvancedInventoryService _instance =
      AdvancedInventoryService._internal();
  factory AdvancedInventoryService() => _instance;
  AdvancedInventoryService._internal();

  // Services
  final WarehouseService _warehouseService = WarehouseService();
  final CostCalculationService _costService = CostCalculationService();
  final PeriodicInventoryService _inventoryService = PeriodicInventoryService();
  final JournalEntryService _journalService = JournalEntryService();
  final item_service.ItemService _itemService = item_service.ItemService();

  // DAOs
  final StockBalanceDao _balanceDao = StockBalanceDao();
  final StockMovementDao _movementDao = StockMovementDao();

  // ==================== إدارة المخزون المتقدمة ====================

  /// إنشاء حركة مخزون مع التكامل الكامل
  Future<Result<StockMovement>> createAdvancedStockMovement({
    required StockMovement movement,
    bool autoCalculateCost = true,
    bool createJournalEntry = true,
    bool validateStock = true,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult = await _validateStockMovement(movement);
      if (!validationResult.isSuccess) {
        return Result.error(validationResult.error!);
      }

      // التحقق من توفر المخزون (للحركات الصادرة)
      if (validateStock && movement.isOutbound) {
        final stockCheck = await _checkStockAvailability(movement);
        if (!stockCheck.isSuccess) {
          return Result.error(stockCheck.error!);
        }
      }

      // حساب التكلفة تلقائياً
      if (autoCalculateCost && movement.isOutbound) {
        final costResult = await _calculateMovementCost(movement);
        if (costResult.isSuccess) {
          movement = movement.copyWith(
            unitCost: costResult.data!.averageCost,
            totalCost: costResult.data!.totalCost,
          );
        }
      }

      // إنشاء الحركة
      final movementResult = await _warehouseService.addStockMovement(movement);
      if (!movementResult.isSuccess) {
        return Result.error(movementResult.error!);
      }

      // الحصول على الحركة المحدثة
      final createdMovement = await _movementDao.getMovementById(
        movementResult.data!,
      );
      if (createdMovement == null) {
        return Result.error('فشل في استرجاع الحركة المنشأة');
      }

      // إنشاء قيد يومي
      if (createJournalEntry) {
        await _createInventoryJournalEntry(createdMovement);
      }

      // تحديث طبقات التكلفة
      await _costService.updateCostLayersAfterMovement(createdMovement);

      return Result.success(createdMovement);
    } catch (e) {
      return Result.error('خطأ في إنشاء حركة المخزون: ${e.toString()}');
    }
  }

  /// نقل مخزون متقدم بين المواقع
  Future<Result<List<StockMovement>>> transferStockAdvanced({
    required int itemId,
    required int fromWarehouseId,
    required int toWarehouseId,
    int? fromLocationId,
    int? toLocationId,
    required double quantity,
    String? batchNumber,
    String? notes,
    String? referenceDocument,
  }) async {
    try {
      // التحقق من توفر المخزون في المصدر
      final stockCheck = await _checkStockAvailability(
        StockMovement(
          documentNumber: 'TEMP',
          type: MovementType.transfer,
          reason: MovementReason.transfer,
          itemId: itemId,
          warehouseId: fromWarehouseId,
          locationId: fromLocationId,
          quantity: quantity,
          batchNumber: batchNumber,
        ),
      );

      if (!stockCheck.isSuccess) {
        return Result.error(stockCheck.error!);
      }

      // حساب التكلفة
      final costResult = await _costService.calculateWeightedAverageCost(
        itemId: itemId,
        warehouseId: fromWarehouseId,
        locationId: fromLocationId,
        quantity: quantity,
        batchNumber: batchNumber,
      );

      if (!costResult.isSuccess) {
        return Result.error('فشل في حساب التكلفة: ${costResult.error}');
      }

      final unitCost = costResult.data!.averageCost;
      final totalCost = costResult.data!.totalCost;

      // إنشاء حركة الصرف
      final outMovement = StockMovement(
        documentNumber: 'TRF-OUT-${DateTime.now().millisecondsSinceEpoch}',
        type: MovementType.transfer,
        reason: MovementReason.transfer,
        itemId: itemId,
        warehouseId: fromWarehouseId,
        locationId: fromLocationId,
        toWarehouseId: toWarehouseId,
        toLocationId: toLocationId,
        quantity: quantity,
        unitCost: unitCost,
        totalCost: totalCost,
        batchNumber: batchNumber,
        notes: notes,
        referenceDocument: referenceDocument,
      );

      // إنشاء حركة الاستلام
      final inMovement = StockMovement(
        documentNumber: 'TRF-IN-${DateTime.now().millisecondsSinceEpoch}',
        type: MovementType.receipt,
        reason: MovementReason.transfer,
        itemId: itemId,
        warehouseId: toWarehouseId,
        locationId: toLocationId,
        quantity: quantity,
        unitCost: unitCost,
        totalCost: totalCost,
        batchNumber: batchNumber,
        notes: notes,
        referenceDocument: referenceDocument,
      );

      // تنفيذ الحركات
      final outResult = await createAdvancedStockMovement(
        movement: outMovement,
        autoCalculateCost: false,
        createJournalEntry: true,
        validateStock: true,
      );

      if (!outResult.isSuccess) {
        return Result.error('فشل في حركة الصرف: ${outResult.error}');
      }

      final inResult = await createAdvancedStockMovement(
        movement: inMovement,
        autoCalculateCost: false,
        createJournalEntry: true,
        validateStock: false,
      );

      if (!inResult.isSuccess) {
        return Result.error('فشل في حركة الاستلام: ${inResult.error}');
      }

      return Result.success([outResult.data!, inResult.data!]);
    } catch (e) {
      return Result.error('خطأ في نقل المخزون: ${e.toString()}');
    }
  }

  /// تسوية مخزون متقدمة مع الجرد
  Future<Result<StockMovement>> performAdvancedStockAdjustment({
    required int itemId,
    required int warehouseId,
    int? locationId,
    required double systemQuantity,
    required double countedQuantity,
    String? batchNumber,
    String? notes,
    String? referenceDocument,
  }) async {
    try {
      final variance = countedQuantity - systemQuantity;
      if (variance == 0) {
        return Result.error('لا توجد فروقات للتسوية');
      }

      // تحديد نوع الحركة
      final movementType = variance > 0
          ? MovementType.found
          : MovementType.loss;
      final movementReason = MovementReason.adjustment;

      // حساب التكلفة
      double unitCost = 0.0;
      if (variance < 0) {
        // للنقص، نحسب التكلفة من المخزون الموجود
        final costResult = await _costService.calculateWeightedAverageCost(
          itemId: itemId,
          warehouseId: warehouseId,
          locationId: locationId,
          quantity: variance.abs(),
          batchNumber: batchNumber,
        );
        if (costResult.isSuccess) {
          unitCost = costResult.data!.averageCost;
        }
      } else {
        // للزيادة، نستخدم آخر تكلفة شراء
        final itemResult = await _itemService.getItemById(itemId);
        unitCost = itemResult.isSuccess ? itemResult.data!.costPrice : 0.0;
      }

      // إنشاء حركة التسوية
      final adjustmentMovement = StockMovement(
        documentNumber: 'ADJ-${DateTime.now().millisecondsSinceEpoch}',
        type: movementType,
        reason: movementReason,
        itemId: itemId,
        warehouseId: warehouseId,
        locationId: locationId,
        quantity: variance.abs(),
        unitCost: unitCost,
        totalCost: variance.abs() * unitCost,
        batchNumber: batchNumber,
        notes: notes ?? 'تسوية مخزون - الفرق: $variance',
        referenceDocument: referenceDocument,
      );

      return await createAdvancedStockMovement(
        movement: adjustmentMovement,
        autoCalculateCost: false,
        createJournalEntry: true,
        validateStock: false,
      );
    } catch (e) {
      return Result.error('خطأ في تسوية المخزون: ${e.toString()}');
    }
  }

  /// إنشاء جرد دوري متقدم
  Future<Result<PeriodicInventory>> createAdvancedPeriodicInventory({
    required String name,
    required List<int> warehouseIds,
    List<int>? itemIds,
    DateTime? scheduledDate,
    String? notes,
  }) async {
    try {
      final inventory = PeriodicInventory(
        inventoryNumber: 'INV-${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: notes,
        type: InventoryType.full,
        status: InventoryStatus.planned,
        plannedDate: scheduledDate ?? DateTime.now(),
        notes: notes,
      );

      final result = await _inventoryService.createInventory(inventory);
      if (!result.isSuccess) {
        return Result.error(result.error!);
      }

      // إضافة الأصناف للجرد
      final inventoryId = result.data!;
      await _addItemsToInventory(inventoryId, warehouseIds, itemIds);

      return Result.success(inventory.copyWith(id: inventoryId));
    } catch (e) {
      return Result.error('خطأ في إنشاء الجرد: ${e.toString()}');
    }
  }

  // ==================== التقارير والتحليلات ====================

  /// تقرير حالة المخزون
  Future<Result<reports.InventoryStatusReport>> getInventoryStatusReport({
    List<int>? warehouseIds,
    List<int>? itemIds,
    List<String>? categories,
  }) async {
    try {
      // الحصول على أرصدة المخزون
      final balances = await _balanceDao.getAllBalances();

      // تصفية البيانات حسب المعايير
      final filteredBalances = balances.where((balance) {
        if (warehouseIds != null &&
            !warehouseIds.contains(balance.warehouseId)) {
          return false;
        }
        if (itemIds != null && !itemIds.contains(balance.itemId)) {
          return false;
        }
        return true;
      }).toList();

      // إنشاء عناصر التقرير
      final items = <reports.InventoryStatusItem>[];
      for (final balance in filteredBalances) {
        final itemResult = await _itemService.getItemById(balance.itemId);
        if (itemResult.isSuccess) {
          final item = itemResult.data!;
          final warehouseResult = await _warehouseService.getWarehouseById(
            balance.warehouseId,
          );
          final warehouse = warehouseResult.isSuccess
              ? warehouseResult.data
              : null;

          // تحديد حالة المخزون
          reports.StockStatus status;
          if (balance.quantity <= 0) {
            status = reports.StockStatus.outOfStock;
          } else if (balance.quantity <= (item.minStockLevel ?? 0)) {
            status = reports.StockStatus.lowStock;
          } else if (balance.quantity >=
              (item.maxStockLevel ?? double.infinity)) {
            status = reports.StockStatus.overStock;
          } else {
            status = reports.StockStatus.normal;
          }

          items.add(
            reports.InventoryStatusItem(
              itemId: item.id!,
              itemCode: item.code,
              itemName: item.name,
              category: item.category ?? 'غير محدد',
              unit: item.unit,
              currentStock: balance.quantity,
              minStockLevel: item.minStockLevel ?? 0,
              maxStockLevel: item.maxStockLevel ?? 0,
              averageCost: balance.averageCost,
              totalValue: balance.quantity * balance.averageCost,
              warehouseName: warehouse?.name ?? 'غير محدد',
              lastMovementDate: balance.lastMovementDate,
            ),
          );
        }
      }

      final report = reports.InventoryStatusReport(
        items: items,
        totalItems: items.length,
        totalValue: items.fold(0.0, (sum, item) => sum + item.totalValue),
        lowStockCount: items
            .where((item) => item.status == reports.StockStatus.lowStock)
            .length,
        outOfStockCount: items
            .where((item) => item.status == reports.StockStatus.outOfStock)
            .length,
        generatedAt: DateTime.now(),
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء تقرير حالة المخزون: ${e.toString()}');
    }
  }

  /// تقرير حركات المخزون
  Future<Result<reports.StockMovementReport>> getStockMovementReport({
    required DateTime fromDate,
    required DateTime toDate,
    List<int>? warehouseIds,
    List<int>? itemIds,
    List<MovementType>? movementTypes,
  }) async {
    try {
      // الحصول على الحركات
      final movements = await _movementDao.getMovementsByDateRange(
        fromDate,
        toDate,
      );

      // تصفية البيانات
      final filteredMovements = movements.where((movement) {
        if (warehouseIds != null &&
            !warehouseIds.contains(movement.warehouseId)) {
          return false;
        }
        if (itemIds != null && !itemIds.contains(movement.itemId)) {
          return false;
        }
        if (movementTypes != null && !movementTypes.contains(movement.type)) {
          return false;
        }
        return true;
      }).toList();

      // تجميع البيانات
      final summaryMap = <String, reports.MovementSummary>{};
      for (final movement in filteredMovements) {
        final key = '${movement.itemId}_${movement.warehouseId}';
        if (summaryMap.containsKey(key)) {
          final existing = summaryMap[key]!;
          if (movement.type == MovementType.receipt ||
              movement.type == MovementType.found) {
            existing.totalInQuantity += movement.quantity;
            existing.totalInValue += movement.totalCost;
          } else {
            existing.totalOutQuantity += movement.quantity;
            existing.totalOutValue += movement.totalCost;
          }
        } else {
          final itemResult = await _itemService.getItemById(movement.itemId);
          final warehouseResult = await _warehouseService.getWarehouseById(
            movement.warehouseId,
          );
          final warehouse = warehouseResult.isSuccess
              ? warehouseResult.data
              : null;

          summaryMap[key] = reports.MovementSummary(
            itemId: movement.itemId,
            itemCode: itemResult.isSuccess ? itemResult.data!.code : 'غير محدد',
            itemName: itemResult.isSuccess ? itemResult.data!.name : 'غير محدد',
            warehouseId: movement.warehouseId,
            warehouseName: warehouse?.name ?? 'غير محدد',
            totalInQuantity:
                movement.type == MovementType.receipt ||
                    movement.type == MovementType.found
                ? movement.quantity
                : 0,
            totalOutQuantity:
                movement.type == MovementType.issue ||
                    movement.type == MovementType.loss
                ? movement.quantity
                : 0,
            totalInValue:
                movement.type == MovementType.receipt ||
                    movement.type == MovementType.found
                ? movement.totalCost
                : 0,
            totalOutValue:
                movement.type == MovementType.issue ||
                    movement.type == MovementType.loss
                ? movement.totalCost
                : 0,
          );
        }
      }

      final totalInValue = summaryMap.values.fold(
        0.0,
        (sum, s) => sum + s.totalInValue,
      );
      final totalOutValue = summaryMap.values.fold(
        0.0,
        (sum, s) => sum + s.totalOutValue,
      );

      final report = reports.StockMovementReport(
        fromDate: fromDate,
        toDate: toDate,
        movements: filteredMovements,
        summary: summaryMap.values.toList(),
        totalInValue: totalInValue,
        totalOutValue: totalOutValue,
        generatedAt: DateTime.now(),
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء تقرير حركات المخزون: ${e.toString()}');
    }
  }

  // ==================== الطرق المساعدة ====================

  /// التحقق من صحة حركة المخزون
  Future<Result<bool>> _validateStockMovement(StockMovement movement) async {
    try {
      // التحقق من وجود الصنف
      final itemResult = await _itemService.getItemById(movement.itemId);
      if (!itemResult.isSuccess) {
        return Result.error('الصنف غير موجود');
      }
      final item = itemResult.data!;

      // التحقق من وجود المستودع
      final warehouseResult = await _warehouseService.getWarehouseById(
        movement.warehouseId,
      );
      if (!warehouseResult.isSuccess) {
        return Result.error('المستودع غير موجود');
      }

      // التحقق من الكمية
      if (movement.quantity <= 0) {
        return Result.error('الكمية يجب أن تكون أكبر من الصفر');
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في التحقق من صحة البيانات: ${e.toString()}');
    }
  }

  /// التحقق من توفر المخزون
  Future<Result<bool>> _checkStockAvailability(StockMovement movement) async {
    try {
      final balance = await _balanceDao.getBalance(
        movement.itemId,
        movement.warehouseId,
        locationId: movement.locationId,
      );

      if (balance == null || balance.quantity < movement.quantity) {
        return Result.error('المخزون غير كافي');
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في التحقق من توفر المخزون: ${e.toString()}');
    }
  }

  /// حساب تكلفة الحركة
  Future<Result<CostCalculationResult>> _calculateMovementCost(
    StockMovement movement,
  ) async {
    try {
      return await _costService.calculateWeightedAverageCost(
        itemId: movement.itemId,
        warehouseId: movement.warehouseId,
        locationId: movement.locationId,
        quantity: movement.quantity,
        batchNumber: movement.batchNumber,
      );
    } catch (e) {
      return Result.error('خطأ في حساب التكلفة: ${e.toString()}');
    }
  }

  /// إنشاء قيد يومي للمخزون
  Future<void> _createInventoryJournalEntry(StockMovement movement) async {
    try {
      // تحديد الحسابات المحاسبية حسب نوع الحركة
      int? inventoryAccountId;
      int? expenseAccountId;
      int? revenueAccountId;

      // الحصول على معلومات الصنف
      final itemResult = await _itemService.getItemById(movement.itemId);
      if (!itemResult.isSuccess) return;
      final item = itemResult.data!;

      // تحديد الحسابات حسب نوع الحركة
      switch (movement.type) {
        case MovementType.receipt:
          // استلام مخزون: مدين المخزون، دائن حساب المشتريات أو الموردين
          inventoryAccountId = 1301; // حساب المخزون الافتراضي
          expenseAccountId = 5101; // حساب المشتريات
          break;
        case MovementType.issue:
          // صرف مخزون: مدين تكلفة البضاعة المباعة، دائن المخزون
          expenseAccountId = 5201; // حساب تكلفة البضاعة المباعة
          inventoryAccountId = 1301; // حساب المخزون
          break;
        case MovementType.transfer:
          // نقل مخزون: لا يحتاج قيد محاسبي (نقل داخلي)
          return;
        case MovementType.found:
          // عجز مخزون: مدين المخزون، دائن حساب الأرباح والخسائر
          inventoryAccountId = 1301;
          revenueAccountId = 4901; // حساب إيرادات متنوعة
          break;
        case MovementType.adjustment:
          // تسوية مخزون: حسب طبيعة التسوية (زيادة أو نقص)
          inventoryAccountId = 1301; // حساب المخزون
          if (movement.quantity > 0) {
            // زيادة مخزون
            revenueAccountId = 4901; // حساب إيرادات متنوعة
          } else {
            // نقص مخزون
            expenseAccountId = 5901; // حساب خسائر متنوعة
          }
          break;
        case MovementType.return_:
          // مرتجع مخزون: عكس العملية الأصلية
          inventoryAccountId = 1301; // حساب المخزون
          expenseAccountId = 5101; // حساب المشتريات (للمرتجعات الواردة)
          break;
        case MovementType.damage:
          // مخزون تالف: مدين حساب الخسائر، دائن المخزون
          expenseAccountId = 5901; // حساب خسائر متنوعة
          inventoryAccountId = 1301;
          break;
        case MovementType.loss:
          // فقدان مخزون: مدين حساب الخسائر، دائن المخزون
          expenseAccountId = 5901; // حساب خسائر متنوعة
          inventoryAccountId = 1301;
          break;
      }

      if ((expenseAccountId == null && revenueAccountId == null)) {
        return; // لا يمكن إنشاء القيد بدون حسابات محددة
      }

      // إنشاء القيد المحاسبي
      final entryNumber = await _generateInventoryEntryNumber(movement);
      final description = _getMovementDescription(movement, item.name);

      final lines = <JournalEntryLine>[];

      if (movement.type == MovementType.receipt ||
          movement.type == MovementType.found) {
        // مدين المخزون
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: inventoryAccountId,
            description: description,
            debitAmount: movement.totalCost,
            creditAmount: 0.0,
            lineOrder: 1,
          ),
        );

        // دائن المشتريات أو الإيرادات
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: expenseAccountId ?? revenueAccountId!,
            description: description,
            debitAmount: 0.0,
            creditAmount: movement.totalCost,
            lineOrder: 2,
          ),
        );
      } else {
        // مدين المصروفات
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: expenseAccountId!,
            description: description,
            debitAmount: movement.totalCost,
            creditAmount: 0.0,
            lineOrder: 1,
          ),
        );

        // دائن المخزون
        lines.add(
          JournalEntryLine(
            journalEntryId: 0,
            accountId: inventoryAccountId,
            description: description,
            debitAmount: 0.0,
            creditAmount: movement.totalCost,
            lineOrder: 2,
          ),
        );
      }

      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        date: movement.movementDate,
        description: description,
        reference: movement.documentNumber,
        lines: lines,
      );

      await _journalService.createJournalEntry(journalEntry);
    } catch (e) {
      // تسجيل الخطأ فقط دون إيقاف العملية
      developer.log(
        'خطأ في إنشاء القيد اليومي: ${e.toString()}',
        name: 'AdvancedInventoryService',
        error: e,
      );
    }
  }

  /// إضافة أصناف للجرد
  Future<void> _addItemsToInventory(
    int inventoryId,
    List<int> warehouseIds,
    List<int>? itemIds,
  ) async {
    try {
      // سيتم تطوير هذه الطريقة لاحقاً
      // await _inventoryService.addItemsToInventory(inventoryId, warehouseIds, itemIds);
    } catch (e) {
      developer.log(
        'خطأ في إضافة الأصناف للجرد: ${e.toString()}',
        name: 'AdvancedInventoryService',
        error: e,
      );
    }
  }

  /// توليد رقم قيد المخزون
  Future<String> _generateInventoryEntryNumber(StockMovement movement) async {
    try {
      final nextNumber = await _journalService.getNextEntryNumber();
      if (nextNumber.isSuccess) {
        return nextNumber.data!;
      }
      // في حالة فشل الحصول على الرقم التالي، استخدم رقم افتراضي
      return 'INV-${DateTime.now().millisecondsSinceEpoch}';
    } catch (e) {
      return 'INV-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// إنشاء وصف الحركة
  String _getMovementDescription(StockMovement movement, String itemName) {
    switch (movement.type) {
      case MovementType.receipt:
        return 'استلام مخزون - $itemName - ${movement.documentNumber}';
      case MovementType.issue:
        return 'صرف مخزون - $itemName - ${movement.documentNumber}';
      case MovementType.transfer:
        return 'نقل مخزون - $itemName - ${movement.documentNumber}';
      case MovementType.adjustment:
        return 'تسوية مخزون - $itemName - ${movement.documentNumber}';
      case MovementType.return_:
        return 'مرتجع مخزون - $itemName - ${movement.documentNumber}';
      case MovementType.damage:
        return 'مخزون تالف - $itemName - ${movement.documentNumber}';
      case MovementType.found:
        return 'زيادة مخزون - $itemName - ${movement.documentNumber}';
      case MovementType.loss:
        return 'نقص مخزون - $itemName - ${movement.documentNumber}';
    }
  }
}
