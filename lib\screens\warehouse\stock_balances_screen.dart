/// شاشة أرصدة المخزون
/// Stock Balances Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/stock_balance.dart';
import '../../models/warehouse.dart';
import '../../models/item.dart';
import '../../services/warehouse_service.dart';
import '../../services/item_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_search_bar.dart';
import '../../widgets/quantum_loading.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/beautiful_buttons.dart';
import '../../theme/app_theme.dart';
import 'stock_movements_screen.dart';

class StockBalancesScreen extends StatefulWidget {
  final int? warehouseId;
  final int? itemId;

  const StockBalancesScreen({super.key, this.warehouseId, this.itemId});

  @override
  State<StockBalancesScreen> createState() => _StockBalancesScreenState();
}

class _StockBalancesScreenState extends State<StockBalancesScreen>
    with TickerProviderStateMixin {
  final WarehouseService _warehouseService = WarehouseService();
  final ItemService _itemService = ItemService();
  final TextEditingController _searchController = TextEditingController();

  List<StockBalance> _balances = [];
  List<StockBalance> _filteredBalances = [];
  List<Warehouse> _warehouses = [];
  List<Item> _items = [];
  bool _isLoading = true;
  String _searchQuery = '';
  int? _selectedWarehouseId;
  int? _selectedItemId;
  bool _showZeroBalances = false;
  bool _showLowStock = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _selectedWarehouseId = widget.warehouseId;
    _selectedItemId = widget.itemId;
    _initializeAnimations();
    _loadData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل البيانات الأساسية
      final warehousesResult = await _warehouseService.getActiveWarehouses();
      final itemsResult = await _itemService.getActiveItems();

      if (warehousesResult.isSuccess) {
        _warehouses = warehousesResult.data!;
      }

      if (itemsResult.isSuccess) {
        _items = itemsResult.data!;
      }

      // تحميل الأرصدة
      await _loadBalances();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadBalances() async {
    try {
      final result = await _warehouseService.getStockBalances(
        warehouseId: _selectedWarehouseId,
        itemId: _selectedItemId,
        includeZeroBalances: _showZeroBalances,
        lowStockOnly: _showLowStock,
      );

      if (result.isSuccess) {
        setState(() {
          _balances = result.data!;
          _filterBalances();
        });
      } else {
        _showErrorSnackBar(result.error!);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل الأرصدة: ${e.toString()}');
    }
  }

  void _filterBalances() {
    setState(() {
      _filteredBalances = _balances.where((balance) {
        final item = _items.firstWhere(
          (i) => i.id == balance.itemId,
          orElse: () => Item(code: '', name: ''),
        );
        final warehouse = _warehouses.firstWhere(
          (w) => w.id == balance.warehouseId,
          orElse: () => Warehouse(code: '', name: ''),
        );

        final matchesSearch =
            _searchQuery.isEmpty ||
            item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            item.code.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            warehouse.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            warehouse.code.toLowerCase().contains(_searchQuery.toLowerCase());

        return matchesSearch;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _filterBalances();
    });
  }

  Future<void> _showFilterDialog() async {
    await showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        selectedWarehouseId: _selectedWarehouseId,
        selectedItemId: _selectedItemId,
        showZeroBalances: _showZeroBalances,
        showLowStock: _showLowStock,
        warehouses: _warehouses,
        items: _items,
        onApply: (warehouseId, itemId, showZero, showLow) {
          setState(() {
            _selectedWarehouseId = warehouseId;
            _selectedItemId = itemId;
            _showZeroBalances = showZero;
            _showLowStock = showLow;
          });
          _loadBalances();
        },
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.backgroundColor,
              AppTheme.backgroundColor.withValues(alpha: 0.8),
              AppTheme.primaryColor.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            // App Bar
            HolographicAppBar(
              title: 'أرصدة المخزون',
              subtitle: 'عرض وإدارة أرصدة المخزون',
              actions: [
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: _showFilterDialog,
                  tooltip: 'فلترة',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadBalances,
                  tooltip: 'تحديث',
                ),
              ],
            ),

            // Search Bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: QuantumSearchBar(
                controller: _searchController,
                hintText: 'البحث في الأرصدة...',
                onChanged: _onSearchChanged,
              ),
            ),

            // Summary Cards
            _buildSummaryCards(),

            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: QuantumLoading())
                  : _buildBalancesList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalValue = _filteredBalances.fold<double>(
      0,
      (sum, balance) => sum + balance.totalValue,
    );
    final totalItems = _filteredBalances.length;
    final lowStockCount = _filteredBalances.where((balance) {
      final item = _items.firstWhere(
        (i) => i.id == balance.itemId,
        orElse: () => Item(code: '', name: '', minStockLevel: 0),
      );
      return (item.minStockLevel ?? 0) > 0 &&
          balance.quantity < (item.minStockLevel ?? 0);
    }).length;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي القيمة',
              '${totalValue.toStringAsFixed(2)} ر.س',
              Icons.attach_money,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'عدد الأصناف',
              totalItems.toString(),
              Icons.inventory,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'مخزون منخفض',
              lowStockCount.toString(),
              Icons.warning,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalancesList() {
    if (_filteredBalances.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد أرصدة مخزون',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإضافة حركات مخزون لعرض الأرصدة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _filteredBalances.length,
          itemBuilder: (context, index) {
            final balance = _filteredBalances[index];
            return _buildBalanceCard(balance, index);
          },
        ),
      ),
    );
  }

  Widget _buildBalanceCard(StockBalance balance, int index) {
    final item = _items.firstWhere(
      (i) => i.id == balance.itemId,
      orElse: () => Item(code: '', name: ''),
    );
    final warehouse = _warehouses.firstWhere(
      (w) => w.id == balance.warehouseId,
      orElse: () => Warehouse(code: '', name: ''),
    );

    final isLowStock =
        (item.minStockLevel ?? 0) > 0 &&
        balance.quantity < (item.minStockLevel ?? 0);
    final isOutOfStock = balance.quantity <= 0;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final delay = index * 0.1;
        final animationValue = Curves.easeOutCubic.transform(
          (_animationController.value - delay).clamp(0.0, 1.0),
        );

        return Transform.translate(
          offset: Offset(0, 50 * (1 - animationValue)),
          child: Opacity(opacity: animationValue, child: child),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: QuantumCard(
          child: InkWell(
            onTap: () => _showBalanceDetails(balance),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getStatusColor(
                            isOutOfStock,
                            isLowStock,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.inventory,
                          color: _getStatusColor(isOutOfStock, isLowStock),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${item.code} - ${item.name}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${warehouse.code} - ${warehouse.name}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildStatusChip(isOutOfStock, isLowStock),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildBalanceInfo(balance, item),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: BeautifulButton(
                          text: 'عرض الحركات',
                          icon: Icons.history,
                          onPressed: () => _navigateToMovements(balance),
                          isOutlined: true,
                          height: 36,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: BeautifulButton(
                          text: 'تسوية',
                          icon: Icons.tune,
                          onPressed: () => _showAdjustmentDialog(balance),
                          height: 36,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(bool isOutOfStock, bool isLowStock) {
    Color color;
    String text;
    IconData icon;

    if (isOutOfStock) {
      color = Colors.red;
      text = 'نفد المخزون';
      icon = Icons.error;
    } else if (isLowStock) {
      color = Colors.orange;
      text = 'مخزون منخفض';
      icon = Icons.warning;
    } else {
      color = Colors.green;
      text = 'متوفر';
      icon = Icons.check_circle;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceInfo(StockBalance balance, Item item) {
    return Column(
      children: [
        _buildInfoRow('الكمية المتاحة', '${balance.quantity} ${item.unit}'),
        _buildInfoRow(
          'متوسط التكلفة',
          '${balance.averageCost.toStringAsFixed(2)} ر.س',
        ),
        _buildInfoRow(
          'إجمالي القيمة',
          '${balance.totalValue.toStringAsFixed(2)} ر.س',
        ),
        if ((item.minStockLevel ?? 0) > 0)
          _buildInfoRow('الحد الأدنى', '${item.minStockLevel} ${item.unit}'),
        _buildInfoRow('آخر حركة', _formatDate(balance.lastMovementDate)),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(bool isOutOfStock, bool isLowStock) {
    if (isOutOfStock) return Colors.red;
    if (isLowStock) return Colors.orange;
    return Colors.green;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showBalanceDetails(StockBalance balance) {
    showDialog(
      context: context,
      builder: (context) => _StockBalanceDetailsDialog(balance: balance),
    );
  }

  void _navigateToMovements(StockBalance balance) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StockMovementsScreen(
          warehouseId: balance.warehouseId,
          itemId: balance.itemId,
        ),
      ),
    );
  }

  void _showAdjustmentDialog(StockBalance balance) {
    showDialog(
      context: context,
      builder: (context) => _StockAdjustmentDialog(
        balance: balance,
        onAdjustmentSaved: () {
          _loadBalances();
        },
      ),
    );
  }
}

// Filter Dialog Widget
class _FilterDialog extends StatefulWidget {
  final int? selectedWarehouseId;
  final int? selectedItemId;
  final bool showZeroBalances;
  final bool showLowStock;
  final List<Warehouse> warehouses;
  final List<Item> items;
  final Function(int?, int?, bool, bool) onApply;

  const _FilterDialog({
    required this.selectedWarehouseId,
    required this.selectedItemId,
    required this.showZeroBalances,
    required this.showLowStock,
    required this.warehouses,
    required this.items,
    required this.onApply,
  });

  @override
  State<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<_FilterDialog> {
  int? _warehouseId;
  int? _itemId;
  bool _showZero = false;
  bool _showLow = false;

  @override
  void initState() {
    super.initState();
    _warehouseId = widget.selectedWarehouseId;
    _itemId = widget.selectedItemId;
    _showZero = widget.showZeroBalances;
    _showLow = widget.showLowStock;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('فلترة الأرصدة'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Warehouse Filter
            QuantumDropdown<int>(
              value: _warehouseId,
              hintText: 'المخزن',
              items: widget.warehouses.map((warehouse) {
                return DropdownMenuItem(
                  value: warehouse.id,
                  child: Text(warehouse.name),
                );
              }).toList(),
              onChanged: (value) => setState(() => _warehouseId = value),
            ),
            const SizedBox(height: 16),

            // Item Filter
            QuantumDropdown<int>(
              value: _itemId,
              hintText: 'الصنف',
              items: widget.items.map((item) {
                return DropdownMenuItem(value: item.id, child: Text(item.name));
              }).toList(),
              onChanged: (value) => setState(() => _itemId = value),
            ),
            const SizedBox(height: 16),

            // Show Zero Balances
            CheckboxListTile(
              title: const Text('عرض الأرصدة الصفرية'),
              value: _showZero,
              onChanged: (value) => setState(() => _showZero = value ?? false),
            ),

            // Show Low Stock Only
            CheckboxListTile(
              title: const Text('المخزون المنخفض فقط'),
              value: _showLow,
              onChanged: (value) => setState(() => _showLow = value ?? false),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            setState(() {
              _warehouseId = null;
              _itemId = null;
              _showZero = false;
              _showLow = false;
            });
          },
          child: const Text('مسح الفلاتر'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApply(_warehouseId, _itemId, _showZero, _showLow);
            Navigator.pop(context);
          },
          child: const Text('تطبيق'),
        ),
      ],
    );
  }
}

// Stock Balance Details Dialog
class _StockBalanceDetailsDialog extends StatelessWidget {
  final StockBalance balance;

  const _StockBalanceDetailsDialog({required this.balance});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تفاصيل رصيد المخزون'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('معرف الصنف:', '${balance.itemId}'),
            _buildDetailRow('معرف المخزن:', '${balance.warehouseId}'),
            _buildDetailRow(
              'معرف الموقع:',
              balance.locationId?.toString() ?? 'غير محدد',
            ),
            _buildDetailRow('الكمية:', '${balance.quantity}'),
            _buildDetailRow(
              'متوسط التكلفة:',
              balance.averageCost.toStringAsFixed(2),
            ),
            _buildDetailRow(
              'إجمالي القيمة:',
              balance.totalValue.toStringAsFixed(2),
            ),
            _buildDetailRow('رقم الدفعة:', balance.batchNumber ?? 'غير محدد'),
            _buildDetailRow(
              'الرقم التسلسلي:',
              balance.serialNumber ?? 'غير محدد',
            ),
            if (balance.expiryDate != null)
              _buildDetailRow(
                'تاريخ الانتهاء:',
                _formatDate(balance.expiryDate!),
              ),
            _buildDetailRow('آخر حركة:', _formatDate(balance.lastMovementDate)),
            _buildDetailRow('آخر تحديث:', _formatDate(balance.updatedAt)),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Stock Adjustment Dialog
class _StockAdjustmentDialog extends StatefulWidget {
  final StockBalance balance;
  final VoidCallback onAdjustmentSaved;

  const _StockAdjustmentDialog({
    required this.balance,
    required this.onAdjustmentSaved,
  });

  @override
  State<_StockAdjustmentDialog> createState() => _StockAdjustmentDialogState();
}

class _StockAdjustmentDialogState extends State<_StockAdjustmentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _reasonController = TextEditingController();
  String _adjustmentType = 'increase'; // increase, decrease, set

  @override
  void initState() {
    super.initState();
    _quantityController.text = widget.balance.quantity.toString();
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل رصيد المخزون'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('الكمية الحالية: ${widget.balance.quantity}'),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _adjustmentType,
                decoration: const InputDecoration(
                  labelText: 'نوع التعديل',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'increase', child: Text('زيادة')),
                  DropdownMenuItem(value: 'decrease', child: Text('نقص')),
                  DropdownMenuItem(value: 'set', child: Text('تحديد الكمية')),
                ],
                onChanged: (value) {
                  setState(() {
                    _adjustmentType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _quantityController,
                decoration: const InputDecoration(
                  labelText: 'الكمية',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الكمية';
                  }
                  final quantity = double.tryParse(value);
                  if (quantity == null || quantity <= 0) {
                    return 'يرجى إدخال كمية صحيحة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _reasonController,
                decoration: const InputDecoration(
                  labelText: 'سبب التعديل',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال سبب التعديل';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(onPressed: _saveAdjustment, child: const Text('حفظ')),
      ],
    );
  }

  void _saveAdjustment() async {
    if (_formKey.currentState!.validate()) {
      try {
        // حساب الكمية الجديدة بناءً على نوع التعديل
        final inputQuantity = double.parse(_quantityController.text);
        double newQuantity;

        switch (_adjustmentType) {
          case 'increase':
            newQuantity = widget.balance.quantity + inputQuantity;
            break;
          case 'decrease':
            newQuantity = widget.balance.quantity - inputQuantity;
            if (newQuantity < 0) {
              if (!mounted) return;
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('لا يمكن أن تكون الكمية سالبة'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }
            break;
          case 'set':
            newQuantity = inputQuantity;
            break;
          default:
            newQuantity = inputQuantity;
        }

        // إنشاء خدمة المخازن
        final warehouseService = WarehouseService();

        // تنفيذ تسوية المخزون
        final result = await warehouseService.adjustStock(
          itemId: widget.balance.itemId,
          warehouseId: widget.balance.warehouseId,
          locationId: widget.balance.locationId,
          newQuantity: newQuantity,
          reason: _reasonController.text,
          userId: 1, // استخدام معرف المستخدم الافتراضي
        );

        if (!mounted) return;

        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ تعديل المخزون بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          widget.onAdjustmentSaved();
          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حفظ التعديل: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في معالجة البيانات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
