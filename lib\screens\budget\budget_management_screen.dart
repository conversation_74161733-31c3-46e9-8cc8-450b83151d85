/// شاشة إدارة الموازنات والتخطيط المالي
/// Budget Management Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/budget.dart';
import '../../services/budget_service.dart';
import '../../widgets/quantum_card.dart';
import 'budget_form_screen.dart';
import 'budget_details_screen.dart';

class BudgetManagementScreen extends StatefulWidget {
  const BudgetManagementScreen({super.key});

  @override
  State<BudgetManagementScreen> createState() => _BudgetManagementScreenState();
}

class _BudgetManagementScreenState extends State<BudgetManagementScreen>
    with TickerProviderStateMixin {
  final BudgetService _budgetService = BudgetService();

  List<Budget> _budgets = [];
  bool _isLoading = false;
  String _searchQuery = '';
  BudgetType? _selectedType;
  BudgetStatus? _selectedStatus;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadBudgets();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadBudgets() async {
    setState(() => _isLoading = true);

    try {
      final result = await _budgetService.getAllBudgets(
        type: _selectedType,
        status: _selectedStatus,
      );

      if (result.isSuccess) {
        setState(() {
          _budgets = result.data!;
        });
      } else {
        _showErrorDialog('خطأ في تحميل الموازنات', result.error!);
      }
    } catch (e) {
      _showErrorDialog('خطأ غير متوقع', e.toString());
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  List<Budget> get _filteredBudgets {
    return _budgets.where((budget) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          budget.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          budget.code.toLowerCase().contains(_searchQuery.toLowerCase());

      return matchesSearch;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  children: [
                    _buildHeader(),
                    _buildFilters(),
                    Expanded(child: _buildBudgetsList()),
                  ],
                ),
              ),
            ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colors.blue, Colors.indigo],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.account_balance_wallet,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Text(
            'إدارة الموازنات',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () {
            Navigator.pushNamed(context, '/financial-reports');
          },
          icon: const Icon(Icons.analytics, color: Colors.blue),
          tooltip: 'التقارير',
        ),
        IconButton(
          onPressed: _loadBudgets,
          icon: const Icon(Icons.refresh, color: Colors.blue),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: QuantumCard(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colors.blue, Colors.indigo],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.dashboard,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'نظام الموازنات والتخطيط المالي',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'إدارة شاملة للموازنات والتنبؤات المالية',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              _buildStatisticsRow(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsRow() {
    final totalBudgets = _budgets.length;
    final activeBudgets = _budgets
        .where((b) => b.status == BudgetStatus.active)
        .length;
    final draftBudgets = _budgets
        .where((b) => b.status == BudgetStatus.draft)
        .length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الموازنات',
            totalBudgets.toString(),
            Icons.account_balance_wallet,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'الموازنات النشطة',
            activeBudgets.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'المسودات',
            draftBudgets.toString(),
            Icons.edit,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: QuantumCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // شريط البحث
              TextField(
                onChanged: (value) => setState(() => _searchQuery = value),
                decoration: InputDecoration(
                  hintText: 'البحث في الموازنات...',
                  prefixIcon: const Icon(Icons.search, color: Colors.blue),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.blue.withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.blue),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // فلاتر
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<BudgetType>(
                      value: _selectedType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الموازنة',
                        border: OutlineInputBorder(),
                      ),
                      items: BudgetType.values.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(type.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() => _selectedType = value);
                        _loadBudgets();
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: DropdownButtonFormField<BudgetStatus>(
                      value: _selectedStatus,
                      decoration: const InputDecoration(
                        labelText: 'حالة الموازنة',
                        border: OutlineInputBorder(),
                      ),
                      items: BudgetStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(status.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() => _selectedStatus = value);
                        _loadBudgets();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBudgetsList() {
    final filteredBudgets = _filteredBudgets;

    if (filteredBudgets.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredBudgets.length,
      itemBuilder: (context, index) {
        final budget = filteredBudgets[index];
        return _buildBudgetCard(budget, index);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.account_balance_wallet,
              size: 64,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'لا توجد موازنات',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'ابدأ بإنشاء موازنة جديدة لإدارة التخطيط المالي',
            style: TextStyle(fontSize: 16, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: _createNewBudget,
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.add, color: Colors.white),
                SizedBox(width: 8),
                Text(
                  'إنشاء موازنة جديدة',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetCard(Budget budget, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: QuantumCard(
                child: InkWell(
                  onTap: () => _viewBudgetDetails(budget),
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBudgetHeader(budget),
                        const SizedBox(height: 16),
                        _buildBudgetInfo(budget),
                        const SizedBox(height: 16),
                        _buildBudgetProgress(budget),
                        const SizedBox(height: 16),
                        _buildBudgetActions(budget),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBudgetHeader(Budget budget) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: _getBudgetStatusGradient(budget.status),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getBudgetTypeIcon(budget.type),
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                budget.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    budget.code,
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getBudgetStatusColor(
                        budget.status,
                      ).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      budget.status.displayName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _getBudgetStatusColor(budget.status),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        PopupMenuButton<String>(
          onSelected: (value) => _handleBudgetAction(value, budget),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility, size: 20),
                  SizedBox(width: 8),
                  Text('عرض التفاصيل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            if (budget.status == BudgetStatus.draft)
              const PopupMenuItem(
                value: 'approve',
                child: Row(
                  children: [
                    Icon(Icons.check, size: 20),
                    SizedBox(width: 8),
                    Text('اعتماد'),
                  ],
                ),
              ),
            if (budget.status == BudgetStatus.approved)
              const PopupMenuItem(
                value: 'activate',
                child: Row(
                  children: [
                    Icon(Icons.play_arrow, size: 20),
                    SizedBox(width: 8),
                    Text('تفعيل'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBudgetInfo(Budget budget) {
    return Row(
      children: [
        Expanded(
          child: _buildInfoItem(
            'النوع',
            budget.type.displayName,
            Icons.category,
          ),
        ),
        Expanded(
          child: _buildInfoItem(
            'الفترة',
            budget.period.displayName,
            Icons.date_range,
          ),
        ),
        Expanded(
          child: _buildInfoItem(
            'المبلغ المخطط',
            '${budget.totalBudgetAmount.toStringAsFixed(2)} ر.س',
            Icons.account_balance,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.blue),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetProgress(Budget budget) {
    final executionPercentage =
        budget.totalActualAmount / budget.totalBudgetAmount * 100;
    final variancePercentage =
        ((budget.totalActualAmount - budget.totalBudgetAmount) /
        budget.totalBudgetAmount *
        100);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'نسبة التنفيذ',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            Text(
              '${executionPercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _getProgressColor(executionPercentage),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: executionPercentage / 100,
          backgroundColor: Colors.grey.withValues(alpha: 0.3),
          valueColor: AlwaysStoppedAnimation<Color>(
            _getProgressColor(executionPercentage),
          ),
        ),
        const SizedBox(height: 8),
        if (variancePercentage != 0)
          Row(
            children: [
              Icon(
                variancePercentage > 0
                    ? Icons.trending_up
                    : Icons.trending_down,
                size: 16,
                color: variancePercentage > 0 ? Colors.red : Colors.green,
              ),
              const SizedBox(width: 4),
              Text(
                'انحراف: ${variancePercentage.abs().toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 12,
                  color: variancePercentage > 0 ? Colors.red : Colors.green,
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildBudgetActions(Budget budget) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => _viewBudgetDetails(budget),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.visibility, size: 16),
                SizedBox(width: 4),
                Text('عرض'),
              ],
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton(
            onPressed: () => _editBudget(budget),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.edit, size: 16, color: Colors.white),
                SizedBox(width: 4),
                Text('تعديل', style: TextStyle(color: Colors.white)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _createNewBudget,
      backgroundColor: Colors.blue,
      icon: const Icon(Icons.add, color: Colors.white),
      label: const Text('موازنة جديدة', style: TextStyle(color: Colors.white)),
    );
  }

  // Helper methods
  IconData _getBudgetTypeIcon(BudgetType type) {
    switch (type) {
      case BudgetType.operational:
        return Icons.business;
      case BudgetType.capital:
        return Icons.account_balance;
      case BudgetType.cash:
        return Icons.attach_money;
      case BudgetType.master:
        return Icons.dashboard;
      case BudgetType.flexible:
        return Icons.tune;
      case BudgetType.static:
        return Icons.lock;
      case BudgetType.rolling:
        return Icons.refresh;
    }
  }

  Gradient _getBudgetStatusGradient(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.draft:
        return const LinearGradient(colors: [Colors.grey, Colors.grey]);
      case BudgetStatus.submitted:
        return const LinearGradient(colors: [Colors.orange, Colors.deepOrange]);
      case BudgetStatus.underReview:
        return const LinearGradient(colors: [Colors.amber, Colors.orange]);
      case BudgetStatus.approved:
        return const LinearGradient(colors: [Colors.blue, Colors.indigo]);
      case BudgetStatus.active:
        return const LinearGradient(colors: [Colors.green, Colors.teal]);
      case BudgetStatus.closed:
        return const LinearGradient(colors: [Colors.purple, Colors.deepPurple]);
      case BudgetStatus.cancelled:
        return const LinearGradient(colors: [Colors.red, Colors.redAccent]);
    }
  }

  Color _getBudgetStatusColor(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.draft:
        return Colors.grey;
      case BudgetStatus.submitted:
        return Colors.orange;
      case BudgetStatus.underReview:
        return Colors.amber;
      case BudgetStatus.approved:
        return Colors.blue;
      case BudgetStatus.active:
        return Colors.green;
      case BudgetStatus.closed:
        return Colors.purple;
      case BudgetStatus.cancelled:
        return Colors.red;
    }
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 80) return Colors.green;
    if (percentage >= 60) return Colors.orange;
    return Colors.red;
  }

  // Action methods
  void _createNewBudget() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const BudgetFormScreen()),
    );

    if (result == true) {
      _loadBudgets();
    }
  }

  void _viewBudgetDetails(Budget budget) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BudgetDetailsScreen(budget: budget),
      ),
    );
  }

  void _editBudget(Budget budget) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (context) => BudgetFormScreen(budget: budget)),
    );

    if (result == true) {
      _loadBudgets();
    }
  }

  void _handleBudgetAction(String action, Budget budget) {
    switch (action) {
      case 'view':
        _viewBudgetDetails(budget);
        break;
      case 'edit':
        _editBudget(budget);
        break;
      case 'approve':
        _approveBudget(budget);
        break;
      case 'activate':
        _activateBudget(budget);
        break;
      case 'delete':
        _deleteBudget(budget);
        break;
    }
  }

  Future<void> _approveBudget(Budget budget) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاعتماد'),
        content: Text('هل تريد اعتماد الموازنة "${budget.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('اعتماد'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      final result = await _budgetService.approveBudget(
        budget.id!,
        'المستخدم الحالي',
      );

      setState(() => _isLoading = false);

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم اعتماد الموازنة بنجاح')),
          );
        }
        _loadBudgets();
      } else {
        if (mounted) {
          _showErrorDialog('خطأ في الاعتماد', result.error!);
        }
      }
    }
  }

  Future<void> _activateBudget(Budget budget) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد التفعيل'),
        content: Text('هل تريد تفعيل الموازنة "${budget.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تفعيل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      final result = await _budgetService.activateBudget(budget.id!);

      setState(() => _isLoading = false);

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تفعيل الموازنة بنجاح')),
          );
        }
        _loadBudgets();
      } else {
        if (mounted) {
          _showErrorDialog('خطأ في التفعيل', result.error!);
        }
      }
    }
  }

  Future<void> _deleteBudget(Budget budget) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل تريد حذف الموازنة "${budget.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      final result = await _budgetService.deleteBudget(budget.id!);

      setState(() => _isLoading = false);

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الموازنة بنجاح')),
          );
        }
        _loadBudgets();
      } else {
        if (mounted) {
          _showErrorDialog('خطأ في الحذف', result.error!);
        }
      }
    }
  }
}
