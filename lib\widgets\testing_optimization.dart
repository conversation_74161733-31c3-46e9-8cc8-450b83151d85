import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// نموذج نتيجة الاختبار
class TestResult {
  final String name;
  final TestStatus status;
  final double duration;
  final String description;

  TestResult({
    required this.name,
    required this.status,
    required this.duration,
    required this.description,
  });
}

/// حالة الاختبار
enum TestStatus { passed, failed, warning, skipped }

/// 🧪 نظام الاختبار والتحسين
/// Testing and Optimization System
///
/// هذا الملف يحتوي على نظام الاختبار والتحسين الأكثر تطوراً في التاريخ
/// This file contains the most advanced testing and optimization system in history

/// 🌟 لوحة الاختبار والتحسين
/// Testing and Optimization Dashboard
class TestingOptimizationDashboard extends StatefulWidget {
  const TestingOptimizationDashboard({super.key});

  @override
  State<TestingOptimizationDashboard> createState() =>
      _TestingOptimizationDashboardState();
}

class _TestingOptimizationDashboardState
    extends State<TestingOptimizationDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _testController;
  late AnimationController _rotationController;
  late Animation<double> _mainAnimation;
  late Animation<double> _testAnimation;
  late Animation<double> _rotationAnimation;

  int _selectedTab =
      0; // 0 for tests, 1 for performance, 2 for optimization, 3 for reports

  // Test Results
  bool _isRunningTests = false;
  double _testProgress = 0.0;
  List<TestResult> _testResults = [];

  // Performance Metrics
  double _memoryUsage = 0.0;
  double _cpuUsage = 0.0;
  double _batteryUsage = 0.0;
  int _frameRate = 60;
  double _loadTime = 0.0;

  // Optimization Settings
  bool _enablePerformanceMode = false;
  bool _enableMemoryOptimization = true;
  bool _enableBatteryOptimization = false;
  bool _enableNetworkOptimization = true;
  double _cacheSize = 100.0; // MB

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _testController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _testAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _testController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _mainController.repeat(reverse: true);
    _testController.repeat(reverse: true);
    _rotationController.repeat();

    _initializeTestResults();
    _simulatePerformanceMetrics();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _testController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _initializeTestResults() {
    _testResults = [
      TestResult(
        name: 'اختبار قاعدة البيانات',
        status: TestStatus.passed,
        duration: 1.2,
        description: 'اختبار عمليات قاعدة البيانات المحلية',
      ),
      TestResult(
        name: 'اختبار واجهة المستخدم',
        status: TestStatus.passed,
        duration: 2.5,
        description: 'اختبار جميع شاشات التطبيق',
      ),
      TestResult(
        name: 'اختبار المحاسبة',
        status: TestStatus.passed,
        duration: 3.1,
        description: 'اختبار العمليات المحاسبية والقيود',
      ),
      TestResult(
        name: 'اختبار المخزون',
        status: TestStatus.passed,
        duration: 1.8,
        description: 'اختبار إدارة المخزون والأصناف',
      ),
      TestResult(
        name: 'اختبار التقارير',
        status: TestStatus.passed,
        duration: 2.3,
        description: 'اختبار جميع التقارير المالية',
      ),
      TestResult(
        name: 'اختبار الأداء',
        status: TestStatus.passed,
        duration: 4.2,
        description: 'اختبار أداء التطبيق والذاكرة',
      ),
    ];
  }

  void _simulatePerformanceMetrics() {
    // محاكاة مقاييس الأداء
    setState(() {
      _memoryUsage = 45.0 + (math.Random().nextDouble() * 10);
      _cpuUsage = 15.0 + (math.Random().nextDouble() * 20);
      _batteryUsage = 8.0 + (math.Random().nextDouble() * 5);
      _frameRate = 58 + math.Random().nextInt(4);
      _loadTime = 1.2 + (math.Random().nextDouble() * 0.8);
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _testAnimation,
        _rotationAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.8 + (_mainAnimation.value * 0.8),
          child: QuantumEnergyEffect(
            intensity: 2.4 + (_testAnimation.value * 0.6),
            primaryColor: const Color(0xFF9C27B0),
            secondaryColor: const Color(0xFFBA68C8),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF4A148C).withValues(alpha: 0.9),
                    const Color(0xFF6A1B9A).withValues(alpha: 0.8),
                    const Color(0xFF9C27B0).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF9C27B0).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF9C27B0).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام الاختبار
                  Row(
                    children: [
                      Transform.scale(
                        scale: _testAnimation.value,
                        child: Transform.rotate(
                          angle: _rotationAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF9C27B0,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFFBA68C8,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.science_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🧪 الاختبار والتحسين',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'اختبار شامل وتحسين أداء التطبيق',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات الاختبار
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  if (_selectedTab == 0) _buildTestsView(),
                  if (_selectedTab == 1) _buildPerformanceView(),
                  if (_selectedTab == 2) _buildOptimizationView(),
                  if (_selectedTab == 3) _buildReportsView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          _buildTabItem(0, 'الاختبارات', Icons.quiz_rounded),
          _buildTabItem(1, 'الأداء', Icons.speed_rounded),
          _buildTabItem(2, 'التحسين', Icons.tune_rounded),
          _buildTabItem(3, 'التقارير', Icons.assessment_rounded),
        ],
      ),
    );
  }

  Widget _buildTabItem(int index, String title, IconData icon) {
    bool isSelected = _selectedTab == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTab = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF9C27B0).withValues(alpha: 0.3)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.6),
                size: 16,
              ),
              const SizedBox(height: 2),
              Text(
                title,
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.6),
                  fontWeight: FontWeight.bold,
                  fontSize: 9,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عرض الاختبارات
  Widget _buildTestsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '🧪 نتائج الاختبارات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const Spacer(),
            GestureDetector(
              onTap: _runAllTests,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMedium,
                  vertical: AppTheme.spacingSmall,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _isRunningTests
                          ? Icons.stop_rounded
                          : Icons.play_arrow_rounded,
                      color: const Color(0xFF4CAF50),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _isRunningTests ? 'إيقاف' : 'تشغيل',
                      style: TextStyle(
                        color: const Color(0xFF4CAF50),
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        if (_isRunningTests) _buildTestProgress(),

        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_testResults.length, (index) {
          return _buildTestResultCard(_testResults[index]);
        }),
      ],
    );
  }

  /// بناء عرض الأداء
  Widget _buildPerformanceView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📊 مقاييس الأداء',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        _buildPerformanceMetric(
          'استخدام الذاكرة',
          _memoryUsage,
          100,
          'MB',
          Icons.memory_rounded,
        ),
        _buildPerformanceMetric(
          'استخدام المعالج',
          _cpuUsage,
          100,
          '%',
          Icons.computer_rounded,
        ),
        _buildPerformanceMetric(
          'استهلاك البطارية',
          _batteryUsage,
          100,
          '%',
          Icons.battery_std_rounded,
        ),
        _buildPerformanceMetric(
          'معدل الإطارات',
          _frameRate.toDouble(),
          60,
          'FPS',
          Icons.speed_rounded,
        ),
        _buildPerformanceMetric(
          'وقت التحميل',
          _loadTime,
          5,
          'ثانية',
          Icons.timer_rounded,
        ),
      ],
    );
  }

  /// بناء عرض التحسين
  Widget _buildOptimizationView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '⚡ إعدادات التحسين',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        _buildOptimizationSwitch(
          'وضع الأداء العالي',
          _enablePerformanceMode,
          Icons.flash_on_rounded,
          (value) {
            setState(() => _enablePerformanceMode = value);
          },
        ),

        _buildOptimizationSwitch(
          'تحسين الذاكرة',
          _enableMemoryOptimization,
          Icons.memory_rounded,
          (value) {
            setState(() => _enableMemoryOptimization = value);
          },
        ),

        _buildOptimizationSwitch(
          'توفير البطارية',
          _enableBatteryOptimization,
          Icons.battery_saver_rounded,
          (value) {
            setState(() => _enableBatteryOptimization = value);
          },
        ),

        _buildOptimizationSwitch(
          'تحسين الشبكة',
          _enableNetworkOptimization,
          Icons.network_check_rounded,
          (value) {
            setState(() => _enableNetworkOptimization = value);
          },
        ),

        _buildCacheSizeSlider(),
      ],
    );
  }

  /// بناء عرض التقارير
  Widget _buildReportsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📋 تقارير الاختبار',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        _buildReportSummary(),

        const SizedBox(height: AppTheme.spacingMedium),

        _buildTestCoverage(),

        const SizedBox(height: AppTheme.spacingMedium),

        _buildQualityMetrics(),
      ],
    );
  }

  /// بناء تقدم الاختبار
  Widget _buildTestProgress() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF9C27B0).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.science_rounded,
                color: const Color(0xFF9C27B0),
                size: 20,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Text(
                'جاري تشغيل الاختبارات...',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${(_testProgress * 100).toInt()}%',
                style: TextStyle(
                  color: const Color(0xFF9C27B0),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          LinearProgressIndicator(
            value: _testProgress,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(const Color(0xFF9C27B0)),
          ),
        ],
      ),
    );
  }

  void _runAllTests() {
    if (_isRunningTests) {
      setState(() {
        _isRunningTests = false;
        _testProgress = 0.0;
      });
      return;
    }

    setState(() {
      _isRunningTests = true;
      _testProgress = 0.0;
    });

    // محاكاة تشغيل الاختبارات
    _simulateTestExecution();
  }

  void _simulateTestExecution() {
    const duration = Duration(milliseconds: 100);
    Timer.periodic(duration, (timer) {
      setState(() {
        _testProgress += 0.02;
      });

      if (_testProgress >= 1.0) {
        timer.cancel();
        setState(() {
          _isRunningTests = false;
          _testProgress = 0.0;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تشغيل جميع الاختبارات بنجاح'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
      }
    });
  }

  /// بناء بطاقة نتيجة الاختبار
  Widget _buildTestResultCard(TestResult result) {
    Color statusColor;
    IconData statusIcon;

    switch (result.status) {
      case TestStatus.passed:
        statusColor = const Color(0xFF4CAF50);
        statusIcon = Icons.check_circle_rounded;
        break;
      case TestStatus.failed:
        statusColor = const Color(0xFFF44336);
        statusIcon = Icons.error_rounded;
        break;
      case TestStatus.warning:
        statusColor = const Color(0xFFFF9800);
        statusIcon = Icons.warning_rounded;
        break;
      case TestStatus.skipped:
        statusColor = const Color(0xFF9E9E9E);
        statusIcon = Icons.skip_next_rounded;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(statusIcon, color: statusColor, size: 16),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  result.description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${result.duration.toStringAsFixed(1)}s',
            style: TextStyle(
              color: statusColor,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مقياس الأداء
  Widget _buildPerformanceMetric(
    String label,
    double value,
    double max,
    String unit,
    IconData icon,
  ) {
    double percentage = (value / max).clamp(0.0, 1.0);
    Color color = percentage < 0.5
        ? const Color(0xFF4CAF50)
        : percentage < 0.8
        ? const Color(0xFFFF9800)
        : const Color(0xFFF44336);

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: 16),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                '${value.toStringAsFixed(1)} $unit',
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  /// بناء مفتاح التحسين
  Widget _buildOptimizationSwitch(
    String label,
    bool value,
    IconData icon,
    Function(bool) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF9C27B0).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF9C27B0).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: const Color(0xFF9C27B0), size: 16),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF9C27B0),
          ),
        ],
      ),
    );
  }

  /// بناء شريط تمرير حجم التخزين المؤقت
  Widget _buildCacheSizeSlider() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF9C27B0).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF9C27B0).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.storage_rounded,
                  color: Color(0xFF9C27B0),
                  size: 16,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Text(
                  'حجم التخزين المؤقت',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                '${_cacheSize.toInt()} MB',
                style: const TextStyle(
                  color: Color(0xFF9C27B0),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Slider(
            value: _cacheSize,
            min: 50,
            max: 500,
            activeColor: const Color(0xFF9C27B0),
            inactiveColor: Colors.white.withValues(alpha: 0.3),
            onChanged: (value) {
              setState(() => _cacheSize = value);
            },
          ),
        ],
      ),
    );
  }

  /// بناء ملخص التقرير
  Widget _buildReportSummary() {
    int passedTests = _testResults
        .where((test) => test.status == TestStatus.passed)
        .length;
    int totalTests = _testResults.length;
    double successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.summarize_rounded,
                  color: Color(0xFF4CAF50),
                  size: 16,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Text(
                'ملخص الاختبارات',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'إجمالي الاختبارات',
                  totalTests.toString(),
                  Icons.quiz_rounded,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'نجح',
                  passedTests.toString(),
                  Icons.check_circle_rounded,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'معدل النجاح',
                  '${successRate.toInt()}%',
                  Icons.trending_up_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الملخص
  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF4CAF50), size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  /// بناء تغطية الاختبار
  Widget _buildTestCoverage() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF2196F3).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF2196F3).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.pie_chart_rounded,
                  color: Color(0xFF2196F3),
                  size: 16,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Text(
                'تغطية الاختبار',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildCoverageItem('الكود', 95.2),
          _buildCoverageItem('الوظائف', 88.7),
          _buildCoverageItem('الفروع', 92.1),
          _buildCoverageItem('الخطوط', 96.8),
        ],
      ),
    );
  }

  /// بناء عنصر التغطية
  Widget _buildCoverageItem(String label, double percentage) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 10,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              valueColor: const AlwaysStoppedAnimation<Color>(
                Color(0xFF2196F3),
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: const TextStyle(
              color: Color(0xFF2196F3),
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مقاييس الجودة
  Widget _buildQualityMetrics() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFFFF9800).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF9800).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.star_rounded,
                  color: Color(0xFFFF9800),
                  size: 16,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Text(
                'مقاييس الجودة',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Row(
            children: [
              Expanded(
                child: _buildQualityItem('الأداء', 'A+', Icons.speed_rounded),
              ),
              Expanded(
                child: _buildQualityItem('الأمان', 'A', Icons.security_rounded),
              ),
              Expanded(
                child: _buildQualityItem(
                  'الموثوقية',
                  'A+',
                  Icons.verified_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الجودة
  Widget _buildQualityItem(String label, String grade, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFFFF9800), size: 20),
        const SizedBox(height: 4),
        Text(
          grade,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 10,
          ),
        ),
      ],
    );
  }
}
