import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 💾 نظام النسخ الاحتياطي والاستعادة
/// Backup and Restore System
///
/// هذا الملف يحتوي على نظام النسخ الاحتياطي والاستعادة لا مثيل له في التاريخ
/// This file contains unprecedented backup and restore system in history

/// 🌟 لوحة النسخ الاحتياطي والاستعادة
/// Backup and Restore Dashboard
class BackupRestoreSystemDashboard extends StatefulWidget {
  const BackupRestoreSystemDashboard({super.key});

  @override
  State<BackupRestoreSystemDashboard> createState() =>
      _BackupRestoreSystemDashboardState();
}

class _BackupRestoreSystemDashboardState
    extends State<BackupRestoreSystemDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _backupController;
  late AnimationController _progressController;
  late Animation<double> _mainAnimation;
  late Animation<double> _backupAnimation;
  late Animation<double> _progressAnimation;

  int _selectedTab =
      0; // 0 for backup, 1 for restore, 2 for schedule, 3 for history

  bool _isBackingUp = false;
  bool _isRestoring = false;
  double _backupProgress = 0.0;
  double _restoreProgress = 0.0;

  final List<BackupItem> _backupHistory = [
    BackupItem(
      id: 'backup_001',
      name: 'نسخة احتياطية تلقائية',
      date: DateTime.now().subtract(const Duration(hours: 2)),
      size: '15.2 MB',
      type: BackupType.automatic,
      status: BackupStatus.completed,
      description: 'نسخة احتياطية شاملة لجميع البيانات',
    ),
    BackupItem(
      id: 'backup_002',
      name: 'نسخة احتياطية يدوية',
      date: DateTime.now().subtract(const Duration(days: 1)),
      size: '14.8 MB',
      type: BackupType.manual,
      status: BackupStatus.completed,
      description: 'نسخة احتياطية قبل التحديث',
    ),
    BackupItem(
      id: 'backup_003',
      name: 'نسخة احتياطية أسبوعية',
      date: DateTime.now().subtract(const Duration(days: 7)),
      size: '13.5 MB',
      type: BackupType.scheduled,
      status: BackupStatus.completed,
      description: 'نسخة احتياطية أسبوعية مجدولة',
    ),
    BackupItem(
      id: 'backup_004',
      name: 'نسخة احتياطية شهرية',
      date: DateTime.now().subtract(const Duration(days: 30)),
      size: '12.1 MB',
      type: BackupType.scheduled,
      status: BackupStatus.completed,
      description: 'نسخة احتياطية شهرية مجدولة',
    ),
  ];

  final List<ScheduledBackup> _scheduledBackups = [
    ScheduledBackup(
      id: 'schedule_001',
      name: 'نسخة احتياطية يومية',
      frequency: BackupFrequency.daily,
      time: const TimeOfDay(hour: 2, minute: 0),
      isEnabled: true,
      lastRun: DateTime.now().subtract(const Duration(hours: 22)),
      nextRun: DateTime.now().add(const Duration(hours: 2)),
    ),
    ScheduledBackup(
      id: 'schedule_002',
      name: 'نسخة احتياطية أسبوعية',
      frequency: BackupFrequency.weekly,
      time: const TimeOfDay(hour: 3, minute: 0),
      isEnabled: true,
      lastRun: DateTime.now().subtract(const Duration(days: 7)),
      nextRun: DateTime.now().add(const Duration(days: 0)),
    ),
    ScheduledBackup(
      id: 'schedule_003',
      name: 'نسخة احتياطية شهرية',
      frequency: BackupFrequency.monthly,
      time: const TimeOfDay(hour: 4, minute: 0),
      isEnabled: false,
      lastRun: DateTime.now().subtract(const Duration(days: 30)),
      nextRun: DateTime.now().add(const Duration(days: 23)),
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _backupController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _backupAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _backupController, curve: Curves.easeInOut),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.linear),
    );

    _mainController.repeat(reverse: true);
    _backupController.repeat(reverse: true);
    _progressController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _backupController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _backupAnimation,
        _progressAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.6 + (_mainAnimation.value * 0.8),
          child: QuantumEnergyEffect(
            intensity: 2.2 + (_backupAnimation.value * 0.6),
            primaryColor: const Color(0xFF9C27B0),
            secondaryColor: const Color(0xFFBA68C8),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF4A148C).withValues(alpha: 0.9),
                    const Color(0xFF6A1B9A).withValues(alpha: 0.8),
                    const Color(0xFF8E24AA).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF9C27B0).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF9C27B0).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام النسخ الاحتياطي
                  Row(
                    children: [
                      Transform.scale(
                        scale: _backupAnimation.value,
                        child: Transform.rotate(
                          angle: _progressAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF9C27B0,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFFBA68C8,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.backup_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '💾 النسخ الاحتياطي والاستعادة',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام متقدم لحماية البيانات والنسخ الاحتياطي التلقائي',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات النظام
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  if (_selectedTab == 0) _buildBackupView(),
                  if (_selectedTab == 1) _buildRestoreView(),
                  if (_selectedTab == 2) _buildScheduleView(),
                  if (_selectedTab == 3) _buildHistoryView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 0),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 0
                      ? const Color(0xFF9C27B0).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.backup_rounded,
                      color: _selectedTab == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'النسخ الاحتياطي',
                      style: TextStyle(
                        color: _selectedTab == 0
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 9,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 1),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 1
                      ? const Color(0xFF9C27B0).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.restore_rounded,
                      color: _selectedTab == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'الاستعادة',
                      style: TextStyle(
                        color: _selectedTab == 1
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 9,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 2),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 2
                      ? const Color(0xFF9C27B0).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.schedule_rounded,
                      color: _selectedTab == 2
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'الجدولة',
                      style: TextStyle(
                        color: _selectedTab == 2
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 9,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 3),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 3
                      ? const Color(0xFF9C27B0).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.history_rounded,
                      color: _selectedTab == 3
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'السجل',
                      style: TextStyle(
                        color: _selectedTab == 3
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 9,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض النسخ الاحتياطي
  Widget _buildBackupView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات النسخ الاحتياطي
        _buildBackupStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // أزرار النسخ الاحتياطي
        _buildBackupActions(),

        const SizedBox(height: AppTheme.spacingLarge),

        // شريط التقدم إذا كان هناك نسخ احتياطي جاري
        if (_isBackingUp) _buildBackupProgress(),

        const SizedBox(height: AppTheme.spacingMedium),

        // آخر النسخ الاحتياطية
        Text(
          '📋 آخر النسخ الاحتياطية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(
          math.min(_backupHistory.length, 3),
          (index) => _buildBackupItemCard(_backupHistory[index]),
        ),
      ],
    );
  }

  /// بناء إحصائيات النسخ الاحتياطي
  Widget _buildBackupStats() {
    int totalBackups = _backupHistory.length;
    double totalSize = _backupHistory.fold(
      0.0,
      (sum, backup) => sum + _parseSize(backup.size),
    );
    DateTime? lastBackup = _backupHistory.isNotEmpty
        ? _backupHistory.first.date
        : null;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي النسخ',
            '$totalBackups',
            Icons.backup_rounded,
            const Color(0xFF9C27B0),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'الحجم الإجمالي',
            '${totalSize.toStringAsFixed(1)} MB',
            Icons.storage_rounded,
            const Color(0xFF673AB7),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'آخر نسخة',
            lastBackup != null ? _formatTimeAgo(lastBackup) : 'لا يوجد',
            Icons.access_time_rounded,
            const Color(0xFF3F51B5),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 8,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء أزرار النسخ الاحتياطي
  Widget _buildBackupActions() {
    return Row(
      children: [
        Expanded(
          child: _buildActionButton(
            'نسخة احتياطية فورية',
            Icons.backup_rounded,
            const Color(0xFF4CAF50),
            _isBackingUp ? null : _startBackup,
          ),
        ),
        const SizedBox(width: AppTheme.spacingMedium),
        Expanded(
          child: _buildActionButton(
            'نسخة احتياطية مخصصة',
            Icons.settings_backup_restore_rounded,
            const Color(0xFF2196F3),
            _isBackingUp ? null : _showCustomBackupDialog,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback? onPressed,
  ) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: onPressed != null
                ? [color.withValues(alpha: 0.8), color.withValues(alpha: 0.6)]
                : [
                    Colors.grey.withValues(alpha: 0.3),
                    Colors.grey.withValues(alpha: 0.2),
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: onPressed != null
                ? color.withValues(alpha: 0.5)
                : Colors.grey.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: onPressed != null
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.5),
              size: 24,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              title,
              style: TextStyle(
                color: onPressed != null
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.5),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط تقدم النسخ الاحتياطي
  Widget _buildBackupProgress() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF9C27B0).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.backup_rounded,
                color: const Color(0xFF9C27B0),
                size: 20,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Text(
                'جاري إنشاء النسخة الاحتياطية...',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${(_backupProgress * 100).toInt()}%',
                style: TextStyle(
                  color: const Color(0xFF9C27B0),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          LinearProgressIndicator(
            value: _backupProgress,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(const Color(0xFF9C27B0)),
          ),
        ],
      ),
    );
  }

  /// بناء عرض الاستعادة
  Widget _buildRestoreView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🔄 استعادة البيانات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        if (_isRestoring) _buildRestoreProgress(),

        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_backupHistory.length, (index) {
          return _buildRestoreItemCard(_backupHistory[index]);
        }),
      ],
    );
  }

  /// بناء عرض الجدولة
  Widget _buildScheduleView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '⏰ النسخ الاحتياطي المجدول',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const Spacer(),
            GestureDetector(
              onTap: _showAddScheduleDialog,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMedium,
                  vertical: AppTheme.spacingSmall,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.add_rounded,
                      color: const Color(0xFF4CAF50),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'إضافة',
                      style: TextStyle(
                        color: const Color(0xFF4CAF50),
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_scheduledBackups.length, (index) {
          return _buildScheduleItemCard(_scheduledBackups[index]);
        }),
      ],
    );
  }

  /// بناء عرض السجل
  Widget _buildHistoryView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📜 سجل النسخ الاحتياطي',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_backupHistory.length, (index) {
          return _buildHistoryItemCard(_backupHistory[index]);
        }),
      ],
    );
  }

  /// بناء بطاقة عنصر النسخ الاحتياطي
  Widget _buildBackupItemCard(BackupItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: _getBackupTypeColor(item.type).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getBackupTypeColor(item.type).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              _getBackupTypeIcon(item.type),
              color: _getBackupTypeColor(item.type),
              size: 16,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${_formatDate(item.date)} • ${item.size}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getBackupStatusColor(item.status).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _getBackupStatusText(item.status),
              style: TextStyle(
                color: _getBackupStatusColor(item.status),
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  double _parseSize(String size) {
    final parts = size.split(' ');
    if (parts.length != 2) return 0.0;

    final value = double.tryParse(parts[0]) ?? 0.0;
    final unit = parts[1].toLowerCase();

    switch (unit) {
      case 'kb':
        return value / 1024;
      case 'mb':
        return value;
      case 'gb':
        return value * 1024;
      default:
        return value;
    }
  }

  String _formatTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getBackupTypeColor(BackupType type) {
    switch (type) {
      case BackupType.manual:
        return const Color(0xFF2196F3);
      case BackupType.automatic:
        return const Color(0xFF4CAF50);
      case BackupType.scheduled:
        return const Color(0xFFFF9800);
    }
  }

  IconData _getBackupTypeIcon(BackupType type) {
    switch (type) {
      case BackupType.manual:
        return Icons.touch_app_rounded;
      case BackupType.automatic:
        return Icons.autorenew_rounded;
      case BackupType.scheduled:
        return Icons.schedule_rounded;
    }
  }

  Color _getBackupStatusColor(BackupStatus status) {
    switch (status) {
      case BackupStatus.completed:
        return const Color(0xFF4CAF50);
      case BackupStatus.inProgress:
        return const Color(0xFFFF9800);
      case BackupStatus.failed:
        return const Color(0xFFF44336);
    }
  }

  String _getBackupStatusText(BackupStatus status) {
    switch (status) {
      case BackupStatus.completed:
        return 'مكتمل';
      case BackupStatus.inProgress:
        return 'جاري';
      case BackupStatus.failed:
        return 'فشل';
    }
  }

  void _startBackup() {
    setState(() {
      _isBackingUp = true;
      _backupProgress = 0.0;
    });

    // محاكاة عملية النسخ الاحتياطي
    _simulateBackupProgress();
  }

  void _simulateBackupProgress() {
    const duration = Duration(milliseconds: 100);
    Timer.periodic(duration, (timer) {
      setState(() {
        _backupProgress += 0.02;
      });

      if (_backupProgress >= 1.0) {
        timer.cancel();
        setState(() {
          _isBackingUp = false;
          _backupProgress = 0.0;
        });

        // إضافة النسخة الاحتياطية الجديدة إلى السجل
        final newBackup = BackupItem(
          id: 'backup_${DateTime.now().millisecondsSinceEpoch}',
          name: 'نسخة احتياطية فورية',
          date: DateTime.now(),
          size:
              '${(math.Random().nextDouble() * 5 + 10).toStringAsFixed(1)} MB',
          type: BackupType.manual,
          status: BackupStatus.completed,
          description: 'نسخة احتياطية تم إنشاؤها يدوياً',
        );

        setState(() {
          _backupHistory.insert(0, newBackup);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء النسخة الاحتياطية بنجاح'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
      }
    });
  }

  void _showCustomBackupDialog() {
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('خيارات النسخ الاحتياطي المخصص قيد التطوير...'),
      ),
    );
  }

  void _showAddScheduleDialog() {
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة جدولة جديدة قيد التطوير...')),
    );
  }

  // Missing methods that need to be implemented
  Widget _buildRestoreProgress() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: const Color(0xFF2196F3).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF2196F3).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.restore_rounded,
                color: const Color(0xFF2196F3),
                size: 20,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Text(
                'جاري استعادة البيانات...',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${(_restoreProgress * 100).toInt()}%',
                style: TextStyle(
                  color: const Color(0xFF2196F3),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          LinearProgressIndicator(
            value: _restoreProgress,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(const Color(0xFF2196F3)),
          ),
        ],
      ),
    );
  }

  Widget _buildRestoreItemCard(BackupItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF2196F3).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.restore_rounded,
              color: const Color(0xFF2196F3),
              size: 16,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${_formatDate(item.date)} • ${item.size}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => _startRestore(item),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF2196F3).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF2196F3).withValues(alpha: 0.5),
                ),
              ),
              child: Text(
                'استعادة',
                style: TextStyle(
                  color: const Color(0xFF2196F3),
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleItemCard(ScheduledBackup schedule) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: schedule.isEnabled
              ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
              : Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: schedule.isEnabled
                  ? const Color(0xFF4CAF50).withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.schedule_rounded,
              color: schedule.isEnabled ? const Color(0xFF4CAF50) : Colors.grey,
              size: 16,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  schedule.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'التالي: ${_formatDate(schedule.nextRun)}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: schedule.isEnabled,
            onChanged: (value) {
              setState(() {
                schedule.isEnabled = value;
              });
            },
            activeColor: const Color(0xFF4CAF50),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryItemCard(BackupItem item) {
    return _buildBackupItemCard(item);
  }

  void _startRestore(BackupItem item) {
    setState(() {
      _isRestoring = true;
      _restoreProgress = 0.0;
    });

    // محاكاة عملية الاستعادة
    _simulateRestoreProgress();
  }

  void _simulateRestoreProgress() {
    const duration = Duration(milliseconds: 150);
    Timer.periodic(duration, (timer) {
      setState(() {
        _restoreProgress += 0.03;
      });

      if (_restoreProgress >= 1.0) {
        timer.cancel();
        setState(() {
          _isRestoring = false;
          _restoreProgress = 0.0;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم استعادة البيانات بنجاح'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
      }
    });
  }
}

/// نموذج عنصر النسخة الاحتياطية
class BackupItem {
  final String id;
  final String name;
  final DateTime date;
  final String size;
  final BackupType type;
  final BackupStatus status;
  final String description;

  BackupItem({
    required this.id,
    required this.name,
    required this.date,
    required this.size,
    required this.type,
    required this.status,
    required this.description,
  });
}

/// نموذج النسخة الاحتياطية المجدولة
class ScheduledBackup {
  final String id;
  final String name;
  final BackupFrequency frequency;
  final TimeOfDay time;
  bool isEnabled;
  final DateTime lastRun;
  final DateTime nextRun;

  ScheduledBackup({
    required this.id,
    required this.name,
    required this.frequency,
    required this.time,
    required this.isEnabled,
    required this.lastRun,
    required this.nextRun,
  });
}

/// أنواع النسخ الاحتياطي
enum BackupType { manual, automatic, scheduled }

/// حالات النسخ الاحتياطي
enum BackupStatus { completed, inProgress, failed }

/// تكرار النسخ الاحتياطي
enum BackupFrequency { daily, weekly, monthly }
