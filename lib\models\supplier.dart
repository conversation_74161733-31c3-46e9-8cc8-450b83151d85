import 'account.dart';

class Supplier {
  final int? id;
  final String code;
  final String name;
  final String? phone;
  final String? email;
  final String? address;
  final String? category;
  final String? taxNumber;
  final double? creditLimit;
  final int? paymentTerms;
  final String? notes;
  final double currentBalance;
  final int? accountId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Account? account;

  Supplier({
    this.id,
    required this.code,
    required this.name,
    this.phone,
    this.email,
    this.address,
    this.category,
    this.taxNumber,
    this.creditLimit,
    this.paymentTerms,
    this.notes,
    this.currentBalance = 0.0,
    this.accountId,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.account,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from database map
  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      category: map['category'] as String?,
      taxNumber: map['tax_number'] as String?,
      creditLimit: (map['credit_limit'] as num?)?.toDouble(),
      paymentTerms: map['payment_terms'] as int?,
      notes: map['notes'] as String?,
      currentBalance: (map['current_balance'] as num?)?.toDouble() ?? 0.0,
      accountId: map['account_id'] as int?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'category': category,
      'tax_number': taxNumber,
      'credit_limit': creditLimit,
      'payment_terms': paymentTerms,
      'notes': notes,
      'current_balance': currentBalance,
      'account_id': accountId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  Supplier copyWith({
    int? id,
    String? code,
    String? name,
    String? phone,
    String? email,
    String? address,
    String? category,
    String? taxNumber,
    double? creditLimit,
    int? paymentTerms,
    String? notes,
    double? currentBalance,
    int? accountId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Account? account,
  }) {
    return Supplier(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      category: category ?? this.category,
      taxNumber: taxNumber ?? this.taxNumber,
      creditLimit: creditLimit ?? this.creditLimit,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      notes: notes ?? this.notes,
      currentBalance: currentBalance ?? this.currentBalance,
      accountId: accountId ?? this.accountId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      account: account ?? this.account,
    );
  }

  // Helper methods
  bool get hasDebitBalance => currentBalance > 0;
  bool get hasCreditBalance => currentBalance < 0;
  bool get isBalanceZero => currentBalance == 0;

  String get balanceStatus {
    if (isBalanceZero) return 'متوازن';
    if (hasDebitBalance) return 'مدين';
    return 'دائن';
  }

  String get displayBalance {
    if (isBalanceZero) return '0.00';
    return '${currentBalance.abs().toStringAsFixed(2)} $balanceStatus';
  }

  // Validation
  List<String> validate() {
    List<String> errors = [];

    if (code.trim().isEmpty) {
      errors.add('رمز المورد مطلوب');
    }

    if (name.trim().isEmpty) {
      errors.add('اسم المورد مطلوب');
    }

    if (email != null && email!.isNotEmpty) {
      if (!_isValidEmail(email!)) {
        errors.add('البريد الإلكتروني غير صحيح');
      }
    }

    if (phone != null && phone!.isNotEmpty) {
      if (!_isValidPhone(phone!)) {
        errors.add('رقم الهاتف غير صحيح');
      }
    }

    return errors;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPhone(String phone) {
    // Simple phone validation - adjust based on your requirements
    return RegExp(r'^[\+]?[0-9\-\s\(\)]{7,15}$').hasMatch(phone);
  }

  @override
  String toString() {
    return 'Supplier{id: $id, code: $code, name: $name, balance: $currentBalance}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Supplier && other.id == id && other.code == code;
  }

  @override
  int get hashCode => id.hashCode ^ code.hashCode;
}

// Supplier summary for reports
class SupplierSummary {
  final int supplierId;
  final String supplierCode;
  final String supplierName;
  final double openingBalance;
  final double totalDebits;
  final double totalCredits;
  final double closingBalance;
  final int transactionCount;

  SupplierSummary({
    required this.supplierId,
    required this.supplierCode,
    required this.supplierName,
    required this.openingBalance,
    required this.totalDebits,
    required this.totalCredits,
    required this.closingBalance,
    required this.transactionCount,
  });

  factory SupplierSummary.fromMap(Map<String, dynamic> map) {
    return SupplierSummary(
      supplierId: map['supplier_id'] as int,
      supplierCode: map['supplier_code'] as String,
      supplierName: map['supplier_name'] as String,
      openingBalance: (map['opening_balance'] as num?)?.toDouble() ?? 0.0,
      totalDebits: (map['total_debits'] as num?)?.toDouble() ?? 0.0,
      totalCredits: (map['total_credits'] as num?)?.toDouble() ?? 0.0,
      closingBalance: (map['closing_balance'] as num?)?.toDouble() ?? 0.0,
      transactionCount: map['transaction_count'] as int? ?? 0,
    );
  }

  double get netMovement => totalDebits - totalCredits;

  String get balanceStatus {
    if (closingBalance == 0) return 'متوازن';
    if (closingBalance > 0) return 'مدين';
    return 'دائن';
  }

  @override
  String toString() {
    return 'SupplierSummary{id: $supplierId, name: $supplierName, balance: $closingBalance}';
  }
}

// Supplier transaction for statement
class SupplierTransaction {
  final int? id;
  final DateTime date;
  final String reference;
  final String description;
  final double debitAmount;
  final double creditAmount;
  final double runningBalance;
  final String transactionType; // 'invoice', 'payment', 'adjustment', etc.

  SupplierTransaction({
    this.id,
    required this.date,
    required this.reference,
    required this.description,
    required this.debitAmount,
    required this.creditAmount,
    required this.runningBalance,
    required this.transactionType,
  });

  factory SupplierTransaction.fromMap(Map<String, dynamic> map) {
    return SupplierTransaction(
      id: map['id'] as int?,
      date: DateTime.parse(map['date'] as String),
      reference: map['reference'] as String,
      description: map['description'] as String,
      debitAmount: (map['debit_amount'] as num?)?.toDouble() ?? 0.0,
      creditAmount: (map['credit_amount'] as num?)?.toDouble() ?? 0.0,
      runningBalance: (map['running_balance'] as num?)?.toDouble() ?? 0.0,
      transactionType: map['transaction_type'] as String,
    );
  }

  double get amount => debitAmount > 0 ? debitAmount : creditAmount;
  bool get isDebit => debitAmount > 0;
  bool get isCredit => creditAmount > 0;
  String get type => isDebit ? 'مدين' : 'دائن';

  @override
  String toString() {
    return 'SupplierTransaction{date: $date, ref: $reference, amount: $amount, type: $type}';
  }
}
