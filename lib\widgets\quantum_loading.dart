/// مؤشر تحميل كمي متقدم لتطبيق Smart Ledger
/// Advanced Quantum Loading Indicator for Smart Ledger Application
library;

import 'package:flutter/material.dart';
import 'dart:math' as math;

/// مؤشر تحميل كمي مع تأثيرات بصرية متقدمة
/// Quantum loading indicator with advanced visual effects
class QuantumLoading extends StatefulWidget {
  final double size;
  final Color? color;
  final double strokeWidth;
  final bool showText;
  final String? text;
  final TextStyle? textStyle;
  final Duration duration;

  const QuantumLoading({
    super.key,
    this.size = 50.0,
    this.color,
    this.strokeWidth = 4.0,
    this.showText = false,
    this.text,
    this.textStyle,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<QuantumLoading> createState() => _QuantumLoadingState();
}

class _QuantumLoadingState extends State<QuantumLoading>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _colorController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _rotationController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: Duration(milliseconds: widget.duration.inMilliseconds ~/ 2),
      vsync: this,
    );

    _colorController = AnimationController(
      duration: Duration(milliseconds: widget.duration.inMilliseconds * 2),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _colorAnimation = ColorTween(begin: Colors.cyan, end: Colors.blue).animate(
      CurvedAnimation(parent: _colorController, curve: Curves.easeInOut),
    );

    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
    _colorController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = widget.color ?? theme.primaryColor;

    return AnimatedBuilder(
      animation: Listenable.merge([
        _rotationAnimation,
        _pulseAnimation,
        _colorAnimation,
      ]),
      builder: (context, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Transform.scale(
              scale: _pulseAnimation.value,
              child: Transform.rotate(
                angle: _rotationAnimation.value,
                child: CustomPaint(
                  size: Size(widget.size, widget.size),
                  painter: QuantumLoadingPainter(
                    color: _colorAnimation.value ?? primaryColor,
                    strokeWidth: widget.strokeWidth,
                    progress: _rotationAnimation.value / (2 * math.pi),
                  ),
                ),
              ),
            ),
            if (widget.showText) ...[
              const SizedBox(height: 16),
              Text(
                widget.text ?? 'جاري التحميل...',
                style:
                    widget.textStyle ??
                    TextStyle(
                      color: primaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// رسام مؤشر التحميل الكمي
/// Quantum loading indicator painter
class QuantumLoadingPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double progress;

  QuantumLoadingPainter({
    required this.color,
    required this.strokeWidth,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // رسم الدائرة الخارجية
    // Draw outer circle
    final outerPaint = Paint()
      ..color = color.withValues(alpha: 0.2)
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, outerPaint);

    // رسم القوس المتحرك
    // Draw moving arc
    final arcPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          color.withValues(alpha: 0.1),
          color,
          color.withValues(alpha: 0.1),
        ],
        stops: const [0.0, 0.5, 1.0],
        transform: GradientRotation(progress * 2 * math.pi),
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      math.pi * 1.5,
      false,
      arcPaint,
    );

    // رسم النقاط المتحركة
    // Draw moving dots
    for (int i = 0; i < 3; i++) {
      final angle = (progress * 2 * math.pi) + (i * math.pi * 2 / 3);
      final dotX = center.dx + radius * math.cos(angle);
      final dotY = center.dy + radius * math.sin(angle);

      final dotPaint = Paint()
        ..color = color.withValues(alpha: 1.0 - (i * 0.3))
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(dotX, dotY), strokeWidth / 2, dotPaint);
    }

    // رسم الدائرة الداخلية
    // Draw inner circle
    final innerPaint = Paint()
      ..color = color.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.3, innerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// مؤشر تحميل كمي مبسط
/// Simple quantum loading indicator
class SimpleQuantumLoading extends StatelessWidget {
  final double size;
  final Color? color;

  const SimpleQuantumLoading({super.key, this.size = 30.0, this.color});

  @override
  Widget build(BuildContext context) {
    return QuantumLoading(size: size, color: color, showText: false);
  }
}

/// مؤشر تحميل كمي مع نص
/// Quantum loading indicator with text
class QuantumLoadingWithText extends StatelessWidget {
  final String text;
  final double size;
  final Color? color;
  final TextStyle? textStyle;

  const QuantumLoadingWithText({
    super.key,
    required this.text,
    this.size = 50.0,
    this.color,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return QuantumLoading(
      size: size,
      color: color,
      showText: true,
      text: text,
      textStyle: textStyle,
    );
  }
}
