import 'package:flutter/material.dart';
import '../../models/customer.dart';
import '../../services/customer_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import '../../widgets/beautiful_inputs.dart';
import '../../widgets/smart_notifications.dart';

/// شاشة إضافة/تعديل العملاء
class AddCustomerScreen extends StatefulWidget {
  final Customer? customer;

  const AddCustomerScreen({super.key, this.customer});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final CustomerService _customerService = CustomerService();

  late TextEditingController _codeController;
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _addressController;
  late TextEditingController _taxNumberController;
  late TextEditingController _creditLimitController;
  late TextEditingController _notesController;

  bool _isLoading = false;
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    if (widget.customer == null) {
      _generateCustomerCode();
    }
  }

  void _initializeControllers() {
    _codeController = TextEditingController(text: widget.customer?.code ?? '');
    _nameController = TextEditingController(text: widget.customer?.name ?? '');
    _phoneController = TextEditingController(
      text: widget.customer?.phone ?? '',
    );
    _emailController = TextEditingController(
      text: widget.customer?.email ?? '',
    );
    _addressController = TextEditingController(
      text: widget.customer?.address ?? '',
    );
    _taxNumberController = TextEditingController(
      text: widget.customer?.taxNumber ?? '',
    );
    _creditLimitController = TextEditingController(
      text: widget.customer?.creditLimit.toString() ?? '0',
    );
    _notesController = TextEditingController(
      text: widget.customer?.notes ?? '',
    );
    _isActive = widget.customer?.isActive ?? true;
  }

  Future<void> _generateCustomerCode() async {
    final result = await _customerService.getNextCustomerCode();
    if (result.isSuccess && mounted) {
      setState(() {
        _codeController.text = result.data!;
      });
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _taxNumberController.dispose();
    _creditLimitController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveCustomer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final customer = Customer(
        id: widget.customer?.id,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        taxNumber: _taxNumberController.text.trim().isEmpty
            ? null
            : _taxNumberController.text.trim(),
        creditLimit: double.tryParse(_creditLimitController.text) ?? 0.0,
        currentBalance: widget.customer?.currentBalance ?? 0.0,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isActive: _isActive,
        createdAt: widget.customer?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = widget.customer == null
          ? await _customerService.createCustomer(customer)
          : await _customerService.updateCustomer(customer);

      if (!mounted) return;

      if (result.isSuccess) {
        SmartNotificationManager.showSuccess(
          context,
          title: widget.customer == null ? 'تم الإنشاء' : 'تم التحديث',
          message: widget.customer == null
              ? 'تم إنشاء العميل بنجاح'
              : 'تم تحديث بيانات العميل بنجاح',
        );
        Navigator.of(context).pop(true);
      } else {
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: result.error ?? 'فشل في حفظ بيانات العميل',
        );
      }
    } catch (e) {
      if (!mounted) return;
      SmartNotificationManager.showError(
        context,
        title: 'خطأ',
        message: 'حدث خطأ أثناء حفظ البيانات: ${e.toString()}',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.customer == null ? 'إضافة عميل جديد' : 'تعديل العميل',
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          children: [
            BeautifulCard(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المعلومات الأساسية',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // رمز العميل
                    BeautifulTextFormField(
                      controller: _codeController,
                      labelText: 'رمز العميل',
                      prefixIcon: Icons.tag,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'رمز العميل مطلوب';
                        }
                        return null;
                      },
                      readOnly: widget.customer != null,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // اسم العميل
                    BeautifulTextFormField(
                      controller: _nameController,
                      labelText: 'اسم العميل',
                      prefixIcon: Icons.person,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'اسم العميل مطلوب';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // رقم الهاتف
                    BeautifulTextFormField(
                      controller: _phoneController,
                      labelText: 'رقم الهاتف',
                      prefixIcon: Icons.phone,
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // البريد الإلكتروني
                    BeautifulTextFormField(
                      controller: _emailController,
                      labelText: 'البريد الإلكتروني',
                      prefixIcon: Icons.email,
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (!RegExp(
                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                          ).hasMatch(value)) {
                            return 'البريد الإلكتروني غير صحيح';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            BeautifulCard(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات إضافية',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // العنوان
                    BeautifulTextFormField(
                      controller: _addressController,
                      labelText: 'العنوان',
                      prefixIcon: Icons.location_on,
                      maxLines: 2,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // الرقم الضريبي
                    BeautifulTextFormField(
                      controller: _taxNumberController,
                      labelText: 'الرقم الضريبي',
                      prefixIcon: Icons.receipt_long,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // الحد الائتماني
                    BeautifulTextFormField(
                      controller: _creditLimitController,
                      labelText: 'الحد الائتماني',
                      prefixIcon: Icons.credit_card,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'يجب أن يكون رقماً صحيحاً';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // ملاحظات
                    BeautifulTextFormField(
                      controller: _notesController,
                      labelText: 'ملاحظات',
                      prefixIcon: Icons.note,
                      maxLines: 3,
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),

                    // حالة العميل
                    SwitchListTile(
                      title: const Text('العميل نشط'),
                      subtitle: Text(
                        _isActive
                            ? 'العميل نشط ويمكن التعامل معه'
                            : 'العميل غير نشط',
                      ),
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingLarge),

            // أزرار الحفظ والإلغاء
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading
                        ? null
                        : () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveCustomer,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(widget.customer == null ? 'إضافة' : 'تحديث'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
