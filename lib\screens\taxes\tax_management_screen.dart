/// شاشة إدارة الضرائب
/// Tax Management Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/tax.dart';
import '../../services/tax_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';
import 'add_tax_screen.dart';
import 'tax_groups_screen.dart';
import 'tax_reports_screen.dart';

class TaxManagementScreen extends StatefulWidget {
  const TaxManagementScreen({super.key});

  @override
  State<TaxManagementScreen> createState() => _TaxManagementScreenState();
}

class _TaxManagementScreenState extends State<TaxManagementScreen>
    with TickerProviderStateMixin {
  final TaxService _taxService = TaxService();
  final TextEditingController _searchController = TextEditingController();

  List<Tax> _taxes = [];
  List<Tax> _filteredTaxes = [];
  bool _isLoading = false;
  String _selectedFilter = 'all';

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadTaxes();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTaxes() async {
    setState(() => _isLoading = true);

    try {
      final result = await _taxService.getAllTaxes();
      if (result.isSuccess) {
        setState(() {
          _taxes = result.data!;
          _applyFilters();
        });
      } else {
        _showError(result.error!);
      }
    } catch (e) {
      _showError('خطأ في تحميل الضرائب: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _applyFilters() {
    List<Tax> filtered = _taxes;

    // تطبيق فلتر النوع
    if (_selectedFilter != 'all') {
      filtered = filtered
          .where((tax) => tax.type.value == _selectedFilter)
          .toList();
    }

    // تطبيق البحث النصي
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      filtered = filtered
          .where(
            (tax) =>
                tax.nameAr.toLowerCase().contains(query) ||
                tax.nameEn.toLowerCase().contains(query) ||
                tax.code.toLowerCase().contains(query),
          )
          .toList();
    }

    setState(() {
      _filteredTaxes = filtered;
    });
  }

  void _showError(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  Future<void> _deleteTax(Tax tax) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الضريبة "${tax.nameAr}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      final result = await _taxService.deleteTax(tax.id!);
      if (!mounted) return;

      if (result.isSuccess) {
        _loadTaxes();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم حذف الضريبة بنجاح')));
      } else {
        _showError(result.error!);
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الضرائب'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الضرائب', icon: Icon(Icons.receipt_long)),
            Tab(text: 'المجموعات', icon: Icon(Icons.group_work)),
            Tab(text: 'التقارير', icon: Icon(Icons.analytics)),
            Tab(text: 'الإعدادات', icon: Icon(Icons.settings)),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildTaxesTab(),
            const TaxGroupsScreen(),
            const TaxReportsScreen(),
            _buildSettingsTab(),
          ],
        ),
      ),
      floatingActionButton: _tabController.index == 0
          ? FloatingActionButton(
              onPressed: () => _navigateToAddTax(),
              backgroundColor: Theme.of(context).primaryColor,
              child: const Icon(Icons.add, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildTaxesTab() {
    return Column(
      children: [
        // شريط البحث والفلاتر
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: QuantumTextField(
                      controller: _searchController,
                      labelText: 'البحث في الضرائب',
                      prefixIcon: Icons.search,
                      onChanged: (_) => _applyFilters(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: QuantumDropdown<String>(
                      value: _selectedFilter,
                      items: const [
                        DropdownMenuItem(
                          value: 'all',
                          child: Text('جميع الأنواع'),
                        ),
                        DropdownMenuItem(
                          value: 'vat',
                          child: Text('ضريبة القيمة المضافة'),
                        ),
                        DropdownMenuItem(
                          value: 'income_tax',
                          child: Text('ضريبة الدخل'),
                        ),
                        DropdownMenuItem(
                          value: 'withholding_tax',
                          child: Text('ضريبة الاستقطاع'),
                        ),
                        DropdownMenuItem(
                          value: 'corporate_tax',
                          child: Text('ضريبة الشركات'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedFilter = value!);
                        _applyFilters();
                      },
                      labelText: 'نوع الضريبة',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // قائمة الضرائب
        Expanded(
          child: _filteredTaxes.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد ضرائب',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اضغط على زر + لإضافة ضريبة جديدة',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredTaxes.length,
                  itemBuilder: (context, index) {
                    final tax = _filteredTaxes[index];
                    return _buildTaxCard(tax);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildTaxCard(Tax tax) {
    return QuantumCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: tax.status == TaxStatus.active
              ? Colors.green[100]
              : Colors.orange[100],
          child: Icon(
            _getTaxTypeIcon(tax.type),
            color: tax.status == TaxStatus.active
                ? Colors.green[700]
                : Colors.orange[700],
          ),
        ),
        title: Text(
          tax.nameAr,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرمز: ${tax.code}'),
            Text('النوع: ${_getTaxTypeName(tax.type)}'),
            Text(
              'المعدل: ${tax.rate}${tax.calculationMethod == TaxCalculationMethod.percentage ? '%' : ' ريال'}',
            ),
            if (tax.description?.isNotEmpty == true)
              Text(tax.description!, style: TextStyle(color: Colors.grey[600])),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _navigateToEditTax(tax);
                break;
              case 'delete':
                _deleteTax(tax);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildSettingsTab() {
    return const Center(child: Text('إعدادات الضرائب - قيد التطوير'));
  }

  IconData _getTaxTypeIcon(TaxType type) {
    switch (type) {
      case TaxType.vat:
        return Icons.receipt;
      case TaxType.incomeTax:
        return Icons.account_balance;
      case TaxType.withholdingTax:
        return Icons.remove_circle;
      case TaxType.corporateTax:
        return Icons.business;
      case TaxType.customsDuty:
        return Icons.local_shipping;
      case TaxType.exciseTax:
        return Icons.local_gas_station;
      case TaxType.other:
        return Icons.category;
    }
  }

  String _getTaxTypeName(TaxType type) {
    switch (type) {
      case TaxType.vat:
        return 'ضريبة القيمة المضافة';
      case TaxType.incomeTax:
        return 'ضريبة الدخل';
      case TaxType.withholdingTax:
        return 'ضريبة الاستقطاع';
      case TaxType.corporateTax:
        return 'ضريبة الشركات';
      case TaxType.customsDuty:
        return 'رسوم جمركية';
      case TaxType.exciseTax:
        return 'ضريبة انتقائية';
      case TaxType.other:
        return 'أخرى';
    }
  }

  void _navigateToAddTax() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddTaxScreen()),
    ).then((_) => _loadTaxes());
  }

  void _navigateToEditTax(Tax tax) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => AddTaxScreen(tax: tax)),
    ).then((_) => _loadTaxes());
  }
}
