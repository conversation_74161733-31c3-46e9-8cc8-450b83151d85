import '../models/asset_depreciation.dart';
import '../models/fixed_asset.dart';
import 'database_helper.dart';
import 'database_schema.dart';

/// Data Access Object لاستهلاك الأصول الثابتة
class AssetDepreciationDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// الحصول على جميع سجلات الاستهلاك
  Future<List<AssetDepreciation>> getAllDepreciations() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAssetDepreciation,
      orderBy: 'depreciation_date DESC',
    );
    return List.generate(
      maps.length,
      (i) => AssetDepreciation.fromMap(maps[i]),
    );
  }

  /// الحصول على استهلاك أصل معين
  Future<List<AssetDepreciation>> getAssetDepreciations(int assetId) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAssetDepreciation,
      where: 'asset_id = ?',
      whereArgs: [assetId],
      orderBy: 'year ASC, month ASC',
    );
    return List.generate(
      maps.length,
      (i) => AssetDepreciation.fromMap(maps[i]),
    );
  }

  /// الحصول على استهلاك سنة معينة
  Future<List<AssetDepreciation>> getDepreciationsByYear(int year) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAssetDepreciation,
      where: 'year = ?',
      whereArgs: [year],
      orderBy: 'month ASC, asset_id ASC',
    );
    return List.generate(
      maps.length,
      (i) => AssetDepreciation.fromMap(maps[i]),
    );
  }

  /// الحصول على استهلاك شهر معين
  Future<List<AssetDepreciation>> getDepreciationsByMonth(
    int year,
    int month,
  ) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAssetDepreciation,
      where: 'year = ? AND month = ?',
      whereArgs: [year, month],
      orderBy: 'asset_id ASC',
    );
    return List.generate(
      maps.length,
      (i) => AssetDepreciation.fromMap(maps[i]),
    );
  }

  /// إدراج سجل استهلاك جديد
  Future<int> insertDepreciation(AssetDepreciation depreciation) async {
    final db = await _dbHelper.database;
    return await db.insert(
      DatabaseSchema.tableAssetDepreciation,
      depreciation.toMap(),
    );
  }

  /// تحديث سجل استهلاك
  Future<int> updateDepreciation(AssetDepreciation depreciation) async {
    final db = await _dbHelper.database;
    return await db.update(
      DatabaseSchema.tableAssetDepreciation,
      depreciation.toMap(),
      where: 'id = ?',
      whereArgs: [depreciation.id],
    );
  }

  /// حذف سجل استهلاك
  Future<int> deleteDepreciation(int id) async {
    final db = await _dbHelper.database;
    return await db.delete(
      DatabaseSchema.tableAssetDepreciation,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف جميع سجلات استهلاك أصل معين
  Future<int> deleteAssetDepreciations(int assetId) async {
    final db = await _dbHelper.database;
    return await db.delete(
      DatabaseSchema.tableAssetDepreciation,
      where: 'asset_id = ?',
      whereArgs: [assetId],
    );
  }

  /// التحقق من وجود استهلاك لأصل في شهر معين
  Future<AssetDepreciation?> getDepreciationByAssetAndMonth(
    int assetId,
    int year,
    int month,
  ) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAssetDepreciation,
      where: 'asset_id = ? AND year = ? AND month = ?',
      whereArgs: [assetId, year, month],
    );
    if (maps.isNotEmpty) {
      return AssetDepreciation.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على إجمالي الاستهلاك لأصل معين
  Future<double> getTotalDepreciationForAsset(int assetId) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT SUM(depreciation_amount) as total FROM ${DatabaseSchema.tableAssetDepreciation} WHERE asset_id = ?',
      [assetId],
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// الحصول على آخر استهلاك لأصل معين
  Future<AssetDepreciation?> getLatestDepreciationForAsset(int assetId) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAssetDepreciation,
      where: 'asset_id = ?',
      whereArgs: [assetId],
      orderBy: 'year DESC, month DESC',
      limit: 1,
    );
    if (maps.isNotEmpty) {
      return AssetDepreciation.fromMap(maps.first);
    }
    return null;
  }

  /// إنشاء جدول استهلاك لأصل معين
  Future<List<AssetDepreciation>> generateDepreciationSchedule(
    FixedAsset asset, {
    DateTime? startDate,
  }) async {
    final schedule = <AssetDepreciation>[];
    final start = startDate ?? asset.purchaseDate;
    final depreciableAmount = asset.purchasePrice - (asset.salvageValue ?? 0.0);

    if (depreciableAmount <= 0) return schedule;

    switch (asset.depreciationMethod) {
      case DepreciationMethod.straightLine:
        schedule.addAll(
          _generateStraightLineSchedule(asset, start, depreciableAmount),
        );
        break;
      case DepreciationMethod.decliningBalance:
        schedule.addAll(_generateDecliningBalanceSchedule(asset, start));
        break;
      case DepreciationMethod.sumOfYears:
        schedule.addAll(
          _generateSumOfYearsSchedule(asset, start, depreciableAmount),
        );
        break;
      case DepreciationMethod.unitsOfProduction:
        // يحتاج إلى بيانات الإنتاج الفعلية
        schedule.addAll(
          _generateStraightLineSchedule(asset, start, depreciableAmount),
        );
        break;
    }

    return schedule;
  }

  /// إنشاء جدول استهلاك بطريقة الخط المستقيم
  List<AssetDepreciation> _generateStraightLineSchedule(
    FixedAsset asset,
    DateTime startDate,
    double depreciableAmount,
  ) {
    final schedule = <AssetDepreciation>[];
    final monthlyDepreciation =
        depreciableAmount / (asset.usefulLifeYears * 12);
    double accumulatedDepreciation = 0.0;

    for (int year = 0; year < asset.usefulLifeYears; year++) {
      for (int month = 1; month <= 12; month++) {
        final depreciationDate = DateTime(
          startDate.year + year,
          startDate.month + month - 1,
          startDate.day,
        );

        accumulatedDepreciation += monthlyDepreciation;
        final bookValue = asset.purchasePrice - accumulatedDepreciation;

        schedule.add(
          AssetDepreciation(
            assetId: asset.id!,
            year: depreciationDate.year,
            month: depreciationDate.month,
            depreciationDate: depreciationDate,
            depreciationAmount: monthlyDepreciation,
            accumulatedDepreciation: accumulatedDepreciation,
            bookValue: bookValue,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        if (accumulatedDepreciation >= depreciableAmount) break;
      }
      if (accumulatedDepreciation >= depreciableAmount) break;
    }

    return schedule;
  }

  /// إنشاء جدول استهلاك بطريقة الرصيد المتناقص
  List<AssetDepreciation> _generateDecliningBalanceSchedule(
    FixedAsset asset,
    DateTime startDate,
  ) {
    final schedule = <AssetDepreciation>[];
    final rate = 2.0 / asset.usefulLifeYears; // معدل الاستهلاك المضاعف
    final monthlyRate = rate / 12;
    double bookValue = asset.purchasePrice;
    double accumulatedDepreciation = 0.0;
    final salvageValue = asset.salvageValue ?? 0.0;

    for (int year = 0; year < asset.usefulLifeYears; year++) {
      for (int month = 1; month <= 12; month++) {
        final depreciationDate = DateTime(
          startDate.year + year,
          startDate.month + month - 1,
          startDate.day,
        );

        final monthlyDepreciation = bookValue * monthlyRate;

        // التأكد من عدم تجاوز القيمة المتبقية
        if (bookValue - monthlyDepreciation < salvageValue) {
          final finalDepreciation = bookValue - salvageValue;
          if (finalDepreciation > 0) {
            accumulatedDepreciation += finalDepreciation;
            bookValue = salvageValue;

            schedule.add(
              AssetDepreciation(
                assetId: asset.id!,
                year: depreciationDate.year,
                month: depreciationDate.month,
                depreciationDate: depreciationDate,
                depreciationAmount: finalDepreciation,
                accumulatedDepreciation: accumulatedDepreciation,
                bookValue: bookValue,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );
          }
          break;
        }

        accumulatedDepreciation += monthlyDepreciation;
        bookValue -= monthlyDepreciation;

        schedule.add(
          AssetDepreciation(
            assetId: asset.id!,
            year: depreciationDate.year,
            month: depreciationDate.month,
            depreciationDate: depreciationDate,
            depreciationAmount: monthlyDepreciation,
            accumulatedDepreciation: accumulatedDepreciation,
            bookValue: bookValue,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
      }
      if (bookValue <= salvageValue) break;
    }

    return schedule;
  }

  /// إنشاء جدول استهلاك بطريقة مجموع السنوات
  List<AssetDepreciation> _generateSumOfYearsSchedule(
    FixedAsset asset,
    DateTime startDate,
    double depreciableAmount,
  ) {
    final schedule = <AssetDepreciation>[];
    final sumOfYears = asset.usefulLifeYears * (asset.usefulLifeYears + 1) / 2;
    double accumulatedDepreciation = 0.0;

    for (int year = 1; year <= asset.usefulLifeYears; year++) {
      final yearFraction = (asset.usefulLifeYears - year + 1) / sumOfYears;
      final yearlyDepreciation = depreciableAmount * yearFraction;
      final monthlyDepreciation = yearlyDepreciation / 12;

      for (int month = 1; month <= 12; month++) {
        final depreciationDate = DateTime(
          startDate.year + year - 1,
          startDate.month + month - 1,
          startDate.day,
        );

        accumulatedDepreciation += monthlyDepreciation;
        final bookValue = asset.purchasePrice - accumulatedDepreciation;

        schedule.add(
          AssetDepreciation(
            assetId: asset.id!,
            year: depreciationDate.year,
            month: depreciationDate.month,
            depreciationDate: depreciationDate,
            depreciationAmount: monthlyDepreciation,
            accumulatedDepreciation: accumulatedDepreciation,
            bookValue: bookValue,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
      }
    }

    return schedule;
  }

  /// الحصول على إحصائيات الاستهلاك
  Future<Map<String, dynamic>> getDepreciationStatistics() async {
    final db = await _dbHelper.database;

    final totalResult = await db.rawQuery(
      'SELECT SUM(depreciation_amount) as total_depreciation, COUNT(*) as total_records FROM ${DatabaseSchema.tableAssetDepreciation}',
    );

    final currentYearResult = await db.rawQuery(
      'SELECT SUM(depreciation_amount) as current_year_depreciation FROM ${DatabaseSchema.tableAssetDepreciation} WHERE year = ?',
      [DateTime.now().year],
    );

    final postedResult = await db.rawQuery(
      'SELECT COUNT(*) as posted_count FROM ${DatabaseSchema.tableAssetDepreciation} WHERE is_posted = 1',
    );

    return {
      'total_depreciation':
          (totalResult.first['total_depreciation'] as double?) ?? 0.0,
      'total_records': totalResult.first['total_records'] as int,
      'current_year_depreciation':
          (currentYearResult.first['current_year_depreciation'] as double?) ??
          0.0,
      'posted_count': postedResult.first['posted_count'] as int,
    };
  }
}
