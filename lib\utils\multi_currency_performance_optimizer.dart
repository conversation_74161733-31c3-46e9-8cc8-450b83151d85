import 'dart:async';
import '../models/currency.dart';
import '../models/exchange_rate.dart';
import '../services/currency_service.dart';
import '../services/exchange_rate_service.dart';

/// محسن الأداء لنظام العملات المتعددة
/// يقوم بتحسين عمليات التحويل والاستعلامات المتكررة
class MultiCurrencyPerformanceOptimizer {
  static final MultiCurrencyPerformanceOptimizer _instance =
      MultiCurrencyPerformanceOptimizer._internal();

  factory MultiCurrencyPerformanceOptimizer() => _instance;

  MultiCurrencyPerformanceOptimizer._internal();

  final CurrencyService _currencyService = CurrencyService();
  final ExchangeRateService _exchangeRateService = ExchangeRateService();

  // ذاكرة التخزين المؤقت للعملات
  Map<String, Currency> _currencyCache = {};
  DateTime? _currencyCacheTime;
  static const Duration _currencyCacheDuration = Duration(hours: 1);

  // ذاكرة التخزين المؤقت لأسعار الصرف
  final Map<String, ExchangeRate> _exchangeRateCache = {};
  DateTime? _exchangeRateCacheTime;
  static const Duration _exchangeRateCacheDuration = Duration(minutes: 15);

  // ذاكرة التخزين المؤقت للتحويلات
  final Map<String, double> _conversionCache = {};
  DateTime? _conversionCacheTime;
  static const Duration _conversionCacheDuration = Duration(minutes: 5);

  // العملة الأساسية المخزنة مؤقتاً
  Currency? _baseCurrency;
  DateTime? _baseCurrencyTime;

  /// الحصول على العملة بتحسين الأداء
  Future<Currency?> getCurrency(String currencyCode) async {
    // التحقق من ذاكرة التخزين المؤقت
    if (_isCurrencyCacheValid() && _currencyCache.containsKey(currencyCode)) {
      return _currencyCache[currencyCode];
    }

    // تحديث ذاكرة التخزين المؤقت إذا لزم الأمر
    await _refreshCurrencyCache();

    return _currencyCache[currencyCode];
  }

  /// الحصول على جميع العملات بتحسين الأداء
  Future<List<Currency>> getAllCurrencies() async {
    if (!_isCurrencyCacheValid()) {
      await _refreshCurrencyCache();
    }

    return _currencyCache.values.toList();
  }

  /// الحصول على العملة الأساسية بتحسين الأداء
  Future<Currency?> getBaseCurrency() async {
    if (_baseCurrency != null &&
        _baseCurrencyTime != null &&
        DateTime.now().difference(_baseCurrencyTime!) <
            _currencyCacheDuration) {
      return _baseCurrency;
    }

    final currencies = await getAllCurrencies();
    _baseCurrency = currencies.where((c) => c.isBaseCurrency).firstOrNull;
    _baseCurrencyTime = DateTime.now();

    return _baseCurrency;
  }

  /// الحصول على سعر الصرف بتحسين الأداء
  Future<ExchangeRate?> getExchangeRate(
    String fromCurrency,
    String toCurrency,
  ) async {
    final key = '${fromCurrency}_$toCurrency';

    // التحقق من ذاكرة التخزين المؤقت
    if (_isExchangeRateCacheValid() && _exchangeRateCache.containsKey(key)) {
      return _exchangeRateCache[key];
    }

    // الحصول من قاعدة البيانات
    final rate = await _exchangeRateService.getLatestExchangeRate(
      fromCurrency,
      toCurrency,
    );

    if (rate != null) {
      _exchangeRateCache[key] = rate;
      _exchangeRateCacheTime = DateTime.now();
    }

    return rate;
  }

  /// تحويل المبلغ بتحسين الأداء
  Future<double> convertAmount({
    required double amount,
    required String fromCurrency,
    required String toCurrency,
  }) async {
    // إذا كانت العملتان متشابهتان
    if (fromCurrency == toCurrency) {
      return amount;
    }

    final conversionKey = '${amount}_${fromCurrency}_$toCurrency';

    // التحقق من ذاكرة التخزين المؤقت
    if (_isConversionCacheValid() &&
        _conversionCache.containsKey(conversionKey)) {
      return _conversionCache[conversionKey]!;
    }

    // الحصول على سعر الصرف
    final exchangeRate = await getExchangeRate(fromCurrency, toCurrency);

    if (exchangeRate == null) {
      throw Exception('لا يوجد سعر صرف متاح من $fromCurrency إلى $toCurrency');
    }

    final convertedAmount = exchangeRate.convertAmount(amount);

    // حفظ في ذاكرة التخزين المؤقت
    _conversionCache[conversionKey] = convertedAmount;
    _conversionCacheTime = DateTime.now();

    return convertedAmount;
  }

  /// تحويل متعدد للمبالغ (أكثر كفاءة للعمليات المتعددة)
  Future<Map<String, double>> convertAmountToMultipleCurrencies({
    required double amount,
    required String fromCurrency,
    required List<String> toCurrencies,
  }) async {
    final results = <String, double>{};

    // تحميل جميع أسعار الصرف مرة واحدة
    final futures = toCurrencies.map((toCurrency) async {
      final convertedAmount = await convertAmount(
        amount: amount,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
      );
      return MapEntry(toCurrency, convertedAmount);
    });

    final entries = await Future.wait(futures);

    for (final entry in entries) {
      results[entry.key] = entry.value;
    }

    return results;
  }

  /// تحديث أسعار الصرف بشكل مجمع
  Future<void> batchUpdateExchangeRates(List<String> currencyCodes) async {
    final baseCurrency = await getBaseCurrency();
    if (baseCurrency == null) return;

    final futures = currencyCodes.map((currencyCode) async {
      if (currencyCode != baseCurrency.code) {
        return await _exchangeRateService.getLatestExchangeRate(
          currencyCode,
          baseCurrency.code,
        );
      }
      return null;
    });

    final rates = await Future.wait(futures);

    // تحديث ذاكرة التخزين المؤقت
    for (int i = 0; i < currencyCodes.length; i++) {
      final rate = rates[i];
      if (rate != null) {
        final key = '${currencyCodes[i]}_${baseCurrency.code}';
        _exchangeRateCache[key] = rate;
      }
    }

    _exchangeRateCacheTime = DateTime.now();
  }

  /// مسح ذاكرة التخزين المؤقت
  void clearCache() {
    _currencyCache.clear();
    _currencyCacheTime = null;

    _exchangeRateCache.clear();
    _exchangeRateCacheTime = null;

    _conversionCache.clear();
    _conversionCacheTime = null;

    _baseCurrency = null;
    _baseCurrencyTime = null;
  }

  /// مسح ذاكرة التخزين المؤقت للعملات فقط
  void clearCurrencyCache() {
    _currencyCache.clear();
    _currencyCacheTime = null;
    _baseCurrency = null;
    _baseCurrencyTime = null;
  }

  /// مسح ذاكرة التخزين المؤقت لأسعار الصرف فقط
  void clearExchangeRateCache() {
    _exchangeRateCache.clear();
    _exchangeRateCacheTime = null;
    _conversionCache.clear();
    _conversionCacheTime = null;
  }

  /// تحديث ذاكرة التخزين المؤقت للعملات
  Future<void> _refreshCurrencyCache() async {
    final currencies = await _currencyService.getAllCurrencies();
    _currencyCache = {
      for (final currency in currencies) currency.code: currency,
    };
    _currencyCacheTime = DateTime.now();
  }

  /// التحقق من صحة ذاكرة التخزين المؤقت للعملات
  bool _isCurrencyCacheValid() {
    return _currencyCacheTime != null &&
        DateTime.now().difference(_currencyCacheTime!) < _currencyCacheDuration;
  }

  /// التحقق من صحة ذاكرة التخزين المؤقت لأسعار الصرف
  bool _isExchangeRateCacheValid() {
    return _exchangeRateCacheTime != null &&
        DateTime.now().difference(_exchangeRateCacheTime!) <
            _exchangeRateCacheDuration;
  }

  /// التحقق من صحة ذاكرة التخزين المؤقت للتحويلات
  bool _isConversionCacheValid() {
    return _conversionCacheTime != null &&
        DateTime.now().difference(_conversionCacheTime!) <
            _conversionCacheDuration;
  }

  /// إحصائيات الأداء
  Map<String, dynamic> getPerformanceStats() {
    return {
      'currencyCacheSize': _currencyCache.length,
      'exchangeRateCacheSize': _exchangeRateCache.length,
      'conversionCacheSize': _conversionCache.length,
      'currencyCacheValid': _isCurrencyCacheValid(),
      'exchangeRateCacheValid': _isExchangeRateCacheValid(),
      'conversionCacheValid': _isConversionCacheValid(),
      'lastCurrencyUpdate': _currencyCacheTime?.toIso8601String(),
      'lastExchangeRateUpdate': _exchangeRateCacheTime?.toIso8601String(),
      'lastConversionUpdate': _conversionCacheTime?.toIso8601String(),
    };
  }

  /// تحسين الذاكرة بإزالة البيانات القديمة
  void optimizeMemory() {
    final now = DateTime.now();

    // إزالة العملات القديمة
    if (_currencyCacheTime != null &&
        now.difference(_currencyCacheTime!) > _currencyCacheDuration) {
      clearCurrencyCache();
    }

    // إزالة أسعار الصرف القديمة
    if (_exchangeRateCacheTime != null &&
        now.difference(_exchangeRateCacheTime!) > _exchangeRateCacheDuration) {
      _exchangeRateCache.clear();
      _exchangeRateCacheTime = null;
    }

    // إزالة التحويلات القديمة
    if (_conversionCacheTime != null &&
        now.difference(_conversionCacheTime!) > _conversionCacheDuration) {
      _conversionCache.clear();
      _conversionCacheTime = null;
    }
  }
}
