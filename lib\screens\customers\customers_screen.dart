import 'package:flutter/material.dart';
import '../../models/customer.dart';
import '../../services/customer_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import '../../widgets/smart_notifications.dart';
import '../../animations/app_animations.dart';
import 'add_customer_screen.dart';
import 'customer_details_screen.dart';

/// شاشة إدارة العملاء
class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  final CustomerService _customerService = CustomerService();
  List<Customer> _customers = [];
  List<Customer> _filteredCustomers = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _customerService.getAllCustomers();
      if (result.isSuccess) {
        setState(() {
          _customers = result.data!;
          _filteredCustomers = _customers;
          _isLoading = false;
        });
      } else {
        if (!mounted) return;
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: result.error ?? 'فشل في تحميل العملاء',
        );
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      SmartNotificationManager.showError(
        context,
        title: 'خطأ',
        message: 'حدث خطأ أثناء تحميل العملاء: ${e.toString()}',
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterCustomers(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredCustomers = _customers;
      } else {
        _filteredCustomers = _customers.where((customer) {
          return customer.name.toLowerCase().contains(query.toLowerCase()) ||
              customer.code.toLowerCase().contains(query.toLowerCase()) ||
              (customer.phone?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (customer.email?.toLowerCase().contains(query.toLowerCase()) ??
                  false);
        }).toList();
      }
    });
  }

  Future<void> _deleteCustomer(Customer customer) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف العميل "${customer.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await _customerService.deleteCustomer(customer.id!);
      if (!mounted) return;

      if (result.isSuccess) {
        SmartNotificationManager.showSuccess(
          context,
          title: 'تم الحذف',
          message: 'تم حذف العميل بنجاح',
        );
        _loadCustomers();
      } else {
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: result.error ?? 'فشل في حذف العميل',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة العملاء'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCustomers,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في العملاء...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterCustomers('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusMedium,
                  ),
                ),
              ),
              onChanged: _filterCustomers,
            ),
          ),

          // قائمة العملاء
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredCustomers.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: AppTheme.spacingMedium),
                        Text(
                          _searchQuery.isEmpty
                              ? 'لا توجد عملاء مسجلين'
                              : 'لا توجد نتائج للبحث',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: AppTheme.spacingSmall),
                        Text(
                          _searchQuery.isEmpty
                              ? 'اضغط على زر + لإضافة عميل جديد'
                              : 'جرب البحث بكلمات مختلفة',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[500]),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(AppTheme.spacingMedium),
                    itemCount: _filteredCustomers.length,
                    itemBuilder: (context, index) {
                      final customer = _filteredCustomers[index];
                      return StaggeredAnimation(
                        index: index,
                        child: _buildCustomerCard(customer),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const AddCustomerScreen()),
          );
          if (result == true) {
            _loadCustomers();
          }
        },
        icon: const Icon(Icons.add),
        label: const Text('إضافة عميل'),
      ),
    );
  }

  Widget _buildCustomerCard(Customer customer) {
    return BeautifulCard(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: Text(
            customer.name.isNotEmpty ? customer.name[0].toUpperCase() : 'ع',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          customer.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرمز: ${customer.code}'),
            if (customer.phone != null && customer.phone!.isNotEmpty)
              Text('الهاتف: ${customer.phone}'),
            if (customer.email != null && customer.email!.isNotEmpty)
              Text('البريد: ${customer.email}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'view':
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) =>
                        CustomerDetailsScreen(customer: customer),
                  ),
                );
                break;
              case 'edit':
                Navigator.of(context)
                    .push(
                      MaterialPageRoute(
                        builder: (context) =>
                            AddCustomerScreen(customer: customer),
                      ),
                    )
                    .then((result) {
                      if (result == true) {
                        _loadCustomers();
                      }
                    });
                break;
              case 'delete':
                _deleteCustomer(customer);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: ListTile(
                leading: Icon(Icons.visibility),
                title: Text('عرض التفاصيل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => CustomerDetailsScreen(customer: customer),
            ),
          );
        },
      ),
    );
  }
}
