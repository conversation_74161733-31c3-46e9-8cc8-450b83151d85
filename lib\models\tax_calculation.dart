/// نماذج حساب الضرائب
/// Tax Calculation Models for Smart Ledger
library;

import 'tax.dart';

/// نموذج حساب الضريبة
class TaxCalculation {
  final int? id;
  final String documentType; // invoice, journal_entry, payment
  final int documentId;
  final int taxId;
  final double baseAmount;
  final double taxAmount;
  final double taxRate;
  final bool isInclusive;
  final String? notes;
  final DateTime calculatedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Tax? tax;

  TaxCalculation({
    this.id,
    required this.documentType,
    required this.documentId,
    required this.taxId,
    required this.baseAmount,
    required this.taxAmount,
    required this.taxRate,
    this.isInclusive = false,
    this.notes,
    DateTime? calculatedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : calculatedAt = calculatedAt ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from map
  factory TaxCalculation.fromMap(Map<String, dynamic> map) {
    return TaxCalculation(
      id: map['id'] as int?,
      documentType: map['document_type'] as String,
      documentId: map['document_id'] as int,
      taxId: map['tax_id'] as int,
      baseAmount: (map['base_amount'] as num).toDouble(),
      taxAmount: (map['tax_amount'] as num).toDouble(),
      taxRate: (map['tax_rate'] as num).toDouble(),
      isInclusive: (map['is_inclusive'] as int) == 1,
      notes: map['notes'] as String?,
      calculatedAt: DateTime.parse(map['calculated_at'] as String),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'document_type': documentType,
      'document_id': documentId,
      'tax_id': taxId,
      'base_amount': baseAmount,
      'tax_amount': taxAmount,
      'tax_rate': taxRate,
      'is_inclusive': isInclusive ? 1 : 0,
      'notes': notes,
      'calculated_at': calculatedAt.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method
  TaxCalculation copyWith({
    int? id,
    String? documentType,
    int? documentId,
    int? taxId,
    double? baseAmount,
    double? taxAmount,
    double? taxRate,
    bool? isInclusive,
    String? notes,
    DateTime? calculatedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    Tax? tax,
  }) {
    return TaxCalculation(
      id: id ?? this.id,
      documentType: documentType ?? this.documentType,
      documentId: documentId ?? this.documentId,
      taxId: taxId ?? this.taxId,
      baseAmount: baseAmount ?? this.baseAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      taxRate: taxRate ?? this.taxRate,
      isInclusive: isInclusive ?? this.isInclusive,
      notes: notes ?? this.notes,
      calculatedAt: calculatedAt ?? this.calculatedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    )..tax = tax ?? this.tax;
  }

  /// حساب النسبة المئوية للضريبة
  double get taxPercentage =>
      baseAmount == 0 ? 0 : (taxAmount / baseAmount) * 100;

  /// حساب المبلغ الإجمالي
  double get totalAmount => isInclusive ? baseAmount : baseAmount + taxAmount;

  /// حساب المبلغ الأساسي من المبلغ الإجمالي (للضرائب المشمولة)
  double calculateBaseFromTotal(double totalAmount) {
    if (!isInclusive) return totalAmount;
    return totalAmount / (1 + (taxRate / 100));
  }

  @override
  String toString() =>
      'Tax Calculation: ${tax?.nameAr ?? 'Unknown'} - $taxAmount';
}

/// نموذج تقرير الضرائب
class TaxReport {
  final DateTime startDate;
  final DateTime endDate;
  final String reportType; // vat_return, income_tax, withholding_tax
  final Map<String, double> taxSummary;
  final List<TaxCalculation> calculations;
  final double totalTaxCollected;
  final double totalTaxPaid;
  final double netTaxDue;
  final DateTime generatedAt;

  TaxReport({
    required this.startDate,
    required this.endDate,
    required this.reportType,
    required this.taxSummary,
    required this.calculations,
    required this.totalTaxCollected,
    required this.totalTaxPaid,
    required this.netTaxDue,
    DateTime? generatedAt,
  }) : generatedAt = generatedAt ?? DateTime.now();

  /// الحصول على حسابات ضريبة المبيعات
  List<TaxCalculation> get salesTaxCalculations {
    return calculations
        .where(
          (calc) =>
              calc.documentType == 'invoice' && calc.tax?.type == TaxType.vat,
        )
        .toList();
  }

  /// الحصول على حسابات ضريبة المشتريات
  List<TaxCalculation> get purchaseTaxCalculations {
    return calculations
        .where(
          (calc) =>
              calc.documentType == 'purchase' && calc.tax?.type == TaxType.vat,
        )
        .toList();
  }

  /// إنشاء تقرير ضريبة القيمة المضافة
  factory TaxReport.vatReturn({
    required DateTime startDate,
    required DateTime endDate,
    required List<TaxCalculation> salesTax,
    required List<TaxCalculation> purchaseTax,
  }) {
    double totalSalesTax = salesTax.fold(
      0.0,
      (sum, calc) => sum + calc.taxAmount,
    );
    double totalPurchaseTax = purchaseTax.fold(
      0.0,
      (sum, calc) => sum + calc.taxAmount,
    );
    double netVAT = totalSalesTax - totalPurchaseTax;

    Map<String, double> summary = {
      'sales_tax': totalSalesTax,
      'purchase_tax': totalPurchaseTax,
      'net_vat': netVAT,
    };

    List<TaxCalculation> allCalculations = [...salesTax, ...purchaseTax];

    return TaxReport(
      startDate: startDate,
      endDate: endDate,
      reportType: 'vat_return',
      taxSummary: summary,
      calculations: allCalculations,
      totalTaxCollected: totalSalesTax,
      totalTaxPaid: totalPurchaseTax,
      netTaxDue: netVAT,
    );
  }

  /// إنشاء تقرير ضريبة الاستقطاع
  factory TaxReport.withholdingTaxReport({
    required DateTime startDate,
    required DateTime endDate,
    required List<TaxCalculation> withholdingCalculations,
  }) {
    double totalWithholding = withholdingCalculations.fold(
      0.0,
      (sum, calc) => sum + calc.taxAmount,
    );

    Map<String, double> summary = {'total_withholding': totalWithholding};

    return TaxReport(
      startDate: startDate,
      endDate: endDate,
      reportType: 'withholding_tax',
      taxSummary: summary,
      calculations: withholdingCalculations,
      totalTaxCollected: 0.0,
      totalTaxPaid: totalWithholding,
      netTaxDue: totalWithholding,
    );
  }

  /// تصدير التقرير إلى CSV
  String toCsv() {
    StringBuffer csv = StringBuffer();

    // Header
    csv.writeln('Tax Report: $reportType');
    csv.writeln(
      'Period: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}',
    );
    csv.writeln('Generated: ${generatedAt.toIso8601String()}');
    csv.writeln('');

    // Summary
    csv.writeln('Summary:');
    taxSummary.forEach((key, value) {
      csv.writeln('$key,$value');
    });
    csv.writeln('');

    // Details
    csv.writeln(
      'Document Type,Document ID,Tax Code,Base Amount,Tax Rate,Tax Amount,Is Inclusive,Date',
    );
    for (TaxCalculation calc in calculations) {
      csv.writeln(
        '${calc.documentType},${calc.documentId},${calc.tax?.code ?? 'N/A'},${calc.baseAmount},${calc.taxRate},${calc.taxAmount},${calc.isInclusive},${calc.calculatedAt.toIso8601String().split('T')[0]}',
      );
    }

    return csv.toString();
  }

  /// تصدير التقرير إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'reportType': reportType,
      'taxSummary': taxSummary,
      'totalTaxCollected': totalTaxCollected,
      'totalTaxPaid': totalTaxPaid,
      'netTaxDue': netTaxDue,
      'generatedAt': generatedAt.toIso8601String(),
      'calculations': calculations.map((calc) => calc.toMap()).toList(),
    };
  }
}

/// نموذج إعدادات الضرائب للشركة
class CompanyTaxSettings {
  final int? id;
  final String taxNumber;
  final String? vatNumber;
  final String taxAuthority;
  final String taxPeriod; // monthly, quarterly, annually
  final DateTime? taxYearStart;
  final DateTime? taxYearEnd;
  final bool isVatRegistered;
  final bool isWithholdingAgent;
  final int? defaultTaxGroupId;
  final Map<String, dynamic> additionalSettings;
  final DateTime createdAt;
  final DateTime updatedAt;

  CompanyTaxSettings({
    this.id,
    required this.taxNumber,
    this.vatNumber,
    required this.taxAuthority,
    this.taxPeriod = 'monthly',
    this.taxYearStart,
    this.taxYearEnd,
    this.isVatRegistered = false,
    this.isWithholdingAgent = false,
    this.defaultTaxGroupId,
    this.additionalSettings = const {},
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from map
  factory CompanyTaxSettings.fromMap(Map<String, dynamic> map) {
    return CompanyTaxSettings(
      id: map['id'] as int?,
      taxNumber: map['tax_number'] as String,
      vatNumber: map['vat_number'] as String?,
      taxAuthority: map['tax_authority'] as String,
      taxPeriod: map['tax_period'] as String? ?? 'monthly',
      taxYearStart: map['tax_year_start'] != null
          ? DateTime.parse(map['tax_year_start'] as String)
          : null,
      taxYearEnd: map['tax_year_end'] != null
          ? DateTime.parse(map['tax_year_end'] as String)
          : null,
      isVatRegistered: (map['is_vat_registered'] as int) == 1,
      isWithholdingAgent: (map['is_withholding_agent'] as int) == 1,
      defaultTaxGroupId: map['default_tax_group_id'] as int?,
      additionalSettings: map['additional_settings'] != null
          ? Map<String, dynamic>.from(map['additional_settings'])
          : {},
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tax_number': taxNumber,
      'vat_number': vatNumber,
      'tax_authority': taxAuthority,
      'tax_period': taxPeriod,
      'tax_year_start': taxYearStart?.toIso8601String().split('T')[0],
      'tax_year_end': taxYearEnd?.toIso8601String().split('T')[0],
      'is_vat_registered': isVatRegistered ? 1 : 0,
      'is_withholding_agent': isWithholdingAgent ? 1 : 0,
      'default_tax_group_id': defaultTaxGroupId,
      'additional_settings': additionalSettings,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method
  CompanyTaxSettings copyWith({
    int? id,
    String? taxNumber,
    String? vatNumber,
    String? taxAuthority,
    String? taxPeriod,
    DateTime? taxYearStart,
    DateTime? taxYearEnd,
    bool? isVatRegistered,
    bool? isWithholdingAgent,
    int? defaultTaxGroupId,
    Map<String, dynamic>? additionalSettings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyTaxSettings(
      id: id ?? this.id,
      taxNumber: taxNumber ?? this.taxNumber,
      vatNumber: vatNumber ?? this.vatNumber,
      taxAuthority: taxAuthority ?? this.taxAuthority,
      taxPeriod: taxPeriod ?? this.taxPeriod,
      taxYearStart: taxYearStart ?? this.taxYearStart,
      taxYearEnd: taxYearEnd ?? this.taxYearEnd,
      isVatRegistered: isVatRegistered ?? this.isVatRegistered,
      isWithholdingAgent: isWithholdingAgent ?? this.isWithholdingAgent,
      defaultTaxGroupId: defaultTaxGroupId ?? this.defaultTaxGroupId,
      additionalSettings: additionalSettings ?? this.additionalSettings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
