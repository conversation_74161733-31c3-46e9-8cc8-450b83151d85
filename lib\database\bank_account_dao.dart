/// Data Access Object for Bank Accounts
/// Bank Account DAO for Smart Ledger
library;


import '../models/bank_account.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class BankAccountDao {
  static final BankAccountDao _instance = BankAccountDao._internal();
  factory BankAccountDao() => _instance;
  BankAccountDao._internal();

  /// Get all bank accounts
  Future<List<BankAccount>> getAllBankAccounts() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      orderBy: 'account_name ASC',
    );

    return List.generate(maps.length, (i) {
      return BankAccount.fromMap(maps[i]);
    });
  }

  /// Get bank account by ID
  Future<BankAccount?> getBankAccountById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return BankAccount.fromMap(maps.first);
    }
    return null;
  }

  /// Get bank account by account number
  Future<BankAccount?> getBankAccountByNumber(String accountNumber) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      where: 'account_number = ?',
      whereArgs: [accountNumber],
    );

    if (maps.isNotEmpty) {
      return BankAccount.fromMap(maps.first);
    }
    return null;
  }

  /// Get active bank accounts
  Future<List<BankAccount>> getActiveBankAccounts() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      where: 'status = ?',
      whereArgs: ['active'],
      orderBy: 'account_name ASC',
    );

    return List.generate(maps.length, (i) {
      return BankAccount.fromMap(maps[i]);
    });
  }

  /// Get bank accounts by type
  Future<List<BankAccount>> getBankAccountsByType(BankAccountType type) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      where: 'account_type = ?',
      whereArgs: [type.value],
      orderBy: 'account_name ASC',
    );

    return List.generate(maps.length, (i) {
      return BankAccount.fromMap(maps[i]);
    });
  }

  /// Get bank accounts by currency
  Future<List<BankAccount>> getBankAccountsByCurrency(String currency) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      where: 'currency = ?',
      whereArgs: [currency],
      orderBy: 'account_name ASC',
    );

    return List.generate(maps.length, (i) {
      return BankAccount.fromMap(maps[i]);
    });
  }

  /// Insert new bank account
  Future<int> insertBankAccount(BankAccount account) async {
    final db = await DatabaseHelper().database;
    
    // Check if account number already exists
    final existing = await getBankAccountByNumber(account.accountNumber);
    if (existing != null) {
      throw Exception('رقم الحساب موجود مسبقاً');
    }

    final accountMap = account.toMap();
    accountMap.remove('id'); // Remove ID for auto-increment
    accountMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableBankAccounts,
      accountMap,
    );
  }

  /// Update bank account
  Future<int> updateBankAccount(BankAccount account) async {
    final db = await DatabaseHelper().database;
    
    // Check if account number already exists for different account
    final existing = await getBankAccountByNumber(account.accountNumber);
    if (existing != null && existing.id != account.id) {
      throw Exception('رقم الحساب موجود مسبقاً');
    }

    final accountMap = account.toMap();
    accountMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableBankAccounts,
      accountMap,
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  /// Update bank account balance
  Future<int> updateBankAccountBalance(int accountId, double newBalance) async {
    final db = await DatabaseHelper().database;
    
    return await db.update(
      DatabaseSchema.tableBankAccounts,
      {
        'current_balance': newBalance,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [accountId],
    );
  }

  /// Delete bank account
  Future<int> deleteBankAccount(int id) async {
    final db = await DatabaseHelper().database;
    
    // Check if account has transactions
    final transactionCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableBankTransactions} WHERE bank_account_id = ?',
      [id],
    );
    
    if ((transactionCount.first['count'] as int) > 0) {
      throw Exception('لا يمكن حذف الحساب لوجود معاملات مرتبطة به');
    }

    return await db.delete(
      DatabaseSchema.tableBankAccounts,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get bank account balance summary
  Future<Map<String, dynamic>> getBankAccountBalanceSummary() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        currency,
        COUNT(*) as account_count,
        SUM(current_balance) as total_balance,
        AVG(current_balance) as average_balance,
        MIN(current_balance) as min_balance,
        MAX(current_balance) as max_balance
      FROM ${DatabaseSchema.tableBankAccounts}
      WHERE status = 'active'
      GROUP BY currency
    ''');

    return {
      'by_currency': result,
      'total_accounts': result.fold<int>(0, (sum, row) => sum + (row['account_count'] as int)),
    };
  }

  /// Search bank accounts
  Future<List<BankAccount>> searchBankAccounts(String query) async {
    final db = await DatabaseHelper().database;
    final searchQuery = '%$query%';
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      where: '''
        account_name LIKE ? OR 
        account_number LIKE ? OR 
        bank_name LIKE ? OR 
        iban LIKE ?
      ''',
      whereArgs: [searchQuery, searchQuery, searchQuery, searchQuery],
      orderBy: 'account_name ASC',
    );

    return List.generate(maps.length, (i) {
      return BankAccount.fromMap(maps[i]);
    });
  }

  /// Get overdrawn accounts
  Future<List<BankAccount>> getOverdrawnAccounts() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableBankAccounts,
      where: 'current_balance < 0 AND status = ?',
      whereArgs: ['active'],
      orderBy: 'current_balance ASC',
    );

    return List.generate(maps.length, (i) {
      return BankAccount.fromMap(maps[i]);
    });
  }

  /// Get accounts near credit limit
  Future<List<BankAccount>> getAccountsNearCreditLimit() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT * FROM ${DatabaseSchema.tableBankAccounts}
      WHERE credit_limit IS NOT NULL 
        AND current_balance <= (credit_limit * -0.9)
        AND status = 'active'
      ORDER BY (current_balance / credit_limit) ASC
    ''');

    return List.generate(maps.length, (i) {
      return BankAccount.fromMap(maps[i]);
    });
  }

  /// Get bank accounts with transactions count
  Future<List<Map<String, dynamic>>> getBankAccountsWithTransactionCount() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        ba.*,
        COALESCE(bt.transaction_count, 0) as transaction_count,
        COALESCE(bt.last_transaction_date, '') as last_transaction_date
      FROM ${DatabaseSchema.tableBankAccounts} ba
      LEFT JOIN (
        SELECT 
          bank_account_id,
          COUNT(*) as transaction_count,
          MAX(transaction_date) as last_transaction_date
        FROM ${DatabaseSchema.tableBankTransactions}
        GROUP BY bank_account_id
      ) bt ON ba.id = bt.bank_account_id
      ORDER BY ba.account_name ASC
    ''');

    return result;
  }
}
