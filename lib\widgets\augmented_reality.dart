import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🥽 نظام الواقع المعزز المستقبلي
/// Futuristic Augmented Reality System
///
/// هذا الملف يحتوي على نظام واقع معزز ثلاثي الأبعاد لا مثيل له في التاريخ
/// This file contains unprecedented 3D augmented reality system in history

/// 🌟 لوحة الواقع المعزز الذكي
/// Smart Augmented Reality Dashboard
class AugmentedRealityDashboard extends StatefulWidget {
  const AugmentedRealityDashboard({super.key});

  @override
  State<AugmentedRealityDashboard> createState() =>
      _AugmentedRealityDashboardState();
}

class _AugmentedRealityDashboardState extends State<AugmentedRealityDashboard>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _floatingController;
  late AnimationController _scanController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _floatingAnimation;
  late Animation<double> _scanAnimation;

  bool _isARActive = false;
  bool _isScanning = false;
  String _detectedObject = '';

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    );

    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _scanController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _floatingAnimation = Tween<double>(begin: -10.0, end: 10.0).animate(
      CurvedAnimation(parent: _floatingController, curve: Curves.easeInOut),
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scanController, curve: Curves.easeInOut),
    );

    _rotationController.repeat();
    _floatingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _floatingController.dispose();
    _scanController.dispose();
    super.dispose();
  }

  void _toggleAR() {
    setState(() {
      _isARActive = !_isARActive;
      if (_isARActive) {
        _startScanning();
      }
    });
  }

  void _startScanning() {
    setState(() {
      _isScanning = true;
    });

    _scanController.repeat();

    Future.delayed(const Duration(seconds: 3), () {
      setState(() {
        _isScanning = false;
        _detectedObject = 'تقرير مالي - الربع الأول 2024';
      });
      _scanController.stop();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _rotationAnimation,
        _floatingAnimation,
        _scanAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: _isARActive ? 3.5 : 2.0,
          child: QuantumEnergyEffect(
            intensity: _isARActive
                ? 3.0
                : 1.5 + (_floatingAnimation.value.abs() * 0.1),
            primaryColor: const Color(0xFFE91E63),
            secondaryColor: const Color(0xFFF06292),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF880E4F).withValues(alpha: 0.9),
                    const Color(0xFFC2185B).withValues(alpha: 0.8),
                    const Color(0xFFE91E63).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(
                    0xFFE91E63,
                  ).withValues(alpha: _isARActive ? 0.9 : 0.6),
                  width: _isARActive ? 4 : 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(
                      0xFFE91E63,
                    ).withValues(alpha: _isARActive ? 0.7 : 0.4),
                    blurRadius: _isARActive ? 45 : 30,
                    offset: const Offset(0, 20),
                    spreadRadius: _isARActive ? 10 : 5,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس الواقع المعزز
                  Row(
                    children: [
                      Transform.translate(
                        offset: Offset(0, _floatingAnimation.value),
                        child: Transform.rotate(
                          angle: _rotationAnimation.value * 0.5,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFFE91E63,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFFF06292,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.view_in_ar_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🥽 الواقع المعزز',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              _isARActive
                                  ? (_isScanning
                                        ? 'جاري المسح...'
                                        : 'نشط - جاهز للتفاعل')
                                  : 'تفاعل ثلاثي الأبعاد',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      // زر التفعيل
                      GestureDetector(
                        onTap: _toggleAR,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _isARActive
                                ? const Color(0xFF4CAF50).withValues(alpha: 0.8)
                                : const Color(
                                    0xFF757575,
                                  ).withValues(alpha: 0.8),
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.4),
                            ),
                          ),
                          child: Icon(
                            _isARActive
                                ? Icons.visibility_rounded
                                : Icons.visibility_off_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // عرض الواقع المعزز ثلاثي الأبعاد
                  _buildAR3DDisplay(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // معلومات الكائن المكتشف
                  if (_detectedObject.isNotEmpty) _buildDetectedObjectInfo(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء عرض الواقع المعزز ثلاثي الأبعاد
  Widget _buildAR3DDisplay() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: const Color(0xFFE91E63).withValues(alpha: 0.4),
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // الخلفية المعززة
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                colors: [
                  const Color(0xFFE91E63).withValues(alpha: 0.2),
                  const Color(0xFFF06292).withValues(alpha: 0.1),
                  Colors.transparent,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
            ),
          ),

          // الشبكة ثلاثية الأبعاد
          if (_isARActive) ...[
            // خطوط الشبكة الأفقية
            ...List.generate(8, (index) {
              final y = (index * 25.0) + 25;
              return Positioned(
                left: 0,
                top: y,
                right: 0,
                child: Container(
                  height: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        const Color(0xFFE91E63).withValues(alpha: 0.6),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            }),

            // خطوط الشبكة العمودية
            ...List.generate(12, (index) {
              final x = (index * 30.0) + 30;
              return Positioned(
                left: x,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        const Color(0xFFE91E63).withValues(alpha: 0.6),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],

          // الكائن المعزز المركزي
          Center(
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateX(_rotationAnimation.value * 0.3)
                ..rotateY(_rotationAnimation.value)
                ..rotateZ(_rotationAnimation.value * 0.2)
                ..translate(0.0, _floatingAnimation.value, 0.0),
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFFE91E63).withValues(alpha: 0.9),
                      const Color(0xFFF06292).withValues(alpha: 0.7),
                      const Color(0xFFF8BBD9).withValues(alpha: 0.5),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.7),
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFE91E63).withValues(alpha: 0.7),
                      blurRadius: 25,
                      spreadRadius: 8,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.analytics_rounded,
                  color: Colors.white,
                  size: 50,
                ),
              ),
            ),
          ),

          // مؤشرات الواقع المعزز
          if (_isARActive) ...[
            // مؤشر الزاوية العلوية اليسرى
            const Positioned(top: 15, left: 15, child: ARCornerIndicator()),
            // مؤشر الزاوية العلوية اليمنى
            const Positioned(top: 15, right: 15, child: ARCornerIndicator()),
            // مؤشر الزاوية السفلية اليسرى
            const Positioned(bottom: 15, left: 15, child: ARCornerIndicator()),
            // مؤشر الزاوية السفلية اليمنى
            const Positioned(bottom: 15, right: 15, child: ARCornerIndicator()),
          ],

          // خط المسح
          if (_isScanning)
            Positioned(
              left: 0,
              right: 0,
              top: 200 * _scanAnimation.value - 2,
              child: Container(
                height: 4,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      const Color(0xFF00BCD4).withValues(alpha: 0.8),
                      const Color(0xFF00BCD4),
                      const Color(0xFF00BCD4).withValues(alpha: 0.8),
                      Colors.transparent,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),

          // معلومات الحالة
          Positioned(
            top: 10,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _isARActive
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.8)
                      : const Color(0xFF757575).withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  _isARActive
                      ? (_isScanning ? 'جاري المسح...' : 'الواقع المعزز نشط')
                      : 'اضغط لتفعيل الواقع المعزز',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الكائن المكتشف
  Widget _buildDetectedObjectInfo() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.center_focus_strong_rounded,
                  color: Color(0xFF4CAF50),
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              const Text(
                '🎯 كائن مكتشف',
                style: TextStyle(
                  color: Color(0xFF4CAF50),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            _detectedObject,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Row(
            children: [
              Expanded(
                child: _buildARAction(
                  'عرض',
                  Icons.visibility_rounded,
                  const Color(0xFF2196F3),
                ),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: _buildARAction(
                  'تحليل',
                  Icons.analytics_rounded,
                  const Color(0xFFFF9800),
                ),
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              Expanded(
                child: _buildARAction(
                  'مشاركة',
                  Icons.share_rounded,
                  const Color(0xFF9C27B0),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildARAction(String title, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        // تنفيذ الإجراء
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: color.withValues(alpha: 0.5)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// مؤشر زاوية الواقع المعزز
class ARCornerIndicator extends StatelessWidget {
  const ARCornerIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: const Color(0xFFE91E63).withValues(alpha: 0.8),
            width: 3,
          ),
          left: BorderSide(
            color: const Color(0xFFE91E63).withValues(alpha: 0.8),
            width: 3,
          ),
        ),
      ),
    );
  }
}

/// 🎮 بطاقة التحكم ثلاثي الأبعاد
/// 3D Control Card
class ThreeDControlCard extends StatefulWidget {
  const ThreeDControlCard({super.key});

  @override
  State<ThreeDControlCard> createState() => _ThreeDControlCardState();
}

class _ThreeDControlCardState extends State<ThreeDControlCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  double _rotationX = 0.0;
  double _rotationY = 0.0;
  bool _isInteracting = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return HologramEffect(
          intensity: _isInteracting ? 2.5 : 1.0 + (_animation.value * 0.5),
          child: GestureDetector(
            onPanUpdate: (details) {
              setState(() {
                _isInteracting = true;
                _rotationY += details.delta.dx * 0.01;
                _rotationX -= details.delta.dy * 0.01;
              });
            },
            onPanEnd: (_) {
              setState(() {
                _isInteracting = false;
              });
            },
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF1A237E).withValues(alpha: 0.9),
                    const Color(0xFF303F9F).withValues(alpha: 0.8),
                    const Color(0xFF3F51B5).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: const Color(
                    0xFF3F51B5,
                  ).withValues(alpha: _isInteracting ? 0.8 : 0.5),
                  width: _isInteracting ? 3 : 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(
                      0xFF3F51B5,
                    ).withValues(alpha: _isInteracting ? 0.5 : 0.3),
                    blurRadius: _isInteracting ? 25 : 15,
                    offset: const Offset(0, 10),
                    spreadRadius: _isInteracting ? 3 : 1,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس التحكم ثلاثي الأبعاد
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF3F51B5), Color(0xFF5C6BC0)],
                          ),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: const Icon(
                          Icons.threed_rotation_rounded,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Text(
                        '🎮 التحكم ثلاثي الأبعاد',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // الكائن ثلاثي الأبعاد
                  Center(
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.001)
                        ..rotateX(_rotationX)
                        ..rotateY(_rotationY),
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3F51B5).withValues(alpha: 0.9),
                              const Color(0xFF5C6BC0).withValues(alpha: 0.7),
                              const Color(0xFF9FA8DA).withValues(alpha: 0.5),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.6),
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(
                                0xFF3F51B5,
                              ).withValues(alpha: 0.6),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.account_balance_wallet_rounded,
                          color: Colors.white,
                          size: 60,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تعليمات التفاعل
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingMedium),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.touch_app_rounded,
                          color: Colors.white,
                          size: 32,
                        ),
                        const SizedBox(height: AppTheme.spacingSmall),
                        Text(
                          _isInteracting
                              ? 'تفاعل نشط!'
                              : 'اسحب للتحكم في الكائن ثلاثي الأبعاد',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
