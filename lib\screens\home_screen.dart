/// الشاشة الرئيسية المحسنة لتطبيق Smart Ledger
/// Enhanced Home Screen for Smart Ledger Application
library;

import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../widgets/advanced_visual_effects.dart';
import '../widgets/enhanced_dashboard_card.dart';
import 'accounts/accounts_screen.dart';
import 'journal_entries/journal_entries_screen.dart';
import 'reports/financial_reports_screen.dart';
import 'warehouse/stock_balances_screen.dart';
import 'customers/customers_screen.dart';
import 'invoices/invoices_screen.dart';
import 'settings/settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _selectedIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<Widget> _screens = [
    const DashboardTab(),
    const AccountsTab(),
    const EntriesTab(),
    const ReportsTab(),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.account_balance,
                color: AppTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Smart Ledger',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                Text(
                  'دفتر الأستاذ الذكي',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
            icon: Icon(Icons.settings, color: AppTheme.primaryColor),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _screens[_selectedIndex],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _selectedIndex,
          onTap: (index) {
            setState(() => _selectedIndex = index);
            _animationController.reset();
            _animationController.forward();
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: AppTheme.primaryColor,
          unselectedItemColor: Colors.grey[400],
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.dashboard),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.account_balance),
              label: 'الحسابات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.receipt_long),
              label: 'القيود',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.analytics),
              label: 'التقارير',
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showQuickActionsDialog,
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _showQuickActionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.flash_on, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            const Text('إجراءات سريعة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildQuickActionTile(
              icon: Icons.receipt_long,
              title: 'قيد محاسبي جديد',
              subtitle: 'إضافة قيد محاسبي',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const JournalEntriesScreen(),
                  ),
                );
              },
            ),
            _buildQuickActionTile(
              icon: Icons.inventory,
              title: 'حركة مخزون',
              subtitle: 'إدارة المخزون',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const StockBalancesScreen(),
                  ),
                );
              },
            ),
            _buildQuickActionTile(
              icon: Icons.receipt,
              title: 'فاتورة جديدة',
              subtitle: 'إنشاء فاتورة',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const InvoicesScreen(),
                  ),
                );
              },
            ),
            _buildQuickActionTile(
              icon: Icons.people,
              title: 'إدارة العملاء',
              subtitle: 'عملاء وموردين',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CustomersScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppTheme.primaryColor, size: 24),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(subtitle),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        tileColor: Colors.grey[50],
      ),
    );
  }
}

// تبويب الرئيسية المحسن
class DashboardTab extends StatelessWidget {
  const DashboardTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // خلفية بتأثيرات الجسيمات
        Positioned.fill(
          child: AnimatedParticles(
            particleCount: 15,
            particleColor: AppTheme.primaryColor.withValues(alpha: 0.1),
            particleSize: 2.0,
            animationDuration: const Duration(seconds: 4),
          ),
        ),
        SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEnhancedWelcomeCard(context),
              const SizedBox(height: 24),
              _buildFinancialStatsGrid(context),
              const SizedBox(height: 24),
              _buildQuickActionsGrid(context),
              const SizedBox(height: 24),
              _buildRecentActivities(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedWelcomeCard(BuildContext context) {
    return HolographicCard(
      enableAnimation: true,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                PulsingGlow(
                  glowColor: Colors.white,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً بك في Smart Ledger',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'نظام المحاسبة الثوري الذي لا مثيل له',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  AppTheme.borderRadiusMedium,
                ),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: Colors.white.withValues(alpha: 0.8),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'اليوم: ${DateTime.now().toString().split(' ')[0]}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.trending_up,
                    color: Colors.white.withValues(alpha: 0.8),
                    size: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'نشط',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialStatsGrid(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات المالية',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            FinancialStatsCard(
              title: 'إجمالي الأصول',
              value: '250,000 ر.س',
              subtitle: 'زيادة عن الشهر الماضي',
              icon: Icons.account_balance_wallet,
              primaryColor: AppTheme.assetColor,
              trend: 'up',
              trendValue: 12.5,
              enableGlowEffect: true,
            ),
            FinancialStatsCard(
              title: 'صافي الدخل',
              value: '100,000 ر.س',
              subtitle: 'أداء ممتاز هذا الشهر',
              icon: Icons.trending_up,
              primaryColor: AppTheme.revenueColor,
              trend: 'up',
              trendValue: 18.7,
              enableHolographicEffect: true,
            ),
            FinancialStatsCard(
              title: 'المخزون',
              value: '45,000 ر.س',
              subtitle: 'مستوى مناسب',
              icon: Icons.inventory,
              primaryColor: AppTheme.warningColor,
              trend: 'flat',
              trendValue: 2.1,
              enableGlowEffect: true,
            ),
            FinancialStatsCard(
              title: 'المبيعات',
              value: '85,000 ر.س',
              subtitle: 'نمو مستمر',
              icon: Icons.point_of_sale,
              primaryColor: AppTheme.infoColor,
              trend: 'up',
              trendValue: 8.3,
              enableGlowEffect: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionsGrid(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1,
          children: [
            QuickActionCard(
              title: 'الحسابات',
              subtitle: 'إدارة دليل الحسابات',
              icon: Icons.account_balance,
              color: AppTheme.primaryColor,
              enableParticleEffect: true,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const AccountsScreen()),
              ),
            ),
            QuickActionCard(
              title: 'القيود اليومية',
              subtitle: 'إدخال القيود المحاسبية',
              icon: Icons.receipt_long,
              color: AppTheme.secondaryColor,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const JournalEntriesScreen(),
                ),
              ),
            ),
            QuickActionCard(
              title: 'التقارير المالية',
              subtitle: 'عرض التقارير والتحليلات',
              icon: Icons.analytics,
              color: AppTheme.infoColor,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FinancialReportsScreen(),
                ),
              ),
            ),
            QuickActionCard(
              title: 'إدارة المخزون',
              subtitle: 'متابعة أرصدة المخزون',
              icon: Icons.inventory,
              color: AppTheme.warningColor,
              enableParticleEffect: true,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StockBalancesScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentActivities(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأنشطة الحديثة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        GlassMorphismCard(
          child: Column(
            children: [
              _buildActivityItem(
                context,
                'قيد محاسبي جديد',
                'تم إضافة قيد رقم #1234',
                Icons.receipt_long,
                AppTheme.secondaryColor,
                '10:30 ص',
              ),
              const Divider(height: 1),
              _buildActivityItem(
                context,
                'فاتورة مبيعات',
                'فاتورة رقم #5678 - 15,000 ر.س',
                Icons.point_of_sale,
                AppTheme.infoColor,
                '09:15 ص',
              ),
              const Divider(height: 1),
              _buildActivityItem(
                context,
                'حركة مخزون',
                'استلام بضاعة - 50 قطعة',
                Icons.inventory,
                AppTheme.warningColor,
                '08:45 ص',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String time,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppTheme.textLightColor),
          ),
        ],
      ),
    );
  }
}

// تبويب الحسابات
class AccountsTab extends StatelessWidget {
  const AccountsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const AccountsScreen();
  }
}

// تبويب القيود
class EntriesTab extends StatelessWidget {
  const EntriesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const JournalEntriesScreen();
  }
}

// تبويب التقارير
class ReportsTab extends StatelessWidget {
  const ReportsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const FinancialReportsScreen();
  }
}
