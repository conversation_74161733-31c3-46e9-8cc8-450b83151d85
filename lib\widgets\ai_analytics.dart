import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 📊 نظام التحليل الذكي المستقبلي
/// Futuristic AI Analytics System
///
/// هذا الملف يحتوي على نظام تحليل ذكي لا مثيل له في التاريخ
/// This file contains unprecedented AI analytics system in history

/// 🌟 لوحة التحليل الذكي
/// AI Analytics Dashboard
class AIAnalyticsDashboard extends StatefulWidget {
  const AIAnalyticsDashboard({super.key});

  @override
  State<AIAnalyticsDashboard> createState() => _AIAnalyticsDashboardState();
}

class _AIAnalyticsDashboardState extends State<AIAnalyticsDashboard>
    with TickerProviderStateMixin {
  late AnimationController _dataFlowController;
  late AnimationController _pulseController;
  late Animation<double> _dataFlowAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _dataFlowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _dataFlowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _dataFlowController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _dataFlowController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _dataFlowController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_dataFlowAnimation, _pulseAnimation]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 1.5,
          child: QuantumEnergyEffect(
            intensity: 1.0 + (_pulseAnimation.value * 0.3),
            primaryColor: const Color(0xFF9C27B0),
            secondaryColor: const Color(0xFFE91E63),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF9C27B0).withValues(alpha: 0.9),
                    const Color(0xFFE91E63).withValues(alpha: 0.8),
                    const Color(0xFF673AB7).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF9C27B0).withValues(alpha: 0.4),
                    blurRadius: 25,
                    offset: const Offset(0, 15),
                    spreadRadius: 3,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس التحليل الذكي
                  Row(
                    children: [
                      Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.4),
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.analytics_rounded,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🤖 التحليل الذكي',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.5,
                                  ),
                            ),
                            Text(
                              'تحليل البيانات بالذكاء الاصطناعي',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.8),
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // مؤشرات التحليل الذكي
                  Row(
                    children: [
                      Expanded(
                        child: _buildAIMetric(
                          'دقة التنبؤ',
                          '94.7%',
                          Icons.precision_manufacturing_rounded,
                          const Color(0xFF4CAF50),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: _buildAIMetric(
                          'كفاءة التحليل',
                          '98.2%',
                          Icons.speed_rounded,
                          const Color(0xFF2196F3),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingMedium),

                  Row(
                    children: [
                      Expanded(
                        child: _buildAIMetric(
                          'توفير التكاليف',
                          '₹ 45,230',
                          Icons.savings_rounded,
                          const Color(0xFFFF9800),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: _buildAIMetric(
                          'نمو الأرباح',
                          '+23.5%',
                          Icons.trending_up_rounded,
                          const Color(0xFF8BC34A),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تدفق البيانات المرئي
                  _buildDataFlowVisualization(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء مؤشر التحليل الذكي
  Widget _buildAIMetric(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء تصور تدفق البيانات
  Widget _buildDataFlowVisualization() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Stack(
        children: [
          // خطوط تدفق البيانات
          ...List.generate(5, (index) {
            final progress = (_dataFlowAnimation.value + (index * 0.2)) % 1.0;
            return Positioned(
              left: progress * 300,
              top: 20 + (index * 8.0),
              child: Container(
                width: 40,
                height: 3,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      const Color(0xFF00E676).withValues(alpha: 0.8),
                      Colors.transparent,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(1.5),
                ),
              ),
            );
          }),

          // نقاط البيانات
          ...List.generate(8, (index) {
            final angle = (index / 8) * 2 * math.pi;
            final radius = 15.0;
            final centerX = 150.0;
            final centerY = 40.0;
            return Positioned(
              left:
                  centerX +
                  radius *
                      math.cos(angle + _dataFlowAnimation.value * 2 * math.pi),
              top:
                  centerY +
                  radius *
                      math.sin(angle + _dataFlowAnimation.value * 2 * math.pi),
              child: Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(
                    0xFF00E676,
                  ).withValues(alpha: 0.6 + (_pulseAnimation.value * 0.4)),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            );
          }),

          // مركز المعالجة
          Positioned(
            left: 135,
            top: 25,
            child: Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    colors: [
                      const Color(0xFF00E676).withValues(alpha: 0.8),
                      const Color(0xFF4CAF50).withValues(alpha: 0.6),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.4),
                    width: 2,
                  ),
                ),
                child: const Icon(
                  Icons.memory_rounded,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 🎯 بطاقة التوصيات الذكية
/// Smart Recommendations Card
class SmartRecommendationsCard extends StatefulWidget {
  const SmartRecommendationsCard({super.key});

  @override
  State<SmartRecommendationsCard> createState() =>
      _SmartRecommendationsCardState();
}

class _SmartRecommendationsCardState extends State<SmartRecommendationsCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  final List<SmartRecommendation> _recommendations = [
    SmartRecommendation(
      title: 'تحسين التدفق النقدي',
      description: 'يمكن تحسين التدفق النقدي بنسبة 15% عبر تسريع عملية التحصيل',
      impact: 'عالي',
      icon: Icons.trending_up_rounded,
      color: const Color(0xFF4CAF50),
    ),
    SmartRecommendation(
      title: 'تقليل المصروفات',
      description:
          'توفير 8,500 ر.س شهرياً من خلال إعادة تقييم المصروفات التشغيلية',
      impact: 'متوسط',
      icon: Icons.savings_rounded,
      color: const Color(0xFFFF9800),
    ),
    SmartRecommendation(
      title: 'استثمار الفائض',
      description: 'استثمار الفائض النقدي في أدوات مالية قصيرة المدى',
      impact: 'منخفض',
      icon: Icons.account_balance_rounded,
      color: const Color(0xFF2196F3),
    ),
  ];

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return HologramEffect(
          intensity: 1.0 + (_animation.value * 0.5),
          child: Container(
            margin: const EdgeInsets.all(AppTheme.spacingMedium),
            padding: const EdgeInsets.all(AppTheme.spacingLarge),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF1A1A2E).withValues(alpha: 0.9),
                  const Color(0xFF16213E).withValues(alpha: 0.8),
                  const Color(0xFF0F3460).withValues(alpha: 0.7),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2196F3).withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس التوصيات
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF2196F3), Color(0xFF03A9F4)],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Icon(
                        Icons.lightbulb_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Text(
                      '💡 التوصيات الذكية',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.spacingLarge),

                // قائمة التوصيات
                ...List.generate(_recommendations.length, (index) {
                  return _buildRecommendationItem(
                    _recommendations[index],
                    index,
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecommendationItem(
    SmartRecommendation recommendation,
    int index,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: recommendation.color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: recommendation.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              recommendation.icon,
              color: recommendation.color,
              size: 20,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  recommendation.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXSmall),
                Text(
                  recommendation.description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: recommendation.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              recommendation.impact,
              style: TextStyle(
                color: recommendation.color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج التوصية الذكية
class SmartRecommendation {
  final String title;
  final String description;
  final String impact;
  final IconData icon;
  final Color color;

  SmartRecommendation({
    required this.title,
    required this.description,
    required this.impact,
    required this.icon,
    required this.color,
  });
}
