/// نماذج الضرائب والامتثال
/// Tax and Compliance Models for Smart Ledger
library;

/// أنواع الضرائب
enum TaxType {
  vat('vat', 'ضريبة القيمة المضافة', 'Value Added Tax'),
  incomeTax('income_tax', 'ضريبة الدخل', 'Income Tax'),
  corporateTax('corporate_tax', 'ضريبة الشركات', 'Corporate Tax'),
  withholdingTax('withholding_tax', 'ضريبة الاستقطاع', 'Withholding Tax'),
  customsDuty('customs_duty', 'رسوم جمركية', 'Customs Duty'),
  exciseTax('excise_tax', 'ضريبة انتقائية', 'Excise Tax'),
  other('other', 'أخرى', 'Other');

  const TaxType(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static TaxType fromString(String value) {
    return TaxType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TaxType.other,
    );
  }

  String get displayName => nameAr;
}

/// طرق حساب الضريبة
enum TaxCalculationMethod {
  percentage('percentage', 'نسبة مئوية', 'Percentage'),
  fixedAmount('fixed_amount', 'مبلغ ثابت', 'Fixed Amount'),
  tiered('tiered', 'متدرج', 'Tiered'),
  compound('compound', 'مركب', 'Compound');

  const TaxCalculationMethod(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static TaxCalculationMethod fromString(String value) {
    return TaxCalculationMethod.values.firstWhere(
      (method) => method.value == value,
      orElse: () => TaxCalculationMethod.percentage,
    );
  }

  String get displayName => nameAr;
}

/// حالة الضريبة
enum TaxStatus {
  active('active', 'نشطة', 'Active'),
  inactive('inactive', 'غير نشطة', 'Inactive'),
  suspended('suspended', 'معلقة', 'Suspended'),
  archived('archived', 'مؤرشفة', 'Archived'),
  expired('expired', 'منتهية الصلاحية', 'Expired');

  const TaxStatus(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static TaxStatus fromString(String value) {
    return TaxStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TaxStatus.active,
    );
  }

  String get displayName => nameAr;
}

/// نموذج الضريبة
class Tax {
  final int? id;
  final String code;
  final String nameAr;
  final String nameEn;
  final String? description;
  final TaxType type;
  final TaxCalculationMethod calculationMethod;
  final double rate; // النسبة أو المبلغ الثابت
  final double? minimumAmount;
  final double? maximumAmount;
  final bool isInclusive; // هل الضريبة مشمولة في السعر أم مضافة
  final bool isCompound; // هل تحسب على ضرائب أخرى
  final int? accountId; // حساب الضريبة
  final TaxStatus status;
  final DateTime? effectiveDate;
  final DateTime? expiryDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Tax({
    this.id,
    required this.code,
    required this.nameAr,
    required this.nameEn,
    this.description,
    required this.type,
    this.calculationMethod = TaxCalculationMethod.percentage,
    required this.rate,
    this.minimumAmount,
    this.maximumAmount,
    this.isInclusive = false,
    this.isCompound = false,
    this.accountId,
    this.status = TaxStatus.active,
    this.effectiveDate,
    this.expiryDate,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from map
  factory Tax.fromMap(Map<String, dynamic> map) {
    return Tax(
      id: map['id'] as int?,
      code: map['code'] as String,
      nameAr: map['name_ar'] as String,
      nameEn: map['name_en'] as String,
      description: map['description'] as String?,
      type: TaxType.fromString(map['type'] as String),
      calculationMethod: TaxCalculationMethod.fromString(
        map['calculation_method'] as String,
      ),
      rate: (map['rate'] as num).toDouble(),
      minimumAmount: (map['minimum_amount'] as num?)?.toDouble(),
      maximumAmount: (map['maximum_amount'] as num?)?.toDouble(),
      isInclusive: (map['is_inclusive'] as int) == 1,
      isCompound: (map['is_compound'] as int) == 1,
      accountId: map['account_id'] as int?,
      status: TaxStatus.fromString(map['status'] as String),
      effectiveDate: map['effective_date'] != null
          ? DateTime.parse(map['effective_date'] as String)
          : null,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name_ar': nameAr,
      'name_en': nameEn,
      'description': description,
      'type': type.value,
      'calculation_method': calculationMethod.value,
      'rate': rate,
      'minimum_amount': minimumAmount,
      'maximum_amount': maximumAmount,
      'is_inclusive': isInclusive ? 1 : 0,
      'is_compound': isCompound ? 1 : 0,
      'account_id': accountId,
      'status': status.value,
      'effective_date': effectiveDate?.toIso8601String().split('T')[0],
      'expiry_date': expiryDate?.toIso8601String().split('T')[0],
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method
  Tax copyWith({
    int? id,
    String? code,
    String? nameAr,
    String? nameEn,
    String? description,
    TaxType? type,
    TaxCalculationMethod? calculationMethod,
    double? rate,
    double? minimumAmount,
    double? maximumAmount,
    bool? isInclusive,
    bool? isCompound,
    int? accountId,
    TaxStatus? status,
    DateTime? effectiveDate,
    DateTime? expiryDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Tax(
      id: id ?? this.id,
      code: code ?? this.code,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      type: type ?? this.type,
      calculationMethod: calculationMethod ?? this.calculationMethod,
      rate: rate ?? this.rate,
      minimumAmount: minimumAmount ?? this.minimumAmount,
      maximumAmount: maximumAmount ?? this.maximumAmount,
      isInclusive: isInclusive ?? this.isInclusive,
      isCompound: isCompound ?? this.isCompound,
      accountId: accountId ?? this.accountId,
      status: status ?? this.status,
      effectiveDate: effectiveDate ?? this.effectiveDate,
      expiryDate: expiryDate ?? this.expiryDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب مبلغ الضريبة
  double calculateTaxAmount(double baseAmount) {
    if (status != TaxStatus.active) return 0.0;
    if (effectiveDate != null && DateTime.now().isBefore(effectiveDate!)) {
      return 0.0;
    }
    if (expiryDate != null && DateTime.now().isAfter(expiryDate!)) return 0.0;

    double taxAmount = 0.0;

    switch (calculationMethod) {
      case TaxCalculationMethod.percentage:
        taxAmount = baseAmount * (rate / 100);
        break;
      case TaxCalculationMethod.fixedAmount:
        taxAmount = rate;
        break;
      case TaxCalculationMethod.tiered:
        // يحتاج إلى تنفيذ منطق الشرائح
        taxAmount = baseAmount * (rate / 100);
        break;
      case TaxCalculationMethod.compound:
        // يحتاج إلى تنفيذ منطق الضريبة المركبة
        taxAmount = baseAmount * (rate / 100);
        break;
    }

    // تطبيق الحد الأدنى والأقصى
    if (minimumAmount != null && taxAmount < minimumAmount!) {
      taxAmount = minimumAmount!;
    }
    if (maximumAmount != null && taxAmount > maximumAmount!) {
      taxAmount = maximumAmount!;
    }

    return taxAmount;
  }

  /// التحقق من صحة البيانات
  List<String> validate() {
    List<String> errors = [];

    if (code.trim().isEmpty) {
      errors.add('رمز الضريبة مطلوب');
    }

    if (nameAr.trim().isEmpty) {
      errors.add('اسم الضريبة بالعربية مطلوب');
    }

    if (nameEn.trim().isEmpty) {
      errors.add('اسم الضريبة بالإنجليزية مطلوب');
    }

    if (rate < 0) {
      errors.add('معدل الضريبة لا يمكن أن يكون سالباً');
    }

    if (calculationMethod == TaxCalculationMethod.percentage && rate > 100) {
      errors.add('معدل الضريبة النسبي لا يمكن أن يكون أكبر من 100%');
    }

    if (minimumAmount != null && minimumAmount! < 0) {
      errors.add('الحد الأدنى لا يمكن أن يكون سالباً');
    }

    if (maximumAmount != null && maximumAmount! < 0) {
      errors.add('الحد الأقصى لا يمكن أن يكون سالباً');
    }

    if (minimumAmount != null &&
        maximumAmount != null &&
        minimumAmount! > maximumAmount!) {
      errors.add('الحد الأدنى لا يمكن أن يكون أكبر من الحد الأقصى');
    }

    if (effectiveDate != null &&
        expiryDate != null &&
        effectiveDate!.isAfter(expiryDate!)) {
      errors.add('تاريخ السريان لا يمكن أن يكون بعد تاريخ الانتهاء');
    }

    return errors;
  }

  /// التحقق من كون الضريبة فعالة
  bool get isEffective {
    if (status != TaxStatus.active) return false;
    final now = DateTime.now();
    if (effectiveDate != null && now.isBefore(effectiveDate!)) return false;
    if (expiryDate != null && now.isAfter(expiryDate!)) return false;
    return true;
  }

  /// التحقق من انتهاء صلاحية الضريبة
  bool get isExpired {
    return expiryDate != null && DateTime.now().isAfter(expiryDate!);
  }

  @override
  String toString() => '$nameAr ($code)';
}

/// نموذج مجموعة الضرائب
class TaxGroup {
  final int? id;
  final String code;
  final String nameAr;
  final String nameEn;
  final String? description;
  final bool isDefault;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // قائمة الضرائب في المجموعة
  List<Tax> taxes = [];

  TaxGroup({
    this.id,
    required this.code,
    required this.nameAr,
    required this.nameEn,
    this.description,
    this.isDefault = false,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from map
  factory TaxGroup.fromMap(Map<String, dynamic> map) {
    return TaxGroup(
      id: map['id'] as int?,
      code: map['code'] as String,
      nameAr: map['name_ar'] as String,
      nameEn: map['name_en'] as String,
      description: map['description'] as String?,
      isDefault: (map['is_default'] as int) == 1,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name_ar': nameAr,
      'name_en': nameEn,
      'description': description,
      'is_default': isDefault ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method
  TaxGroup copyWith({
    int? id,
    String? code,
    String? nameAr,
    String? nameEn,
    String? description,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaxGroup(
      id: id ?? this.id,
      code: code ?? this.code,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب إجمالي الضرائب للمجموعة
  double calculateTotalTax(double baseAmount) {
    double totalTax = 0.0;
    double currentBase = baseAmount;

    // ترتيب الضرائب حسب الأولوية (غير المركبة أولاً)
    List<Tax> nonCompoundTaxes = taxes.where((tax) => !tax.isCompound).toList();
    List<Tax> compoundTaxes = taxes.where((tax) => tax.isCompound).toList();

    // حساب الضرائب غير المركبة
    for (Tax tax in nonCompoundTaxes) {
      if (tax.isEffective) {
        double taxAmount = tax.calculateTaxAmount(currentBase);
        totalTax += taxAmount;
        if (!tax.isInclusive) {
          currentBase += taxAmount;
        }
      }
    }

    // حساب الضرائب المركبة
    for (Tax tax in compoundTaxes) {
      if (tax.isEffective) {
        double taxAmount = tax.calculateTaxAmount(currentBase);
        totalTax += taxAmount;
        if (!tax.isInclusive) {
          currentBase += taxAmount;
        }
      }
    }

    return totalTax;
  }

  /// الحصول على تفاصيل حساب الضرائب
  Map<String, dynamic> getTaxBreakdown(double baseAmount) {
    Map<String, double> taxBreakdown = {};
    double totalTax = 0.0;
    double currentBase = baseAmount;

    List<Tax> nonCompoundTaxes = taxes.where((tax) => !tax.isCompound).toList();
    List<Tax> compoundTaxes = taxes.where((tax) => tax.isCompound).toList();

    for (Tax tax in nonCompoundTaxes) {
      if (tax.isEffective) {
        double taxAmount = tax.calculateTaxAmount(currentBase);
        taxBreakdown[tax.code] = taxAmount;
        totalTax += taxAmount;
        if (!tax.isInclusive) {
          currentBase += taxAmount;
        }
      }
    }

    for (Tax tax in compoundTaxes) {
      if (tax.isEffective) {
        double taxAmount = tax.calculateTaxAmount(currentBase);
        taxBreakdown[tax.code] = taxAmount;
        totalTax += taxAmount;
        if (!tax.isInclusive) {
          currentBase += taxAmount;
        }
      }
    }

    return {
      'baseAmount': baseAmount,
      'taxBreakdown': taxBreakdown,
      'totalTax': totalTax,
      'totalAmount': currentBase,
    };
  }

  @override
  String toString() => '$nameAr ($code)';
}

/// الضرائب الافتراضية للمملكة العربية السعودية
class DefaultTaxes {
  static List<Tax> get saudiTaxes => [
    Tax(
      code: 'VAT_15',
      nameAr: 'ضريبة القيمة المضافة',
      nameEn: 'Value Added Tax',
      description: 'ضريبة القيمة المضافة بمعدل 15%',
      type: TaxType.vat,
      rate: 15.0,
    ),
    Tax(
      code: 'VAT_0',
      nameAr: 'ضريبة القيمة المضافة - معفى',
      nameEn: 'VAT - Exempt',
      description: 'ضريبة القيمة المضافة للسلع المعفاة',
      type: TaxType.vat,
      rate: 0.0,
    ),
    Tax(
      code: 'WHT_5',
      nameAr: 'ضريبة الاستقطاع 5%',
      nameEn: 'Withholding Tax 5%',
      description: 'ضريبة الاستقطاع للمقيمين',
      type: TaxType.withholdingTax,
      rate: 5.0,
    ),
    Tax(
      code: 'WHT_20',
      nameAr: 'ضريبة الاستقطاع 20%',
      nameEn: 'Withholding Tax 20%',
      description: 'ضريبة الاستقطاع لغير المقيمين',
      type: TaxType.withholdingTax,
      rate: 20.0,
    ),
  ];

  static List<TaxGroup> get defaultTaxGroups => [
    TaxGroup(
      code: 'STANDARD',
      nameAr: 'ضرائب قياسية',
      nameEn: 'Standard Taxes',
      description: 'مجموعة الضرائب القياسية',
      isDefault: true,
    ),
    TaxGroup(
      code: 'EXEMPT',
      nameAr: 'معفى من الضريبة',
      nameEn: 'Tax Exempt',
      description: 'مجموعة السلع المعفاة من الضريبة',
    ),
    TaxGroup(
      code: 'ZERO_RATED',
      nameAr: 'معدل صفر',
      nameEn: 'Zero Rated',
      description: 'مجموعة السلع بمعدل ضريبة صفر',
    ),
  ];
}
