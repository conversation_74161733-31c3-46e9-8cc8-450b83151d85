import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../theme/app_theme.dart';
import '../../models/financial_reports.dart';
import '../../services/financial_reports_service.dart';

/// شاشة الميزانية العمومية
class BalanceSheetScreen extends StatefulWidget {
  const BalanceSheetScreen({super.key});

  @override
  State<BalanceSheetScreen> createState() => _BalanceSheetScreenState();
}

class _BalanceSheetScreenState extends State<BalanceSheetScreen>
    with TickerProviderStateMixin {
  final FinancialReportsService _reportsService = FinancialReportsService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  BalanceSheetReport? _report;
  bool _isLoading = false;
  String? _error;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadBalanceSheet();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _loadBalanceSheet() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final result = await _reportsService.generateBalanceSheet(
        asOfDate: _selectedDate,
      );

      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _report = result.data;
            _isLoading = false;
          });
        } else {
          setState(() {
            _error = result.error;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل الميزانية العمومية: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadBalanceSheet();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          '🏛️ الميزانية العمومية',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: 'اختيار التاريخ',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBalanceSheet,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(opacity: _fadeAnimation, child: _buildBody());
        },
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // شريط التاريخ والمعلومات
        _buildDateHeader(),

        // المحتوى الرئيسي
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildDateHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green,
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'الميزانية العمومية كما في ${_formatDate(_selectedDate)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (_report != null) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildHeaderStat(
                  'إجمالي الأصول',
                  _report!.totalAssets,
                  Icons.account_balance_wallet,
                ),
                _buildHeaderStat(
                  'إجمالي الخصوم',
                  _report!.totalLiabilities,
                  Icons.credit_card,
                ),
                _buildHeaderStat(
                  'حقوق الملكية',
                  _report!.totalEquity,
                  Icons.business,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, double value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        Text(
          '${value.toStringAsFixed(2)} ر.س',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.green),
            SizedBox(height: 16),
            Text(
              'جاري إنشاء الميزانية العمومية...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadBalanceSheet,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_report == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد بيانات لعرضها', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          children: [
            // قسم الأصول
            AnimationConfiguration.staggeredList(
              position: 0,
              duration: const Duration(milliseconds: 600),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(child: _buildAssetsSection()),
              ),
            ),

            const SizedBox(height: 20),

            // قسم الخصوم وحقوق الملكية
            AnimationConfiguration.staggeredList(
              position: 1,
              duration: const Duration(milliseconds: 600),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: _buildLiabilitiesAndEquitySection(),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // ملخص التوازن
            AnimationConfiguration.staggeredList(
              position: 2,
              duration: const Duration(milliseconds: 600),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(child: _buildBalanceSummary()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssetsSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس قسم الأصول
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: Colors.green[700],
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'الأصول',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_report!.totalAssets.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),

          // بنود الأصول
          ..._report!.assets.map((item) => _buildBalanceSheetItem(item)),
        ],
      ),
    );
  }

  Widget _buildLiabilitiesAndEquitySection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // قسم الخصوم
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.credit_card, color: Colors.orange[700], size: 24),
                const SizedBox(width: 8),
                const Text(
                  'الخصوم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_report!.totalLiabilities.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
          ),

          // بنود الخصوم
          ..._report!.liabilities.map((item) => _buildBalanceSheetItem(item)),

          // فاصل
          Container(
            height: 1,
            color: Colors.grey[300],
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),

          // قسم حقوق الملكية
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
            ),
            child: Row(
              children: [
                Icon(Icons.business, color: Colors.blue[700], size: 24),
                const SizedBox(width: 8),
                const Text(
                  'حقوق الملكية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_report!.totalEquity.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
          ),

          // بنود حقوق الملكية
          _buildBalanceSheetItem(
            BalanceSheetItem(
              accountCode: 'EQ001',
              accountName: 'حقوق الملكية',
              amount: _report!.equity,
            ),
          ),
          _buildBalanceSheetItem(
            BalanceSheetItem(
              accountCode: 'EQ002',
              accountName: 'الأرباح المحتجزة',
              amount: _report!.retainedEarnings,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSheetItem(BalanceSheetItem item) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              item.accountName,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
          Text(
            '${item.amount.toStringAsFixed(2)} ر.س',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSummary() {
    final isBalanced =
        (_report!.totalAssets -
                (_report!.totalLiabilities + _report!.totalEquity))
            .abs() <
        0.01;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            isBalanced ? Colors.green : Colors.red,
            (isBalanced ? Colors.green : Colors.red).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (isBalanced ? Colors.green : Colors.red).withValues(
              alpha: 0.3,
            ),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isBalanced ? Icons.check_circle : Icons.error,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                isBalanced ? 'الميزانية متوازنة' : 'الميزانية غير متوازنة',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSummaryCard('الأصول', _report!.totalAssets),
              _buildSummaryCard(
                'الخصوم + حقوق الملكية',
                _report!.totalLiabilities + _report!.totalEquity,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String label, double amount) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} ر.س',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
