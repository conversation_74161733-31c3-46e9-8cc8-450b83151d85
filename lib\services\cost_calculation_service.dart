/// خدمة حساب التكلفة
/// Cost Calculation Service for Smart Ledger
library;

import '../models/cost_calculation.dart';
import '../models/stock_movement.dart';
import '../database/cost_layer_dao.dart';
import '../utils/result.dart';
import 'item_service.dart' as item_service;

class CostCalculationService {
  static final CostCalculationService _instance =
      CostCalculationService._internal();
  factory CostCalculationService() => _instance;
  CostCalculationService._internal();

  final CostLayerDao _costLayerDao = CostLayerDao();
  final item_service.ItemService _itemService = item_service.ItemService();

  /// حساب تكلفة الإصدار باستخدام FIFO
  Future<Result<CostCalculationResult>> calculateFIFOCost({
    required int itemId,
    required int warehouseId,
    int? locationId,
    required double quantity,
    String? batchNumber,
  }) async {
    try {
      final layers = await _costLayerDao.getCostLayersForItem(
        itemId: itemId,
        warehouseId: warehouseId,
        locationId: locationId,
        batchNumber: batchNumber,
        orderBy: 'received_date ASC, id ASC', // FIFO: الأقدم أولاً
      );

      return _consumeLayers(layers, quantity);
    } catch (e) {
      return Result.error('خطأ في حساب تكلفة FIFO: ${e.toString()}');
    }
  }

  /// حساب تكلفة الإصدار باستخدام LIFO
  Future<Result<CostCalculationResult>> calculateLIFOCost({
    required int itemId,
    required int warehouseId,
    int? locationId,
    required double quantity,
    String? batchNumber,
  }) async {
    try {
      final layers = await _costLayerDao.getCostLayersForItem(
        itemId: itemId,
        warehouseId: warehouseId,
        locationId: locationId,
        batchNumber: batchNumber,
        orderBy: 'received_date DESC, id DESC', // LIFO: الأحدث أولاً
      );

      return _consumeLayers(layers, quantity);
    } catch (e) {
      return Result.error('خطأ في حساب تكلفة LIFO: ${e.toString()}');
    }
  }

  /// حساب تكلفة الإصدار باستخدام المتوسط المرجح
  Future<Result<CostCalculationResult>> calculateWeightedAverageCost({
    required int itemId,
    required int warehouseId,
    int? locationId,
    required double quantity,
    String? batchNumber,
  }) async {
    try {
      final layers = await _costLayerDao.getCostLayersForItem(
        itemId: itemId,
        warehouseId: warehouseId,
        locationId: locationId,
        batchNumber: batchNumber,
      );

      if (layers.isEmpty) {
        return Result.error('لا توجد طبقات تكلفة متاحة');
      }

      // حساب المتوسط المرجح
      double totalQuantity = 0;
      double totalValue = 0;

      for (final layer in layers) {
        totalQuantity += layer.quantity;
        totalValue += layer.totalCost;
      }

      if (totalQuantity == 0) {
        return Result.error('الكمية الإجمالية صفر');
      }

      final averageCost = totalValue / totalQuantity;
      final totalCost = quantity * averageCost;

      // إنشاء قائمة الاستهلاك بالتناسب
      final consumedLayers = <CostLayerConsumption>[];
      double remainingQuantity = quantity;

      for (final layer in layers) {
        if (remainingQuantity <= 0) break;

        final consumedFromLayer = remainingQuantity > layer.quantity
            ? layer.quantity
            : remainingQuantity;

        consumedLayers.add(
          CostLayerConsumption(
            layer: layer,
            consumedQuantity: consumedFromLayer,
            consumedCost: consumedFromLayer * averageCost,
          ),
        );

        remainingQuantity -= consumedFromLayer;
      }

      // تحديث الطبقات المتبقية
      final remainingLayers = <CostLayer>[];
      for (int i = 0; i < layers.length; i++) {
        final layer = layers[i];
        final consumed = consumedLayers.firstWhere(
          (c) => c.layer.id == layer.id,
          orElse: () => CostLayerConsumption(
            layer: layer,
            consumedQuantity: 0,
            consumedCost: 0,
          ),
        );

        final remainingQty = layer.quantity - consumed.consumedQuantity;
        if (remainingQty > 0) {
          remainingLayers.add(
            layer.copyWith(
              quantity: remainingQty,
              totalCost: remainingQty * layer.unitCost,
            ),
          );
        }
      }

      return Result.success(
        CostCalculationResult(
          totalCost: totalCost,
          averageCost: averageCost,
          consumedLayers: consumedLayers,
          remainingLayers: remainingLayers,
        ),
      );
    } catch (e) {
      return Result.error('خطأ في حساب المتوسط المرجح: ${e.toString()}');
    }
  }

  /// حساب التكلفة حسب الطريقة المحددة
  Future<Result<CostCalculationResult>> calculateCost({
    required int itemId,
    required int warehouseId,
    int? locationId,
    required double quantity,
    required CostingMethod method,
    String? batchNumber,
    double? standardCost,
  }) async {
    switch (method) {
      case CostingMethod.fifo:
        return calculateFIFOCost(
          itemId: itemId,
          warehouseId: warehouseId,
          locationId: locationId,
          quantity: quantity,
          batchNumber: batchNumber,
        );

      case CostingMethod.lifo:
        return calculateLIFOCost(
          itemId: itemId,
          warehouseId: warehouseId,
          locationId: locationId,
          quantity: quantity,
          batchNumber: batchNumber,
        );

      case CostingMethod.weightedAverage:
        return calculateWeightedAverageCost(
          itemId: itemId,
          warehouseId: warehouseId,
          locationId: locationId,
          quantity: quantity,
          batchNumber: batchNumber,
        );

      case CostingMethod.standardCost:
        if (standardCost == null || standardCost <= 0) {
          return Result.error('التكلفة المعيارية مطلوبة');
        }
        return Result.success(
          CostCalculationResult(
            totalCost: quantity * standardCost,
            averageCost: standardCost,
            consumedLayers: [],
            remainingLayers: [],
          ),
        );

      case CostingMethod.specificIdentification:
        // يتطلب تحديد محدد للدفعة أو الرقم التسلسلي
        if (batchNumber == null) {
          return Result.error('رقم الدفعة مطلوب للتحديد المحدد');
        }
        return calculateFIFOCost(
          itemId: itemId,
          warehouseId: warehouseId,
          locationId: locationId,
          quantity: quantity,
          batchNumber: batchNumber,
        );
    }
  }

  /// إضافة طبقة تكلفة جديدة (عند الاستلام)
  Future<Result<int>> addCostLayer(CostLayer layer) async {
    try {
      final id = await _costLayerDao.insertCostLayer(layer);
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إضافة طبقة التكلفة: ${e.toString()}');
    }
  }

  /// تحديث طبقات التكلفة بعد الحركة
  Future<Result<bool>> updateCostLayersAfterMovement(
    StockMovement movement,
  ) async {
    try {
      if (movement.isInbound) {
        // إضافة طبقة تكلفة جديدة
        final layer = CostLayer(
          itemId: movement.itemId,
          warehouseId: movement.warehouseId,
          locationId: movement.locationId,
          batchNumber: movement.batchNumber,
          serialNumber: movement.serialNumber,
          quantity: movement.quantity,
          unitCost: movement.unitCost,
          totalCost: movement.totalCost,
          receivedDate: movement.movementDate,
          referenceDocument: movement.referenceDocument,
          movementId: movement.id,
        );

        await _costLayerDao.insertCostLayer(layer);
      } else if (!movement.isInbound) {
        // استهلاك من الطبقات الموجودة
        final costingSettings = await _costLayerDao.getItemCostingSettings(
          movement.itemId,
        );
        final method =
            costingSettings?.costingMethod ?? CostingMethod.weightedAverage;

        final result = await calculateCost(
          itemId: movement.itemId,
          warehouseId: movement.warehouseId,
          locationId: movement.locationId,
          quantity: movement.quantity,
          method: method,
          batchNumber: movement.batchNumber,
          standardCost: costingSettings?.standardCost,
        );

        if (result.isSuccess) {
          // تحديث الطبقات
          for (final consumption in result.data!.consumedLayers) {
            await _costLayerDao.consumeFromCostLayer(
              consumption.layer.id!,
              consumption.consumedQuantity,
            );
          }
        }
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في تحديث طبقات التكلفة: ${e.toString()}');
    }
  }

  /// الحصول على تقرير تحليل التكلفة
  Future<Result<CostAnalysisReport>> getCostAnalysisReport(int itemId) async {
    try {
      final layers = await _costLayerDao.getAllCostLayersForItem(itemId);

      if (layers.isEmpty) {
        return Result.error('لا توجد طبقات تكلفة للصنف');
      }

      double totalQuantity = 0;
      double totalValue = 0;
      double lastCost = 0;

      for (final layer in layers) {
        totalQuantity += layer.quantity;
        totalValue += layer.totalCost;
      }

      if (layers.isNotEmpty) {
        lastCost = layers.last.unitCost;
      }

      final averageCost = totalQuantity > 0
          ? (totalValue / totalQuantity).toDouble()
          : 0.0;

      // الحصول على معلومات الصنف
      final itemResult = await _itemService.getItemById(itemId);
      String itemName = 'صنف غير معروف';
      String itemCode = 'غير محدد';

      if (itemResult.isSuccess) {
        itemName = itemResult.data!.name;
        itemCode = itemResult.data!.code;
      }

      final report = CostAnalysisReport(
        itemId: itemId,
        itemName: itemName,
        itemCode: itemCode,
        totalQuantity: totalQuantity,
        totalValue: totalValue,
        averageCost: averageCost,
        lastCost: lastCost,
        costLayers: layers,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء تقرير التكلفة: ${e.toString()}');
    }
  }

  /// استهلاك الطبقات بالترتيب
  Result<CostCalculationResult> _consumeLayers(
    List<CostLayer> layers,
    double quantity,
  ) {
    if (layers.isEmpty) {
      return Result.error('لا توجد طبقات تكلفة متاحة');
    }

    final consumedLayers = <CostLayerConsumption>[];
    final remainingLayers = <CostLayer>[];
    double remainingQuantity = quantity;
    double totalCost = 0;

    for (final layer in layers) {
      if (remainingQuantity <= 0) {
        remainingLayers.add(layer);
        continue;
      }

      final consumedFromLayer = remainingQuantity > layer.quantity
          ? layer.quantity
          : remainingQuantity;

      final consumedCost = consumedFromLayer * layer.unitCost;

      consumedLayers.add(
        CostLayerConsumption(
          layer: layer,
          consumedQuantity: consumedFromLayer,
          consumedCost: consumedCost,
        ),
      );

      totalCost += consumedCost;
      remainingQuantity -= consumedFromLayer;

      // إضافة الكمية المتبقية من الطبقة إن وجدت
      final remainingInLayer = layer.quantity - consumedFromLayer;
      if (remainingInLayer > 0) {
        remainingLayers.add(
          layer.copyWith(
            quantity: remainingInLayer,
            totalCost: remainingInLayer * layer.unitCost,
          ),
        );
      }
    }

    if (remainingQuantity > 0) {
      return Result.error('الكمية المطلوبة أكبر من المتاح في المخزون');
    }

    final averageCost = quantity > 0 ? (totalCost / quantity).toDouble() : 0.0;

    return Result.success(
      CostCalculationResult(
        totalCost: totalCost,
        averageCost: averageCost,
        consumedLayers: consumedLayers,
        remainingLayers: remainingLayers,
      ),
    );
  }
}
