import 'package:flutter/material.dart';
import '../../models/fixed_asset.dart';
import '../../models/asset_depreciation.dart';
import '../../services/fixed_asset_service.dart';
import 'fixed_asset_form_screen.dart';

/// شاشة تفاصيل الأصل الثابت
class FixedAssetDetailsScreen extends StatefulWidget {
  final FixedAsset asset;

  const FixedAssetDetailsScreen({super.key, required this.asset});

  @override
  State<FixedAssetDetailsScreen> createState() =>
      _FixedAssetDetailsScreenState();
}

class _FixedAssetDetailsScreenState extends State<FixedAssetDetailsScreen>
    with SingleTickerProviderStateMixin {
  final FixedAssetService _assetService = FixedAssetService();
  late TabController _tabController;

  List<AssetDepreciation> _depreciationSchedule = [];
  bool _isLoadingSchedule = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadDepreciationSchedule();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDepreciationSchedule() async {
    final result = await _assetService.getAssetDepreciationSchedule(
      widget.asset.id!,
    );
    if (mounted) {
      setState(() {
        _isLoadingSchedule = false;
        if (result.isSuccess) {
          _depreciationSchedule = result.data!;
        }
      });
    }
  }

  void _editAsset() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FixedAssetFormScreen(asset: widget.asset),
      ),
    );

    if (result == true && mounted) {
      Navigator.pop(context, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.asset.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editAsset,
            tooltip: 'تعديل',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'change_status':
                  _showChangeStatusDialog();
                  break;
                case 'delete':
                  _showDeleteDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'change_status',
                child: Row(
                  children: [
                    Icon(Icons.swap_horiz),
                    SizedBox(width: 8),
                    Text('تغيير الحالة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'التفاصيل', icon: Icon(Icons.info)),
            Tab(text: 'الاستهلاك', icon: Icon(Icons.trending_down)),
            Tab(text: 'السجل', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDetailsTab(),
          _buildDepreciationTab(),
          _buildHistoryTab(),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSummaryCard(),
          const SizedBox(height: 16),
          _buildBasicInfoCard(),
          const SizedBox(height: 16),
          _buildFinancialInfoCard(),
          const SizedBox(height: 16),
          _buildTechnicalInfoCard(),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final currentBookValue = widget.asset.getCurrentBookValue();
    final totalDepreciation = widget.asset.getTotalDepreciation();
    final depreciationPercentage =
        (totalDepreciation / widget.asset.purchasePrice) * 100;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(
                      widget.asset.category,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getCategoryIcon(widget.asset.category),
                    color: _getCategoryColor(widget.asset.category),
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.asset.name,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        widget.asset.code,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(
                            widget.asset.status,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          _getStatusName(widget.asset.status),
                          style: TextStyle(
                            color: _getStatusColor(widget.asset.status),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'سعر الشراء',
                    '${widget.asset.purchasePrice.toStringAsFixed(2)} ر.س',
                    Icons.attach_money,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'القيمة الدفترية',
                    '${currentBookValue.toStringAsFixed(2)} ر.س',
                    Icons.account_balance_wallet,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الاستهلاك',
                    '${totalDepreciation.toStringAsFixed(2)} ر.س',
                    Icons.trending_down,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'نسبة الاستهلاك',
                    '${depreciationPercentage.toStringAsFixed(1)}%',
                    Icons.pie_chart,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('الفئة', _getCategoryName(widget.asset.category)),
            _buildInfoRow('الوصف', widget.asset.description ?? 'غير محدد'),
            _buildInfoRow('الموقع', widget.asset.location ?? 'غير محدد'),
            _buildInfoRow(
              'تاريخ الشراء',
              '${widget.asset.purchaseDate.day}/${widget.asset.purchaseDate.month}/${widget.asset.purchaseDate.year}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات المالية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'سعر الشراء',
              '${widget.asset.purchasePrice.toStringAsFixed(2)} ر.س',
            ),
            _buildInfoRow(
              'القيمة المتبقية',
              widget.asset.salvageValue != null
                  ? '${widget.asset.salvageValue!.toStringAsFixed(2)} ر.س'
                  : 'غير محدد',
            ),
            _buildInfoRow(
              'العمر الإنتاجي',
              '${widget.asset.usefulLifeYears} سنة',
            ),
            _buildInfoRow(
              'طريقة الاستهلاك',
              _getDepreciationMethodName(widget.asset.depreciationMethod),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnicalInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات التقنية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'الرقم التسلسلي',
              widget.asset.serialNumber ?? 'غير محدد',
            ),
            _buildInfoRow('الموديل', widget.asset.model ?? 'غير محدد'),
            _buildInfoRow(
              'الشركة المصنعة',
              widget.asset.manufacturer ?? 'غير محدد',
            ),
            _buildInfoRow(
              'معلومات الضمان',
              widget.asset.warrantyInfo ?? 'غير محدد',
            ),
            _buildInfoRow(
              'انتهاء الضمان',
              widget.asset.warrantyExpiry != null
                  ? '${widget.asset.warrantyExpiry!.day}/${widget.asset.warrantyExpiry!.month}/${widget.asset.warrantyExpiry!.year}'
                  : 'غير محدد',
            ),
            if (widget.asset.notes != null && widget.asset.notes!.isNotEmpty)
              _buildInfoRow('ملاحظات', widget.asset.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepreciationTab() {
    if (_isLoadingSchedule) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_depreciationSchedule.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.trending_down, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا يوجد جدول استهلاك'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _depreciationSchedule.length,
      itemBuilder: (context, index) {
        final depreciation = _depreciationSchedule[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: depreciation.isPosted
                  ? Colors.green
                  : Colors.orange,
              child: Icon(
                depreciation.isPosted ? Icons.check : Icons.schedule,
                color: Colors.white,
              ),
            ),
            title: Text('${depreciation.month}/${depreciation.year}'),
            subtitle: Text(
              '${depreciation.depreciationAmount.toStringAsFixed(2)} ر.س',
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'القيمة الدفترية',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  '${depreciation.bookValue.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHistoryTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('سجل المعاملات قيد التطوير'),
        ],
      ),
    );
  }

  void _showChangeStatusDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير حالة الأصل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AssetStatus.values.map((status) {
            return ListTile(
              title: Text(_getStatusName(status)),
              leading: Radio<AssetStatus>(
                value: status,
                groupValue: widget.asset.status,
                onChanged: (value) {
                  Navigator.pop(context);
                  _changeAssetStatus(value!);
                },
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الأصل الثابت'),
        content: const Text(
          'هل أنت متأكد من حذف هذا الأصل الثابت؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAsset();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _changeAssetStatus(AssetStatus newStatus) async {
    final result = await _assetService.changeAssetStatus(
      widget.asset.id!,
      newStatus,
    );

    if (mounted) {
      if (result.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تغيير حالة الأصل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _deleteAsset() async {
    final result = await _assetService.deleteFixedAsset(widget.asset.id!);

    if (mounted) {
      if (result.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الأصل الثابت بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!), backgroundColor: Colors.red),
        );
      }
    }
  }

  // Helper methods
  String _getCategoryName(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return 'مباني';
      case AssetCategory.machinery:
        return 'آلات ومعدات';
      case AssetCategory.vehicle:
        return 'مركبات';
      case AssetCategory.furniture:
        return 'أثاث ومفروشات';
      case AssetCategory.computer:
        return 'أجهزة حاسوب';
      case AssetCategory.other:
        return 'أخرى';
    }
  }

  String _getStatusName(AssetStatus status) {
    switch (status) {
      case AssetStatus.active:
        return 'نشط';
      case AssetStatus.inactive:
        return 'غير نشط';
      case AssetStatus.disposed:
        return 'مستبعد';
      case AssetStatus.underMaintenance:
        return 'تحت الصيانة';
    }
  }

  String _getDepreciationMethodName(DepreciationMethod method) {
    switch (method) {
      case DepreciationMethod.straightLine:
        return 'خط مستقيم';
      case DepreciationMethod.decliningBalance:
        return 'رصيد متناقص';
      case DepreciationMethod.unitsOfProduction:
        return 'وحدات الإنتاج';
      case DepreciationMethod.sumOfYears:
        return 'مجموع السنوات';
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return Icons.business;
      case AssetCategory.machinery:
        return Icons.precision_manufacturing;
      case AssetCategory.vehicle:
        return Icons.directions_car;
      case AssetCategory.furniture:
        return Icons.chair;
      case AssetCategory.computer:
        return Icons.computer;
      case AssetCategory.other:
        return Icons.category;
    }
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return Colors.brown;
      case AssetCategory.machinery:
        return Colors.orange;
      case AssetCategory.vehicle:
        return Colors.blue;
      case AssetCategory.furniture:
        return Colors.green;
      case AssetCategory.computer:
        return Colors.purple;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  Color _getStatusColor(AssetStatus status) {
    switch (status) {
      case AssetStatus.active:
        return Colors.green;
      case AssetStatus.inactive:
        return Colors.orange;
      case AssetStatus.disposed:
        return Colors.red;
      case AssetStatus.underMaintenance:
        return Colors.blue;
    }
  }
}
