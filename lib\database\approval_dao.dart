/// Data Access Object for Approval System
/// Approval DAO for Smart Ledger
library;


import '../models/approval_workflow.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class ApprovalDao {
  static final ApprovalDao _instance = ApprovalDao._internal();
  factory ApprovalDao() => _instance;
  ApprovalDao._internal();

  /// Get all workflows
  Future<List<ApprovalWorkflow>> getAllWorkflows() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalWorkflows,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return ApprovalWorkflow.fromMap(maps[i]);
    });
  }

  /// Get workflow by ID
  Future<ApprovalWorkflow?> getWorkflowById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalWorkflows,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final workflow = ApprovalWorkflow.fromMap(maps.first);
      final steps = await getWorkflowSteps(id);
      return workflow.copyWith(steps: steps);
    }
    return null;
  }

  /// Get workflow for document type and amount
  Future<ApprovalWorkflow?> getWorkflowForDocument({
    required DocumentType documentType,
    double? amount,
  }) async {
    final db = await DatabaseHelper().database;
    
    String whereClause = 'document_type = ? AND is_active = 1';
    List<dynamic> whereArgs = [documentType.value];

    if (amount != null) {
      whereClause += ' AND (min_amount IS NULL OR min_amount <= ?)';
      whereClause += ' AND (max_amount IS NULL OR max_amount >= ?)';
      whereArgs.addAll([amount, amount]);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalWorkflows,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'min_amount DESC, id ASC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      final workflow = ApprovalWorkflow.fromMap(maps.first);
      final steps = await getWorkflowSteps(workflow.id!);
      return workflow.copyWith(steps: steps);
    }
    return null;
  }

  /// Insert workflow
  Future<int> insertWorkflow(ApprovalWorkflow workflow) async {
    final db = await DatabaseHelper().database;
    
    final workflowMap = workflow.toMap();
    workflowMap.remove('id');
    workflowMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableApprovalWorkflows,
      workflowMap,
    );
  }

  /// Update workflow
  Future<int> updateWorkflow(ApprovalWorkflow workflow) async {
    final db = await DatabaseHelper().database;
    
    final workflowMap = workflow.toMap();
    workflowMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableApprovalWorkflows,
      workflowMap,
      where: 'id = ?',
      whereArgs: [workflow.id],
    );
  }

  /// Delete workflow
  Future<int> deleteWorkflow(int id) async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tableApprovalWorkflows,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get workflow steps
  Future<List<ApprovalStep>> getWorkflowSteps(int workflowId) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalSteps,
      where: 'workflow_id = ?',
      whereArgs: [workflowId],
      orderBy: 'step_order ASC',
    );

    return List.generate(maps.length, (i) {
      return ApprovalStep.fromMap(maps[i]);
    });
  }

  /// Get workflow step by ID
  Future<ApprovalStep?> getWorkflowStepById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalSteps,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return ApprovalStep.fromMap(maps.first);
    }
    return null;
  }

  /// Insert workflow step
  Future<int> insertWorkflowStep(ApprovalStep step) async {
    final db = await DatabaseHelper().database;
    
    final stepMap = step.toMap();
    stepMap.remove('id');
    stepMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableApprovalSteps,
      stepMap,
    );
  }

  /// Update workflow step
  Future<int> updateWorkflowStep(ApprovalStep step) async {
    final db = await DatabaseHelper().database;
    
    final stepMap = step.toMap();
    stepMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableApprovalSteps,
      stepMap,
      where: 'id = ?',
      whereArgs: [step.id],
    );
  }

  /// Delete workflow step
  Future<int> deleteWorkflowStep(int id) async {
    final db = await DatabaseHelper().database;
    
    return await db.delete(
      DatabaseSchema.tableApprovalSteps,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get approval request by ID
  Future<ApprovalRequest?> getApprovalRequestById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalRequests,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return ApprovalRequest.fromMap(maps.first);
    }
    return null;
  }

  /// Get approval request by document
  Future<ApprovalRequest?> getApprovalRequestByDocument({
    required DocumentType documentType,
    required int documentId,
  }) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalRequests,
      where: 'document_type = ? AND document_id = ?',
      whereArgs: [documentType.value, documentId],
      orderBy: 'created_at DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return ApprovalRequest.fromMap(maps.first);
    }
    return null;
  }

  /// Insert approval request
  Future<int> insertApprovalRequest(ApprovalRequest request) async {
    final db = await DatabaseHelper().database;
    
    final requestMap = request.toMap();
    requestMap.remove('id');
    requestMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableApprovalRequests,
      requestMap,
    );
  }

  /// Update approval request
  Future<int> updateApprovalRequest(ApprovalRequest request) async {
    final db = await DatabaseHelper().database;
    
    final requestMap = request.toMap();
    requestMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableApprovalRequests,
      requestMap,
      where: 'id = ?',
      whereArgs: [request.id],
    );
  }

  /// Get pending approvals for user
  Future<List<ApprovalRequest>> getPendingApprovalsForUser(int userId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT DISTINCT ar.*
      FROM ${DatabaseSchema.tableApprovalRequests} ar
      INNER JOIN ${DatabaseSchema.tableApprovalSteps} ast ON ar.current_step_id = ast.id
      WHERE ar.status = 'pending'
      AND (ast.approver_id = ? OR ast.role_id IN (
        SELECT role_id FROM user_roles WHERE user_id = ?
      ))
      ORDER BY ar.requested_at ASC
    ''', [userId, userId]);

    return List.generate(result.length, (i) {
      return ApprovalRequest.fromMap(result[i]);
    });
  }

  /// Insert approval action
  Future<int> insertApprovalAction(ApprovalAction action) async {
    final db = await DatabaseHelper().database;
    
    final actionMap = action.toMap();
    actionMap.remove('id');

    return await db.insert(
      DatabaseSchema.tableApprovalActions,
      actionMap,
    );
  }

  /// Get approval history for document
  Future<List<ApprovalAction>> getApprovalHistoryForDocument({
    required DocumentType documentType,
    required int documentId,
  }) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT aa.*
      FROM ${DatabaseSchema.tableApprovalActions} aa
      INNER JOIN ${DatabaseSchema.tableApprovalRequests} ar ON aa.request_id = ar.id
      WHERE ar.document_type = ? AND ar.document_id = ?
      ORDER BY aa.action_at ASC
    ''', [documentType.value, documentId]);

    return List.generate(result.length, (i) {
      return ApprovalAction.fromMap(result[i]);
    });
  }

  /// Get user roles
  Future<List<int>> getUserRoles(int userId) async {
    final db = await DatabaseHelper().database;
    
    final result = await db.query(
      'user_roles',
      columns: ['role_id'],
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    return result.map((row) => row['role_id'] as int).toList();
  }

  /// Get approval requests by status
  Future<List<ApprovalRequest>> getApprovalRequestsByStatus(ApprovalStatus status) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalRequests,
      where: 'status = ?',
      whereArgs: [status.value],
      orderBy: 'requested_at DESC',
    );

    return List.generate(maps.length, (i) {
      return ApprovalRequest.fromMap(maps[i]);
    });
  }

  /// Get approval requests by date range
  Future<List<ApprovalRequest>> getApprovalRequestsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalRequests,
      where: 'requested_at BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'requested_at DESC',
    );

    return List.generate(maps.length, (i) {
      return ApprovalRequest.fromMap(maps[i]);
    });
  }

  /// Search approval requests
  Future<List<ApprovalRequest>> searchApprovalRequests(String query) async {
    final db = await DatabaseHelper().database;
    final searchQuery = '%$query%';
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableApprovalRequests,
      where: '''
        document_number LIKE ? OR 
        description LIKE ?
      ''',
      whereArgs: [searchQuery, searchQuery],
      orderBy: 'requested_at DESC',
    );

    return List.generate(maps.length, (i) {
      return ApprovalRequest.fromMap(maps[i]);
    });
  }

  /// Get approval statistics
  Future<Map<String, dynamic>> getApprovalStatistics() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        status,
        COUNT(*) as count,
        AVG(
          CASE 
            WHEN completed_at IS NOT NULL 
            THEN (julianday(completed_at) - julianday(requested_at)) * 24 
            ELSE NULL 
          END
        ) as avg_processing_hours
      FROM ${DatabaseSchema.tableApprovalRequests}
      GROUP BY status
    ''');

    final stats = <String, dynamic>{};
    for (final row in result) {
      stats[row['status'] as String] = {
        'count': row['count'],
        'avg_processing_hours': row['avg_processing_hours'],
      };
    }

    return stats;
  }
}
