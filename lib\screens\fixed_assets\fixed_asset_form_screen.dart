import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/fixed_asset.dart';
import '../../services/fixed_asset_service.dart';
import '../../utils/validators.dart';

/// شاشة نموذج إضافة/تعديل الأصول الثابتة
class FixedAssetFormScreen extends StatefulWidget {
  final FixedAsset? asset;

  const FixedAssetFormScreen({super.key, this.asset});

  @override
  State<FixedAssetFormScreen> createState() => _FixedAssetFormScreenState();
}

class _FixedAssetFormScreenState extends State<FixedAssetFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final FixedAssetService _assetService = FixedAssetService();
  
  // Controllers
  final _codeController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _salvageValueController = TextEditingController();
  final _usefulLifeController = TextEditingController();
  final _serialNumberController = TextEditingController();
  final _modelController = TextEditingController();
  final _manufacturerController = TextEditingController();
  final _warrantyInfoController = TextEditingController();
  final _notesController = TextEditingController();

  // Form values
  AssetCategory _selectedCategory = AssetCategory.other;
  DepreciationMethod _selectedDepreciationMethod = DepreciationMethod.straightLine;
  AssetStatus _selectedStatus = AssetStatus.active;
  DateTime _purchaseDate = DateTime.now();
  DateTime? _warrantyExpiry;

  bool _isLoading = false;
  bool get _isEditing => widget.asset != null;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _purchasePriceController.dispose();
    _salvageValueController.dispose();
    _usefulLifeController.dispose();
    _serialNumberController.dispose();
    _modelController.dispose();
    _manufacturerController.dispose();
    _warrantyInfoController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (_isEditing) {
      final asset = widget.asset!;
      _codeController.text = asset.code;
      _nameController.text = asset.name;
      _descriptionController.text = asset.description ?? '';
      _locationController.text = asset.location ?? '';
      _purchasePriceController.text = asset.purchasePrice.toString();
      _salvageValueController.text = asset.salvageValue?.toString() ?? '';
      _usefulLifeController.text = asset.usefulLifeYears.toString();
      _serialNumberController.text = asset.serialNumber ?? '';
      _modelController.text = asset.model ?? '';
      _manufacturerController.text = asset.manufacturer ?? '';
      _warrantyInfoController.text = asset.warrantyInfo ?? '';
      _notesController.text = asset.notes ?? '';
      
      _selectedCategory = asset.category;
      _selectedDepreciationMethod = asset.depreciationMethod;
      _selectedStatus = asset.status;
      _purchaseDate = asset.purchaseDate;
      _warrantyExpiry = asset.warrantyExpiry;
    } else {
      _generateAssetCode();
    }
  }

  Future<void> _generateAssetCode() async {
    final result = await _assetService.getNextAssetCode();
    if (result.isSuccess && mounted) {
      setState(() {
        _codeController.text = result.data!;
      });
    }
  }

  Future<void> _saveAsset() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final asset = FixedAsset(
        id: _isEditing ? widget.asset!.id : null,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        category: _selectedCategory,
        location: _locationController.text.trim().isEmpty 
            ? null 
            : _locationController.text.trim(),
        purchaseDate: _purchaseDate,
        purchasePrice: double.parse(_purchasePriceController.text),
        salvageValue: _salvageValueController.text.trim().isEmpty 
            ? null 
            : double.parse(_salvageValueController.text),
        usefulLifeYears: int.parse(_usefulLifeController.text),
        depreciationMethod: _selectedDepreciationMethod,
        status: _selectedStatus,
        serialNumber: _serialNumberController.text.trim().isEmpty 
            ? null 
            : _serialNumberController.text.trim(),
        model: _modelController.text.trim().isEmpty 
            ? null 
            : _modelController.text.trim(),
        manufacturer: _manufacturerController.text.trim().isEmpty 
            ? null 
            : _manufacturerController.text.trim(),
        warrantyInfo: _warrantyInfoController.text.trim().isEmpty 
            ? null 
            : _warrantyInfoController.text.trim(),
        warrantyExpiry: _warrantyExpiry,
        notes: _notesController.text.trim().isEmpty 
            ? null 
            : _notesController.text.trim(),
        createdAt: _isEditing ? widget.asset!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = _isEditing
          ? await _assetService.updateFixedAsset(asset)
          : await _assetService.createFixedAsset(asset);

      if (mounted) {
        setState(() => _isLoading = false);

        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isEditing 
                  ? 'تم تحديث الأصل الثابت بنجاح'
                  : 'تم إضافة الأصل الثابت بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل أصل ثابت' : 'إضافة أصل ثابت'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveAsset,
              child: Text(
                _isEditing ? 'تحديث' : 'حفظ',
                style: const TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildBasicInfoSection(),
            const SizedBox(height: 24),
            _buildFinancialInfoSection(),
            const SizedBox(height: 24),
            _buildTechnicalInfoSection(),
            const SizedBox(height: 24),
            _buildAdditionalInfoSection(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _codeController,
                    decoration: const InputDecoration(
                      labelText: 'رمز الأصل *',
                      border: OutlineInputBorder(),
                    ),
                    validator: Validators.required,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<AssetCategory>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'الفئة *',
                      border: OutlineInputBorder(),
                    ),
                    items: AssetCategory.values.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(_getCategoryName(category)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الأصل *',
                border: OutlineInputBorder(),
              ),
              validator: Validators.required,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _locationController,
                    decoration: const InputDecoration(
                      labelText: 'الموقع',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<AssetStatus>(
                    value: _selectedStatus,
                    decoration: const InputDecoration(
                      labelText: 'الحالة *',
                      border: OutlineInputBorder(),
                    ),
                    items: AssetStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusName(status)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات المالية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _purchasePriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر الشراء *',
                      border: OutlineInputBorder(),
                      suffixText: 'ر.س',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'سعر الشراء مطلوب';
                      }
                      final price = double.tryParse(value);
                      if (price == null || price <= 0) {
                        return 'سعر الشراء يجب أن يكون أكبر من صفر';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _salvageValueController,
                    decoration: const InputDecoration(
                      labelText: 'القيمة المتبقية',
                      border: OutlineInputBorder(),
                      suffixText: 'ر.س',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final salvageValue = double.tryParse(value);
                        if (salvageValue == null || salvageValue < 0) {
                          return 'القيمة المتبقية لا يمكن أن تكون سالبة';
                        }
                        final purchasePrice = double.tryParse(_purchasePriceController.text);
                        if (purchasePrice != null && salvageValue >= purchasePrice) {
                          return 'القيمة المتبقية يجب أن تكون أقل من سعر الشراء';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _usefulLifeController,
                    decoration: const InputDecoration(
                      labelText: 'العمر الإنتاجي *',
                      border: OutlineInputBorder(),
                      suffixText: 'سنة',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'العمر الإنتاجي مطلوب';
                      }
                      final years = int.tryParse(value);
                      if (years == null || years <= 0) {
                        return 'العمر الإنتاجي يجب أن يكون أكبر من صفر';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<DepreciationMethod>(
                    value: _selectedDepreciationMethod,
                    decoration: const InputDecoration(
                      labelText: 'طريقة الاستهلاك *',
                      border: OutlineInputBorder(),
                    ),
                    items: DepreciationMethod.values.map((method) {
                      return DropdownMenuItem(
                        value: method,
                        child: Text(_getDepreciationMethodName(method)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedDepreciationMethod = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _purchaseDate,
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _purchaseDate = date;
                  });
                }
              },
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ الشراء *',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  '${_purchaseDate.day}/${_purchaseDate.month}/${_purchaseDate.year}',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnicalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات التقنية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _serialNumberController,
                    decoration: const InputDecoration(
                      labelText: 'الرقم التسلسلي',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _modelController,
                    decoration: const InputDecoration(
                      labelText: 'الموديل',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _manufacturerController,
              decoration: const InputDecoration(
                labelText: 'الشركة المصنعة',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _warrantyInfoController,
              decoration: const InputDecoration(
                labelText: 'معلومات الضمان',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _warrantyExpiry ?? DateTime.now().add(const Duration(days: 365)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
                );
                if (date != null) {
                  setState(() {
                    _warrantyExpiry = date;
                  });
                }
              },
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'انتهاء الضمان',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _warrantyExpiry != null
                      ? '${_warrantyExpiry!.day}/${_warrantyExpiry!.month}/${_warrantyExpiry!.year}'
                      : 'اختر تاريخ انتهاء الضمان',
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  String _getCategoryName(AssetCategory category) {
    switch (category) {
      case AssetCategory.building:
        return 'مباني';
      case AssetCategory.machinery:
        return 'آلات ومعدات';
      case AssetCategory.vehicle:
        return 'مركبات';
      case AssetCategory.furniture:
        return 'أثاث ومفروشات';
      case AssetCategory.computer:
        return 'أجهزة حاسوب';
      case AssetCategory.other:
        return 'أخرى';
    }
  }

  String _getStatusName(AssetStatus status) {
    switch (status) {
      case AssetStatus.active:
        return 'نشط';
      case AssetStatus.inactive:
        return 'غير نشط';
      case AssetStatus.disposed:
        return 'مستبعد';
      case AssetStatus.underMaintenance:
        return 'تحت الصيانة';
    }
  }

  String _getDepreciationMethodName(DepreciationMethod method) {
    switch (method) {
      case DepreciationMethod.straightLine:
        return 'خط مستقيم';
      case DepreciationMethod.decliningBalance:
        return 'رصيد متناقص';
      case DepreciationMethod.unitsOfProduction:
        return 'وحدات الإنتاج';
      case DepreciationMethod.sumOfYears:
        return 'مجموع السنوات';
    }
  }
}
