/// شاشة إضافة القيود المحاسبية المحسنة
/// Enhanced Add Journal Entry Screen
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/journal_entry.dart';
import '../../models/account.dart';
import '../../services/journal_entry_service.dart';
import '../../services/account_service.dart';
import '../../services/user_service.dart';
import '../../theme/app_theme.dart';

class EnhancedAddJournalEntryScreen extends StatefulWidget {
  final JournalEntry? entry;

  const EnhancedAddJournalEntryScreen({super.key, this.entry});

  @override
  State<EnhancedAddJournalEntryScreen> createState() =>
      _EnhancedAddJournalEntryScreenState();
}

class _EnhancedAddJournalEntryScreenState
    extends State<EnhancedAddJournalEntryScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();

  // Controllers
  final TextEditingController _entryNumberController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _referenceController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  List<JournalEntryLine> _lines = [];
  List<Account> _accounts = [];
  bool _isLoading = false;
  bool _isLoadingAccounts = true;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadAccounts();
    _initializeForm();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _entryNumberController.dispose();
    _descriptionController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (widget.entry != null) {
      _entryNumberController.text = widget.entry!.entryNumber;
      _descriptionController.text = widget.entry!.description;
      _referenceController.text = widget.entry!.reference ?? '';
      _selectedDate = widget.entry!.date;
      _lines = List.from(widget.entry!.lines);
    } else {
      _generateEntryNumber();
      _addEmptyLine();
      _addEmptyLine();
    }
  }

  Future<void> _loadAccounts() async {
    setState(() => _isLoadingAccounts = true);

    try {
      final result = await _accountService.getAllAccounts();
      if (mounted) {
        if (result.isSuccess) {
          setState(() {
            _accounts = result.data!
                .where((account) => account.isActive)
                .toList();
          });
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل الحسابات: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingAccounts = false);
      }
    }
  }

  Future<void> _generateEntryNumber() async {
    try {
      final result = await _journalEntryService.getNextEntryNumber();
      if (mounted && result.isSuccess) {
        setState(() {
          _entryNumberController.text = result.data!;
        });
      }
    } catch (e) {
      // Handle error silently for entry number generation
    }
  }

  void _addEmptyLine() {
    setState(() {
      _lines.add(
        JournalEntryLine(
          journalEntryId: 0,
          accountId: 0,
          description: '',
          debitAmount: 0.0,
          creditAmount: 0.0,
        ),
      );
    });
  }

  void _removeLine(int index) {
    if (_lines.length > 2) {
      setState(() {
        _lines.removeAt(index);
      });
    }
  }

  double get _totalDebits =>
      _lines.fold(0.0, (sum, line) => sum + line.debitAmount);
  double get _totalCredits =>
      _lines.fold(0.0, (sum, line) => sum + line.creditAmount);
  bool get _isBalanced => (_totalDebits - _totalCredits).abs() < 0.01;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          widget.entry == null ? 'قيد محاسبي جديد' : 'تعديل القيد المحاسبي',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (!_isLoading)
            TextButton.icon(
              onPressed: _isBalanced ? _saveEntry : null,
              icon: const Icon(Icons.save, color: Colors.white),
              label: const Text(
                'حفظ',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: _isLoadingAccounts
              ? const Center(child: CircularProgressIndicator())
              : Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      _buildHeaderCard(),
                      Expanded(child: _buildJournalLinesSection()),
                      _buildSummaryCard(),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.receipt_long,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'معلومات القيد الأساسية',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildTextField(
                  controller: _entryNumberController,
                  label: 'رقم القيد',
                  icon: Icons.numbers,
                  readOnly: true,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(flex: 3, child: _buildDateField()),
            ],
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _descriptionController,
            label: 'وصف القيد',
            icon: Icons.description,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال وصف القيد';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _referenceController,
            label: 'المرجع (اختياري)',
            icon: Icons.link,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    bool readOnly = false,
  }) {
    return TextFormField(
      controller: controller,
      readOnly: readOnly,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppTheme.primaryColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
        filled: true,
        fillColor: readOnly ? Colors.grey[100] : Colors.white,
      ),
    );
  }

  Widget _buildDateField() {
    return GestureDetector(
      onTap: _selectDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: AppTheme.primaryColor),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تاريخ القيد',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJournalLinesSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.list_alt,
                      color: Colors.green,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'بنود القيد المحاسبي',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: _addEmptyLine,
                icon: const Icon(Icons.add, size: 18),
                label: const Text('إضافة بند'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: _lines.length,
              itemBuilder: (context, index) => _buildJournalLineCard(index),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJournalLineCard(int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'البند ${index + 1}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              if (_lines.length > 2)
                IconButton(
                  onPressed: () => _removeLine(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'حذف البند',
                ),
            ],
          ),
          const SizedBox(height: 12),
          _buildAccountDropdown(index),
          const SizedBox(height: 12),
          _buildLineDescriptionField(index),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildAmountField(index, true)), // Debit
              const SizedBox(width: 12),
              Expanded(child: _buildAmountField(index, false)), // Credit
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountDropdown(int index) {
    final line = _lines[index];
    return DropdownButtonFormField<int>(
      value: line.accountId == 0 ? null : line.accountId,
      decoration: InputDecoration(
        labelText: 'الحساب',
        prefixIcon: Icon(Icons.account_balance, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.white,
      ),
      items: _accounts.map((account) {
        return DropdownMenuItem<int>(
          value: account.id,
          child: Text('${account.code} - ${account.name}'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _lines[index] = line.copyWith(accountId: value);
          });
        }
      },
      validator: (value) {
        if (value == null || value == 0) {
          return 'يرجى اختيار الحساب';
        }
        return null;
      },
    );
  }

  Widget _buildLineDescriptionField(int index) {
    final line = _lines[index];
    return TextFormField(
      initialValue: line.description,
      decoration: InputDecoration(
        labelText: 'وصف البند',
        prefixIcon: Icon(Icons.description, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.white,
      ),
      onChanged: (value) {
        setState(() {
          _lines[index] = line.copyWith(description: value);
        });
      },
    );
  }

  Widget _buildAmountField(int index, bool isDebit) {
    final line = _lines[index];
    final amount = isDebit ? line.debitAmount : line.creditAmount;

    return TextFormField(
      initialValue: amount > 0 ? amount.toString() : '',
      decoration: InputDecoration(
        labelText: isDebit ? 'مدين' : 'دائن',
        prefixIcon: Icon(
          isDebit ? Icons.add : Icons.remove,
          color: isDebit ? Colors.green : Colors.red,
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.white,
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
      ],
      onChanged: (value) {
        final newAmount = double.tryParse(value) ?? 0.0;
        setState(() {
          if (isDebit) {
            _lines[index] = line.copyWith(
              debitAmount: newAmount,
              creditAmount: 0.0,
            );
          } else {
            _lines[index] = line.copyWith(
              debitAmount: 0.0,
              creditAmount: newAmount,
            );
          }
        });
      },
      validator: (value) {
        final debit = _lines[index].debitAmount;
        final credit = _lines[index].creditAmount;
        if (debit == 0 && credit == 0) {
          return 'يرجى إدخال مبلغ';
        }
        return null;
      },
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isBalanced ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isBalanced ? Colors.green : Colors.red,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                _isBalanced ? Icons.check_circle : Icons.error,
                color: _isBalanced ? Colors.green : Colors.red,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                _isBalanced ? 'القيد متوازن' : 'القيد غير متوازن',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isBalanced ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildSummaryItem('إجمالي المدين', _totalDebits, Colors.green),
              _buildSummaryItem('إجمالي الدائن', _totalCredits, Colors.red),
              _buildSummaryItem(
                'الفرق',
                (_totalDebits - _totalCredits).abs(),
                _isBalanced ? Colors.green : Colors.red,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, double amount, Color color) {
    return Column(
      children: [
        Text(label, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} ر.س',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(primary: AppTheme.primaryColor),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveEntry() async {
    if (!_formKey.currentState!.validate() || !_isBalanced) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Get current user name from user service
      final currentUserName = await UserService.instance.getCurrentUserName();

      final entry = JournalEntry(
        id: widget.entry?.id,
        entryNumber: _entryNumberController.text,
        date: _selectedDate,
        description: _descriptionController.text,
        reference: _referenceController.text.isEmpty
            ? null
            : _referenceController.text,
        lines: _lines.where((line) => line.accountId != 0).toList(),
        isPosted: false,
        createdAt: widget.entry?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: currentUserName,
      );

      final result = widget.entry == null
          ? await _journalEntryService.createJournalEntry(entry)
          : await _journalEntryService.updateJournalEntry(entry);

      if (mounted) {
        if (result.isSuccess) {
          Navigator.pop(context, true);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.entry == null
                    ? 'تم إنشاء القيد بنجاح'
                    : 'تم تحديث القيد بنجاح',
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          _showErrorSnackBar(result.error ?? 'خطأ في حفظ القيد');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في حفظ القيد: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
