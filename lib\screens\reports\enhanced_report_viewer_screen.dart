/// شاشة عارض التقارير المحسنة
/// Enhanced Report Viewer Screen
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:excel/excel.dart';
import '../../models/financial_report.dart';
import '../../models/company_settings.dart';
import '../../services/company_settings_service.dart';
import '../../theme/app_theme.dart';
import '../../utils/result.dart';

class EnhancedReportViewerScreen extends StatefulWidget {
  final FinancialReport report;
  final String? customTitle;

  const EnhancedReportViewerScreen({
    super.key,
    required this.report,
    this.customTitle,
  });

  @override
  State<EnhancedReportViewerScreen> createState() =>
      _EnhancedReportViewerScreenState();
}

class _EnhancedReportViewerScreenState extends State<EnhancedReportViewerScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _showDetails = true;
  bool _isFullScreen = false;

  final CompanySettingsService _companySettingsService =
      CompanySettingsService();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _isFullScreen ? null : _buildAppBar(),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(),
            ),
          );
        },
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        widget.customTitle ?? widget.report.title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () => setState(() => _showDetails = !_showDetails),
          icon: Icon(_showDetails ? Icons.visibility_off : Icons.visibility),
          tooltip: _showDetails ? 'إخفاء التفاصيل' : 'إظهار التفاصيل',
        ),
        IconButton(
          onPressed: _toggleFullScreen,
          icon: Icon(_isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen),
          tooltip: _isFullScreen ? 'إلغاء ملء الشاشة' : 'ملء الشاشة',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export_pdf',
              child: Row(
                children: [
                  Icon(Icons.picture_as_pdf, color: Colors.red),
                  SizedBox(width: 8),
                  Text('تصدير PDF'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export_excel',
              child: Row(
                children: [
                  Icon(Icons.table_chart, color: Colors.green),
                  SizedBox(width: 8),
                  Text('تصدير Excel'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('طباعة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('مشاركة'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(_isFullScreen ? 8 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isFullScreen) _buildFullScreenHeader(),
          if (_showDetails && !_isFullScreen) _buildReportHeader(),
          const SizedBox(height: 16),
          _buildReportContent(),
          if (_showDetails && !_isFullScreen) ...[
            const SizedBox(height: 24),
            _buildReportSummary(),
          ],
        ],
      ),
    );
  }

  Widget _buildFullScreenHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.report.title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (widget.report.subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    widget.report.subtitle!,
                    style: const TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                ],
              ],
            ),
          ),
          IconButton(
            onPressed: _toggleFullScreen,
            icon: const Icon(Icons.fullscreen_exit, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildReportHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.report.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          if (widget.report.subtitle != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.report.subtitle!,
              style: const TextStyle(fontSize: 16, color: Colors.white70),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHeaderInfo(
                icon: Icons.calendar_today,
                label: 'تاريخ الإنشاء',
                value: _formatDate(widget.report.generatedAt),
              ),
              const SizedBox(width: 24),
              _buildHeaderInfo(
                icon: Icons.assessment,
                label: 'نوع التقرير',
                value: _getReportTypeName(widget.report.type),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderInfo({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, color: Colors.white70, size: 16),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 12, color: Colors.white70),
            ),
            Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReportContent() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: widget.report.sections.map((section) {
          return _buildReportSection(section);
        }).toList(),
      ),
    );
  }

  Widget _buildReportSection(ReportSection section) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            section.title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          ...section.lines.map((line) => _buildReportLine(line)),
          if (section.totals.isNotEmpty) ...[
            const Divider(thickness: 2),
            ...section.totals.entries.map((entry) => _buildTotalLine(entry)),
          ],
        ],
      ),
    );
  }

  Widget _buildReportLine(ReportLine line) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          if (line.accountCode.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                line.accountCode,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontFamily: 'monospace',
                ),
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Text(
              line.accountName,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontWeight: line.isBold ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            _formatAmount(line.amount),
            style: TextStyle(
              fontSize: 14,
              fontWeight: line.isBold ? FontWeight.bold : FontWeight.w500,
              color: line.amount >= 0 ? Colors.black87 : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalLine(MapEntry<String, dynamic> entry) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Expanded(
            child: Text(
              _getTotalLabel(entry.key),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Text(
            _formatAmount(entry.value as double),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportSummary() {
    if (widget.report.summary.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص التقرير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          ...widget.report.summary.entries.map((entry) {
            return _buildSummaryItem(entry.key, entry.value);
          }),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String key, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              _getSummaryLabel(key),
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
          Text(
            _formatSummaryValue(value),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    if (_isFullScreen) return const SizedBox.shrink();

    return FloatingActionButton.extended(
      onPressed: _showQuickActions,
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.more_horiz),
      label: const Text('إجراءات'),
    );
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_pdf':
        _exportToPdf();
        break;
      case 'export_excel':
        _exportToExcel();
        break;
      case 'print':
        _printReport();
        break;
      case 'share':
        _shareReport();
        break;
    }
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'إجراءات التقرير',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('تصدير PDF'),
              onTap: () {
                Navigator.pop(context);
                _exportToPdf();
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('تصدير Excel'),
              onTap: () {
                Navigator.pop(context);
                _exportToExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.print, color: Colors.blue),
              title: const Text('طباعة'),
              onTap: () {
                Navigator.pop(context);
                _printReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.share, color: Colors.orange),
              title: const Text('مشاركة'),
              onTap: () {
                Navigator.pop(context);
                _shareReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy, color: Colors.purple),
              title: const Text('نسخ البيانات'),
              onTap: () {
                Navigator.pop(context);
                _copyReportData();
              },
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(2);
  }

  String _formatSummaryValue(dynamic value) {
    if (value is double) {
      return _formatAmount(value);
    } else if (value is bool) {
      return value ? 'نعم' : 'لا';
    }
    return value.toString();
  }

  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.balanceSheet:
        return 'الميزانية العمومية';
      case ReportType.incomeStatement:
        return 'قائمة الدخل';
      case ReportType.trialBalance:
        return 'ميزان المراجعة';
      case ReportType.accountStatement:
        return 'كشف حساب';
      case ReportType.cashFlow:
        return 'قائمة التدفق النقدي';
      default:
        return 'تقرير مالي';
    }
  }

  String _getTotalLabel(String key) {
    switch (key) {
      case 'total':
        return 'الإجمالي';
      case 'total_assets':
        return 'إجمالي الأصول';
      case 'total_liabilities':
        return 'إجمالي الخصوم';
      case 'total_equity':
        return 'إجمالي حقوق الملكية';
      case 'total_revenue':
        return 'إجمالي الإيرادات';
      case 'total_expenses':
        return 'إجمالي المصروفات';
      case 'net_income':
        return 'صافي الدخل';
      default:
        return key;
    }
  }

  String _getSummaryLabel(String key) {
    switch (key) {
      case 'total_assets':
        return 'إجمالي الأصول';
      case 'total_liabilities_equity':
        return 'إجمالي الخصوم وحقوق الملكية';
      case 'is_balanced':
        return 'متوازن';
      case 'profit_margin':
        return 'هامش الربح (%)';
      default:
        return key;
    }
  }

  // Action methods
  void _exportToPdf() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Generate PDF
      final result = await _generateReportPdf();

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ التقرير في: ${result.data}'),
              backgroundColor: Colors.green,
              action: SnackBarAction(
                label: 'فتح',
                onPressed: () => _openPdfFile(result.data!),
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تصدير PDF: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Hide loading indicator if still showing
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportToExcel() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Generate Excel file
      final result = await _generateExcelFile();

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تصدير التقرير بنجاح: ${result.data}'),
              backgroundColor: Colors.green,
              action: SnackBarAction(
                label: 'فتح',
                onPressed: () {
                  // Open the file location or share
                  _shareFile(result.data!);
                },
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تصدير Excel: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Hide loading indicator if still showing
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Generate Excel file from report data
  Future<Result<String>> _generateExcelFile() async {
    try {
      // Create Excel workbook
      final excel = Excel.createExcel();

      // Remove default sheet and create a new one with Arabic name
      excel.delete('Sheet1');
      final sheet = excel['التقرير المالي'];

      // Set RTL direction for Arabic text
      sheet.isRTL = true;

      int currentRow = 0;

      // Add report header
      _addExcelHeader(sheet, currentRow);
      currentRow += 4; // Skip header rows

      // Add report sections
      for (final section in widget.report.sections) {
        currentRow = _addExcelSection(sheet, section, currentRow);
        currentRow += 2; // Add spacing between sections
      }

      // Add summary if available
      if (widget.report.summary.isNotEmpty) {
        currentRow = _addExcelSummary(sheet, widget.report.summary, currentRow);
      }

      // Apply styling
      _applyExcelStyling(sheet);

      // Save file
      final output = await getApplicationDocumentsDirectory();
      final fileName =
          'report_${widget.report.type.value}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${output.path}/$fileName';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        return Result.success(filePath);
      } else {
        return Result.error('فشل في حفظ ملف Excel');
      }
    } catch (e) {
      return Result.error('خطأ في إنشاء ملف Excel: ${e.toString()}');
    }
  }

  /// Add header information to Excel sheet
  void _addExcelHeader(Sheet sheet, int startRow) {
    // Report title
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow))
        .value = TextCellValue(
      widget.report.title,
    );

    // Report subtitle if available
    if (widget.report.subtitle != null) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 1),
          )
          .value = TextCellValue(
        widget.report.subtitle!,
      );
    }

    // Date range
    sheet
        .cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 2),
        )
        .value = TextCellValue(
      'من تاريخ: ${_formatDate(widget.report.fromDate)}',
    );

    sheet
        .cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 3),
        )
        .value = TextCellValue(
      'إلى تاريخ: ${_formatDate(widget.report.toDate)}',
    );
  }

  /// Add a report section to Excel sheet
  int _addExcelSection(Sheet sheet, ReportSection section, int startRow) {
    int currentRow = startRow;

    // Section title
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      section.title,
    );
    currentRow++;

    // Table headers
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'رمز الحساب',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = TextCellValue(
      'اسم الحساب',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow))
        .value = TextCellValue(
      'المبلغ',
    );

    if (section.lines.isNotEmpty &&
        section.lines.first.previousAmount != null) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: currentRow),
          )
          .value = TextCellValue(
        'المبلغ السابق',
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: currentRow),
          )
          .value = TextCellValue(
        'النسبة %',
      );
    }
    currentRow++;

    // Section data
    for (final line in section.lines) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        line.accountCode,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = TextCellValue(
        line.accountName,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow),
          )
          .value = DoubleCellValue(
        line.amount,
      );

      if (line.previousAmount != null) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: currentRow),
            )
            .value = DoubleCellValue(
          line.previousAmount!,
        );
      }

      if (line.percentage != null) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: currentRow),
            )
            .value = DoubleCellValue(
          line.percentage!,
        );
      }

      currentRow++;
    }

    // Section totals
    if (section.totals.isNotEmpty) {
      currentRow++; // Add spacing
      for (final total in section.totals.entries) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
            )
            .value = TextCellValue(
          _getTotalLabel(total.key),
        );
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow),
            )
            .value = DoubleCellValue(
          total.value as double,
        );
        currentRow++;
      }
    }

    return currentRow;
  }

  /// Add summary section to Excel sheet
  int _addExcelSummary(
    Sheet sheet,
    Map<String, dynamic> summary,
    int startRow,
  ) {
    int currentRow = startRow;

    // Summary title
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'ملخص التقرير',
    );
    currentRow += 2;

    // Summary data
    for (final entry in summary.entries) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        entry.key,
      );

      if (entry.value is double) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
            )
            .value = DoubleCellValue(
          entry.value,
        );
      } else {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
            )
            .value = TextCellValue(
          entry.value.toString(),
        );
      }
      currentRow++;
    }

    return currentRow;
  }

  /// Apply styling to Excel sheet
  void _applyExcelStyling(Sheet sheet) {
    // This is a basic implementation
    // The excel package has limited styling options
    // You can enhance this based on your needs
  }

  /// Share or open the generated file
  void _shareFile(String filePath) {
    // For now, just show the file path
    // You can integrate with share_plus package for actual sharing
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ملف Excel'),
        content: Text('تم حفظ الملف في:\n$filePath'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _printReport() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Get company settings
      final companyResult = await _companySettingsService.getCompanySettings();
      if (!companyResult.isSuccess) {
        if (mounted) Navigator.of(context).pop();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'خطأ في الحصول على إعدادات الشركة: ${companyResult.error}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final companySettings = companyResult.data!;

      // Create PDF document
      final pdf = await _createReportPdf(widget.report, companySettings);

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Print the document
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: widget.report.title,
      );
    } catch (e) {
      // Hide loading indicator if still showing
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الطباعة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareReport() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Get company settings
      final companyResult = await _companySettingsService.getCompanySettings();
      if (!companyResult.isSuccess) {
        if (mounted) Navigator.of(context).pop();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'خطأ في الحصول على إعدادات الشركة: ${companyResult.error}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final companySettings = companyResult.data!;

      // Create PDF document
      final pdf = await _createReportPdf(widget.report, companySettings);

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Share the document
      await Printing.sharePdf(
        bytes: await pdf.save(),
        filename:
            'report_${widget.report.type.value}_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );
    } catch (e) {
      // Hide loading indicator if still showing
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المشاركة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _copyReportData() {
    // Create a text representation of the report
    String reportText = '${widget.report.title}\n';
    if (widget.report.subtitle != null) {
      reportText += '${widget.report.subtitle}\n';
    }
    reportText += '\n';

    for (var section in widget.report.sections) {
      reportText += '${section.title}\n';
      reportText += '${'-' * 30}\n';

      for (var line in section.lines) {
        reportText += '${line.accountName}: ${_formatAmount(line.amount)}\n';
      }

      if (section.totals.isNotEmpty) {
        reportText += '\n';
        for (var total in section.totals.entries) {
          reportText +=
              '${_getTotalLabel(total.key)}: ${_formatAmount(total.value as double)}\n';
        }
      }
      reportText += '\n';
    }

    Clipboard.setData(ClipboardData(text: reportText));
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم نسخ بيانات التقرير')));
  }

  /// Generate PDF for the financial report
  Future<Result<String>> _generateReportPdf() async {
    try {
      // Get company settings
      final companyResult = await _companySettingsService.getCompanySettings();
      if (!companyResult.isSuccess) {
        return Result.error(
          'خطأ في الحصول على إعدادات الشركة: ${companyResult.error}',
        );
      }

      final companySettings = companyResult.data!;

      // Create PDF document
      final pdf = await _createReportPdf(widget.report, companySettings);

      // Save the file
      final output = await getApplicationDocumentsDirectory();
      final fileName =
          'report_${widget.report.type.value}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${output.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      return Result.success(file.path);
    } catch (e) {
      return Result.error('خطأ في توليد PDF: ${e.toString()}');
    }
  }

  /// Open PDF file using system default application
  void _openPdfFile(String filePath) async {
    try {
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async {
          final file = File(filePath);
          return file.readAsBytes();
        },
        name: widget.report.title,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الملف: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Create PDF document for the financial report
  Future<pw.Document> _createReportPdf(
    FinancialReport report,
    CompanySettings companySettings,
  ) async {
    final pdf = pw.Document();

    // Load Arabic font
    final arabicFont = await _loadArabicFont();

    // Add report page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(base: arabicFont),
        build: (pw.Context context) {
          return [
            _buildReportHeaderPdf(report, companySettings, arabicFont),
            pw.SizedBox(height: 20),
            _buildReportContentPdf(report, arabicFont),
          ];
        },
      ),
    );

    return pdf;
  }

  /// Load Arabic font for PDF
  Future<pw.Font> _loadArabicFont() async {
    try {
      // Try to load Arabic font from assets
      final fontData = await rootBundle.load(
        'assets/fonts/NotoSansArabic-Regular.ttf',
      );
      return pw.Font.ttf(fontData);
    } catch (e) {
      // If font not found, use default font
      return pw.Font.helvetica();
    }
  }

  /// Build report header for PDF
  pw.Widget _buildReportHeaderPdf(
    FinancialReport report,
    CompanySettings companySettings,
    pw.Font arabicFont,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Company header
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  companySettings.companyName,
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                if (companySettings.address?.isNotEmpty == true)
                  pw.Text(
                    companySettings.address!,
                    style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  ),
                if (companySettings.phone?.isNotEmpty == true)
                  pw.Text(
                    'هاتف: ${companySettings.phone!}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  ),
              ],
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'تاريخ التقرير: ${_formatDateForPdf(report.generatedAt)}',
                  style: pw.TextStyle(font: arabicFont, fontSize: 10),
                ),
                pw.Text(
                  'الفترة: ${_formatDateForPdf(report.fromDate)} - ${_formatDateForPdf(report.toDate)}',
                  style: pw.TextStyle(font: arabicFont, fontSize: 10),
                ),
              ],
            ),
          ],
        ),
        pw.SizedBox(height: 20),
        // Report title
        pw.Center(
          child: pw.Column(
            children: [
              pw.Text(
                report.title,
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              if (report.subtitle != null)
                pw.Text(
                  report.subtitle!,
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                ),
            ],
          ),
        ),
        pw.Divider(),
      ],
    );
  }

  /// Build report content for PDF
  pw.Widget _buildReportContentPdf(FinancialReport report, pw.Font arabicFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Build sections
        for (var section in report.sections) ...[
          _buildReportSectionPdf(section, arabicFont),
          pw.SizedBox(height: 15),
        ],

        // Build summary if available
        if (report.summary.isNotEmpty) ...[
          pw.Divider(),
          pw.SizedBox(height: 10),
          _buildReportSummaryPdf(report.summary, arabicFont),
        ],

        // Build notes if available
        if (report.notes?.isNotEmpty == true) ...[
          pw.SizedBox(height: 15),
          pw.Text(
            'ملاحظات:',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.Text(
            report.notes!,
            style: pw.TextStyle(font: arabicFont, fontSize: 10),
          ),
        ],
      ],
    );
  }

  /// Build individual report section for PDF
  pw.Widget _buildReportSectionPdf(ReportSection section, pw.Font arabicFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title
        pw.Text(
          section.title,
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),

        // Section content table
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(1),
            1: const pw.FlexColumnWidth(3),
            2: const pw.FlexColumnWidth(2),
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('رمز الحساب', arabicFont, isHeader: true),
                _buildTableCell('اسم الحساب', arabicFont, isHeader: true),
                _buildTableCell('المبلغ', arabicFont, isHeader: true),
              ],
            ),
            // Data rows
            for (var line in section.lines)
              pw.TableRow(
                children: [
                  _buildTableCell(line.accountCode, arabicFont),
                  _buildTableCell(line.accountName, arabicFont),
                  _buildTableCell(
                    _formatAmount(line.amount),
                    arabicFont,
                    isAmount: true,
                  ),
                ],
              ),
          ],
        ),

        // Section totals
        if (section.totals.isNotEmpty) ...[
          pw.SizedBox(height: 8),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey300),
              color: PdfColors.grey50,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                for (var total in section.totals.entries)
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        _getTotalLabel(total.key),
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 11,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        _formatAmount(total.value as double),
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 11,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build report summary for PDF
  pw.Widget _buildReportSummaryPdf(
    Map<String, dynamic> summary,
    pw.Font arabicFont,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        color: PdfColors.grey50,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص التقرير',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 8),
          for (var item in summary.entries)
            pw.Padding(
              padding: const pw.EdgeInsets.symmetric(vertical: 2),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    _getSummaryLabel(item.key),
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                  ),
                  pw.Text(
                    _formatSummaryValue(item.value),
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build table cell for PDF
  pw.Widget _buildTableCell(
    String text,
    pw.Font arabicFont, {
    bool isHeader = false,
    bool isAmount = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: isAmount ? pw.TextAlign.right : pw.TextAlign.right,
      ),
    );
  }

  /// Format date for PDF
  String _formatDateForPdf(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
