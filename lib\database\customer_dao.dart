import '../models/customer.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class CustomerDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Create a new customer
  Future<int> insertCustomer(Customer customer) async {
    Map<String, dynamic> customerMap = customer.toMap();
    customerMap.remove('id'); // Remove id for insert
    return await _dbHelper.insert(DatabaseSchema.tableCustomers, customerMap);
  }

  // Get all customers
  Future<List<Customer>> getAllCustomers() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      orderBy: 'name ASC',
    );
    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Get customer by ID
  Future<Customer?> getCustomerById(int id) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  // Get customer by code
  Future<Customer?> getCustomerByCode(String code) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where: 'code = ?',
      whereArgs: [code],
    );
    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  // Get active customers only
  Future<List<Customer>> getActiveCustomers() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );
    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Search customers by name, code, phone, or email
  Future<List<Customer>> searchCustomers(String query) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where: 'name LIKE ? OR code LIKE ? OR phone LIKE ? OR email LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Get customers with debit balance
  Future<List<Customer>> getCustomersWithDebitBalance() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where: 'current_balance > 0 AND is_active = 1',
      orderBy: 'current_balance DESC',
    );
    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Get customers with credit balance
  Future<List<Customer>> getCustomersWithCreditBalance() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where: 'current_balance < 0 AND is_active = 1',
      orderBy: 'current_balance ASC',
    );
    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Get customers who exceeded credit limit
  Future<List<Customer>> getCustomersExceedingCreditLimit() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where:
          'current_balance > credit_limit AND credit_limit > 0 AND is_active = 1',
      orderBy: 'current_balance DESC',
    );
    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Update customer
  Future<int> updateCustomer(Customer customer) async {
    return await _dbHelper.update(
      DatabaseSchema.tableCustomers,
      customer.toMap(),
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  // Update customer balance
  Future<int> updateCustomerBalance(int customerId, double newBalance) async {
    return await _dbHelper.update(
      DatabaseSchema.tableCustomers,
      {
        'current_balance': newBalance,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [customerId],
    );
  }

  // Deactivate customer (soft delete)
  Future<int> deactivateCustomer(int customerId) async {
    return await _dbHelper.update(
      DatabaseSchema.tableCustomers,
      {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [customerId],
    );
  }

  // Hard delete customer (use with caution)
  Future<int> deleteCustomer(int customerId) async {
    return await _dbHelper.delete(
      DatabaseSchema.tableCustomers,
      where: 'id = ?',
      whereArgs: [customerId],
    );
  }

  // Get next customer code
  Future<String> getNextCustomerCode() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      columns: ['code'],
      orderBy: 'code DESC',
      limit: 1,
    );

    if (maps.isEmpty) {
      return 'C001';
    }

    String lastCode = maps.first['code'];
    // Extract number from code (assuming format like C001, C002, etc.)
    RegExp regExp = RegExp(r'C(\d+)');
    Match? match = regExp.firstMatch(lastCode);

    if (match != null) {
      int lastNumber = int.parse(match.group(1)!);
      int nextNumber = lastNumber + 1;
      return 'C${nextNumber.toString().padLeft(3, '0')}';
    }

    return 'C001';
  }

  // Get customer statistics
  Future<Map<String, dynamic>> getCustomerStatistics(int customerId) async {
    final db = await _dbHelper.database;

    // Get total sales from invoices
    final salesResult = await db.rawQuery(
      '''
      SELECT
        COUNT(*) as invoice_count,
        COALESCE(SUM(total_amount), 0) as total_sales,
        COALESCE(SUM(paid_amount), 0) as total_paid,
        COALESCE(SUM(total_amount - paid_amount), 0) as total_outstanding
      FROM ${DatabaseSchema.tableInvoices}
      WHERE customer_id = ? AND status != 'cancelled'
    ''',
      [customerId],
    );

    if (salesResult.isNotEmpty) {
      return salesResult.first;
    }

    return {
      'invoice_count': 0,
      'total_sales': 0.0,
      'total_paid': 0.0,
      'total_outstanding': 0.0,
    };
  }

  // Get customer balance
  Future<double> getCustomerBalance(int customerId) async {
    final customer = await getCustomerById(customerId);
    return customer?.currentBalance ?? 0.0;
  }

  // Check if customer has invoices
  Future<bool> hasInvoices(int customerId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'customer_id = ?',
      whereArgs: [customerId],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  // Get customers with outstanding balance
  Future<List<Customer>> getCustomersWithOutstandingBalance() async {
    final db = await _dbHelper.database;

    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT c.* FROM ${DatabaseSchema.tableCustomers} c
      WHERE c.is_active = 1 AND (
        c.current_balance > 0 OR
        EXISTS (
          SELECT 1 FROM ${DatabaseSchema.tableInvoices} i
          WHERE i.customer_id = c.id AND i.total_amount > i.paid_amount
        )
      )
      ORDER BY c.current_balance DESC
    ''');

    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Get top customers by sales
  Future<List<Customer>> getTopCustomers({int limit = 10}) async {
    final db = await _dbHelper.database;

    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT c.*, COALESCE(SUM(i.total_amount), 0) as total_sales
      FROM ${DatabaseSchema.tableCustomers} c
      LEFT JOIN ${DatabaseSchema.tableInvoices} i ON c.id = i.customer_id
      WHERE c.is_active = 1
      GROUP BY c.id
      ORDER BY total_sales DESC
      LIMIT ?
    ''',
      [limit],
    );

    return maps.map((map) => Customer.fromMap(map)).toList();
  }

  // Toggle customer status
  Future<void> toggleCustomerStatus(int customerId) async {
    final customer = await getCustomerById(customerId);
    if (customer != null) {
      await _dbHelper.update(
        DatabaseSchema.tableCustomers,
        {
          'is_active': customer.isActive ? 0 : 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [customerId],
      );
    }
  }

  // Get customers count
  Future<int> getCustomersCount() async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      columns: ['COUNT(*) as count'],
    );
    return maps.first['count'] ?? 0;
  }

  // Get total customer sales
  Future<double> getTotalCustomerSales() async {
    final db = await _dbHelper.database;

    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT COALESCE(SUM(total_amount), 0) as total_sales
      FROM ${DatabaseSchema.tableInvoices}
      WHERE status != 'cancelled'
    ''');

    return maps.first['total_sales']?.toDouble() ?? 0.0;
  }

  // Check if customer code exists
  Future<bool> isCodeExists(String code, {int? excludeId}) async {
    String whereClause = 'code = ?';
    List<dynamic> whereArgs = [code];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableCustomers,
      where: whereClause,
      whereArgs: whereArgs,
    );
    return maps.isNotEmpty;
  }

  // Check if customer has transactions
  Future<bool> hasTransactions(int customerId) async {
    final List<Map<String, dynamic>> maps = await _dbHelper.query(
      DatabaseSchema.tableInvoices,
      where: 'customer_id = ?',
      whereArgs: [customerId],
      limit: 1,
    );
    return maps.isNotEmpty;
  }

  // Get customer balance summary
  Future<Map<String, double>> getCustomerBalanceSummary() async {
    final debitResult = await _dbHelper.rawQuery(
      'SELECT SUM(current_balance) as total FROM ${DatabaseSchema.tableCustomers} WHERE current_balance > 0 AND is_active = 1',
    );

    final creditResult = await _dbHelper.rawQuery(
      'SELECT SUM(ABS(current_balance)) as total FROM ${DatabaseSchema.tableCustomers} WHERE current_balance < 0 AND is_active = 1',
    );

    double totalDebit = 0.0;
    double totalCredit = 0.0;

    if (debitResult.isNotEmpty && debitResult.first['total'] != null) {
      totalDebit = (debitResult.first['total'] as num).toDouble();
    }

    if (creditResult.isNotEmpty && creditResult.first['total'] != null) {
      totalCredit = (creditResult.first['total'] as num).toDouble();
    }

    return {
      'total_debit': totalDebit,
      'total_credit': totalCredit,
      'net_balance': totalDebit - totalCredit,
    };
  }

  // Get customer transactions for statement
  Future<List<CustomerTransaction>> getCustomerTransactions(
    int customerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    String query =
        '''
      SELECT 
        je.date,
        je.entry_number as reference,
        je.description,
        jel.debit_amount,
        jel.credit_amount,
        'journal_entry' as transaction_type
      FROM ${DatabaseSchema.tableJournalEntries} je
      INNER JOIN ${DatabaseSchema.tableJournalEntryLines} jel ON je.id = jel.journal_entry_id
      INNER JOIN ${DatabaseSchema.tableAccounts} a ON jel.account_id = a.id
      INNER JOIN ${DatabaseSchema.tableCustomers} c ON a.id = c.account_id
      WHERE c.id = ? AND je.is_posted = 1
    ''';

    List<dynamic> args = [customerId];

    if (startDate != null) {
      query += ' AND je.date >= ?';
      args.add(startDate.toIso8601String().split('T')[0]);
    }

    if (endDate != null) {
      query += ' AND je.date <= ?';
      args.add(endDate.toIso8601String().split('T')[0]);
    }

    query += ' ORDER BY je.date ASC, je.entry_number ASC';

    final List<Map<String, dynamic>> maps = await _dbHelper.rawQuery(
      query,
      args,
    );

    List<CustomerTransaction> transactions = [];
    double runningBalance = 0.0;

    for (Map<String, dynamic> map in maps) {
      double debitAmount = (map['debit_amount'] as num?)?.toDouble() ?? 0.0;
      double creditAmount = (map['credit_amount'] as num?)?.toDouble() ?? 0.0;
      runningBalance += debitAmount - creditAmount;

      transactions.add(
        CustomerTransaction(
          date: DateTime.parse(map['date'] as String),
          reference: map['reference'] as String,
          description: map['description'] as String,
          debitAmount: debitAmount,
          creditAmount: creditAmount,
          runningBalance: runningBalance,
          transactionType: map['transaction_type'] as String,
        ),
      );
    }

    return transactions;
  }

  // Get customer summary for a period
  Future<CustomerSummary?> getCustomerSummary(
    int customerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    Customer? customer = await getCustomerById(customerId);
    if (customer == null) return null;

    List<CustomerTransaction> transactions = await getCustomerTransactions(
      customerId,
      startDate: startDate,
      endDate: endDate,
    );

    double totalDebits = 0.0;
    double totalCredits = 0.0;

    for (CustomerTransaction transaction in transactions) {
      totalDebits += transaction.debitAmount;
      totalCredits += transaction.creditAmount;
    }

    return CustomerSummary(
      customerId: customerId,
      customerCode: customer.code,
      customerName: customer.name,
      openingBalance: customer.currentBalance - (totalDebits - totalCredits),
      totalDebits: totalDebits,
      totalCredits: totalCredits,
      closingBalance: customer.currentBalance,
      transactionCount: transactions.length,
    );
  }
}
