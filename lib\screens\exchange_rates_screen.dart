import 'package:flutter/material.dart';
import '../models/exchange_rate.dart';
import '../models/currency.dart';
import '../services/exchange_rate_service.dart';
import '../services/currency_service.dart';
import '../widgets/quantum_card.dart';
import '../widgets/quantum_text_field.dart';
import '../widgets/quantum_dropdown.dart';
import 'add_edit_exchange_rate_screen.dart';

/// شاشة إدارة أسعار الصرف
class ExchangeRatesScreen extends StatefulWidget {
  const ExchangeRatesScreen({super.key});

  @override
  State<ExchangeRatesScreen> createState() => _ExchangeRatesScreenState();
}

class _ExchangeRatesScreenState extends State<ExchangeRatesScreen>
    with TickerProviderStateMixin {
  final ExchangeRateService _exchangeRateService = ExchangeRateService();
  final CurrencyService _currencyService = CurrencyService();
  final TextEditingController _searchController = TextEditingController();
  
  List<ExchangeRate> _exchangeRates = [];
  List<ExchangeRate> _filteredExchangeRates = [];
  List<Currency> _currencies = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';
  String? _selectedCurrency;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadData();
    _searchController.addListener(_filterExchangeRates);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final exchangeRates = await _exchangeRateService.getAllExchangeRates();
      final currencies = await _currencyService.getActiveCurrencies();
      
      setState(() {
        _exchangeRates = exchangeRates;
        _filteredExchangeRates = exchangeRates;
        _currencies = currencies;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: ${e.toString()}')),
        );
      }
    }
  }

  void _filterExchangeRates() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredExchangeRates = _exchangeRates.where((rate) {
        final matchesSearch = rate.fromCurrencyCode.toLowerCase().contains(query) ||
            rate.toCurrencyCode.toLowerCase().contains(query) ||
            rate.currencyPair.toLowerCase().contains(query);
        
        final matchesFilter = _selectedFilter == 'all' ||
            (_selectedFilter == 'active' && rate.isActive && rate.isEffective) ||
            (_selectedFilter == 'inactive' && !rate.isActive) ||
            (_selectedFilter == 'expired' && rate.isExpired);
        
        final matchesCurrency = _selectedCurrency == null ||
            rate.fromCurrencyCode == _selectedCurrency ||
            rate.toCurrencyCode == _selectedCurrency;
        
        return matchesSearch && matchesFilter && matchesCurrency;
      }).toList();
    });
  }

  Future<void> _toggleExchangeRateStatus(ExchangeRate rate) async {
    final result = await _exchangeRateService.toggleExchangeRateStatus(
      rate.id!,
      !rate.isActive,
    );
    
    if (result.isSuccess) {
      await _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(rate.isActive 
                ? 'تم إلغاء تفعيل سعر الصرف بنجاح'
                : 'تم تفعيل سعر الصرف بنجاح'),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!)),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أسعار الصرف'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildHeader(),
            _buildSearchAndFilter(),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildExchangeRateList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditExchangeRateScreen(),
            ),
          );
          if (result == true) {
            await _loadData();
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildHeader() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            const Icon(Icons.currency_exchange, size: 32, color: Colors.green),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'أسعار الصرف',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'إدارة أسعار صرف العملات',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Text(
              '${_filteredExchangeRates.length}',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 2,
                child: QuantumTextField(
                  controller: _searchController,
                  labelText: 'البحث في أسعار الصرف',
                  prefixIcon: Icons.search,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: QuantumDropdown<String>(
                  value: _selectedFilter,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('الكل')),
                    DropdownMenuItem(value: 'active', child: Text('نشطة')),
                    DropdownMenuItem(value: 'inactive', child: Text('غير نشطة')),
                    DropdownMenuItem(value: 'expired', child: Text('منتهية الصلاحية')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedFilter = value!);
                    _filterExchangeRates();
                  },
                  labelText: 'الحالة',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          QuantumDropdown<String>(
            value: _selectedCurrency,
            items: [
              const DropdownMenuItem(value: null, child: Text('جميع العملات')),
              ..._currencies.map((currency) => DropdownMenuItem(
                value: currency.code,
                child: Text('${currency.nameAr} (${currency.code})'),
              )),
            ],
            onChanged: (value) {
              setState(() => _selectedCurrency = value);
              _filterExchangeRates();
            },
            labelText: 'تصفية بالعملة',
          ),
        ],
      ),
    );
  }

  Widget _buildExchangeRateList() {
    if (_filteredExchangeRates.isEmpty) {
      return const Center(
        child: Text('لا توجد أسعار صرف'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _filteredExchangeRates.length,
      itemBuilder: (context, index) {
        final rate = _filteredExchangeRates[index];
        return _buildExchangeRateCard(rate);
      },
    );
  }

  Widget _buildExchangeRateCard(ExchangeRate rate) {
    final isEffective = rate.isEffective;
    final isExpired = rate.isExpired;
    
    return QuantumCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isEffective ? Colors.green : 
                          isExpired ? Colors.red : Colors.orange,
          child: const Icon(Icons.currency_exchange, color: Colors.white),
        ),
        title: Text(
          rate.currencyPair,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('السعر: ${rate.rate.toStringAsFixed(4)}'),
            Text('تاريخ السريان: ${rate.effectiveDate.toString().split(' ')[0]}'),
            if (rate.expiryDate != null)
              Text('تاريخ الانتهاء: ${rate.expiryDate!.toString().split(' ')[0]}'),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: isEffective ? Colors.green : 
                           isExpired ? Colors.red : Colors.orange,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isEffective ? 'فعال' : isExpired ? 'منتهي' : 'غير فعال',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    rate.source.displayName,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) async {
            switch (value) {
              case 'edit':
                final result = await Navigator.push<bool>(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AddEditExchangeRateScreen(exchangeRate: rate),
                  ),
                );
                if (result == true) {
                  await _loadData();
                }
                break;
              case 'toggle':
                await _toggleExchangeRateStatus(rate);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
              ),
            ),
            PopupMenuItem(
              value: 'toggle',
              child: ListTile(
                leading: Icon(rate.isActive ? Icons.visibility_off : Icons.visibility),
                title: Text(rate.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
