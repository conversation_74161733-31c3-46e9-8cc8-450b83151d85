/// نماذج بيانات الإجازات
/// Leave Data Models
library;

/// حالة طلب الإجازة
/// Leave Request Status
enum LeaveStatus {
  pending, // معلق
  approved, // موافق عليه
  rejected, // مرفوض
  cancelled, // ملغي
}

/// طلب الإجازة
/// Leave Request Model
class LeaveRequest {
  final int? id;
  final String employeeCode;
  final int leaveTypeId;
  final DateTime startDate;
  final DateTime endDate;
  final int daysCount;
  final String reason;
  final LeaveStatus status;
  final String? approvedBy;
  final String? rejectionReason;
  final DateTime createdAt;
  final DateTime updatedAt;

  LeaveRequest({
    this.id,
    required this.employeeCode,
    required this.leaveTypeId,
    required this.startDate,
    required this.endDate,
    required this.daysCount,
    required this.reason,
    this.status = LeaveStatus.pending,
    this.approvedBy,
    this.rejectionReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'leave_type_id': leaveTypeId,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate.toIso8601String().split('T')[0],
      'days_count': daysCount,
      'reason': reason,
      'status': status.name,
      'approved_by': approvedBy,
      'rejection_reason': rejectionReason,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory LeaveRequest.fromMap(Map<String, dynamic> map) {
    return LeaveRequest(
      id: map['id']?.toInt(),
      employeeCode: map['employee_code'] ?? '',
      leaveTypeId: map['leave_type_id']?.toInt() ?? 0,
      startDate: DateTime.parse(map['start_date']),
      endDate: DateTime.parse(map['end_date']),
      daysCount: map['days_count']?.toInt() ?? 0,
      reason: map['reason'] ?? '',
      status: LeaveStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => LeaveStatus.pending,
      ),
      approvedBy: map['approved_by'],
      rejectionReason: map['rejection_reason'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// نسخ مع تعديل
  LeaveRequest copyWith({
    int? id,
    String? employeeCode,
    int? leaveTypeId,
    DateTime? startDate,
    DateTime? endDate,
    int? daysCount,
    String? reason,
    LeaveStatus? status,
    String? approvedBy,
    String? rejectionReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeaveRequest(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      leaveTypeId: leaveTypeId ?? this.leaveTypeId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      daysCount: daysCount ?? this.daysCount,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      approvedBy: approvedBy ?? this.approvedBy,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'LeaveRequest(id: $id, employeeCode: $employeeCode, leaveTypeId: $leaveTypeId, startDate: $startDate, endDate: $endDate, daysCount: $daysCount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LeaveRequest &&
        other.id == id &&
        other.employeeCode == employeeCode &&
        other.leaveTypeId == leaveTypeId &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.daysCount == daysCount &&
        other.reason == reason &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        employeeCode.hashCode ^
        leaveTypeId.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        daysCount.hashCode ^
        reason.hashCode ^
        status.hashCode;
  }
}

/// نوع الإجازة
/// Leave Type Model
class LeaveType {
  final int? id;
  final String name;
  final String description;
  final int maxDaysPerYear;
  final bool isPaid;
  final bool requiresApproval;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  LeaveType({
    this.id,
    required this.name,
    required this.description,
    required this.maxDaysPerYear,
    this.isPaid = true,
    this.requiresApproval = true,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'max_days_per_year': maxDaysPerYear,
      'is_paid': isPaid ? 1 : 0,
      'requires_approval': requiresApproval ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory LeaveType.fromMap(Map<String, dynamic> map) {
    return LeaveType(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      maxDaysPerYear: map['max_days_per_year']?.toInt() ?? 0,
      isPaid: (map['is_paid'] ?? 1) == 1,
      requiresApproval: (map['requires_approval'] ?? 1) == 1,
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// نسخ مع تعديل
  LeaveType copyWith({
    int? id,
    String? name,
    String? description,
    int? maxDaysPerYear,
    bool? isPaid,
    bool? requiresApproval,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeaveType(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      maxDaysPerYear: maxDaysPerYear ?? this.maxDaysPerYear,
      isPaid: isPaid ?? this.isPaid,
      requiresApproval: requiresApproval ?? this.requiresApproval,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'LeaveType(id: $id, name: $name, maxDaysPerYear: $maxDaysPerYear, isPaid: $isPaid, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LeaveType &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.maxDaysPerYear == maxDaysPerYear &&
        other.isPaid == isPaid &&
        other.requiresApproval == requiresApproval &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        description.hashCode ^
        maxDaysPerYear.hashCode ^
        isPaid.hashCode ^
        requiresApproval.hashCode ^
        isActive.hashCode;
  }
}

/// رصيد الإجازة
/// Leave Balance Model
class LeaveBalance {
  final int? id;
  final String employeeCode;
  final int leaveTypeId;
  final int year;
  final int allocatedDays;
  final int usedDays;
  final int remainingDays;
  final DateTime createdAt;
  final DateTime updatedAt;

  LeaveBalance({
    this.id,
    required this.employeeCode,
    required this.leaveTypeId,
    required this.year,
    required this.allocatedDays,
    this.usedDays = 0,
    int? remainingDays,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : remainingDays = remainingDays ?? (allocatedDays - usedDays),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'leave_type_id': leaveTypeId,
      'year': year,
      'allocated_days': allocatedDays,
      'used_days': usedDays,
      'remaining_days': remainingDays,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory LeaveBalance.fromMap(Map<String, dynamic> map) {
    return LeaveBalance(
      id: map['id']?.toInt(),
      employeeCode: map['employee_code'] ?? '',
      leaveTypeId: map['leave_type_id']?.toInt() ?? 0,
      year: map['year']?.toInt() ?? DateTime.now().year,
      allocatedDays: map['allocated_days']?.toInt() ?? 0,
      usedDays: map['used_days']?.toInt() ?? 0,
      remainingDays: map['remaining_days']?.toInt() ?? 0,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  /// نسخ مع تعديل
  LeaveBalance copyWith({
    int? id,
    String? employeeCode,
    int? leaveTypeId,
    int? year,
    int? allocatedDays,
    int? usedDays,
    int? remainingDays,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeaveBalance(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      leaveTypeId: leaveTypeId ?? this.leaveTypeId,
      year: year ?? this.year,
      allocatedDays: allocatedDays ?? this.allocatedDays,
      usedDays: usedDays ?? this.usedDays,
      remainingDays: remainingDays ?? this.remainingDays,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'LeaveBalance(id: $id, employeeCode: $employeeCode, leaveTypeId: $leaveTypeId, year: $year, allocatedDays: $allocatedDays, usedDays: $usedDays, remainingDays: $remainingDays)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LeaveBalance &&
        other.id == id &&
        other.employeeCode == employeeCode &&
        other.leaveTypeId == leaveTypeId &&
        other.year == year &&
        other.allocatedDays == allocatedDays &&
        other.usedDays == usedDays &&
        other.remainingDays == remainingDays;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        employeeCode.hashCode ^
        leaveTypeId.hashCode ^
        year.hashCode ^
        allocatedDays.hashCode ^
        usedDays.hashCode ^
        remainingDays.hashCode;
  }
}
