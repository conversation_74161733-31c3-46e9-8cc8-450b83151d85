# 📚 توثيق API - Smart Ledger
## API Documentation

هذا المستند يوضح جميع الخدمات والواجهات البرمجية المتاحة في Smart Ledger.

## 📋 **جدول المحتويات**

- [نظرة عامة](#نظرة-عامة)
- [خدمات المحاسبة](#خدمات-المحاسبة)
- [خدمات العملاء](#خدمات-العملاء)
- [خدمات المخزون](#خدمات-المخزون)
- [خدمات التقارير](#خدمات-التقارير)
- [معالجة الأخطاء](#معالجة-الأخطاء)

## 🎯 **نظرة عامة**

جميع الخدمات في Smart Ledger تستخدم نمط **Result Pattern** لمعالجة الأخطاء بطريقة آمنة ومتسقة.

### Result Pattern

```dart
class Result<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  Result.success(this.data) : error = null, isSuccess = true;
  Result.error(this.error) : data = null, isSuccess = false;
}
```

## 💰 **خدمات المحاسبة**

### AccountService

#### إضافة حساب جديد

```dart
Future<Result<Account>> addAccount(Account account)
```

**المعاملات:**
- `account`: كائن الحساب المراد إضافته

**المثال:**
```dart
final accountService = AccountService();
final account = Account(
  code: '1001',
  name: 'النقدية',
  accountType: AccountType.asset,
  parentId: null,
);

final result = await accountService.addAccount(account);
if (result.isSuccess) {
  print('تم إضافة الحساب: ${result.data?.name}');
} else {
  print('خطأ: ${result.error}');
}
```

#### الحصول على جميع الحسابات

```dart
Future<Result<List<Account>>> getAccounts()
```

**المثال:**
```dart
final result = await accountService.getAccounts();
if (result.isSuccess) {
  for (final account in result.data!) {
    print('${account.code} - ${account.name}');
  }
}
```

#### البحث في الحسابات

```dart
Future<Result<List<Account>>> searchAccounts(String query)
```

**المعاملات:**
- `query`: نص البحث

### JournalEntryService

#### إضافة قيد محاسبي

```dart
Future<Result<JournalEntry>> addJournalEntry(JournalEntry entry)
```

**المثال:**
```dart
final journalService = JournalEntryService();
final entry = JournalEntry(
  entryNumber: 'JE001',
  entryDate: DateTime.now(),
  description: 'قيد افتتاحي',
  lines: [
    JournalEntryLine(
      accountId: '1001',
      debit: 10000,
      credit: 0,
      description: 'نقدية',
    ),
    JournalEntryLine(
      accountId: '3001',
      debit: 0,
      credit: 10000,
      description: 'رأس المال',
    ),
  ],
);

final result = await journalService.addJournalEntry(entry);
```

## 👥 **خدمات العملاء**

### CustomerService

#### إضافة عميل جديد

```dart
Future<Result<Customer>> addCustomer(Customer customer)
```

**المثال:**
```dart
final customerService = CustomerService();
final customer = Customer(
  name: 'أحمد محمد',
  email: '<EMAIL>',
  phone: '+************',
  address: 'الرياض، السعودية',
  creditLimit: 50000,
);

final result = await customerService.addCustomer(customer);
```

#### تحديث بيانات العميل

```dart
Future<Result<Customer>> updateCustomer(Customer customer)
```

#### حذف عميل

```dart
Future<Result<bool>> deleteCustomer(String customerId)
```

#### الحصول على رصيد العميل

```dart
Future<Result<double>> getCustomerBalance(String customerId)
```

**المثال:**
```dart
final result = await customerService.getCustomerBalance('customer_123');
if (result.isSuccess) {
  print('رصيد العميل: ${result.data} ريال');
}
```

## 📦 **خدمات المخزون**

### InventoryService

#### إضافة صنف جديد

```dart
Future<Result<Item>> addItem(Item item)
```

**المثال:**
```dart
final inventoryService = InventoryService();
final item = Item(
  code: 'ITEM001',
  name: 'لابتوب ديل',
  category: 'إلكترونيات',
  unitPrice: 2500,
  costingMethod: CostingMethod.fifo,
);

final result = await inventoryService.addItem(item);
```

#### حركة مخزون

```dart
Future<Result<StockMovement>> addStockMovement(StockMovement movement)
```

**المثال:**
```dart
final movement = StockMovement(
  itemId: 'item_123',
  warehouseId: 'warehouse_001',
  movementType: MovementType.purchase,
  quantity: 10,
  unitCost: 2000,
  movementDate: DateTime.now(),
  reference: 'PO-001',
);

final result = await inventoryService.addStockMovement(movement);
```

#### الحصول على رصيد المخزون

```dart
Future<Result<double>> getItemBalance(String itemId, String warehouseId)
```

## 📊 **خدمات التقارير**

### ReportService

#### ميزان المراجعة

```dart
Future<Result<TrialBalance>> getTrialBalance(DateTime fromDate, DateTime toDate)
```

**المثال:**
```dart
final reportService = ReportService();
final fromDate = DateTime(2024, 1, 1);
final toDate = DateTime(2024, 12, 31);

final result = await reportService.getTrialBalance(fromDate, toDate);
if (result.isSuccess) {
  final trialBalance = result.data!;
  print('إجمالي المدين: ${trialBalance.totalDebit}');
  print('إجمالي الدائن: ${trialBalance.totalCredit}');
}
```

#### قائمة الدخل

```dart
Future<Result<IncomeStatement>> getIncomeStatement(DateTime fromDate, DateTime toDate)
```

#### الميزانية العمومية

```dart
Future<Result<BalanceSheet>> getBalanceSheet(DateTime asOfDate)
```

#### تقرير المبيعات

```dart
Future<Result<SalesReport>> getSalesReport(DateTime fromDate, DateTime toDate)
```

**المثال:**
```dart
final result = await reportService.getSalesReport(fromDate, toDate);
if (result.isSuccess) {
  final report = result.data!;
  print('إجمالي المبيعات: ${report.totalSales}');
  print('عدد الفواتير: ${report.invoiceCount}');
}
```

## 🔧 **خدمات الأداء**

### PerformanceService

#### بدء مراقبة الأداء

```dart
void startPerformanceMonitoring()
```

#### الحصول على مقاييس الأداء

```dart
Map<String, List<double>> getPerformanceMetrics()
```

**المثال:**
```dart
final performanceService = PerformanceService();
performanceService.startPerformanceMonitoring();

// بعد فترة من الوقت
final metrics = performanceService.getPerformanceMetrics();
print('استخدام الذاكرة: ${metrics['memory_usage_mb']?.last} MB');
print('وقت استجابة قاعدة البيانات: ${metrics['db_response_time_ms']?.last} ms');
```

## 🔒 **خدمات الأمان**

### SecurityService

#### تشفير البيانات

```dart
String encryptData(String data)
```

#### فك تشفير البيانات

```dart
String decryptData(String encryptedData)
```

#### إنشاء نسخة احتياطية

```dart
Future<Result<String>> createBackup()
```

**المثال:**
```dart
final securityService = SecurityService();
final result = await securityService.createBackup();
if (result.isSuccess) {
  print('تم إنشاء النسخة الاحتياطية: ${result.data}');
}
```

## ❌ **معالجة الأخطاء**

### أنواع الأخطاء

#### ValidationError
```dart
class ValidationError extends Exception {
  final String message;
  final String field;
  
  ValidationError(this.message, this.field);
}
```

#### DatabaseError
```dart
class DatabaseError extends Exception {
  final String message;
  final String? query;
  
  DatabaseError(this.message, [this.query]);
}
```

#### BusinessLogicError
```dart
class BusinessLogicError extends Exception {
  final String message;
  final String operation;
  
  BusinessLogicError(this.message, this.operation);
}
```

### مثال على معالجة الأخطاء

```dart
try {
  final result = await customerService.addCustomer(customer);
  
  if (result.isSuccess) {
    // نجحت العملية
    showSuccessMessage('تم إضافة العميل بنجاح');
  } else {
    // فشلت العملية
    showErrorMessage(result.error!);
  }
} on ValidationError catch (e) {
  showFieldError(e.field, e.message);
} on DatabaseError catch (e) {
  showErrorMessage('خطأ في قاعدة البيانات: ${e.message}');
} catch (e) {
  showErrorMessage('خطأ غير متوقع: $e');
}
```

## 🔄 **أنماط الاستخدام الشائعة**

### إضافة فاتورة مبيعات كاملة

```dart
Future<void> createSalesInvoice() async {
  // 1. إنشاء الفاتورة
  final invoice = Invoice(
    invoiceNumber: 'INV-001',
    customerId: 'customer_123',
    invoiceDate: DateTime.now(),
    items: [
      InvoiceItem(
        itemId: 'item_456',
        quantity: 2,
        unitPrice: 1000,
      ),
    ],
  );

  // 2. حفظ الفاتورة
  final invoiceResult = await invoiceService.addInvoice(invoice);
  if (!invoiceResult.isSuccess) {
    throw Exception(invoiceResult.error);
  }

  // 3. تحديث المخزون
  for (final item in invoice.items) {
    final movement = StockMovement(
      itemId: item.itemId,
      movementType: MovementType.sale,
      quantity: -item.quantity,
      reference: invoice.invoiceNumber,
    );
    
    await inventoryService.addStockMovement(movement);
  }

  // 4. إنشاء القيد المحاسبي
  final journalEntry = JournalEntry(
    entryNumber: 'JE-${invoice.invoiceNumber}',
    description: 'فاتورة مبيعات ${invoice.invoiceNumber}',
    lines: [
      JournalEntryLine(
        accountId: '1201', // العملاء
        debit: invoice.totalAmount,
        credit: 0,
      ),
      JournalEntryLine(
        accountId: '4001', // المبيعات
        debit: 0,
        credit: invoice.totalAmount,
      ),
    ],
  );

  await journalEntryService.addJournalEntry(journalEntry);
}
```

## 📞 **الدعم والمساعدة**

للحصول على مساعدة إضافية:

- 📧 **البريد الإلكتروني**: <EMAIL>
- 📚 **الوثائق**: [رابط الوثائق]
- 🐛 **الأخطاء**: [GitHub Issues]

---

**ملاحظة**: هذا التوثيق يتطور مع تطور التطبيق. تأكد من مراجعة أحدث إصدار.
