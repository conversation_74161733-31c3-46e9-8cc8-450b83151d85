/// Data Access Object للمخازن
/// Warehouse DAO for Smart Ledger
library;

import 'dart:developer' as developer;
import '../models/warehouse.dart';
import 'database_Helper.dart';

class WarehouseDao {
  final DatabaseHelper _databaseService = DatabaseHelper();

  /// الحصول على جميع المخازن
  Future<List<Warehouse>> getAllWarehouses() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Warehouse.fromMap(maps[i]);
    });
  }

  /// الحصول على المخازن النشطة فقط
  Future<List<Warehouse>> getActiveWarehouses() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: 'status = ?',
      whereArgs: ['active'],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Warehouse.fromMap(maps[i]);
    });
  }

  /// الحصول على مخزن بالمعرف
  Future<Warehouse?> getWarehouseById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Warehouse.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على مخزن بالكود
  Future<Warehouse?> getWarehouseByCode(String code) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Warehouse.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة مخزن جديد
  Future<int> insertWarehouse(Warehouse warehouse) async {
    final db = await _databaseService.database;
    return await db.insert('warehouses', warehouse.toMap());
  }

  /// تحديث مخزن
  Future<int> updateWarehouse(Warehouse warehouse) async {
    final db = await _databaseService.database;
    return await db.update(
      'warehouses',
      warehouse.toMap(),
      where: 'id = ?',
      whereArgs: [warehouse.id],
    );
  }

  /// حذف مخزن
  Future<int> deleteWarehouse(int id) async {
    final db = await _databaseService.database;
    return await db.delete('warehouses', where: 'id = ?', whereArgs: [id]);
  }

  /// البحث في المخازن
  Future<List<Warehouse>> searchWarehouses(String searchTerm) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: 'code LIKE ? OR name LIKE ? OR description LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Warehouse.fromMap(maps[i]);
    });
  }

  /// الحصول على المخازن الرئيسية
  Future<List<Warehouse>> getMainWarehouses() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: 'is_main_warehouse = ? AND status = ?',
      whereArgs: [1, 'active'],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Warehouse.fromMap(maps[i]);
    });
  }

  /// الحصول على المخازن الفرعية لمخزن معين
  Future<List<Warehouse>> getSubWarehouses(int parentWarehouseId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: 'parent_warehouse_id = ?',
      whereArgs: [parentWarehouseId],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Warehouse.fromMap(maps[i]);
    });
  }

  /// الحصول على إحصائيات المخازن
  Future<Map<String, dynamic>> getWarehouseStatistics() async {
    final db = await _databaseService.database;

    // إجمالي عدد المخازن
    final totalResult = await db.rawQuery(
      'SELECT COUNT(*) as total FROM warehouses',
    );
    final total = totalResult.first['total'] as int;

    // المخازن النشطة
    final activeResult = await db.rawQuery(
      'SELECT COUNT(*) as active FROM warehouses WHERE status = ?',
      ['active'],
    );
    final active = activeResult.first['active'] as int;

    // المخازن غير النشطة
    final inactiveResult = await db.rawQuery(
      'SELECT COUNT(*) as inactive FROM warehouses WHERE status != ?',
      ['active'],
    );
    final inactive = inactiveResult.first['inactive'] as int;

    // المخازن الرئيسية
    final mainResult = await db.rawQuery(
      'SELECT COUNT(*) as main FROM warehouses WHERE is_main_warehouse = ?',
      [1],
    );
    final main = mainResult.first['main'] as int;

    // إحصائيات حسب النوع
    final typeStats = await db.rawQuery('''
      SELECT type, COUNT(*) as count 
      FROM warehouses 
      GROUP BY type
      ORDER BY count DESC
    ''');

    return {
      'total': total,
      'active': active,
      'inactive': inactive,
      'main': main,
      'by_type': typeStats,
    };
  }

  /// التحقق من وجود كود مخزن
  Future<bool> isWarehouseCodeExists(String code, {int? excludeId}) async {
    final db = await _databaseService.database;
    String whereClause = 'code = ?';
    List<dynamic> whereArgs = [code];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isNotEmpty;
  }

  /// الحصول على المخازن مع عدد المواقع
  Future<List<Map<String, dynamic>>> getWarehousesWithLocationCount() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT w.*, 
             COALESCE(l.location_count, 0) as location_count
      FROM warehouses w
      LEFT JOIN (
        SELECT warehouse_id, COUNT(*) as location_count
        FROM warehouse_locations
        WHERE is_active = 1
        GROUP BY warehouse_id
      ) l ON w.id = l.warehouse_id
      ORDER BY w.code ASC
    ''');

    return maps;
  }

  /// الحصول على المخازن مع إجمالي قيمة المخزون
  Future<List<Map<String, dynamic>>> getWarehousesWithStockValue() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT w.*, 
             COALESCE(s.total_value, 0) as stock_value,
             COALESCE(s.item_count, 0) as item_count
      FROM warehouses w
      LEFT JOIN (
        SELECT warehouse_id, 
               SUM(total_value) as total_value,
               COUNT(DISTINCT item_id) as item_count
        FROM stock_balances
        WHERE quantity > 0
        GROUP BY warehouse_id
      ) s ON w.id = s.warehouse_id
      ORDER BY w.code ASC
    ''');

    return maps;
  }

  /// تحديث حالة مخزن
  Future<int> updateWarehouseStatus(int id, WarehouseStatus status) async {
    final db = await _databaseService.database;
    return await db.update(
      'warehouses',
      {'status': status.name, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// تحديث مدير المخزن
  Future<int> updateWarehouseManager(int id, String managerName) async {
    final db = await _databaseService.database;
    return await db.update(
      'warehouses',
      {
        'manager_name': managerName,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على المخازن حسب المدينة
  Future<List<Warehouse>> getWarehousesByCity(String city) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'warehouses',
      where: 'city = ?',
      whereArgs: [city],
      orderBy: 'code ASC',
    );

    return List.generate(maps.length, (i) {
      return Warehouse.fromMap(maps[i]);
    });
  }

  /// الحصول على جميع المدن
  Future<List<String>> getAllCities() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT city 
      FROM warehouses 
      WHERE city IS NOT NULL AND city != ''
      ORDER BY city ASC
    ''');

    return maps.map((map) => map['city'] as String).toList();
  }

  /// نسخ احتياطي للمخازن
  Future<List<Map<String, dynamic>>> exportWarehouses() async {
    final db = await _databaseService.database;
    return await db.query('warehouses', orderBy: 'code ASC');
  }

  /// استيراد المخازن
  Future<int> importWarehouses(List<Map<String, dynamic>> warehouses) async {
    final db = await _databaseService.database;
    int importedCount = 0;

    await db.transaction((txn) async {
      for (final warehouseData in warehouses) {
        try {
          await txn.insert('warehouses', warehouseData);
          importedCount++;
        } catch (e) {
          // تجاهل الأخطاء والمتابعة
          developer.log('Error importing warehouse: $e', name: 'WarehouseDao');
        }
      }
    });

    return importedCount;
  }
}
