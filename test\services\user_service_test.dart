import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smart_ledger/models/user.dart';
import 'package:smart_ledger/services/user_service.dart';

void main() {
  group('UserService Tests', () {
    late UserService userService;

    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
      userService = UserService.instance;
      await userService.initialize();
    });

    test('should initialize with no user configured', () async {
      final isConfigured = await userService.isUserConfigured();
      expect(isConfigured, false);
    });

    test(
      'should return default user name when no user is configured',
      () async {
        final userName = await userService.getCurrentUserName();
        expect(userName, 'المستخدم الافتراضي');
      },
    );

    test('should set and get current user', () async {
      final user = User(
        name: 'أحمد محمد',
        email: '<EMAIL>',
        role: 'محاسب أول',
      );

      final setResult = await userService.setCurrentUser(user);
      expect(setResult.isSuccess, true);

      final isConfigured = await userService.isUserConfigured();
      expect(isConfigured, true);

      final getUserResult = await userService.getCurrentUser();
      expect(getUserResult.isSuccess, true);
      expect(getUserResult.data?.name, 'أحمد محمد');
      expect(getUserResult.data?.email, '<EMAIL>');
      expect(getUserResult.data?.role, 'محاسب أول');
    });

    test('should return user name when user is configured', () async {
      final user = User(
        name: 'سارة أحمد',
        email: '<EMAIL>',
        role: 'مدير مالي',
      );

      await userService.setCurrentUser(user);
      final userName = await userService.getCurrentUserName();
      expect(userName, 'سارة أحمد');
    });

    test('should handle user without email', () async {
      final user = User(name: 'محمد علي', role: 'محاسب');

      final setResult = await userService.setCurrentUser(user);
      expect(setResult.isSuccess, true);

      final getUserResult = await userService.getCurrentUser();
      expect(getUserResult.isSuccess, true);
      expect(getUserResult.data?.name, 'محمد علي');
      expect(getUserResult.data?.email, null);
      expect(getUserResult.data?.role, 'محاسب');
    });

    test('should provide available roles', () {
      final roles = userService.getAvailableRoles();
      expect(roles, isNotEmpty);
      expect(roles, contains('محاسب'));
      expect(roles, contains('محاسب أول'));
      expect(roles, contains('مدير مالي'));
      expect(roles, contains('مدير عام'));
      expect(roles, contains('مراجع'));
      expect(roles, contains('مساعد محاسب'));
    });

    test('should validate user data', () {
      // Valid user
      final validUser = User(
        name: 'أحمد محمد',
        email: '<EMAIL>',
        role: 'محاسب',
      );
      expect(validUser.validate(), isEmpty);

      // Invalid user - empty name
      final invalidUser1 = User(
        name: '',
        email: '<EMAIL>',
        role: 'محاسب',
      );
      expect(invalidUser1.validate(), isNotEmpty);

      // Invalid user - invalid email
      final invalidUser2 = User(
        name: 'أحمد محمد',
        email: 'invalid-email',
        role: 'محاسب',
      );
      expect(invalidUser2.validate(), isNotEmpty);

      // Valid user - no email
      final validUser2 = User(name: 'سارة أحمد', role: 'محاسب');
      expect(validUser2.validate(), isEmpty);
    });

    test('should handle persistence errors gracefully', () async {
      // This test would require mocking SharedPreferences to throw errors
      // For now, we'll just verify the service handles normal operations
      final user = User(name: 'Test User', role: 'محاسب');
      final result = await userService.setCurrentUser(user);
      expect(result.isSuccess, true);
    });
  });
}
