import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🔔 نظام الإشعارات الذكية المستقبلي
/// Futuristic Smart Notifications System
///
/// هذا الملف يحتوي على نظام إشعارات ذكي لا مثيل له في التاريخ
/// This file contains unprecedented smart notifications system in history

/// 🌟 إشعار ذكي مع تأثيرات هولوجرافية
/// Smart Notification with Holographic Effects
class SmartNotification extends StatefulWidget {
  final String title;
  final String message;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;
  final Duration duration;

  const SmartNotification({
    super.key,
    required this.title,
    required this.message,
    required this.icon,
    this.primaryColor = const Color(0xFF2196F3),
    this.secondaryColor = const Color(0xFF21CBF3),
    this.onTap,
    this.onDismiss,
    this.duration = const Duration(seconds: 4),
  });

  @override
  State<SmartNotification> createState() => _SmartNotificationState();
}

class _SmartNotificationState extends State<SmartNotification>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late AnimationController _dismissController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _dismissAnimation;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _dismissController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(1.0, 0.0), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
        );

    _pulseAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _dismissAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _dismissController, curve: Curves.easeInOut),
    );

    // بدء الرسوم المتحركة
    _slideController.forward();
    _pulseController.repeat(reverse: true);

    // إخفاء تلقائي بعد المدة المحددة
    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _pulseController.dispose();
    _dismissController.dispose();
    super.dispose();
  }

  void _dismiss() {
    _dismissController.forward().then((_) {
      if (widget.onDismiss != null) {
        widget.onDismiss!();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _slideAnimation,
        _pulseAnimation,
        _dismissAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _dismissAnimation.value,
          child: SlideTransition(
            position: _slideAnimation,
            child: GestureDetector(
              onTap: widget.onTap,
              onHorizontalDragEnd: (details) {
                if (details.primaryVelocity! > 300) {
                  _dismiss();
                }
              },
              child: HologramEffect(
                intensity: 1.5,
                child: QuantumEnergyEffect(
                  intensity: 1.0 + (_pulseAnimation.value * 0.5),
                  primaryColor: widget.primaryColor,
                  secondaryColor: widget.secondaryColor,
                  child: Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingMedium,
                      vertical: AppTheme.spacingSmall,
                    ),
                    padding: const EdgeInsets.all(AppTheme.spacingLarge),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          widget.primaryColor.withValues(alpha: 0.9),
                          widget.secondaryColor.withValues(alpha: 0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: widget.primaryColor.withValues(alpha: 0.4),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // أيقونة مع تأثير النيون
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.4),
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            widget.icon,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),

                        const SizedBox(width: AppTheme.spacingMedium),

                        // المحتوى
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                widget.title,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 0.5,
                                    ),
                              ),
                              const SizedBox(height: AppTheme.spacingXSmall),
                              Text(
                                widget.message,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                      height: 1.4,
                                    ),
                              ),
                            ],
                          ),
                        ),

                        // زر الإغلاق
                        GestureDetector(
                          onTap: _dismiss,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Icon(
                              Icons.close_rounded,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 🎯 مدير الإشعارات الذكية
/// Smart Notifications Manager
class SmartNotificationManager {
  static final List<SmartNotification> _notifications = [];
  static OverlayEntry? _overlayEntry;

  /// عرض إشعار ذكي
  /// Show smart notification
  static void show(
    BuildContext context, {
    required String title,
    required String message,
    required IconData icon,
    Color primaryColor = const Color(0xFF2196F3),
    Color secondaryColor = const Color(0xFF21CBF3),
    VoidCallback? onTap,
    Duration duration = const Duration(seconds: 4),
  }) {
    late SmartNotification notification;

    notification = SmartNotification(
      title: title,
      message: message,
      icon: icon,
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      onTap: onTap,
      duration: duration,
      onDismiss: () {
        _removeNotification(notification);
      },
    );

    _notifications.add(notification);
    _updateOverlay(context);
  }

  /// إزالة إشعار
  /// Remove notification
  static void _removeNotification(SmartNotification notification) {
    _notifications.remove(notification);
    if (_notifications.isEmpty && _overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    } else if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  /// تحديث طبقة الإشعارات
  /// Update notifications overlay
  static void _updateOverlay(BuildContext context) {
    if (_overlayEntry == null) {
      _overlayEntry = OverlayEntry(
        builder: (context) => _buildNotificationsStack(),
      );
      Overlay.of(context).insert(_overlayEntry!);
    } else {
      _overlayEntry!.markNeedsBuild();
    }
  }

  /// بناء مكدس الإشعارات
  /// Build notifications stack
  static Widget _buildNotificationsStack() {
    return Positioned(
      top: 100,
      right: 0,
      left: 0,
      child: Column(
        children: _notifications.map((notification) => notification).toList(),
      ),
    );
  }

  /// إشعار نجاح
  /// Success notification
  static void showSuccess(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onTap,
  }) {
    show(
      context,
      title: title,
      message: message,
      icon: Icons.check_circle_rounded,
      primaryColor: const Color(0xFF4CAF50),
      secondaryColor: const Color(0xFF8BC34A),
      onTap: onTap,
    );
  }

  /// إشعار خطأ
  /// Error notification
  static void showError(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onTap,
  }) {
    show(
      context,
      title: title,
      message: message,
      icon: Icons.error_rounded,
      primaryColor: const Color(0xFFF44336),
      secondaryColor: const Color(0xFFFF5722),
      onTap: onTap,
    );
  }

  /// إشعار تحذير
  /// Warning notification
  static void showWarning(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onTap,
  }) {
    show(
      context,
      title: title,
      message: message,
      icon: Icons.warning_rounded,
      primaryColor: const Color(0xFFFF9800),
      secondaryColor: const Color(0xFFFFC107),
      onTap: onTap,
    );
  }

  /// إشعار معلومات
  /// Info notification
  static void showInfo(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onTap,
  }) {
    show(
      context,
      title: title,
      message: message,
      icon: Icons.info_rounded,
      primaryColor: const Color(0xFF2196F3),
      secondaryColor: const Color(0xFF03A9F4),
      onTap: onTap,
    );
  }
}
