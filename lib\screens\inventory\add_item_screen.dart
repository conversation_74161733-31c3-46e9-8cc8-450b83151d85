import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/item.dart';
import '../../services/item_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_inputs.dart';

class AddItemScreen extends StatefulWidget {
  final Item? item;

  const AddItemScreen({super.key, this.item});

  @override
  State<AddItemScreen> createState() => _AddItemScreenState();
}

class _AddItemScreenState extends State<AddItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final ItemService _itemService = ItemService();

  // Controllers
  final _codeController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _unitController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _currentStockController = TextEditingController();
  final _minStockController = TextEditingController();
  final _maxStockController = TextEditingController();
  final _categoryController = TextEditingController();
  final _barcodeController = TextEditingController();

  bool _isActive = true;
  bool _isLoading = false;
  List<String> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
    if (widget.item != null) {
      _populateFields();
    } else {
      _generateItemCode();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _unitController.dispose();
    _costPriceController.dispose();
    _sellingPriceController.dispose();
    _currentStockController.dispose();
    _minStockController.dispose();
    _maxStockController.dispose();
    _categoryController.dispose();
    _barcodeController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    final result = await _itemService.getItemCategories();
    if (result.isSuccess && result.data != null) {
      setState(() {
        _categories = result.data!;
      });
    }
  }

  void _populateFields() {
    final item = widget.item!;
    _codeController.text = item.code;
    _nameController.text = item.name;
    _descriptionController.text = item.description ?? '';
    _unitController.text = item.unit;
    _costPriceController.text = item.costPrice.toString();
    _sellingPriceController.text = item.sellingPrice.toString();
    _currentStockController.text = item.currentStock.toString();
    _minStockController.text = item.minStockLevel?.toString() ?? '';
    _maxStockController.text = item.maxStockLevel?.toString() ?? '';
    _categoryController.text = item.category ?? '';
    _barcodeController.text = item.barcode ?? '';
    _isActive = item.isActive;
  }

  Future<void> _generateItemCode() async {
    final result = await _itemService.getNextItemCode();
    if (result.isSuccess && result.data != null) {
      _codeController.text = result.data!;
    }
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final item = Item(
        id: widget.item?.id,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        unit: _unitController.text.trim(),
        costPrice: double.tryParse(_costPriceController.text) ?? 0.0,
        sellingPrice: double.tryParse(_sellingPriceController.text) ?? 0.0,
        currentStock: double.tryParse(_currentStockController.text) ?? 0.0,
        minStockLevel: _minStockController.text.trim().isEmpty
            ? null
            : double.tryParse(_minStockController.text),
        maxStockLevel: _maxStockController.text.trim().isEmpty
            ? null
            : double.tryParse(_maxStockController.text),
        category: _categoryController.text.trim().isEmpty
            ? null
            : _categoryController.text.trim(),
        barcode: _barcodeController.text.trim().isEmpty
            ? null
            : _barcodeController.text.trim(),
        isActive: _isActive,
        createdAt: widget.item?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = widget.item == null
          ? await _itemService.createItem(item)
          : await _itemService.updateItem(item);

      if (mounted) {
        if (result.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.item == null
                  ? 'تم إنشاء الصنف بنجاح'
                  : 'تم تحديث الصنف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'حدث خطأ'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(widget.item == null ? 'إضافة صنف جديد' : 'تعديل الصنف'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(AppTheme.spacingMedium),
          children: [
            // معلومات أساسية
            _buildSectionCard(
              'المعلومات الأساسية',
              Icons.info_outline,
              [
                Row(
                  children: [
                    Expanded(
                      child: BeautifulTextFormField(
                        controller: _codeController,
                        labelText: 'رمز الصنف *',
                        prefixIcon: Icons.qr_code,
                        validator: (value) {
                          if (value?.trim().isEmpty ?? true) {
                            return 'رمز الصنف مطلوب';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Expanded(
                      child: BeautifulTextFormField(
                        controller: _barcodeController,
                        labelText: 'الباركود',
                        prefixIcon: Icons.barcode_reader,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                BeautifulTextFormField(
                  controller: _nameController,
                  labelText: 'اسم الصنف *',
                  prefixIcon: Icons.inventory_2,
                  validator: (value) {
                    if (value?.trim().isEmpty ?? true) {
                      return 'اسم الصنف مطلوب';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                BeautifulTextFormField(
                  controller: _descriptionController,
                  labelText: 'الوصف',
                  prefixIcon: Icons.description,
                  maxLines: 3,
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Row(
                  children: [
                    Expanded(
                      child: BeautifulTextFormField(
                        controller: _unitController,
                        labelText: 'وحدة القياس *',
                        prefixIcon: Icons.straighten,
                        validator: (value) {
                          if (value?.trim().isEmpty ?? true) {
                            return 'وحدة القياس مطلوبة';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _categoryController.text.isEmpty ? null : _categoryController.text,
                        decoration: const InputDecoration(
                          labelText: 'الفئة',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                        ),
                        items: _categories.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                        onChanged: (value) {
                          _categoryController.text = value ?? '';
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingLarge),

            // الأسعار
            _buildSectionCard(
              'الأسعار',
              Icons.attach_money,
              [
                Row(
                  children: [
                    Expanded(
                      child: BeautifulTextFormField(
                        controller: _costPriceController,
                        labelText: 'سعر التكلفة *',
                        prefixIcon: Icons.money_off,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value?.trim().isEmpty ?? true) {
                            return 'سعر التكلفة مطلوب';
                          }
                          final price = double.tryParse(value!);
                          if (price == null || price < 0) {
                            return 'سعر التكلفة غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Expanded(
                      child: BeautifulTextFormField(
                        controller: _sellingPriceController,
                        labelText: 'سعر البيع *',
                        prefixIcon: Icons.monetization_on,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value?.trim().isEmpty ?? true) {
                            return 'سعر البيع مطلوب';
                          }
                          final price = double.tryParse(value!);
                          if (price == null || price < 0) {
                            return 'سعر البيع غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingLarge),

            // المخزون
            _buildSectionCard(
              'إدارة المخزون',
              Icons.warehouse,
              [
                BeautifulTextFormField(
                  controller: _currentStockController,
                  labelText: 'المخزون الحالي *',
                  prefixIcon: Icons.inventory,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value?.trim().isEmpty ?? true) {
                      return 'المخزون الحالي مطلوب';
                    }
                    final stock = double.tryParse(value!);
                    if (stock == null || stock < 0) {
                      return 'المخزون الحالي غير صحيح';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppTheme.spacingMedium),
                Row(
                  children: [
                    Expanded(
                      child: BeautifulTextFormField(
                        controller: _minStockController,
                        labelText: 'الحد الأدنى',
                        prefixIcon: Icons.trending_down,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingMedium),
                    Expanded(
                      child: BeautifulTextFormField(
                        controller: _maxStockController,
                        labelText: 'الحد الأقصى',
                        prefixIcon: Icons.trending_up,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingLarge),

            // حالة الصنف
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMedium),
                child: Row(
                  children: [
                    const Icon(Icons.toggle_on, color: AppTheme.primaryColor),
                    const SizedBox(width: AppTheme.spacingMedium),
                    const Text(
                      'حالة الصنف',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Switch(
                      value: _isActive,
                      onChanged: (value) {
                        setState(() => _isActive = value);
                      },
                      activeColor: AppTheme.primaryColor,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppTheme.spacingXLarge),

            // أزرار الحفظ والإلغاء
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveItem,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(widget.item == null ? 'إنشاء' : 'تحديث'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor),
                const SizedBox(width: AppTheme.spacingSmall),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            ...children,
          ],
        ),
      ),
    );
  }
}
