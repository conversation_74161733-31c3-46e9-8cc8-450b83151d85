/// قائمة منسدلة كمية متقدمة لتطبيق Smart Ledger
/// Advanced Quantum Dropdown for Smart Ledger Application
library;

import 'package:flutter/material.dart';

/// قائمة منسدلة كمية مع تأثيرات بصرية متقدمة
/// Quantum dropdown with advanced visual effects
class QuantumDropdown<T> extends StatefulWidget {
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool enabled;
  final EdgeInsetsGeometry? contentPadding;
  final double borderRadius;
  final bool enableGlowEffect;
  final bool enableFocusEffect;
  final String? Function(T?)? validator;

  const QuantumDropdown({
    super.key,
    this.value,
    required this.items,
    this.onChanged,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.enabled = true,
    this.contentPadding,
    this.borderRadius = 12.0,
    this.enableGlowEffect = true,
    this.enableFocusEffect = true,
    this.validator,
  });

  @override
  State<QuantumDropdown<T>> createState() => _QuantumDropdownState<T>();
}

class _QuantumDropdownState<T> extends State<QuantumDropdown<T>>
    with TickerProviderStateMixin {
  late AnimationController _focusController;
  late AnimationController _glowController;
  late Animation<double> _focusAnimation;
  late Animation<double> _glowAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  void _initializeAnimations() {
    _focusController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _focusAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _focusController, curve: Curves.easeInOut),
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    if (widget.enableGlowEffect) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _focusController.dispose();
    _glowController.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() => _isFocused = _focusNode.hasFocus);

    if (widget.enableFocusEffect) {
      if (_isFocused) {
        _focusController.forward();
      } else {
        _focusController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedBuilder(
      animation: Listenable.merge([_focusAnimation, _glowAnimation]),
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            boxShadow: [
              if (widget.enableGlowEffect && widget.enabled)
                BoxShadow(
                  color: Colors.cyan.withValues(
                    alpha: _glowAnimation.value * 0.2,
                  ),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              if (_isFocused && widget.enableFocusEffect)
                BoxShadow(
                  color: colorScheme.primary.withValues(
                    alpha: _focusAnimation.value * 0.3,
                  ),
                  blurRadius: 15,
                  offset: const Offset(0, 0),
                ),
            ],
          ),
          child: DropdownButtonFormField<T>(
            value: widget.value,
            items: widget.items,
            onChanged: widget.enabled ? widget.onChanged : null,
            focusNode: _focusNode,
            validator: widget.validator,
            decoration: InputDecoration(
              labelText: widget.labelText,
              hintText: widget.hintText,
              helperText: widget.helperText,
              errorText: widget.errorText,
              prefixIcon: widget.prefixIcon != null
                  ? Icon(
                      widget.prefixIcon,
                      color: _isFocused
                          ? colorScheme.primary
                          : colorScheme.onSurface.withValues(alpha: 0.6),
                    )
                  : null,
              suffixIcon: widget.suffixIcon,
              contentPadding:
                  widget.contentPadding ??
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              filled: true,
              fillColor: colorScheme.surface.withValues(alpha: 0.1),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(color: colorScheme.primary, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(color: colorScheme.error, width: 2),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(color: colorScheme.error, width: 2),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                borderSide: BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              labelStyle: TextStyle(
                color: _isFocused
                    ? colorScheme.primary
                    : colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 16,
              ),
              hintStyle: TextStyle(
                color: colorScheme.onSurface.withValues(alpha: 0.4),
                fontSize: 16,
              ),
              helperStyle: TextStyle(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 12,
              ),
              errorStyle: TextStyle(color: colorScheme.error, fontSize: 12),
            ),
            style: TextStyle(
              color: widget.enabled
                  ? colorScheme.onSurface
                  : colorScheme.onSurface.withValues(alpha: 0.6),
              fontSize: 16,
            ),
            dropdownColor: colorScheme.surface,
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: _isFocused
                  ? colorScheme.primary
                  : colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            isExpanded: true,
          ),
        );
      },
    );
  }
}
