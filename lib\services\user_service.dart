import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../utils/result.dart';

/// خدمة إدارة المستخدمين
/// User Management Service
class UserService {
  static const String _currentUserKey = 'current_user_name';
  static const String _currentUserEmailKey = 'current_user_email';
  static const String _currentUserRoleKey = 'current_user_role';
  static const String _isUserSetKey = 'is_user_set';

  static UserService? _instance;
  User? _currentUser;

  UserService._internal();

  /// Singleton instance
  static UserService get instance {
    _instance ??= UserService._internal();
    return _instance!;
  }

  /// Initialize user service and load current user
  Future<void> initialize() async {
    await _loadCurrentUser();
  }

  /// Get current user
  Future<Result<User>> getCurrentUser() async {
    try {
      if (_currentUser != null) {
        return Result.success(_currentUser!);
      }

      await _loadCurrentUser();
      
      if (_currentUser != null) {
        return Result.success(_currentUser!);
      }

      // If no user is set, return default user
      return Result.success(_getDefaultUser());
    } catch (e) {
      return Result.error('خطأ في جلب المستخدم الحالي: ${e.toString()}');
    }
  }

  /// Set current user
  Future<Result<void>> setCurrentUser(User user) async {
    try {
      // Validate user data
      List<String> errors = user.validate();
      if (errors.isNotEmpty) {
        return Result.error(errors.join(', '));
      }

      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString(_currentUserKey, user.name);
      await prefs.setString(_currentUserEmailKey, user.email ?? '');
      await prefs.setString(_currentUserRoleKey, user.role ?? 'محاسب');
      await prefs.setBool(_isUserSetKey, true);

      _currentUser = user;
      
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حفظ بيانات المستخدم: ${e.toString()}');
    }
  }

  /// Check if user is configured
  Future<bool> isUserConfigured() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isUserSetKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get current user name for journal entries
  Future<String> getCurrentUserName() async {
    try {
      final userResult = await getCurrentUser();
      if (userResult.isSuccess) {
        return userResult.data!.displayName;
      }
      return 'المستخدم الافتراضي';
    } catch (e) {
      return 'المستخدم الافتراضي';
    }
  }

  /// Update current user information
  Future<Result<void>> updateCurrentUser({
    String? name,
    String? email,
    String? role,
  }) async {
    try {
      final currentUserResult = await getCurrentUser();
      if (!currentUserResult.isSuccess) {
        return Result.error('لا يوجد مستخدم حالي');
      }

      final updatedUser = currentUserResult.data!.copyWith(
        name: name,
        email: email,
        role: role,
        updatedAt: DateTime.now(),
      );

      return await setCurrentUser(updatedUser);
    } catch (e) {
      return Result.error('خطأ في تحديث بيانات المستخدم: ${e.toString()}');
    }
  }

  /// Clear current user (logout)
  Future<Result<void>> clearCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.remove(_currentUserKey);
      await prefs.remove(_currentUserEmailKey);
      await prefs.remove(_currentUserRoleKey);
      await prefs.setBool(_isUserSetKey, false);

      _currentUser = null;
      
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في مسح بيانات المستخدم: ${e.toString()}');
    }
  }

  /// Load current user from shared preferences
  Future<void> _loadCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final isUserSet = prefs.getBool(_isUserSetKey) ?? false;
      if (!isUserSet) {
        _currentUser = null;
        return;
      }

      final name = prefs.getString(_currentUserKey);
      final email = prefs.getString(_currentUserEmailKey);
      final role = prefs.getString(_currentUserRoleKey);

      if (name != null && name.isNotEmpty) {
        _currentUser = User(
          name: name,
          email: email?.isEmpty == true ? null : email,
          role: role?.isEmpty == true ? 'محاسب' : role,
        );
      }
    } catch (e) {
      _currentUser = null;
    }
  }

  /// Get default user when no user is configured
  User _getDefaultUser() {
    return User(
      name: 'المستخدم الافتراضي',
      role: 'محاسب',
    );
  }

  /// Get available user roles
  List<String> getAvailableRoles() {
    return [
      'محاسب',
      'محاسب أول',
      'مدير مالي',
      'مدير عام',
      'مراجع',
      'مساعد محاسب',
    ];
  }
}
