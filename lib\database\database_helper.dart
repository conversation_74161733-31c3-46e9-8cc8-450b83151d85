import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'database_schema.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), DatabaseSchema.databaseName);

    return await openDatabase(
      path,
      version: DatabaseSchema.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create all tables
    for (String script in DatabaseSchema.createTableScripts) {
      await db.execute(script);
    }

    // Create indexes
    for (String index in DatabaseSchema.createIndexes) {
      await db.execute(index);
    }

    // Insert default data
    await _insertDefaultData(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add description column to accounts table
      await db.execute(
        'ALTER TABLE ${DatabaseSchema.tableAccounts} ADD COLUMN description TEXT',
      );
    }

    // For major changes, recreate the database
    if (oldVersion < newVersion && newVersion > 2) {
      await _dropAllTables(db);
      await _onCreate(db, newVersion);
    }
  }

  Future<void> _dropAllTables(Database db) async {
    await db.execute(
      'DROP TABLE IF EXISTS ${DatabaseSchema.tableInvoiceLines}',
    );
    await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.tableInvoices}');
    await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.tableItems}');
    await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.tableSuppliers}');
    await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.tableCustomers}');
    await db.execute(
      'DROP TABLE IF EXISTS ${DatabaseSchema.tableJournalEntryLines}',
    );
    await db.execute(
      'DROP TABLE IF EXISTS ${DatabaseSchema.tableJournalEntries}',
    );
    await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.tableAccounts}');
    await db.execute(
      'DROP TABLE IF EXISTS ${DatabaseSchema.tableCompanySettings}',
    );
  }

  Future<void> _insertDefaultData(Database db) async {
    // Insert default company settings
    await db.insert(
      DatabaseSchema.tableCompanySettings,
      DatabaseSchema.defaultCompanySettings,
    );

    // Insert default chart of accounts
    Map<String, int> accountIds = {};

    for (var account in DatabaseSchema.defaultAccounts) {
      Map<String, dynamic> accountData = Map.from(account);

      // Handle parent relationship
      if (accountData.containsKey('parent_code')) {
        String parentCode = accountData.remove('parent_code');
        accountData['parent_id'] = accountIds[parentCode];
      }

      int id = await db.insert(DatabaseSchema.tableAccounts, accountData);
      accountIds[account['code']] = id;
    }
  }

  // Generic CRUD operations
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    return await db.insert(table, data);
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      distinct: distinct,
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      groupBy: groupBy,
      having: having,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    data['updated_at'] = DateTime.now().toIso8601String();
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  // Custom query execution
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  Future<int> rawInsert(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawInsert(sql, arguments);
  }

  Future<int> rawUpdate(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawUpdate(sql, arguments);
  }

  Future<int> rawDelete(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawDelete(sql, arguments);
  }

  // Transaction support
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    final db = await database;
    return await db.transaction(action);
  }

  // Batch operations
  Future<List<dynamic>> batch(Function(Batch batch) operations) async {
    final db = await database;
    Batch batch = db.batch();
    operations(batch);
    return await batch.commit();
  }

  // Database maintenance
  Future<void> vacuum() async {
    final db = await database;
    await db.execute('VACUUM');
  }

  Future<int> getDatabaseSize() async {
    final db = await database;
    var result = await db.rawQuery('PRAGMA page_count');
    var pageCount = result.first['page_count'] as int;

    result = await db.rawQuery('PRAGMA page_size');
    var pageSize = result.first['page_size'] as int;

    return pageCount * pageSize;
  }

  // Close database
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }

  // Reset database (for testing or data reset)
  Future<void> resetDatabase() async {
    await close();
    String path = join(await getDatabasesPath(), DatabaseSchema.databaseName);
    await deleteDatabase(path);
    _database = await _initDatabase();
  }

  // Backup and restore methods
  Future<String> getDatabasePath() async {
    return join(await getDatabasesPath(), DatabaseSchema.databaseName);
  }

  // Check if database exists
  Future<bool> databaseExists() async {
    String path = join(await getDatabasesPath(), DatabaseSchema.databaseName);
    return await databaseFactory.databaseExists(path);
  }

  // Get database info
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    final db = await database;

    var versionResult = await db.rawQuery('PRAGMA user_version');
    var version = versionResult.first['user_version'] as int;

    var tablesResult = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
    );
    var tables = tablesResult.map((row) => row['name'] as String).toList();

    return {
      'version': version,
      'tables': tables,
      'size': await getDatabaseSize(),
      'path': await getDatabasePath(),
    };
  }
}
