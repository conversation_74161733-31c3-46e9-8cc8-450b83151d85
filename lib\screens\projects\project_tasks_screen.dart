/// شاشة إدارة مهام المشروع
/// Project Tasks Management Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/project.dart';
import '../../models/project_cost.dart';
import '../../services/project_service.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/quantum_loading.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_search_bar.dart';
import '../../widgets/quantum_fab.dart';
import '../../theme/app_theme.dart';
import '../../utils/app_text_styles.dart';

class ProjectTasksScreen extends StatefulWidget {
  final Project project;

  const ProjectTasksScreen({super.key, required this.project});

  @override
  State<ProjectTasksScreen> createState() => _ProjectTasksScreenState();
}

class _ProjectTasksScreenState extends State<ProjectTasksScreen>
    with TickerProviderStateMixin {
  final ProjectService _projectService = ProjectService();
  final TextEditingController _searchController = TextEditingController();

  List<ProjectTask> _tasks = [];
  List<ProjectTask> _filteredTasks = [];
  Map<String, dynamic>? _statistics;
  bool _isLoading = false;

  // Filters
  TaskStatus? _selectedStatus;
  TaskPriority? _selectedPriority;
  int? _selectedPhaseId;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadTasks();
    _loadStatistics();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTasks() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _projectService.getProjectTasks(
        widget.project.id!,
        status: _selectedStatus,
        priority: _selectedPriority,
        phaseId: _selectedPhaseId,
        searchTerm: _searchController.text.trim().isEmpty
            ? null
            : _searchController.text.trim(),
      );

      if (result.isSuccess && result.data != null) {
        setState(() {
          _tasks = result.data!;
          _filteredTasks = _tasks;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المهام: ${e.toString()}')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final result = await _projectService.getProjectTasksStatistics(
        widget.project.id!,
      );

      if (result.isSuccess && result.data != null) {
        setState(() {
          _statistics = result.data!;
        });
      }
    } catch (e) {
      // Handle error silently for statistics
    }
  }

  void _onSearchChanged(String value) {
    setState(() {
      if (value.isEmpty) {
        _filteredTasks = _tasks;
      } else {
        _filteredTasks = _tasks
            .where(
              (task) =>
                  task.name.toLowerCase().contains(value.toLowerCase()) ||
                  (task.description?.toLowerCase().contains(
                        value.toLowerCase(),
                      ) ??
                      false),
            )
            .toList();
      }
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة المهام'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            QuantumDropdown<TaskStatus>(
              value: _selectedStatus,
              hintText: 'الحالة',
              items: TaskStatus.values
                  .map(
                    (status) => DropdownMenuItem(
                      value: status,
                      child: Text(status.nameAr),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),
            const SizedBox(height: 16),
            QuantumDropdown<TaskPriority>(
              value: _selectedPriority,
              hintText: 'الأولوية',
              items: TaskPriority.values
                  .map(
                    (priority) => DropdownMenuItem(
                      value: priority,
                      child: Text(priority.nameAr),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedStatus = null;
                _selectedPriority = null;
                _selectedPhaseId = null;
              });
              Navigator.of(context).pop();
              _loadTasks();
            },
            child: const Text('إعادة تعيين'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadTasks();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  List<ProjectTask> _getTasksByStatus(TaskStatus status) {
    return _filteredTasks.where((task) => task.status == status).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: BoxDecoration(gradient: AppTheme.primaryGradient),
        child: Column(
          children: [
            // App Bar
            HolographicAppBar(
              title: 'مهام المشروع',
              subtitle: widget.project.name,
              actions: [
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: _showFilterDialog,
                  tooltip: 'فلترة',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    _loadTasks();
                    _loadStatistics();
                  },
                  tooltip: 'تحديث',
                ),
              ],
            ),

            // Statistics Cards
            if (_statistics != null) _buildStatisticsCards(),

            // Search Bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: QuantumSearchBar(
                controller: _searchController,
                hintText: 'البحث في المهام...',
                onChanged: _onSearchChanged,
              ),
            ),

            // Tab Bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                indicator: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(25),
                ),
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                tabs: [
                  Tab(text: 'الكل (${_filteredTasks.length})'),
                  Tab(
                    text:
                        'لم تبدأ (${_getTasksByStatus(TaskStatus.notStarted).length})',
                  ),
                  Tab(
                    text:
                        'قيد التنفيذ (${_getTasksByStatus(TaskStatus.inProgress).length})',
                  ),
                  Tab(
                    text:
                        'مكتملة (${_getTasksByStatus(TaskStatus.completed).length})',
                  ),
                  Tab(
                    text:
                        'معلقة (${_getTasksByStatus(TaskStatus.onHold).length})',
                  ),
                  Tab(
                    text:
                        'ملغاة (${_getTasksByStatus(TaskStatus.cancelled).length})',
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: QuantumLoading())
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildTasksList(_filteredTasks),
                        _buildTasksList(
                          _getTasksByStatus(TaskStatus.notStarted),
                        ),
                        _buildTasksList(
                          _getTasksByStatus(TaskStatus.inProgress),
                        ),
                        _buildTasksList(
                          _getTasksByStatus(TaskStatus.completed),
                        ),
                        _buildTasksList(_getTasksByStatus(TaskStatus.onHold)),
                        _buildTasksList(
                          _getTasksByStatus(TaskStatus.cancelled),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: QuantumFAB(
        onPressed: () => _showAddTaskDialog(),
        icon: Icons.add_task,
        tooltip: 'إضافة مهمة',
      ),
    );
  }

  Widget _buildStatisticsCards() {
    final stats = _statistics!;
    return Container(
      height: 120,
      margin: const EdgeInsets.all(16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildStatCard(
            'إجمالي المهام',
            '${stats['total_tasks']}',
            Icons.task_alt,
            AppTheme.primaryColor,
          ),
          _buildStatCard(
            'مكتملة',
            '${stats['completed']}',
            Icons.check_circle,
            Colors.green,
          ),
          _buildStatCard(
            'قيد التنفيذ',
            '${stats['in_progress']}',
            Icons.play_circle,
            Colors.orange,
          ),
          _buildStatCard(
            'الساعات المقدرة',
            (stats['total_estimated_hours'] as num).toStringAsFixed(1),
            Icons.schedule,
            Colors.blue,
          ),
          _buildStatCard(
            'الساعات الفعلية',
            (stats['total_actual_hours'] as num).toStringAsFixed(1),
            Icons.timer,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 12),
      child: QuantumCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTextStyles.headlineMedium.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTasksList(List<ProjectTask> tasks) {
    if (tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.task_alt,
              size: 64,
              color: Colors.white.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مهام',
              style: AppTextStyles.headlineMedium.copyWith(
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return _buildTaskCard(task);
      },
    );
  }

  Widget _buildTaskCard(ProjectTask task) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: QuantumCard(
        child: InkWell(
          onTap: () => _showTaskDetailsDialog(task),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        task.name,
                        style: AppTextStyles.titleLarge.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    _buildStatusChip(task.status),
                  ],
                ),
                if (task.description != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    task.description!,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  children: [
                    _buildPriorityChip(task.priority),
                    const SizedBox(width: 8),
                    if (task.estimatedHours > 0) ...[
                      Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        '${task.estimatedHours.toStringAsFixed(1)}س',
                        style: AppTextStyles.bodySmall,
                      ),
                      const SizedBox(width: 12),
                    ],
                    if (task.actualHours > 0) ...[
                      Icon(Icons.timer, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        '${task.actualHours.toStringAsFixed(1)}س',
                        style: AppTextStyles.bodySmall,
                      ),
                    ],
                  ],
                ),
                if (task.startDate != null || task.endDate != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (task.startDate != null) ...[
                        Icon(
                          Icons.play_arrow,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${task.startDate!.day}/${task.startDate!.month}',
                          style: AppTextStyles.bodySmall,
                        ),
                      ],
                      if (task.startDate != null && task.endDate != null)
                        const SizedBox(width: 12),
                      if (task.endDate != null) ...[
                        Icon(Icons.stop, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          '${task.endDate!.day}/${task.endDate!.month}',
                          style: AppTextStyles.bodySmall,
                        ),
                      ],
                    ],
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: () => _showEditTaskDialog(task),
                      tooltip: 'تعديل',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20),
                      onPressed: () => _confirmDeleteTask(task),
                      tooltip: 'حذف',
                    ),
                    if (task.status != TaskStatus.completed)
                      IconButton(
                        icon: const Icon(Icons.check_circle, size: 20),
                        onPressed: () =>
                            _updateTaskStatus(task, TaskStatus.completed),
                        tooltip: 'إكمال',
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(TaskStatus status) {
    Color color;
    switch (status) {
      case TaskStatus.notStarted:
        color = Colors.grey;
        break;
      case TaskStatus.inProgress:
        color = Colors.orange;
        break;
      case TaskStatus.completed:
        color = Colors.green;
        break;
      case TaskStatus.onHold:
        color = Colors.yellow[700]!;
        break;
      case TaskStatus.cancelled:
        color = Colors.red;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status.nameAr,
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPriorityChip(TaskPriority priority) {
    Color color;
    switch (priority) {
      case TaskPriority.low:
        color = Colors.green;
        break;
      case TaskPriority.medium:
        color = Colors.orange;
        break;
      case TaskPriority.high:
        color = Colors.red;
        break;
      case TaskPriority.critical:
        color = Colors.purple;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        priority.nameAr,
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showAddTaskDialog() {
    _showTaskDialog();
  }

  void _showEditTaskDialog(ProjectTask task) {
    _showTaskDialog(task: task);
  }

  void _showTaskDialog({ProjectTask? task}) {
    final isEditing = task != null;
    final nameController = TextEditingController(text: task?.name ?? '');
    final descriptionController = TextEditingController(
      text: task?.description ?? '',
    );
    final estimatedHoursController = TextEditingController(
      text: task?.estimatedHours.toString() ?? '0',
    );
    final actualHoursController = TextEditingController(
      text: task?.actualHours.toString() ?? '0',
    );

    TaskStatus selectedStatus = task?.status ?? TaskStatus.notStarted;
    TaskPriority selectedPriority = task?.priority ?? TaskPriority.medium;
    DateTime? startDate = task?.startDate;
    DateTime? endDate = task?.endDate;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(isEditing ? 'تعديل المهمة' : 'إضافة مهمة جديدة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                QuantumTextField(
                  controller: nameController,
                  labelText: 'اسم المهمة',
                ),
                const SizedBox(height: 16),
                QuantumTextField(
                  controller: descriptionController,
                  labelText: 'الوصف',
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: QuantumDropdown<TaskStatus>(
                        value: selectedStatus,
                        hintText: 'الحالة',
                        items: TaskStatus.values
                            .map(
                              (status) => DropdownMenuItem(
                                value: status,
                                child: Text(status.nameAr),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedStatus = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: QuantumDropdown<TaskPriority>(
                        value: selectedPriority,
                        hintText: 'الأولوية',
                        items: TaskPriority.values
                            .map(
                              (priority) => DropdownMenuItem(
                                value: priority,
                                child: Text(priority.nameAr),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedPriority = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: QuantumTextField(
                        controller: estimatedHoursController,
                        labelText: 'الساعات المقدرة',
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: QuantumTextField(
                        controller: actualHoursController,
                        labelText: 'الساعات الفعلية',
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: startDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime(2030),
                          );
                          if (date != null) {
                            setState(() {
                              startDate = date;
                            });
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today),
                              const SizedBox(width: 8),
                              Text(
                                startDate != null
                                    ? '${startDate!.day}/${startDate!.month}/${startDate!.year}'
                                    : 'تاريخ البداية',
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: endDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime(2030),
                          );
                          if (date != null) {
                            setState(() {
                              endDate = date;
                            });
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today),
                              const SizedBox(width: 8),
                              Text(
                                endDate != null
                                    ? '${endDate!.day}/${endDate!.month}/${endDate!.year}'
                                    : 'تاريخ النهاية',
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _saveTask(
                isEditing,
                task,
                nameController.text,
                descriptionController.text,
                selectedStatus,
                selectedPriority,
                double.tryParse(estimatedHoursController.text) ?? 0,
                double.tryParse(actualHoursController.text) ?? 0,
                startDate,
                endDate,
              ),
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveTask(
    bool isEditing,
    ProjectTask? existingTask,
    String name,
    String description,
    TaskStatus status,
    TaskPriority priority,
    double estimatedHours,
    double actualHours,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    if (name.trim().isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('اسم المهمة مطلوب')));
      return;
    }

    final task = ProjectTask(
      id: isEditing ? existingTask!.id : null,
      projectId: widget.project.id!,
      name: name.trim(),
      description: description.trim().isEmpty ? null : description.trim(),
      status: status,
      priority: priority,
      estimatedHours: estimatedHours,
      actualHours: actualHours,
      startDate: startDate,
      endDate: endDate,
    );

    try {
      final result = isEditing
          ? await _projectService.updateProjectTask(task)
          : await _projectService.createProjectTask(task);

      if (!mounted) return;

      if (result.isSuccess) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEditing ? 'تم تحديث المهمة بنجاح' : 'تم إضافة المهمة بنجاح',
            ),
          ),
        );
        _loadTasks();
        _loadStatistics();
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(result.error ?? 'حدث خطأ')));
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('خطأ: ${e.toString()}')));
    }
  }

  void _confirmDeleteTask(ProjectTask task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المهمة "${task.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteTask(task);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteTask(ProjectTask task) async {
    try {
      final result = await _projectService.deleteProjectTask(task.id!);

      if (!mounted) return;

      if (result.isSuccess) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم حذف المهمة بنجاح')));
        _loadTasks();
        _loadStatistics();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error ?? 'فشل في حذف المهمة')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في حذف المهمة: ${e.toString()}')),
      );
    }
  }

  Future<void> _updateTaskStatus(ProjectTask task, TaskStatus newStatus) async {
    try {
      final result = await _projectService.updateTaskStatus(
        task.id!,
        newStatus,
      );

      if (!mounted) return;

      if (result.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث حالة المهمة بنجاح')),
        );
        _loadTasks();
        _loadStatistics();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error ?? 'فشل في تحديث حالة المهمة')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحديث حالة المهمة: ${e.toString()}')),
      );
    }
  }

  void _showTaskDetailsDialog(ProjectTask task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(task.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (task.description != null) ...[
                const Text(
                  'الوصف:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(task.description!),
                const SizedBox(height: 12),
              ],
              const Text(
                'الحالة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              _buildStatusChip(task.status),
              const SizedBox(height: 12),
              const Text(
                'الأولوية:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              _buildPriorityChip(task.priority),
              const SizedBox(height: 12),
              if (task.estimatedHours > 0) ...[
                Text(
                  'الساعات المقدرة: ${task.estimatedHours.toStringAsFixed(1)}',
                ),
                const SizedBox(height: 8),
              ],
              if (task.actualHours > 0) ...[
                Text('الساعات الفعلية: ${task.actualHours.toStringAsFixed(1)}'),
                const SizedBox(height: 8),
              ],
              if (task.startDate != null) ...[
                Text(
                  'تاريخ البداية: ${task.startDate!.day}/${task.startDate!.month}/${task.startDate!.year}',
                ),
                const SizedBox(height: 8),
              ],
              if (task.endDate != null) ...[
                Text(
                  'تاريخ النهاية: ${task.endDate!.day}/${task.endDate!.month}/${task.endDate!.year}',
                ),
                const SizedBox(height: 8),
              ],
              if (task.actualEndDate != null) ...[
                Text(
                  'تاريخ الإنجاز الفعلي: ${task.actualEndDate!.day}/${task.actualEndDate!.month}/${task.actualEndDate!.year}',
                ),
                const SizedBox(height: 8),
              ],
              if (task.notes != null) ...[
                const Text(
                  'ملاحظات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(task.notes!),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showEditTaskDialog(task);
            },
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }
}
