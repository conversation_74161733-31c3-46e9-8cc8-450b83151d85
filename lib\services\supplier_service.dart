import '../models/supplier.dart';
import '../database/supplier_dao.dart';
import '../database/journal_entry_dao.dart';

/// نتيجة العملية
class Result<T> {
  final bool isSuccess;
  final T? data;
  final String? error;

  Result.success(this.data) : isSuccess = true, error = null;
  Result.error(this.error) : isSuccess = false, data = null;
}

/// خدمة إدارة الموردين
class SupplierService {
  final SupplierDao _supplierDao = SupplierDao();
  final JournalEntryDao _journalEntryDao = JournalEntryDao();

  /// الحصول على جميع الموردين
  Future<Result<List<Supplier>>> getAllSuppliers() async {
    try {
      final suppliers = await _supplierDao.getAllSuppliers();
      return Result.success(suppliers);
    } catch (e) {
      return Result.error('خطأ في جلب الموردين: ${e.toString()}');
    }
  }

  /// الحصول على مورد بالمعرف
  Future<Result<Supplier>> getSupplierById(int id) async {
    try {
      final supplier = await _supplierDao.getSupplierById(id);
      if (supplier != null) {
        return Result.success(supplier);
      } else {
        return Result.error('المورد غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب المورد: ${e.toString()}');
    }
  }

  /// الحصول على مورد بالرمز
  Future<Result<Supplier>> getSupplierByCode(String code) async {
    try {
      final supplier = await _supplierDao.getSupplierByCode(code);
      if (supplier != null) {
        return Result.success(supplier);
      } else {
        return Result.error('المورد غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب المورد: ${e.toString()}');
    }
  }

  /// إنشاء مورد جديد
  Future<Result<Supplier>> createSupplier(Supplier supplier) async {
    try {
      // التحقق من عدم تكرار الرمز
      final existingSupplier = await _supplierDao.getSupplierByCode(
        supplier.code,
      );
      if (existingSupplier != null) {
        return Result.error('رمز المورد موجود مسبقاً');
      }

      // التحقق من صحة البيانات
      final validation = _validateSupplier(supplier);
      if (!validation.isSuccess) {
        return validation;
      }

      final id = await _supplierDao.insertSupplier(supplier);
      final newSupplier = supplier.copyWith(id: id);
      return Result.success(newSupplier);
    } catch (e) {
      return Result.error('خطأ في إنشاء المورد: ${e.toString()}');
    }
  }

  /// تحديث مورد
  Future<Result<Supplier>> updateSupplier(Supplier supplier) async {
    try {
      if (supplier.id == null) {
        return Result.error('معرف المورد مطلوب للتحديث');
      }

      // التحقق من وجود المورد
      final existingSupplier = await _supplierDao.getSupplierById(supplier.id!);
      if (existingSupplier == null) {
        return Result.error('المورد غير موجود');
      }

      // التحقق من عدم تكرار الرمز (إذا تم تغييره)
      if (existingSupplier.code != supplier.code) {
        final duplicateSupplier = await _supplierDao.getSupplierByCode(
          supplier.code,
        );
        if (duplicateSupplier != null) {
          return Result.error('رمز المورد موجود مسبقاً');
        }
      }

      // التحقق من صحة البيانات
      final validation = _validateSupplier(supplier);
      if (!validation.isSuccess) {
        return validation;
      }

      await _supplierDao.updateSupplier(supplier);
      return Result.success(supplier);
    } catch (e) {
      return Result.error('خطأ في تحديث المورد: ${e.toString()}');
    }
  }

  /// حذف مورد
  Future<Result<void>> deleteSupplier(int id) async {
    try {
      // التحقق من وجود المورد
      final supplier = await _supplierDao.getSupplierById(id);
      if (supplier == null) {
        return Result.error('المورد غير موجود');
      }

      // فحص الفواتير والمعاملات المرتبطة بالمورد
      final hasInvoices = await _supplierDao.hasTransactions(id);
      if (hasInvoices) {
        return Result.error(
          'لا يمكن حذف المورد لوجود فواتير أو معاملات مرتبطة به',
        );
      }

      // فحص القيود المحاسبية المرتبطة بحساب المورد
      if (supplier.accountId != null) {
        final accountTransactions = await _journalEntryDao
            .getAccountTransactions(supplier.accountId!);
        if (accountTransactions.isNotEmpty) {
          return Result.error(
            'لا يمكن حذف المورد لوجود قيود محاسبية مرتبطة بحسابه',
          );
        }
      }

      // فحص الرصيد الحالي للمورد
      if (supplier.currentBalance != 0.0) {
        return Result.error(
          'لا يمكن حذف المورد لوجود رصيد حالي غير صفر (${supplier.currentBalance.toStringAsFixed(2)})',
        );
      }

      await _supplierDao.deleteSupplier(id);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف المورد: ${e.toString()}');
    }
  }

  /// البحث في الموردين
  Future<Result<List<Supplier>>> searchSuppliers(String query) async {
    try {
      final suppliers = await _supplierDao.searchSuppliers(query);
      return Result.success(suppliers);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  /// الحصول على الموردين النشطين
  Future<Result<List<Supplier>>> getActiveSuppliers() async {
    try {
      final suppliers = await _supplierDao.getActiveSuppliers();
      return Result.success(suppliers);
    } catch (e) {
      return Result.error('خطأ في جلب الموردين النشطين: ${e.toString()}');
    }
  }

  /// الحصول على رمز المورد التالي
  Future<Result<String>> getNextSupplierCode() async {
    try {
      final code = await _supplierDao.getNextSupplierCode();
      return Result.success(code);
    } catch (e) {
      return Result.error('خطأ في توليد رمز المورد: ${e.toString()}');
    }
  }

  /// تفعيل/إلغاء تفعيل مورد
  Future<Result<void>> toggleSupplierStatus(int id) async {
    try {
      final supplier = await _supplierDao.getSupplierById(id);
      if (supplier == null) {
        return Result.error('المورد غير موجود');
      }

      final updatedSupplier = supplier.copyWith(
        isActive: !supplier.isActive,
        updatedAt: DateTime.now(),
      );

      await _supplierDao.updateSupplier(updatedSupplier);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تغيير حالة المورد: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات المورد
  Future<Result<Map<String, dynamic>>> getSupplierStatistics(
    int supplierId,
  ) async {
    try {
      final stats = await _supplierDao.getSupplierStatisticsById(supplierId);
      return Result.success(stats);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات المورد: ${e.toString()}');
    }
  }

  /// الحصول على رصيد المورد
  Future<Result<double>> getSupplierBalance(int supplierId) async {
    try {
      final balance = await _supplierDao.getSupplierBalance(supplierId);
      return Result.success(balance);
    } catch (e) {
      return Result.error('خطأ في جلب رصيد المورد: ${e.toString()}');
    }
  }

  /// الحصول على إجمالي المشتريات من المورد
  Future<Result<double>> getSupplierTotalPurchases(
    int supplierId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final total = await _supplierDao.getSupplierTotalPurchases(
        supplierId,
        startDate: startDate,
        endDate: endDate,
      );
      return Result.success(total);
    } catch (e) {
      return Result.error('خطأ في جلب إجمالي المشتريات: ${e.toString()}');
    }
  }

  /// الحصول على عدد الموردين
  Future<Result<int>> getSuppliersCount() async {
    try {
      final count = await _supplierDao.getSuppliersCount();
      return Result.success(count);
    } catch (e) {
      return Result.error('خطأ في جلب عدد الموردين: ${e.toString()}');
    }
  }

  /// الحصول على الموردين حسب الفئة
  Future<Result<List<Supplier>>> getSuppliersByCategory(String category) async {
    try {
      final suppliers = await _supplierDao.getSuppliersByCategory(category);
      return Result.success(suppliers);
    } catch (e) {
      return Result.error('خطأ في جلب الموردين: ${e.toString()}');
    }
  }

  /// الحصول على جميع فئات الموردين
  Future<Result<List<String>>> getSupplierCategories() async {
    try {
      final categories = await _supplierDao.getSupplierCategories();
      return Result.success(categories);
    } catch (e) {
      return Result.error('خطأ في جلب فئات الموردين: ${e.toString()}');
    }
  }

  /// تصدير الموردين إلى CSV
  Future<Result<String>> exportSuppliersToCSV() async {
    try {
      final suppliers = await _supplierDao.getAllSuppliers();

      // إنشاء محتوى CSV
      final csvContent = StringBuffer();
      csvContent.writeln(
        'الرمز,الاسم,الهاتف,البريد الإلكتروني,العنوان,الفئة,الرقم الضريبي,حد الائتمان,شروط الدفع,الحالة,تاريخ الإنشاء',
      );

      for (final supplier in suppliers) {
        csvContent.writeln(
          [
            supplier.code,
            supplier.name,
            supplier.phone ?? '',
            supplier.email ?? '',
            supplier.address ?? '',
            supplier.category ?? '',
            supplier.taxNumber ?? '',
            supplier.creditLimit?.toString() ?? '',
            supplier.paymentTerms?.toString() ?? '',
            supplier.isActive ? 'نشط' : 'غير نشط',
            supplier.createdAt.toString(),
          ].join(','),
        );
      }

      // هنا يمكن حفظ الملف أو إرجاع المحتوى
      return Result.success(csvContent.toString());
    } catch (e) {
      return Result.error('خطأ في تصدير الموردين: ${e.toString()}');
    }
  }

  /// استيراد الموردين من CSV
  Future<Result<int>> importSuppliersFromCSV(String csvContent) async {
    try {
      final lines = csvContent.split('\n');
      if (lines.isEmpty) {
        return Result.error('الملف فارغ');
      }

      // تخطي السطر الأول (العناوين)
      int importedCount = 0;
      for (int i = 1; i < lines.length; i++) {
        final line = lines[i].trim();
        if (line.isEmpty) continue;

        final fields = line.split(',');
        if (fields.length < 2) continue;

        try {
          final supplier = Supplier(
            code: fields[0].trim(),
            name: fields[1].trim(),
            phone: fields.length > 2 && fields[2].trim().isNotEmpty
                ? fields[2].trim()
                : null,
            email: fields.length > 3 && fields[3].trim().isNotEmpty
                ? fields[3].trim()
                : null,
            address: fields.length > 4 && fields[4].trim().isNotEmpty
                ? fields[4].trim()
                : null,
            category: fields.length > 5 && fields[5].trim().isNotEmpty
                ? fields[5].trim()
                : null,
            taxNumber: fields.length > 6 && fields[6].trim().isNotEmpty
                ? fields[6].trim()
                : null,
            creditLimit: fields.length > 7 && fields[7].trim().isNotEmpty
                ? double.tryParse(fields[7].trim())
                : null,
            paymentTerms: fields.length > 8 && fields[8].trim().isNotEmpty
                ? int.tryParse(fields[8].trim())
                : null,
            isActive: fields.length > 9 ? fields[9].trim() == 'نشط' : true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // التحقق من عدم وجود المورد
          final existing = await _supplierDao.getSupplierByCode(supplier.code);
          if (existing == null) {
            await _supplierDao.insertSupplier(supplier);
            importedCount++;
          }
        } catch (e) {
          // تخطي السطر في حالة الخطأ
          continue;
        }
      }

      return Result.success(importedCount);
    } catch (e) {
      return Result.error('خطأ في استيراد الموردين: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات المورد
  Result<Supplier> _validateSupplier(Supplier supplier) {
    // التحقق من الحقول المطلوبة
    if (supplier.code.trim().isEmpty) {
      return Result.error('رمز المورد مطلوب');
    }

    if (supplier.name.trim().isEmpty) {
      return Result.error('اسم المورد مطلوب');
    }

    // التحقق من صحة البريد الإلكتروني
    if (supplier.email != null && supplier.email!.isNotEmpty) {
      if (!RegExp(
        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
      ).hasMatch(supplier.email!)) {
        return Result.error('البريد الإلكتروني غير صحيح');
      }
    }

    // التحقق من صحة حد الائتمان
    if (supplier.creditLimit != null && supplier.creditLimit! < 0) {
      return Result.error('حد الائتمان لا يمكن أن يكون سالباً');
    }

    // التحقق من صحة شروط الدفع
    if (supplier.paymentTerms != null && supplier.paymentTerms! < 0) {
      return Result.error('شروط الدفع لا يمكن أن تكون سالبة');
    }

    return Result.success(supplier);
  }
}
