/// شاشة عرض التقارير المالية
/// Report Viewer Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import '../../models/financial_report.dart';
import '../../models/financial_reports.dart';
import '../../services/financial_report_service.dart';

class ReportViewerScreen extends StatefulWidget {
  final ReportType reportType;
  final DateTime? fromDate;
  final DateTime? toDate;
  final DateTime? asOfDate;
  final String? accountId;

  const ReportViewerScreen({
    super.key,
    required this.reportType,
    this.fromDate,
    this.toDate,
    this.asOfDate,
    this.accountId,
  });

  @override
  State<ReportViewerScreen> createState() => _ReportViewerScreenState();
}

class _ReportViewerScreenState extends State<ReportViewerScreen> {
  final FinancialReportService _reportService = FinancialReportService();

  bool _isLoading = true;
  String? _error;
  FinancialReport? _report;
  TrialBalance? _trialBalance;
  CashFlowStatement? _cashFlowStatement;
  GeneralLedgerReport? _generalLedgerReport;

  @override
  void initState() {
    super.initState();
    _generateReport();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getReportTitle()),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading && _error == null)
            PopupMenuButton<ReportFormat>(
              icon: const Icon(Icons.download),
              onSelected: _exportReport,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: ReportFormat.pdf,
                  child: Row(
                    children: [
                      Icon(Icons.picture_as_pdf),
                      SizedBox(width: 8),
                      Text('تصدير PDF'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ReportFormat.excel,
                  child: Row(
                    children: [
                      Icon(Icons.table_chart),
                      SizedBox(width: 8),
                      Text('تصدير Excel'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ReportFormat.csv,
                  child: Row(
                    children: [
                      Icon(Icons.text_snippet),
                      SizedBox(width: 8),
                      Text('تصدير CSV'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ReportFormat.html,
                  child: Row(
                    children: [
                      Icon(Icons.web),
                      SizedBox(width: 8),
                      Text('تصدير HTML'),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري إنشاء التقرير...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.red[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _generateReport,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    switch (widget.reportType) {
      case ReportType.trialBalance:
        return _buildTrialBalanceView();
      case ReportType.cashFlow:
        return _buildCashFlowView();
      case ReportType.generalLedger:
        return _buildGeneralLedgerView();
      default:
        return _buildStandardReportView();
    }
  }

  Widget _buildStandardReportView() {
    if (_report == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReportHeader(_report!),
          const SizedBox(height: 24),
          ..._report!.sections.map((section) => _buildReportSection(section)),
          const SizedBox(height: 24),
          _buildReportSummary(_report!),
        ],
      ),
    );
  }

  Widget _buildTrialBalanceView() {
    if (_trialBalance == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ميزان المراجعة',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'كما في: ${_formatDate(_trialBalance!.asOfDate)}',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Table(
                columnWidths: const {
                  0: FlexColumnWidth(2),
                  1: FlexColumnWidth(3),
                  2: FlexColumnWidth(2),
                  3: FlexColumnWidth(2),
                },
                children: [
                  TableRow(
                    decoration: BoxDecoration(color: Colors.grey[200]),
                    children: [
                      _buildTableHeader('رمز الحساب'),
                      _buildTableHeader('اسم الحساب'),
                      _buildTableHeader('مدين'),
                      _buildTableHeader('دائن'),
                    ],
                  ),
                  ..._trialBalance!.lines.map(
                    (line) => TableRow(
                      children: [
                        _buildTableCell(line.accountCode),
                        _buildTableCell(line.accountName),
                        _buildTableCell(
                          _formatAmount(line.debitBalance),
                          isAmount: true,
                        ),
                        _buildTableCell(
                          _formatAmount(line.creditBalance),
                          isAmount: true,
                        ),
                      ],
                    ),
                  ),
                  TableRow(
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).primaryColor.withValues(alpha: 0.1),
                    ),
                    children: [
                      _buildTableCell('المجموع', isBold: true),
                      _buildTableCell(''),
                      _buildTableCell(
                        _formatAmount(_trialBalance!.totalDebits),
                        isAmount: true,
                        isBold: true,
                      ),
                      _buildTableCell(
                        _formatAmount(_trialBalance!.totalCredits),
                        isAmount: true,
                        isBold: true,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _trialBalance!.isBalanced
                  ? Colors.green[100]
                  : Colors.red[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _trialBalance!.isBalanced ? Icons.check_circle : Icons.error,
                  color: _trialBalance!.isBalanced ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  _trialBalance!.isBalanced
                      ? 'الميزان متوازن'
                      : 'الميزان غير متوازن',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _trialBalance!.isBalanced
                        ? Colors.green[800]
                        : Colors.red[800],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCashFlowView() {
    if (_cashFlowStatement == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'قائمة التدفق النقدي',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'للفترة من ${_formatDate(_cashFlowStatement!.fromDate)} إلى ${_formatDate(_cashFlowStatement!.toDate)}',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildCashFlowSection(_cashFlowStatement!.operatingActivities),
          _buildCashFlowSection(_cashFlowStatement!.investingActivities),
          _buildCashFlowSection(_cashFlowStatement!.financingActivities),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildCashFlowSummaryRow(
                    'النقد في بداية الفترة',
                    _cashFlowStatement!.beginningCash,
                  ),
                  _buildCashFlowSummaryRow(
                    'صافي التدفق النقدي',
                    _cashFlowStatement!.netCashFlow,
                  ),
                  const Divider(),
                  _buildCashFlowSummaryRow(
                    'النقد في نهاية الفترة',
                    _cashFlowStatement!.endingCash,
                    isBold: true,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralLedgerView() {
    if (_generalLedgerReport == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'دفتر الأستاذ العام',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'للفترة من ${_formatDate(_generalLedgerReport!.startDate)} إلى ${_formatDate(_generalLedgerReport!.endDate)}',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildGeneralLedgerStat(
                        'عدد الحسابات',
                        _generalLedgerReport!.totalAccounts.toString(),
                        Icons.account_balance,
                      ),
                      const SizedBox(width: 16),
                      _buildGeneralLedgerStat(
                        'إجمالي الحركات',
                        _generalLedgerReport!.totalTransactions.toString(),
                        Icons.receipt_long,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          ..._generalLedgerReport!.accountStatements.map(
            (statement) => _buildAccountStatementCard(statement),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ملخص دفتر الأستاذ العام',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('إجمالي المدين:'),
                      Text(
                        _formatAmount(_generalLedgerReport!.totalDebits),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('إجمالي الدائن:'),
                      Text(
                        _formatAmount(_generalLedgerReport!.totalCredits),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportHeader(FinancialReport report) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              report.title,
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            if (report.subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                report.subtitle!,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
            const SizedBox(height: 8),
            Text(
              'تم الإنشاء في: ${_formatDateTime(report.generatedAt)}',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportSection(ReportSection section) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              section.title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Table(
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(3),
                2: FlexColumnWidth(2),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey[200]),
                  children: [
                    _buildTableHeader('رمز الحساب'),
                    _buildTableHeader('اسم الحساب'),
                    _buildTableHeader('المبلغ'),
                  ],
                ),
                ...section.lines.map(
                  (line) => TableRow(
                    children: [
                      _buildTableCell(line.accountCode, isBold: line.isBold),
                      Padding(
                        padding: EdgeInsets.only(
                          left: line.level * 16.0 + 8,
                          top: 8,
                          bottom: 8,
                          right: 8,
                        ),
                        child: Text(
                          line.accountName,
                          style: TextStyle(
                            fontWeight: line.isBold
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                      _buildTableCell(
                        _formatAmount(line.amount),
                        isAmount: true,
                        isBold: line.isBold,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (section.totals.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'المجموع:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      _formatAmount(section.totals['total'] ?? 0.0),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReportSummary(FinancialReport report) {
    if (report.summary.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص التقرير',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...report.summary.entries.map(
              (entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(_translateSummaryKey(entry.key)),
                    Text(_formatSummaryValue(entry.value)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCashFlowSection(CashFlowSection section) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              section.title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 12),
            ...section.items.map(
              (item) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(item.description),
                    Text(_formatAmount(item.amount)),
                  ],
                ),
              ),
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'صافي ${section.title}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  _formatAmount(section.total),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCashFlowSummaryRow(
    String label,
    double amount, {
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            _formatAmount(amount),
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralLedgerStat(String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, size: 20, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(label, style: Theme.of(context).textTheme.bodySmall),
                  Text(
                    value,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountStatementCard(AccountStatementReport statement) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          '${statement.account.code} - ${statement.account.name}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'الرصيد الختامي: ${_formatAmount(statement.closingBalance)}',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الرصيد الافتتاحي: ${_formatAmount(statement.openingBalance)}',
                    ),
                    Text('عدد الحركات: ${statement.transactionCount}'),
                  ],
                ),
                const SizedBox(height: 12),
                if (statement.transactions.isNotEmpty) ...[
                  Table(
                    columnWidths: const {
                      0: FlexColumnWidth(2),
                      1: FlexColumnWidth(3),
                      2: FlexColumnWidth(1.5),
                      3: FlexColumnWidth(1.5),
                      4: FlexColumnWidth(1.5),
                    },
                    children: [
                      TableRow(
                        decoration: BoxDecoration(color: Colors.grey[200]),
                        children: [
                          _buildTableHeader('التاريخ'),
                          _buildTableHeader('البيان'),
                          _buildTableHeader('مدين'),
                          _buildTableHeader('دائن'),
                          _buildTableHeader('الرصيد'),
                        ],
                      ),
                      ...statement.transactions
                          .take(10)
                          .map(
                            (transaction) => TableRow(
                              children: [
                                _buildTableCell(_formatDate(transaction.date)),
                                _buildTableCell(transaction.description),
                                _buildTableCell(
                                  _formatAmount(transaction.debitAmount),
                                  isAmount: true,
                                ),
                                _buildTableCell(
                                  _formatAmount(transaction.creditAmount),
                                  isAmount: true,
                                ),
                                _buildTableCell(
                                  _formatAmount(transaction.balance),
                                  isAmount: true,
                                ),
                              ],
                            ),
                          ),
                    ],
                  ),
                  if (statement.transactions.length > 10) ...[
                    const SizedBox(height: 8),
                    Text(
                      'عرض أول 10 حركات من أصل ${statement.transactions.length} حركة',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ] else
                  const Text('لا توجد حركات في هذه الفترة'),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'إجمالي المدين: ${_formatAmount(statement.totalDebits)}',
                      ),
                      Text(
                        'إجمالي الدائن: ${_formatAmount(statement.totalCredits)}',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Text(
        text,
        style: const TextStyle(fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(
    String text, {
    bool isAmount = false,
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
        ),
        textAlign: isAmount ? TextAlign.right : TextAlign.start,
      ),
    );
  }

  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      switch (widget.reportType) {
        case ReportType.balanceSheet:
          final result = await _reportService.generateBalanceSheet(
            asOfDate: widget.asOfDate ?? DateTime.now(),
          );
          if (result.isSuccess) {
            _report = result.data;
          } else {
            _error = result.error;
          }
          break;

        case ReportType.incomeStatement:
          final result = await _reportService.generateIncomeStatement(
            fromDate:
                widget.fromDate ??
                DateTime.now().subtract(const Duration(days: 30)),
            toDate: widget.toDate ?? DateTime.now(),
          );
          if (result.isSuccess) {
            _report = result.data;
          } else {
            _error = result.error;
          }
          break;

        case ReportType.trialBalance:
          final result = await _reportService.generateTrialBalance(
            asOfDate: widget.asOfDate ?? DateTime.now(),
          );
          if (result.isSuccess) {
            _trialBalance = result.data;
          } else {
            _error = result.error;
          }
          break;

        case ReportType.cashFlow:
          final result = await _reportService.generateCashFlowStatement(
            fromDate:
                widget.fromDate ??
                DateTime.now().subtract(const Duration(days: 30)),
            toDate: widget.toDate ?? DateTime.now(),
          );
          if (result.isSuccess) {
            _cashFlowStatement = result.data;
          } else {
            _error = result.error;
          }
          break;

        case ReportType.accountStatement:
          if (widget.accountId != null) {
            final result = await _reportService.generateAccountStatement(
              accountId: int.parse(widget.accountId!),
              fromDate:
                  widget.fromDate ??
                  DateTime.now().subtract(const Duration(days: 30)),
              toDate: widget.toDate ?? DateTime.now(),
            );
            if (result.isSuccess) {
              _report = result.data;
            } else {
              _error = result.error;
            }
          } else {
            _error = 'معرف الحساب مطلوب لكشف الحساب';
          }
          break;

        case ReportType.generalLedger:
          final result = await _reportService.generateGeneralLedger(
            fromDate:
                widget.fromDate ??
                DateTime.now().subtract(const Duration(days: 30)),
            toDate: widget.toDate ?? DateTime.now(),
          );
          if (result.isSuccess) {
            _generalLedgerReport = result.data;
          } else {
            _error = result.error;
          }
          break;

        case ReportType.profitLoss:
          // For now, use income statement as profit/loss is similar
          final result = await _reportService.generateIncomeStatement(
            fromDate:
                widget.fromDate ??
                DateTime.now().subtract(const Duration(days: 30)),
            toDate: widget.toDate ?? DateTime.now(),
          );
          if (result.isSuccess) {
            _report = result.data;
          } else {
            _error = result.error;
          }
          break;

        case ReportType.cashFlowDetailed:
          // For now, use regular cash flow statement
          final result = await _reportService.generateCashFlowStatement(
            fromDate:
                widget.fromDate ??
                DateTime.now().subtract(const Duration(days: 30)),
            toDate: widget.toDate ?? DateTime.now(),
          );
          if (result.isSuccess) {
            _cashFlowStatement = result.data;
          } else {
            _error = result.error;
          }
          break;
      }
    } catch (e) {
      _error = 'حدث خطأ في إنشاء التقرير: ${e.toString()}';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _exportReport(ReportFormat format) async {
    if (_report == null &&
        _trialBalance == null &&
        _cashFlowStatement == null &&
        _generalLedgerReport == null) {
      return;
    }

    try {
      final result = await _reportService.exportReport(
        report:
            _report ??
            FinancialReport(
              type: widget.reportType,
              title: _getReportTitle(),
              fromDate:
                  widget.fromDate ??
                  DateTime.now().subtract(const Duration(days: 30)),
              toDate: widget.toDate ?? DateTime.now(),
              generatedAt: DateTime.now(),
              sections: [],
              summary: {},
            ),
        format: format,
      );

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تصدير التقرير بنجاح: ${result.data}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تصدير التقرير: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في التصدير: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getReportTitle() {
    switch (widget.reportType) {
      case ReportType.balanceSheet:
        return 'الميزانية العمومية';
      case ReportType.incomeStatement:
        return 'قائمة الدخل';
      case ReportType.trialBalance:
        return 'ميزان المراجعة';
      case ReportType.cashFlow:
        return 'قائمة التدفق النقدي';
      case ReportType.accountStatement:
        return 'كشف الحساب';
      case ReportType.generalLedger:
        return 'دفتر الأستاذ العام';
      case ReportType.profitLoss:
        return 'الأرباح والخسائر';
      case ReportType.cashFlowDetailed:
        return 'التدفق النقدي التفصيلي';
    }
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(2);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _translateSummaryKey(String key) {
    switch (key) {
      case 'total_assets':
        return 'إجمالي الأصول';
      case 'total_liabilities_equity':
        return 'إجمالي الخصوم وحقوق الملكية';
      case 'is_balanced':
        return 'متوازن';
      case 'total_revenue':
        return 'إجمالي الإيرادات';
      case 'total_expenses':
        return 'إجمالي المصروفات';
      case 'net_income':
        return 'صافي الدخل';
      case 'profit_margin':
        return 'هامش الربح %';
      default:
        return key;
    }
  }

  String _formatSummaryValue(dynamic value) {
    if (value is double) {
      return _formatAmount(value);
    } else if (value is bool) {
      return value ? 'نعم' : 'لا';
    } else {
      return value.toString();
    }
  }
}
