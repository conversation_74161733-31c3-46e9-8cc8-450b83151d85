/// زر كمي متقدم لتطبيق Smart Ledger
/// Advanced Quantum Button for Smart Ledger Application
library;

import 'package:flutter/material.dart';

/// أنواع الأزرار الكمية
/// Quantum button variants
enum QuantumButtonVariant {
  primary,
  secondary,
  success,
  warning,
  danger,
  info,
  outline,
}

/// زر كمي مع تأثيرات بصرية متقدمة
/// Quantum button with advanced visual effects
class QuantumButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final QuantumButtonVariant variant;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final double? width;
  final double? height;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final bool enableGlowEffect;
  final bool enableHoverEffect;

  const QuantumButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = QuantumButtonVariant.primary,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.width,
    this.height,
    this.borderRadius = 12.0,
    this.padding,
    this.textStyle,
    this.enableGlowEffect = true,
    this.enableHoverEffect = true,
  });

  @override
  State<QuantumButton> createState() => _QuantumButtonState();
}

class _QuantumButtonState extends State<QuantumButton>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _glowController;
  late AnimationController _pressController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  late Animation<double> _pressAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pressController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    _pressAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _pressController, curve: Curves.easeInOut),
    );

    if (widget.enableGlowEffect && widget.onPressed != null) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _glowController.dispose();
    _pressController.dispose();
    super.dispose();
  }

  Color _getButtonColor() {
    switch (widget.variant) {
      case QuantumButtonVariant.primary:
        return Colors.blue;
      case QuantumButtonVariant.secondary:
        return Colors.grey;
      case QuantumButtonVariant.success:
        return Colors.green;
      case QuantumButtonVariant.warning:
        return Colors.orange;
      case QuantumButtonVariant.danger:
        return Colors.red;
      case QuantumButtonVariant.info:
        return Colors.cyan;
      case QuantumButtonVariant.outline:
        return Colors.transparent;
    }
  }

  Color _getTextColor() {
    if (widget.variant == QuantumButtonVariant.outline) {
      return Colors.blue;
    }
    return Colors.white;
  }

  void _onHover(bool isHovered) {
    if (!widget.enableHoverEffect || widget.onPressed == null) return;

    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed == null) return;
    _pressController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.onPressed == null) return;
    _pressController.reverse();
  }

  void _onTapCancel() {
    if (widget.onPressed == null) return;
    _pressController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final buttonColor = _getButtonColor();
    final textColor = _getTextColor();
    final isDisabled = widget.onPressed == null;

    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimation,
        _glowAnimation,
        _pressAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * _pressAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _onHover(true),
            onExit: (_) => _onHover(false),
            child: GestureDetector(
              onTapDown: _onTapDown,
              onTapUp: _onTapUp,
              onTapCancel: _onTapCancel,
              onTap: widget.isLoading ? null : widget.onPressed,
              child: Container(
                width: widget.isFullWidth ? double.infinity : widget.width,
                height: widget.height ?? 48,
                padding:
                    widget.padding ??
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  gradient: widget.variant != QuantumButtonVariant.outline
                      ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            buttonColor.withValues(
                              alpha: isDisabled ? 0.5 : 1.0,
                            ),
                            buttonColor.withValues(
                              alpha: isDisabled ? 0.3 : 0.8,
                            ),
                          ],
                        )
                      : null,
                  border: widget.variant == QuantumButtonVariant.outline
                      ? Border.all(
                          color: buttonColor.withValues(
                            alpha: isDisabled ? 0.5 : 1.0,
                          ),
                          width: 2,
                        )
                      : null,
                  boxShadow: [
                    if (!isDisabled)
                      BoxShadow(
                        color: buttonColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    if (widget.enableGlowEffect && !isDisabled)
                      BoxShadow(
                        color: buttonColor.withValues(
                          alpha: _glowAnimation.value * 0.5,
                        ),
                        blurRadius: 20,
                        offset: const Offset(0, 0),
                      ),
                  ],
                ),
                child: widget.isLoading
                    ? Center(
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              textColor,
                            ),
                          ),
                        ),
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (widget.icon != null) ...[
                            Icon(
                              widget.icon,
                              color: textColor.withValues(
                                alpha: isDisabled ? 0.5 : 1.0,
                              ),
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                          ],
                          Text(
                            widget.text,
                            style: (widget.textStyle ?? const TextStyle())
                                .copyWith(
                                  color: textColor.withValues(
                                    alpha: isDisabled ? 0.5 : 1.0,
                                  ),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        );
      },
    );
  }
}
