// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Smart Ledger';

  @override
  String get home => 'Home';

  @override
  String get accounts => 'Accounts';

  @override
  String get entries => 'Entries';

  @override
  String get customers => 'Customers';

  @override
  String get suppliers => 'Suppliers';

  @override
  String get inventory => 'Inventory';

  @override
  String get invoices => 'Invoices';

  @override
  String get reports => 'Reports';

  @override
  String get settings => 'Settings';

  @override
  String get add => 'Add';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get date => 'Date';

  @override
  String get amount => 'Amount';

  @override
  String get description => 'Description';

  @override
  String get balance => 'Balance';

  @override
  String get debit => 'Debit';

  @override
  String get credit => 'Credit';

  @override
  String get total => 'Total';

  @override
  String get accountName => 'Account Name';

  @override
  String get accountCode => 'Account Code';

  @override
  String get accountType => 'Account Type';

  @override
  String get parentAccount => 'Parent Account';

  @override
  String get journalEntry => 'Journal Entry';

  @override
  String get reference => 'Reference';

  @override
  String get customerName => 'Customer Name';

  @override
  String get supplierName => 'Supplier Name';

  @override
  String get phone => 'Phone';

  @override
  String get email => 'Email';

  @override
  String get address => 'Address';

  @override
  String get itemName => 'Item Name';

  @override
  String get quantity => 'Quantity';

  @override
  String get price => 'Price';

  @override
  String get unit => 'Unit';

  @override
  String get invoiceNumber => 'Invoice Number';

  @override
  String get salesInvoice => 'Sales Invoice';

  @override
  String get purchaseInvoice => 'Purchase Invoice';

  @override
  String get trialBalance => 'Trial Balance';

  @override
  String get balanceSheet => 'Balance Sheet';

  @override
  String get incomeStatement => 'Income Statement';

  @override
  String get accountStatement => 'Account Statement';

  @override
  String get backup => 'Backup';

  @override
  String get restore => 'Restore';

  @override
  String get companyInfo => 'Company Info';

  @override
  String get currency => 'Currency';

  @override
  String get fiscalYear => 'Fiscal Year';
}
