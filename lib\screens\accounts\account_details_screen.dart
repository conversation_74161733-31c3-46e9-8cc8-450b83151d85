import 'package:flutter/material.dart';
import '../../models/account.dart';
import '../../models/journal_entry.dart';
import '../../services/account_service.dart';
import '../../services/journal_entry_service.dart';
import 'add_account_screen.dart';
import 'package:intl/intl.dart';

class AccountDetailsScreen extends StatefulWidget {
  final Account account;

  const AccountDetailsScreen({super.key, required this.account});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final AccountService _accountService = AccountService();
  final JournalEntryService _journalEntryService = JournalEntryService();

  late Account _currentAccount;
  List<JournalEntryLine> _accountTransactions = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _currentAccount = widget.account;
    _loadAccountDetails();
  }

  Future<void> _loadAccountDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final accountResult = await _accountService.getAccountById(
        _currentAccount.id!,
      );
      if (accountResult.isSuccess) {
        _currentAccount = accountResult.data!;
      }

      final transactionsResult = await _journalEntryService
          .getAccountTransactions(_currentAccount.id!);
      if (transactionsResult.isSuccess) {
        _accountTransactions = transactionsResult.data!;
      }

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل بيانات الحساب: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_currentAccount.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      AddAccountScreen(account: _currentAccount),
                ),
              );
              if (result == true) {
                _loadAccountDetails();
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAccountDetails,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'معلومات الحساب', icon: Icon(Icons.info)),
            Tab(text: 'الحركات', icon: Icon(Icons.list)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
          ? _buildErrorWidget()
          : TabBarView(
              controller: _tabController,
              children: [_buildAccountInfoTab(), _buildTransactionsTab()],
            ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(_errorMessage!),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadAccountDetails,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountInfoTab() {
    final currencyFormatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 2,
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _getAccountTypeColor(
                            _currentAccount.accountType,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getAccountTypeIcon(_currentAccount.accountType),
                          color: _getAccountTypeColor(
                            _currentAccount.accountType,
                          ),
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _currentAccount.name,
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'رمز الحساب: ${_currentAccount.code}',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _currentAccount.balance >= 0
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'الرصيد الحالي',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          currencyFormatter.format(_currentAccount.balance),
                          style: Theme.of(context).textTheme.headlineMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: _currentAccount.balance >= 0
                                    ? Colors.green
                                    : Colors.red,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل الحساب',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    'نوع الحساب',
                    _currentAccount.accountType.displayName,
                  ),
                  if (_currentAccount.nameEn != null)
                    _buildDetailRow('الاسم الإنجليزي', _currentAccount.nameEn!),
                  if (_currentAccount.description != null)
                    _buildDetailRow('الوصف', _currentAccount.description!),
                  _buildDetailRow(
                    'حالة الحساب',
                    _currentAccount.isActive ? 'نشط' : 'غير نشط',
                  ),
                  _buildDetailRow(
                    'تاريخ الإنشاء',
                    DateFormat(
                      'dd/MM/yyyy HH:mm',
                      'ar_SA',
                    ).format(_currentAccount.createdAt),
                  ),
                  _buildDetailRow(
                    'آخر تحديث',
                    DateFormat(
                      'dd/MM/yyyy HH:mm',
                      'ar_SA',
                    ).format(_currentAccount.updatedAt),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    if (_accountTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد حركات على هذا الحساب',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      );
    }

    final currencyFormatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 2,
    );

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _accountTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _accountTransactions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: transaction.debitAmount > 0
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                transaction.debitAmount > 0 ? Icons.add : Icons.remove,
                color: transaction.debitAmount > 0 ? Colors.green : Colors.red,
              ),
            ),
            title: Text(transaction.description ?? 'قيد محاسبي'),
            subtitle: Text(
              DateFormat('dd/MM/yyyy', 'ar_SA').format(transaction.createdAt),
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (transaction.debitAmount > 0)
                  Text(
                    currencyFormatter.format(transaction.debitAmount),
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                if (transaction.creditAmount > 0)
                  Text(
                    currencyFormatter.format(transaction.creditAmount),
                    style: const TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.asset:
        return Colors.green;
      case AccountType.liability:
        return Colors.red;
      case AccountType.equity:
        return Colors.blue;
      case AccountType.revenue:
        return Colors.orange;
      case AccountType.expense:
        return Colors.purple;
    }
  }

  IconData _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.asset:
        return Icons.account_balance_wallet;
      case AccountType.liability:
        return Icons.credit_card;
      case AccountType.equity:
        return Icons.business;
      case AccountType.revenue:
        return Icons.trending_up;
      case AccountType.expense:
        return Icons.trending_down;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
