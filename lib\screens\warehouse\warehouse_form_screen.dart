/// شاشة نموذج إضافة/تعديل المخزن
/// Warehouse Form Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/warehouse.dart';
import '../../services/warehouse_service.dart';
import '../../providers/language_provider.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/quantum_loading.dart';

class WarehouseFormScreen extends StatefulWidget {
  final Warehouse? warehouse;

  const WarehouseFormScreen({super.key, this.warehouse});

  @override
  State<WarehouseFormScreen> createState() => _WarehouseFormScreenState();
}

class _WarehouseFormScreenState extends State<WarehouseFormScreen>
    with TickerProviderStateMixin {
  final WarehouseService _warehouseService = WarehouseService();
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _codeController;
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _addressController;
  late TextEditingController _cityController;
  late TextEditingController _countryController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _managerNameController;
  late TextEditingController _areaController;
  late TextEditingController _capacityController;
  
  WarehouseType _selectedType = WarehouseType.main;
  WarehouseStatus _selectedStatus = WarehouseStatus.active;
  bool _isMainWarehouse = false;
  bool _allowNegativeStock = false;
  bool _requireApproval = false;
  bool _isLoading = false;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    
    if (widget.warehouse != null) {
      _populateFields();
    }
  }

  void _initializeControllers() {
    _codeController = TextEditingController();
    _nameController = TextEditingController();
    _descriptionController = TextEditingController();
    _addressController = TextEditingController();
    _cityController = TextEditingController();
    _countryController = TextEditingController();
    _phoneController = TextEditingController();
    _emailController = TextEditingController();
    _managerNameController = TextEditingController();
    _areaController = TextEditingController();
    _capacityController = TextEditingController();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _populateFields() {
    final warehouse = widget.warehouse!;
    _codeController.text = warehouse.code;
    _nameController.text = warehouse.name;
    _descriptionController.text = warehouse.description ?? '';
    _addressController.text = warehouse.address ?? '';
    _cityController.text = warehouse.city ?? '';
    _countryController.text = warehouse.country ?? '';
    _phoneController.text = warehouse.phone ?? '';
    _emailController.text = warehouse.email ?? '';
    _managerNameController.text = warehouse.managerName ?? '';
    _areaController.text = warehouse.area?.toString() ?? '';
    _capacityController.text = warehouse.capacity?.toString() ?? '';
    
    _selectedType = warehouse.type;
    _selectedStatus = warehouse.status;
    _isMainWarehouse = warehouse.isMainWarehouse;
    _allowNegativeStock = warehouse.allowNegativeStock;
    _requireApproval = warehouse.requireApproval;
  }

  Future<void> _saveWarehouse() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final warehouse = Warehouse(
      id: widget.warehouse?.id,
      code: _codeController.text.trim(),
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      type: _selectedType,
      status: _selectedStatus,
      address: _addressController.text.trim().isEmpty 
          ? null 
          : _addressController.text.trim(),
      city: _cityController.text.trim().isEmpty 
          ? null 
          : _cityController.text.trim(),
      country: _countryController.text.trim().isEmpty 
          ? null 
          : _countryController.text.trim(),
      phone: _phoneController.text.trim().isEmpty 
          ? null 
          : _phoneController.text.trim(),
      email: _emailController.text.trim().isEmpty 
          ? null 
          : _emailController.text.trim(),
      managerName: _managerNameController.text.trim().isEmpty 
          ? null 
          : _managerNameController.text.trim(),
      area: _areaController.text.trim().isEmpty 
          ? null 
          : double.tryParse(_areaController.text.trim()),
      capacity: _capacityController.text.trim().isEmpty 
          ? null 
          : double.tryParse(_capacityController.text.trim()),
      isMainWarehouse: _isMainWarehouse,
      allowNegativeStock: _allowNegativeStock,
      requireApproval: _requireApproval,
    );

    final result = widget.warehouse == null
        ? await _warehouseService.addWarehouse(warehouse)
        : await _warehouseService.updateWarehouse(warehouse);

    setState(() => _isLoading = false);

    if (result.isSuccess) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.warehouse == null
                  ? 'تم إضافة المخزن بنجاح'
                  : 'تم تحديث المخزن بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildBasicInfoSection() {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;
    
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'المعلومات الأساسية' : 'Basic Information',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _codeController,
                    labelText: isArabic ? 'كود المخزن' : 'Warehouse Code',
                    prefixIcon: Icons.qr_code,
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return isArabic ? 'كود المخزن مطلوب' : 'Warehouse code is required';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _nameController,
                    labelText: isArabic ? 'اسم المخزن' : 'Warehouse Name',
                    prefixIcon: Icons.warehouse,
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return isArabic ? 'اسم المخزن مطلوب' : 'Warehouse name is required';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _descriptionController,
              labelText: isArabic ? 'الوصف' : 'Description',
              prefixIcon: Icons.description,
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<WarehouseType>(
                    value: _selectedType,
                    labelText: isArabic ? 'نوع المخزن' : 'Warehouse Type',
                    prefixIcon: Icons.category,
                    items: WarehouseType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.arabicName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedType = value!);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<WarehouseStatus>(
                    value: _selectedStatus,
                    labelText: isArabic ? 'حالة المخزن' : 'Warehouse Status',
                    prefixIcon: Icons.info,
                    items: WarehouseStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(status.arabicName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedStatus = value!);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;
    
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات الموقع' : 'Location Information',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _addressController,
              labelText: isArabic ? 'العنوان' : 'Address',
              prefixIcon: Icons.location_on,
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _cityController,
                    labelText: isArabic ? 'المدينة' : 'City',
                    prefixIcon: Icons.location_city,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _countryController,
                    labelText: isArabic ? 'البلد' : 'Country',
                    prefixIcon: Icons.flag,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection() {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;
    
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'معلومات الاتصال' : 'Contact Information',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumTextField(
                    controller: _phoneController,
                    labelText: isArabic ? 'رقم الهاتف' : 'Phone Number',
                    prefixIcon: Icons.phone,
                    keyboardType: TextInputType.phone,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumTextField(
                    controller: _emailController,
                    labelText: isArabic ? 'البريد الإلكتروني' : 'Email',
                    prefixIcon: Icons.email,
                    keyboardType: TextInputType.emailAddress,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _managerNameController,
              labelText: isArabic ? 'اسم المدير' : 'Manager Name',
              prefixIcon: Icons.person,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;

    return Scaffold(
      appBar: HolographicAppBar(
        title: widget.warehouse == null
            ? (isArabic ? 'إضافة مخزن جديد' : 'Add New Warehouse')
            : (isArabic ? 'تعديل المخزن' : 'Edit Warehouse'),
        subtitle: widget.warehouse?.name,
      ),
      body: _isLoading
          ? const Center(child: QuantumLoading())
          : SlideTransition(
              position: _slideAnimation,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Form(
                  key: _formKey,
                  child: ListView(
                    padding: const EdgeInsets.all(16),
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 16),
                      _buildLocationSection(),
                      const SizedBox(height: 16),
                      _buildContactSection(),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Expanded(
                            child: QuantumButton(
                              onPressed: () => Navigator.pop(context),
                              text: isArabic ? 'إلغاء' : 'Cancel',
                              variant: QuantumButtonVariant.secondary,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: QuantumButton(
                              onPressed: _saveWarehouse,
                              text: widget.warehouse == null
                                  ? (isArabic ? 'إضافة' : 'Add')
                                  : (isArabic ? 'تحديث' : 'Update'),
                              variant: QuantumButtonVariant.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _codeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _managerNameController.dispose();
    _areaController.dispose();
    _capacityController.dispose();
    super.dispose();
  }
}
