/// Data Access Object for Cash Vaults
/// Cash Vault DAO for Smart Ledger
library;


import '../models/cash_vault.dart';
import 'database_helper.dart';
import 'database_schema.dart';

class CashVaultDao {
  static final CashVaultDao _instance = CashVaultDao._internal();
  factory CashVaultDao() => _instance;
  CashVaultDao._internal();

  /// Get all cash vaults
  Future<List<CashVault>> getAllCashVaults() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashVaults,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return CashVault.fromMap(maps[i]);
    });
  }

  /// Get cash vault by ID
  Future<CashVault?> getCashVaultById(int id) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashVaults,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return CashVault.fromMap(maps.first);
    }
    return null;
  }

  /// Get cash vault by code
  Future<CashVault?> getCashVaultByCode(String code) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashVaults,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return CashVault.fromMap(maps.first);
    }
    return null;
  }

  /// Get active cash vaults
  Future<List<CashVault>> getActiveCashVaults() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashVaults,
      where: 'status = ?',
      whereArgs: ['active'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return CashVault.fromMap(maps[i]);
    });
  }

  /// Get cash vaults by type
  Future<List<CashVault>> getCashVaultsByType(CashVaultType type) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashVaults,
      where: 'type = ?',
      whereArgs: [type.value],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return CashVault.fromMap(maps[i]);
    });
  }

  /// Get cash vaults by currency
  Future<List<CashVault>> getCashVaultsByCurrency(String currency) async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashVaults,
      where: 'currency = ?',
      whereArgs: [currency],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return CashVault.fromMap(maps[i]);
    });
  }

  /// Insert new cash vault
  Future<int> insertCashVault(CashVault vault) async {
    final db = await DatabaseHelper().database;
    
    // Check if code already exists
    final existing = await getCashVaultByCode(vault.code);
    if (existing != null) {
      throw Exception('رمز الخزينة موجود مسبقاً');
    }

    final vaultMap = vault.toMap();
    vaultMap.remove('id'); // Remove ID for auto-increment
    vaultMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.insert(
      DatabaseSchema.tableCashVaults,
      vaultMap,
    );
  }

  /// Update cash vault
  Future<int> updateCashVault(CashVault vault) async {
    final db = await DatabaseHelper().database;
    
    // Check if code already exists for different vault
    final existing = await getCashVaultByCode(vault.code);
    if (existing != null && existing.id != vault.id) {
      throw Exception('رمز الخزينة موجود مسبقاً');
    }

    final vaultMap = vault.toMap();
    vaultMap['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      DatabaseSchema.tableCashVaults,
      vaultMap,
      where: 'id = ?',
      whereArgs: [vault.id],
    );
  }

  /// Update cash vault balance
  Future<int> updateCashVaultBalance(int vaultId, double newBalance) async {
    final db = await DatabaseHelper().database;
    
    return await db.update(
      DatabaseSchema.tableCashVaults,
      {
        'current_balance': newBalance,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [vaultId],
    );
  }

  /// Delete cash vault
  Future<int> deleteCashVault(int id) async {
    final db = await DatabaseHelper().database;
    
    // Check if vault has transactions
    final transactionCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableCashTransactions} WHERE cash_vault_id = ?',
      [id],
    );
    
    if ((transactionCount.first['count'] as int) > 0) {
      throw Exception('لا يمكن حذف الخزينة لوجود معاملات مرتبطة بها');
    }

    return await db.delete(
      DatabaseSchema.tableCashVaults,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get cash vault balance summary
  Future<Map<String, dynamic>> getCashVaultBalanceSummary() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        currency,
        COUNT(*) as vault_count,
        SUM(current_balance) as total_balance,
        AVG(current_balance) as average_balance,
        MIN(current_balance) as min_balance,
        MAX(current_balance) as max_balance
      FROM ${DatabaseSchema.tableCashVaults}
      WHERE status = 'active'
      GROUP BY currency
    ''');

    return {
      'by_currency': result,
      'total_vaults': result.fold<int>(0, (sum, row) => sum + (row['vault_count'] as int)),
    };
  }

  /// Search cash vaults
  Future<List<CashVault>> searchCashVaults(String query) async {
    final db = await DatabaseHelper().database;
    final searchQuery = '%$query%';
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableCashVaults,
      where: '''
        name LIKE ? OR 
        code LIKE ? OR 
        description LIKE ? OR 
        location LIKE ? OR
        responsible_person LIKE ?
      ''',
      whereArgs: [searchQuery, searchQuery, searchQuery, searchQuery, searchQuery],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return CashVault.fromMap(maps[i]);
    });
  }

  /// Get vaults near limit
  Future<List<CashVault>> getVaultsNearLimit() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT * FROM ${DatabaseSchema.tableCashVaults}
      WHERE max_limit IS NOT NULL 
        AND current_balance >= (max_limit * 0.9)
        AND status = 'active'
      ORDER BY (current_balance / max_limit) DESC
    ''');

    return List.generate(maps.length, (i) {
      return CashVault.fromMap(maps[i]);
    });
  }

  /// Get vaults over limit
  Future<List<CashVault>> getVaultsOverLimit() async {
    final db = await DatabaseHelper().database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT * FROM ${DatabaseSchema.tableCashVaults}
      WHERE max_limit IS NOT NULL 
        AND current_balance > max_limit
        AND status = 'active'
      ORDER BY (current_balance / max_limit) DESC
    ''');

    return List.generate(maps.length, (i) {
      return CashVault.fromMap(maps[i]);
    });
  }

  /// Get cash vaults with transaction count
  Future<List<Map<String, dynamic>>> getCashVaultsWithTransactionCount() async {
    final db = await DatabaseHelper().database;
    
    final result = await db.rawQuery('''
      SELECT 
        cv.*,
        COALESCE(ct.transaction_count, 0) as transaction_count,
        COALESCE(ct.last_transaction_date, '') as last_transaction_date
      FROM ${DatabaseSchema.tableCashVaults} cv
      LEFT JOIN (
        SELECT 
          cash_vault_id,
          COUNT(*) as transaction_count,
          MAX(transaction_date) as last_transaction_date
        FROM ${DatabaseSchema.tableCashTransactions}
        GROUP BY cash_vault_id
      ) ct ON cv.id = ct.cash_vault_id
      ORDER BY cv.name ASC
    ''');

    return result;
  }

  /// Get vault balance history
  Future<List<Map<String, dynamic>>> getVaultBalanceHistory(int vaultId, int days) async {
    final db = await DatabaseHelper().database;
    final startDate = DateTime.now().subtract(Duration(days: days));
    
    final result = await db.rawQuery('''
      SELECT 
        DATE(transaction_date) as date,
        SUM(CASE WHEN type IN ('receipt', 'withdrawal') THEN 
          CASE WHEN type = 'receipt' THEN amount ELSE -amount END
        ELSE 0 END) as net_change
      FROM ${DatabaseSchema.tableCashTransactions}
      WHERE cash_vault_id = ? 
        AND status = 'completed'
        AND transaction_date >= ?
      GROUP BY DATE(transaction_date)
      ORDER BY date ASC
    ''', [vaultId, startDate.toIso8601String().split('T')[0]]);

    return result;
  }
}
