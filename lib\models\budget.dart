/// نماذج نظام الموازنات والتخطيط المالي
/// Budget and Financial Planning Models for Smart Ledger
library;

import 'account.dart';

/// نموذج الموازنة الرئيسية
class Budget {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final BudgetType type;
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final BudgetStatus status;
  final String? approvedBy;
  final DateTime? approvedAt;
  final String? createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  List<BudgetLine> lines = [];
  List<BudgetRevision> revisions = [];

  Budget({
    this.id,
    required this.code,
    required this.name,
    this.description,
    required this.type,
    required this.period,
    required this.startDate,
    required this.endDate,
    this.status = BudgetStatus.draft,
    this.approvedBy,
    this.approvedAt,
    this.createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<BudgetLine>? lines,
    List<BudgetRevision>? revisions,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now() {
    if (lines != null) this.lines = lines;
    if (revisions != null) this.revisions = revisions;
  }

  factory Budget.fromMap(Map<String, dynamic> map) {
    return Budget(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      type: BudgetType.fromString(map['type'] as String),
      period: BudgetPeriod.fromString(map['period'] as String),
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      status: BudgetStatus.fromString(map['status'] as String),
      approvedBy: map['approved_by'] as String?,
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      createdBy: map['created_by'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'type': type.value,
      'period': period.value,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status.value,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Budget copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    BudgetType? type,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    BudgetStatus? status,
    String? approvedBy,
    DateTime? approvedAt,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<BudgetLine>? lines,
    List<BudgetRevision>? revisions,
  }) {
    return Budget(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lines: lines ?? this.lines,
      revisions: revisions ?? this.revisions,
    );
  }

  /// حساب إجمالي الموازنة
  double get totalBudgetAmount {
    return lines.fold(0.0, (sum, line) => sum + line.budgetAmount);
  }

  /// حساب إجمالي المبلغ الفعلي
  double get totalActualAmount {
    return lines.fold(0.0, (sum, line) => sum + line.actualAmount);
  }

  /// حساب إجمالي الانحراف
  double get totalVariance {
    return totalActualAmount - totalBudgetAmount;
  }

  /// حساب نسبة الانحراف
  double get variancePercentage {
    if (totalBudgetAmount == 0) return 0.0;
    return (totalVariance / totalBudgetAmount) * 100;
  }

  /// التحقق من صحة الموازنة
  List<String> validate() {
    List<String> errors = [];

    if (code.trim().isEmpty) {
      errors.add('كود الموازنة مطلوب');
    }

    if (name.trim().isEmpty) {
      errors.add('اسم الموازنة مطلوب');
    }

    if (startDate.isAfter(endDate)) {
      errors.add('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
    }

    if (lines.isEmpty) {
      errors.add('يجب إضافة بنود للموازنة');
    }

    // التحقق من عدم تكرار الحسابات
    Set<int> accountIds = {};
    for (BudgetLine line in lines) {
      if (accountIds.contains(line.accountId)) {
        errors.add('لا يمكن تكرار نفس الحساب في الموازنة');
        break;
      }
      accountIds.add(line.accountId);
    }

    return errors;
  }

  @override
  String toString() {
    return 'Budget{id: $id, code: $code, name: $name, type: $type, period: $period}';
  }
}

/// بند الموازنة
class BudgetLine {
  final int? id;
  final int budgetId;
  final int accountId;
  final double budgetAmount;
  final double actualAmount;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Account? account;

  BudgetLine({
    this.id,
    required this.budgetId,
    required this.accountId,
    required this.budgetAmount,
    this.actualAmount = 0.0,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.account,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory BudgetLine.fromMap(Map<String, dynamic> map) {
    return BudgetLine(
      id: map['id'] as int?,
      budgetId: map['budget_id'] as int,
      accountId: map['account_id'] as int,
      budgetAmount: (map['budget_amount'] as num).toDouble(),
      actualAmount: (map['actual_amount'] as num?)?.toDouble() ?? 0.0,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'budget_id': budgetId,
      'account_id': accountId,
      'budget_amount': budgetAmount,
      'actual_amount': actualAmount,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// حساب الانحراف
  double get variance => actualAmount - budgetAmount;

  /// حساب نسبة الانحراف
  double get variancePercentage {
    if (budgetAmount == 0) return 0.0;
    return (variance / budgetAmount) * 100;
  }

  /// حساب نسبة التنفيذ
  double get executionPercentage {
    if (budgetAmount == 0) return 0.0;
    return (actualAmount / budgetAmount) * 100;
  }

  BudgetLine copyWith({
    int? id,
    int? budgetId,
    int? accountId,
    double? budgetAmount,
    double? actualAmount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    Account? account,
  }) {
    return BudgetLine(
      id: id ?? this.id,
      budgetId: budgetId ?? this.budgetId,
      accountId: accountId ?? this.accountId,
      budgetAmount: budgetAmount ?? this.budgetAmount,
      actualAmount: actualAmount ?? this.actualAmount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      account: account ?? this.account,
    );
  }
}

/// مراجعة الموازنة
class BudgetRevision {
  final int? id;
  final int budgetId;
  final int revisionNumber;
  final String description;
  final String? revisedBy;
  final DateTime revisionDate;
  final DateTime createdAt;

  BudgetRevision({
    this.id,
    required this.budgetId,
    required this.revisionNumber,
    required this.description,
    this.revisedBy,
    DateTime? revisionDate,
    DateTime? createdAt,
  }) : revisionDate = revisionDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now();

  factory BudgetRevision.fromMap(Map<String, dynamic> map) {
    return BudgetRevision(
      id: map['id'] as int?,
      budgetId: map['budget_id'] as int,
      revisionNumber: map['revision_number'] as int,
      description: map['description'] as String,
      revisedBy: map['revised_by'] as String?,
      revisionDate: DateTime.parse(map['revision_date'] as String),
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'budget_id': budgetId,
      'revision_number': revisionNumber,
      'description': description,
      'revised_by': revisedBy,
      'revision_date': revisionDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  BudgetRevision copyWith({
    int? id,
    int? budgetId,
    int? revisionNumber,
    String? description,
    String? revisedBy,
    DateTime? revisionDate,
    DateTime? createdAt,
  }) {
    return BudgetRevision(
      id: id ?? this.id,
      budgetId: budgetId ?? this.budgetId,
      revisionNumber: revisionNumber ?? this.revisionNumber,
      description: description ?? this.description,
      revisedBy: revisedBy ?? this.revisedBy,
      revisionDate: revisionDate ?? this.revisionDate,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// أنواع الموازنات
enum BudgetType {
  operational('operational', 'موازنة تشغيلية', 'Operational Budget'),
  capital('capital', 'موازنة رأسمالية', 'Capital Budget'),
  cash('cash', 'موازنة نقدية', 'Cash Budget'),
  master('master', 'الموازنة الرئيسية', 'Master Budget'),
  flexible('flexible', 'موازنة مرنة', 'Flexible Budget'),
  static('static', 'موازنة ثابتة', 'Static Budget'),
  rolling('rolling', 'موازنة متدحرجة', 'Rolling Budget');

  const BudgetType(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static BudgetType fromString(String value) {
    return BudgetType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => BudgetType.operational,
    );
  }

  String get displayName => nameAr;
}

/// فترات الموازنة
enum BudgetPeriod {
  monthly('monthly', 'شهرية', 'Monthly'),
  quarterly('quarterly', 'ربع سنوية', 'Quarterly'),
  semiAnnual('semi_annual', 'نصف سنوية', 'Semi-Annual'),
  annual('annual', 'سنوية', 'Annual'),
  biennial('biennial', 'كل سنتين', 'Biennial'),
  custom('custom', 'مخصصة', 'Custom');

  const BudgetPeriod(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static BudgetPeriod fromString(String value) {
    return BudgetPeriod.values.firstWhere(
      (period) => period.value == value,
      orElse: () => BudgetPeriod.annual,
    );
  }

  String get displayName => nameAr;

  /// حساب عدد الأشهر للفترة
  int get monthsCount {
    switch (this) {
      case BudgetPeriod.monthly:
        return 1;
      case BudgetPeriod.quarterly:
        return 3;
      case BudgetPeriod.semiAnnual:
        return 6;
      case BudgetPeriod.annual:
        return 12;
      case BudgetPeriod.biennial:
        return 24;
      case BudgetPeriod.custom:
        return 0; // يحدد حسب التواريخ
    }
  }
}

/// حالات الموازنة
enum BudgetStatus {
  draft('draft', 'مسودة', 'Draft'),
  submitted('submitted', 'مقدمة للمراجعة', 'Submitted'),
  underReview('under_review', 'قيد المراجعة', 'Under Review'),
  approved('approved', 'معتمدة', 'Approved'),
  active('active', 'نشطة', 'Active'),
  closed('closed', 'مغلقة', 'Closed'),
  cancelled('cancelled', 'ملغية', 'Cancelled');

  const BudgetStatus(this.value, this.nameAr, this.nameEn);

  final String value;
  final String nameAr;
  final String nameEn;

  static BudgetStatus fromString(String value) {
    return BudgetStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => BudgetStatus.draft,
    );
  }

  String get displayName => nameAr;

  /// التحقق من إمكانية التعديل
  bool get canEdit {
    return this == BudgetStatus.draft || this == BudgetStatus.submitted;
  }

  /// التحقق من إمكانية الحذف
  bool get canDelete {
    return this == BudgetStatus.draft;
  }

  /// التحقق من إمكانية الاعتماد
  bool get canApprove {
    return this == BudgetStatus.submitted || this == BudgetStatus.underReview;
  }
}
