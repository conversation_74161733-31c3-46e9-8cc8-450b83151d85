import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/department.dart';

/// DAO للأقسام
/// Department Data Access Object
class DepartmentDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج قسم جديد
  Future<int> insertDepartment(Department department) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableDepartments,
      department.toMap()..remove('id'),
    );
  }

  /// تحديث قسم
  Future<int> updateDepartment(Department department) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableDepartments,
      department.toMap(),
      where: 'id = ?',
      whereArgs: [department.id],
    );
  }

  /// حذف قسم
  Future<int> deleteDepartment(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      DatabaseSchema.tableDepartments,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على قسم بالمعرف
  Future<Department?> getDepartmentById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDepartments,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Department.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على قسم بالرمز
  Future<Department?> getDepartmentByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDepartments,
      where: 'code = ?',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Department.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDepartments,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Department.fromMap(maps[i]);
    });
  }

  /// الحصول على الأقسام النشطة فقط
  Future<List<Department>> getActiveDepartments() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDepartments,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Department.fromMap(maps[i]);
    });
  }

  /// الحصول على الأقسام الفرعية
  Future<List<Department>> getSubDepartments(int parentId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDepartments,
      where: 'parent_department_id = ? AND is_active = 1',
      whereArgs: [parentId],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Department.fromMap(maps[i]);
    });
  }

  /// الحصول على الأقسام الرئيسية (بدون قسم أب)
  Future<List<Department>> getRootDepartments() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDepartments,
      where: 'parent_department_id IS NULL AND is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Department.fromMap(maps[i]);
    });
  }

  /// البحث في الأقسام
  Future<List<Department>> searchDepartments(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableDepartments,
      where: 'name LIKE ? OR code LIKE ? OR description LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Department.fromMap(maps[i]);
    });
  }

  /// الحصول على عدد الموظفين في القسم
  Future<int> getEmployeeCount(int departmentId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableEmployees} WHERE department_id = ? AND is_active = 1',
      [departmentId],
    );
    return result.first['count'] as int;
  }

  /// الحصول على إجمالي رواتب القسم
  Future<double> getTotalSalaries(int departmentId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(basic_salary) as total FROM ${DatabaseSchema.tableEmployees} WHERE department_id = ? AND is_active = 1',
      [departmentId],
    );
    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  /// تحديث حالة القسم (تفعيل/إلغاء تفعيل)
  Future<int> updateDepartmentStatus(int id, bool isActive) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableDepartments,
      {
        'is_active': isActive ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على الأقسام مع عدد الموظفين
  Future<List<Map<String, dynamic>>> getDepartmentsWithEmployeeCount() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        d.*,
        COUNT(e.id) as employee_count,
        SUM(e.basic_salary) as total_salaries
      FROM ${DatabaseSchema.tableDepartments} d
      LEFT JOIN ${DatabaseSchema.tableEmployees} e ON d.id = e.department_id AND e.is_active = 1
      WHERE d.is_active = 1
      GROUP BY d.id
      ORDER BY d.name ASC
    ''');

    return maps;
  }

  /// التحقق من إمكانية حذف القسم
  Future<bool> canDeleteDepartment(int id) async {
    final employeeCount = await getEmployeeCount(id);
    final subDepartments = await getSubDepartments(id);
    return employeeCount == 0 && subDepartments.isEmpty;
  }

  /// الحصول على التسلسل الهرمي للقسم
  Future<List<Department>> getDepartmentHierarchy(int departmentId) async {
    final List<Department> hierarchy = [];
    Department? current = await getDepartmentById(departmentId);

    while (current != null) {
      hierarchy.insert(0, current);
      if (current.parentDepartmentId != null) {
        current = await getDepartmentById(current.parentDepartmentId!);
      } else {
        current = null;
      }
    }

    return hierarchy;
  }

  /// الحصول على جميع الأقسام الفرعية (بشكل تكراري)
  Future<List<Department>> getAllSubDepartments(int parentId) async {
    final List<Department> allSubDepartments = [];
    final directSubs = await getSubDepartments(parentId);

    for (final dept in directSubs) {
      allSubDepartments.add(dept);
      if (dept.id != null) {
        final subSubs = await getAllSubDepartments(dept.id!);
        allSubDepartments.addAll(subSubs);
      }
    }

    return allSubDepartments;
  }

  /// إحصائيات الأقسام
  Future<Map<String, dynamic>> getDepartmentStatistics() async {
    final db = await _databaseHelper.database;

    final totalDepartments = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableDepartments} WHERE is_active = 1',
    );

    final totalEmployees = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseSchema.tableEmployees} WHERE is_active = 1',
    );

    final totalSalaries = await db.rawQuery(
      'SELECT SUM(basic_salary) as total FROM ${DatabaseSchema.tableEmployees} WHERE is_active = 1',
    );

    return {
      'total_departments': totalDepartments.first['count'] as int,
      'total_employees': totalEmployees.first['count'] as int,
      'total_salaries':
          (totalSalaries.first['total'] as num?)?.toDouble() ?? 0.0,
    };
  }
}
