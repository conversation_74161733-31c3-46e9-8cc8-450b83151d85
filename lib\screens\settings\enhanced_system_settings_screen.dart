/// شاشة إعدادات النظام المحسنة
/// Enhanced System Settings Screen
library;

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../theme/app_theme.dart';

class EnhancedSystemSettingsScreen extends StatefulWidget {
  const EnhancedSystemSettingsScreen({super.key});

  @override
  State<EnhancedSystemSettingsScreen> createState() =>
      _EnhancedSystemSettingsScreenState();
}

class _EnhancedSystemSettingsScreenState
    extends State<EnhancedSystemSettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Settings state
  bool _notificationsEnabled = true;
  bool _autoBackupEnabled = false;
  bool _darkModeEnabled = false;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _autoSyncEnabled = true;
  bool _offlineModeEnabled = false;
  bool _debugModeEnabled = false;

  String _selectedLanguage = 'ar';
  String _selectedTheme = 'light';
  String _selectedBackupFrequency = 'daily';
  int _selectedSessionTimeout = 30;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadSettings();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      setState(() {
        // Load notification settings
        _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
        _soundEnabled = prefs.getBool('sound_enabled') ?? true;
        _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;

        // Load appearance settings
        _selectedLanguage = prefs.getString('selected_language') ?? 'ar';
        _selectedTheme = prefs.getString('selected_theme') ?? 'light';
        _darkModeEnabled = prefs.getBool('dark_mode_enabled') ?? false;

        // Load backup and sync settings
        _autoBackupEnabled = prefs.getBool('auto_backup_enabled') ?? false;
        _selectedBackupFrequency =
            prefs.getString('backup_frequency') ?? 'daily';
        _autoSyncEnabled = prefs.getBool('auto_sync_enabled') ?? true;

        // Load security settings
        _selectedSessionTimeout = prefs.getInt('session_timeout') ?? 30;
        _offlineModeEnabled = prefs.getBool('offline_mode_enabled') ?? false;

        // Load developer settings
        _debugModeEnabled = prefs.getBool('debug_mode_enabled') ?? false;
      });
    } catch (e) {
      // If loading fails, keep default values
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save notification settings
      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setBool('vibration_enabled', _vibrationEnabled);

      // Save appearance settings
      await prefs.setString('selected_language', _selectedLanguage);
      await prefs.setString('selected_theme', _selectedTheme);
      await prefs.setBool('dark_mode_enabled', _darkModeEnabled);

      // Save backup and sync settings
      await prefs.setBool('auto_backup_enabled', _autoBackupEnabled);
      await prefs.setString('backup_frequency', _selectedBackupFrequency);
      await prefs.setBool('auto_sync_enabled', _autoSyncEnabled);

      // Save security settings
      await prefs.setInt('session_timeout', _selectedSessionTimeout);
      await prefs.setBool('offline_mode_enabled', _offlineModeEnabled);

      // Save developer settings
      await prefs.setBool('debug_mode_enabled', _debugModeEnabled);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      debugPrint('خطأ في حفظ الإعدادات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(),
            ),
          );
        },
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'إعدادات النظام',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            title: 'الإشعارات والتنبيهات',
            icon: Icons.notifications,
            children: [
              _buildSwitchTile(
                title: 'تفعيل الإشعارات',
                subtitle: 'استقبال إشعارات التطبيق',
                value: _notificationsEnabled,
                onChanged: (value) {
                  setState(() {
                    _notificationsEnabled = value;
                  });
                },
              ),
              _buildSwitchTile(
                title: 'الأصوات',
                subtitle: 'تشغيل أصوات التنبيهات',
                value: _soundEnabled,
                onChanged: (value) {
                  setState(() {
                    _soundEnabled = value;
                  });
                },
              ),
              _buildSwitchTile(
                title: 'الاهتزاز',
                subtitle: 'تفعيل الاهتزاز للتنبيهات',
                value: _vibrationEnabled,
                onChanged: (value) {
                  setState(() {
                    _vibrationEnabled = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: 'المظهر والواجهة',
            icon: Icons.palette,
            children: [
              _buildLanguageSelector(),
              const SizedBox(height: 16),
              _buildThemeSelector(),
              const SizedBox(height: 16),
              _buildSwitchTile(
                title: 'الوضع المظلم',
                subtitle: 'استخدام المظهر المظلم',
                value: _darkModeEnabled,
                onChanged: (value) {
                  setState(() {
                    _darkModeEnabled = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: 'النسخ الاحتياطي والمزامنة',
            icon: Icons.backup,
            children: [
              _buildSwitchTile(
                title: 'النسخ الاحتياطي التلقائي',
                subtitle: 'إنشاء نسخة احتياطية تلقائياً',
                value: _autoBackupEnabled,
                onChanged: (value) {
                  setState(() {
                    _autoBackupEnabled = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              _buildBackupFrequencySelector(),
              const SizedBox(height: 16),
              _buildSwitchTile(
                title: 'المزامنة التلقائية',
                subtitle: 'مزامنة البيانات تلقائياً',
                value: _autoSyncEnabled,
                onChanged: (value) {
                  setState(() {
                    _autoSyncEnabled = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: 'الأمان والخصوصية',
            icon: Icons.security,
            children: [
              _buildSessionTimeoutSelector(),
              const SizedBox(height: 16),
              _buildSwitchTile(
                title: 'الوضع غير المتصل',
                subtitle: 'العمل بدون اتصال بالإنترنت',
                value: _offlineModeEnabled,
                onChanged: (value) {
                  setState(() {
                    _offlineModeEnabled = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: 'إعدادات المطور',
            icon: Icons.developer_mode,
            children: [
              _buildSwitchTile(
                title: 'وضع التطوير',
                subtitle: 'تفعيل خيارات المطور المتقدمة',
                value: _debugModeEnabled,
                onChanged: (value) {
                  setState(() {
                    _debugModeEnabled = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 100), // Space for FAB
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Colors.grey[50]!],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: AppTheme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: SwitchListTile(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[600])),
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    final languages = [
      {'code': 'ar', 'name': 'العربية'},
      {'code': 'en', 'name': 'English'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedLanguage,
      decoration: InputDecoration(
        labelText: 'اللغة',
        prefixIcon: Icon(Icons.language, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: languages.map((language) {
        return DropdownMenuItem<String>(
          value: language['code'],
          child: Text(language['name']!),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedLanguage = value;
          });
        }
      },
    );
  }

  Widget _buildThemeSelector() {
    final themes = [
      {'value': 'light', 'name': 'فاتح'},
      {'value': 'dark', 'name': 'مظلم'},
      {'value': 'auto', 'name': 'تلقائي'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedTheme,
      decoration: InputDecoration(
        labelText: 'المظهر',
        prefixIcon: Icon(Icons.color_lens, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: themes.map((theme) {
        return DropdownMenuItem<String>(
          value: theme['value'],
          child: Text(theme['name']!),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedTheme = value;
          });
        }
      },
    );
  }

  Widget _buildBackupFrequencySelector() {
    final frequencies = [
      {'value': 'daily', 'name': 'يومياً'},
      {'value': 'weekly', 'name': 'أسبوعياً'},
      {'value': 'monthly', 'name': 'شهرياً'},
      {'value': 'manual', 'name': 'يدوياً'},
    ];

    return DropdownButtonFormField<String>(
      value: _selectedBackupFrequency,
      decoration: InputDecoration(
        labelText: 'تكرار النسخ الاحتياطي',
        prefixIcon: Icon(Icons.schedule, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: frequencies.map((frequency) {
        return DropdownMenuItem<String>(
          value: frequency['value'],
          child: Text(frequency['name']!),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedBackupFrequency = value;
          });
        }
      },
    );
  }

  Widget _buildSessionTimeoutSelector() {
    final timeouts = [15, 30, 60, 120, 0]; // 0 means never

    return DropdownButtonFormField<int>(
      value: _selectedSessionTimeout,
      decoration: InputDecoration(
        labelText: 'انتهاء الجلسة (دقيقة)',
        prefixIcon: Icon(Icons.timer, color: AppTheme.primaryColor),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: timeouts.map((timeout) {
        return DropdownMenuItem<int>(
          value: timeout,
          child: Text(timeout == 0 ? 'أبداً' : '$timeout دقيقة'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedSessionTimeout = value;
          });
        }
      },
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _saveSettings,
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.save),
      label: const Text('حفظ الإعدادات'),
    );
  }
}
