/// خدمة الموازنات والتخطيط المالي
/// Budget Service for Smart Ledger
library;

import '../dao/budget_dao.dart';
import '../database/account_dao.dart';
import '../database/journal_entry_dao.dart';
import '../models/budget.dart';
import '../models/budget_reports.dart';
import '../models/account.dart';
import '../utils/result.dart';

class BudgetService {
  static final BudgetService _instance = BudgetService._internal();
  factory BudgetService() => _instance;
  BudgetService._internal();

  final BudgetDao _budgetDao = BudgetDao();
  final AccountDao _accountDao = AccountDao();
  final JournalEntryDao _journalEntryDao = JournalEntryDao();

  /// إنشاء موازنة جديدة
  Future<Result<Budget>> createBudget(Budget budget) async {
    try {
      // التحقق من صحة البيانات
      final validationErrors = budget.validate();
      if (validationErrors.isNotEmpty) {
        return Result.error('خطأ في البيانات: ${validationErrors.join(', ')}');
      }

      // التحقق من وجود الحسابات
      for (var line in budget.lines) {
        final account = await _accountDao.getAccountById(line.accountId);
        if (account == null) {
          return Result.error('الحساب غير موجود: ${line.accountId}');
        }
        line.account = account;
      }

      // إنشاء الموازنة
      return await _budgetDao.createBudget(budget);
    } catch (e) {
      return Result.error('خطأ في إنشاء الموازنة: ${e.toString()}');
    }
  }

  /// تحديث موازنة
  Future<Result<Budget>> updateBudget(Budget budget) async {
    try {
      // التحقق من صحة البيانات
      final validationErrors = budget.validate();
      if (validationErrors.isNotEmpty) {
        return Result.error('خطأ في البيانات: ${validationErrors.join(', ')}');
      }

      // التحقق من وجود الحسابات
      for (var line in budget.lines) {
        final account = await _accountDao.getAccountById(line.accountId);
        if (account == null) {
          return Result.error('الحساب غير موجود: ${line.accountId}');
        }
        line.account = account;
      }

      return await _budgetDao.updateBudget(budget);
    } catch (e) {
      return Result.error('خطأ في تحديث الموازنة: ${e.toString()}');
    }
  }

  /// حذف موازنة
  Future<Result<void>> deleteBudget(int budgetId) async {
    return await _budgetDao.deleteBudget(budgetId);
  }

  /// جلب موازنة بالمعرف
  Future<Result<Budget>> getBudgetById(int id) async {
    return await _budgetDao.getBudgetById(id);
  }

  /// جلب موازنة بالكود
  Future<Result<Budget>> getBudgetByCode(String code) async {
    return await _budgetDao.getBudgetByCode(code);
  }

  /// جلب جميع الموازنات
  Future<Result<List<Budget>>> getAllBudgets({
    BudgetType? type,
    BudgetStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    return await _budgetDao.getAllBudgets(
      type: type,
      status: status,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
      offset: offset,
    );
  }

  /// اعتماد موازنة
  Future<Result<Budget>> approveBudget(int budgetId, String approvedBy) async {
    return await _budgetDao.approveBudget(budgetId, approvedBy);
  }

  /// تفعيل موازنة
  Future<Result<Budget>> activateBudget(int budgetId) async {
    return await _budgetDao.activateBudget(budgetId);
  }

  /// إضافة مراجعة للموازنة
  Future<Result<BudgetRevision>> addBudgetRevision(
    int budgetId,
    String description,
    String? revisedBy,
  ) async {
    final revision = BudgetRevision(
      budgetId: budgetId,
      revisionNumber: 0, // سيتم حسابه في DAO
      description: description,
      revisedBy: revisedBy,
    );

    return await _budgetDao.addBudgetRevision(revision);
  }

  /// تحديث المبالغ الفعلية تلقائياً
  Future<Result<void>> updateActualAmountsAutomatically(int budgetId) async {
    try {
      // جلب الموازنة
      final budgetResult = await _budgetDao.getBudgetById(budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error(budgetResult.error!);
      }

      final budget = budgetResult.data!;

      // حساب المبالغ الفعلية
      final actualAmountsResult = await _budgetDao.calculateActualAmounts(
        budgetId,
        budget.startDate,
        budget.endDate,
      );

      if (!actualAmountsResult.isSuccess) {
        return Result.error(actualAmountsResult.error!);
      }

      // تحديث المبالغ الفعلية
      return await _budgetDao.updateActualAmounts(
        budgetId,
        actualAmountsResult.data!,
      );
    } catch (e) {
      return Result.error('خطأ في تحديث المبالغ الفعلية: ${e.toString()}');
    }
  }

  /// إنشاء تقرير الموازنة مقابل الفعلي
  Future<Result<BudgetVsActualReport>> generateBudgetVsActualReport({
    required int budgetId,
    bool updateActualAmounts = true,
  }) async {
    try {
      // تحديث المبالغ الفعلية إذا طُلب ذلك
      if (updateActualAmounts) {
        await updateActualAmountsAutomatically(budgetId);
      }

      // جلب الموازنة
      final budgetResult = await _budgetDao.getBudgetById(budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error(budgetResult.error!);
      }

      final budget = budgetResult.data!;

      // إنشاء بنود التقرير
      List<BudgetVsActualItem> items = [];
      for (var line in budget.lines) {
        if (line.account != null) {
          final variance = line.actualAmount - line.budgetAmount;
          final variancePercentage = line.budgetAmount == 0
              ? 0.0
              : (variance / line.budgetAmount) * 100;

          items.add(
            BudgetVsActualItem(
              accountCode: line.account!.code,
              accountName: line.account!.name,
              accountType: line.account!.accountType,
              budgetAmount: line.budgetAmount,
              actualAmount: line.actualAmount,
              variance: variance,
              variancePercentage: variancePercentage,
              account: line.account,
            ),
          );
        }
      }

      // حساب الملخص
      final totalBudgetAmount = budget.totalBudgetAmount;
      final totalActualAmount = budget.totalActualAmount;
      final totalVariance = budget.totalVariance;
      final totalVariancePercentage = budget.variancePercentage;

      int favorableVariances = 0;
      int unfavorableVariances = 0;
      int noVariances = 0;

      for (var item in items) {
        if (item.hasNoVariance) {
          noVariances++;
        } else if (item.hasPositiveVariance) {
          // للإيرادات: الزيادة مواتية، للمصروفات: الزيادة غير مواتية
          if (item.accountType == AccountType.revenue) {
            favorableVariances++;
          } else if (item.accountType == AccountType.expense) {
            unfavorableVariances++;
          } else {
            favorableVariances++; // افتراضي للأصول والخصوم
          }
        } else {
          // للإيرادات: النقص غير مواتي، للمصروفات: النقص مواتي
          if (item.accountType == AccountType.revenue) {
            unfavorableVariances++;
          } else if (item.accountType == AccountType.expense) {
            favorableVariances++;
          } else {
            unfavorableVariances++; // افتراضي للأصول والخصوم
          }
        }
      }

      final summary = BudgetVsActualSummary(
        totalBudgetAmount: totalBudgetAmount,
        totalActualAmount: totalActualAmount,
        totalVariance: totalVariance,
        totalVariancePercentage: totalVariancePercentage,
        favorableVariances: favorableVariances,
        unfavorableVariances: unfavorableVariances,
        noVariances: noVariances,
      );

      final report = BudgetVsActualReport(
        startDate: budget.startDate,
        endDate: budget.endDate,
        items: items,
        summary: summary,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير الموازنة مقابل الفعلي: ${e.toString()}',
      );
    }
  }

  /// إنشاء تقرير تحليل الانحرافات
  Future<Result<VarianceAnalysisReport>> generateVarianceAnalysisReport({
    required int budgetId,
    double significanceThreshold = 10.0,
    bool updateActualAmounts = true,
  }) async {
    try {
      // إنشاء تقرير الموازنة مقابل الفعلي أولاً
      final budgetVsActualResult = await generateBudgetVsActualReport(
        budgetId: budgetId,
        updateActualAmounts: updateActualAmounts,
      );

      if (!budgetVsActualResult.isSuccess) {
        return Result.error(budgetVsActualResult.error!);
      }

      final budgetVsActual = budgetVsActualResult.data!;

      // إنشاء بنود تحليل الانحرافات
      List<VarianceAnalysisItem> items = [];
      for (var item in budgetVsActual.items) {
        VarianceType varianceType;

        if (item.hasNoVariance) {
          varianceType = VarianceType.neutral;
        } else {
          // تحديد نوع الانحراف حسب نوع الحساب
          bool isFavorable = false;
          if (item.accountType == AccountType.revenue) {
            isFavorable = item.variance > 0; // زيادة الإيرادات مواتية
          } else if (item.accountType == AccountType.expense) {
            isFavorable = item.variance < 0; // نقص المصروفات مواتي
          } else {
            isFavorable = item.variance > 0; // افتراضي
          }

          varianceType = isFavorable
              ? VarianceType.favorable
              : VarianceType.unfavorable;
        }

        // إضافة توصيات وتفسيرات
        String? explanation;
        String? recommendation;

        if (item.variancePercentage.abs() > significanceThreshold) {
          if (item.accountType == AccountType.revenue) {
            if (item.variance > 0) {
              explanation = 'زيادة في الإيرادات عن المخطط له';
              recommendation =
                  'مراجعة استراتيجيات المبيعات والتسويق لفهم أسباب الزيادة';
            } else {
              explanation = 'نقص في الإيرادات عن المخطط له';
              recommendation = 'تحليل أسباب انخفاض الإيرادات ووضع خطط تصحيحية';
            }
          } else if (item.accountType == AccountType.expense) {
            if (item.variance > 0) {
              explanation = 'زيادة في المصروفات عن المخطط له';
              recommendation =
                  'مراجعة وتحليل أسباب زيادة المصروفات واتخاذ إجراءات تحكم';
            } else {
              explanation = 'نقص في المصروفات عن المخطط له';
              recommendation =
                  'التأكد من تنفيذ جميع الأنشطة المخططة رغم توفير التكاليف';
            }
          }
        }

        items.add(
          VarianceAnalysisItem(
            accountCode: item.accountCode,
            accountName: item.accountName,
            accountType: item.accountType,
            budgetAmount: item.budgetAmount,
            actualAmount: item.actualAmount,
            varianceAmount: item.variance,
            variancePercentage: item.variancePercentage,
            varianceType: varianceType,
            explanation: explanation,
            recommendation: recommendation,
          ),
        );
      }

      // حساب الملخص
      final totalVariances = items.length;
      final favorableVariances = items
          .where((item) => item.varianceType == VarianceType.favorable)
          .length;
      final unfavorableVariances = items
          .where((item) => item.varianceType == VarianceType.unfavorable)
          .length;
      final significantVariances = items
          .where((item) => item.isSignificant)
          .length;
      final criticalVariances = items.where((item) => item.isCritical).length;
      final totalVarianceAmount = items.fold(
        0.0,
        (sum, item) => sum + item.varianceAmount,
      );
      final averageVariancePercentage = totalVariances == 0
          ? 0.0
          : items.fold(
                  0.0,
                  (sum, item) => sum + item.variancePercentage.abs(),
                ) /
                totalVariances;

      final summary = VarianceAnalysisSummary(
        totalVariances: totalVariances,
        favorableVariances: favorableVariances,
        unfavorableVariances: unfavorableVariances,
        significantVariances: significantVariances,
        criticalVariances: criticalVariances,
        totalVarianceAmount: totalVarianceAmount,
        averageVariancePercentage: averageVariancePercentage,
      );

      final report = VarianceAnalysisReport(
        reportDate: DateTime.now(),
        items: items,
        summary: summary,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير تحليل الانحرافات: ${e.toString()}',
      );
    }
  }

  /// إنشاء تقرير التنبؤات المالية
  Future<Result<FinancialForecastReport>> generateFinancialForecastReport({
    required ForecastPeriod period,
    required ForecastMethod method,
    int historicalPeriods = 12,
  }) async {
    try {
      // جلب البيانات التاريخية
      final endDate = DateTime.now();
      final startDate = DateTime(
        endDate.year - (historicalPeriods ~/ 12),
        endDate.month,
        endDate.day,
      );

      // جلب جميع الحسابات
      final accounts = await _accountDao.getAllAccounts();
      List<ForecastItem> items = [];

      // إنشاء تنبؤات لكل حساب
      for (var account in accounts) {
        if (account.accountType == AccountType.revenue ||
            account.accountType == AccountType.expense) {
          // حساب المبلغ التاريخي
          final historicalAmount = await _calculateHistoricalAmount(
            account.id!,
            startDate,
            endDate,
          );

          // حساب التنبؤ حسب الطريقة المختارة
          final forecastData = await _calculateForecast(
            account.id!,
            historicalAmount,
            method,
            period,
            startDate,
            endDate,
          );

          items.add(
            ForecastItem(
              accountCode: account.code,
              accountName: account.name,
              accountType: account.accountType,
              historicalAmount: historicalAmount,
              forecastAmount: forecastData['amount'] as double,
              growthRate: forecastData['growthRate'] as double,
              method: method,
              confidence: forecastData['confidence'] as double,
            ),
          );
        }
      }

      // حساب الملخص
      final totalRevenueForecast = items
          .where((item) => item.accountType == AccountType.revenue)
          .fold(0.0, (sum, item) => sum + item.forecastAmount);

      final totalExpenseForecast = items
          .where((item) => item.accountType == AccountType.expense)
          .fold(0.0, (sum, item) => sum + item.forecastAmount);

      final netIncomeForecast = totalRevenueForecast - totalExpenseForecast;

      final revenueItems = items
          .where((item) => item.accountType == AccountType.revenue)
          .toList();
      final expenseItems = items
          .where((item) => item.accountType == AccountType.expense)
          .toList();

      final revenueGrowthRate = revenueItems.isEmpty
          ? 0.0
          : revenueItems.fold(0.0, (sum, item) => sum + item.growthRate) /
                revenueItems.length;

      final expenseGrowthRate = expenseItems.isEmpty
          ? 0.0
          : expenseItems.fold(0.0, (sum, item) => sum + item.growthRate) /
                expenseItems.length;

      final averageConfidence = items.isEmpty
          ? 0.0
          : items.fold(0.0, (sum, item) => sum + item.confidence) /
                items.length;

      final summary = ForecastSummary(
        totalRevenueForecast: totalRevenueForecast,
        totalExpenseForecast: totalExpenseForecast,
        netIncomeForecast: netIncomeForecast,
        revenueGrowthRate: revenueGrowthRate,
        expenseGrowthRate: expenseGrowthRate,
        averageConfidence: averageConfidence,
      );

      final report = FinancialForecastReport(
        forecastDate: DateTime.now(),
        period: period,
        items: items,
        summary: summary,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير التنبؤات المالية: ${e.toString()}',
      );
    }
  }

  /// حساب المبلغ التاريخي لحساب معين
  Future<double> _calculateHistoricalAmount(
    int accountId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      // جلب جميع القيود المرحلة في الفترة المحددة
      final entries = await _journalEntryDao.getJournalEntriesByDateRange(
        startDate,
        endDate,
      );

      double totalDebit = 0.0;
      double totalCredit = 0.0;

      for (var entry in entries) {
        // التأكد من أن القيد مرحل
        if (entry.isPosted) {
          for (var line in entry.lines) {
            if (line.accountId == accountId) {
              totalDebit += line.debitAmount;
              totalCredit += line.creditAmount;
            }
          }
        }
      }

      return totalDebit - totalCredit;
    } catch (e) {
      return 0.0;
    }
  }

  /// حساب التنبؤ حسب الطريقة المختارة
  Future<Map<String, double>> _calculateForecast(
    int accountId,
    double historicalAmount,
    ForecastMethod method,
    ForecastPeriod period,
    DateTime startDate,
    DateTime endDate,
  ) async {
    double forecastAmount = historicalAmount;
    double growthRate = 0.0;
    double confidence = 0.7; // ثقة افتراضية

    switch (method) {
      case ForecastMethod.historical:
        // استخدام المتوسط التاريخي
        forecastAmount = historicalAmount;
        growthRate = 0.0;
        confidence = 0.8;
        break;

      case ForecastMethod.trend:
        // حساب الاتجاه العام
        final trendData = await _calculateTrend(accountId, startDate, endDate);
        growthRate = trendData['growthRate'] ?? 0.0;
        forecastAmount = historicalAmount * (1 + growthRate / 100);
        confidence = trendData['confidence'] ?? 0.7;
        break;

      case ForecastMethod.seasonal:
        // تحليل الموسمية
        final seasonalData = await _calculateSeasonal(
          accountId,
          startDate,
          endDate,
        );
        final seasonalFactor = seasonalData['factor'] ?? 1.0;
        forecastAmount = historicalAmount * seasonalFactor;
        growthRate = (seasonalFactor - 1) * 100;
        confidence = seasonalData['confidence'] ?? 0.6;
        break;

      case ForecastMethod.regression:
        // تحليل الانحدار البسيط
        final regressionData = await _calculateRegression(
          accountId,
          startDate,
          endDate,
        );
        forecastAmount = regressionData['forecast'] ?? historicalAmount;
        growthRate = regressionData['growthRate'] ?? 0.0;
        confidence = regressionData['confidence'] ?? 0.75;
        break;

      case ForecastMethod.expert:
        // تقدير خبير (يمكن تحسينه لاحقاً)
        growthRate = 5.0; // نمو افتراضي 5%
        forecastAmount = historicalAmount * 1.05;
        confidence = 0.6;
        break;
    }

    return {
      'amount': forecastAmount,
      'growthRate': growthRate,
      'confidence': confidence,
    };
  }

  /// حساب الاتجاه العام
  Future<Map<String, double>> _calculateTrend(
    int accountId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // تنفيذ مبسط لحساب الاتجاه
    // يمكن تحسينه لاستخدام خوارزميات أكثر تعقيداً

    final monthsDiff = endDate.difference(startDate).inDays / 30;
    if (monthsDiff < 3) {
      return {'growthRate': 0.0, 'confidence': 0.5};
    }

    // حساب النمو الشهري المتوسط (مبسط)
    final growthRate = (monthsDiff > 0)
        ? (5.0 / monthsDiff) * 12
        : 0.0; // نمو افتراضي
    final confidence = monthsDiff >= 12 ? 0.8 : 0.6;

    return {'growthRate': growthRate, 'confidence': confidence};
  }

  /// حساب العامل الموسمي
  Future<Map<String, double>> _calculateSeasonal(
    int accountId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // تنفيذ مبسط للموسمية
    final currentMonth = DateTime.now().month;

    // عوامل موسمية افتراضية (يمكن تحسينها بناءً على البيانات الفعلية)
    final seasonalFactors = {
      1: 0.9, // يناير
      2: 0.85, // فبراير
      3: 1.0, // مارس
      4: 1.05, // أبريل
      5: 1.1, // مايو
      6: 1.15, // يونيو
      7: 1.2, // يوليو
      8: 1.15, // أغسطس
      9: 1.1, // سبتمبر
      10: 1.05, // أكتوبر
      11: 1.0, // نوفمبر
      12: 1.1, // ديسمبر
    };

    final factor = seasonalFactors[currentMonth] ?? 1.0;

    return {'factor': factor, 'confidence': 0.6};
  }

  /// حساب الانحدار البسيط
  Future<Map<String, double>> _calculateRegression(
    int accountId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // تنفيذ مبسط للانحدار الخطي
    // في التطبيق الحقيقي، يجب استخدام مكتبة إحصائية متخصصة

    final monthsDiff = endDate.difference(startDate).inDays / 30;
    if (monthsDiff < 6) {
      return {'forecast': 0.0, 'growthRate': 0.0, 'confidence': 0.5};
    }

    // حساب مبسط للانحدار
    final growthRate = 3.0; // نمو افتراضي 3%
    final forecast = 0.0; // سيتم حسابه في الدالة الرئيسية
    final confidence = monthsDiff >= 12 ? 0.75 : 0.6;

    return {
      'forecast': forecast,
      'growthRate': growthRate,
      'confidence': confidence,
    };
  }

  /// جلب الموازنة النشطة حسب النوع
  Future<Result<Budget?>> getActiveBudget(BudgetType type) async {
    return await _budgetDao.getActiveBudget(type);
  }

  /// جلب إحصائيات الموازنات
  Future<Result<Map<String, dynamic>>> getBudgetStatistics() async {
    return await _budgetDao.getBudgetStatistics();
  }

  /// إنشاء موازنة من قالب
  Future<Result<Budget>> createBudgetFromTemplate({
    required String code,
    required String name,
    required BudgetType type,
    required BudgetPeriod period,
    required DateTime startDate,
    required DateTime endDate,
    required int templateBudgetId,
    double adjustmentPercentage = 0.0,
  }) async {
    try {
      // جلب الموازنة المرجعية
      final templateResult = await _budgetDao.getBudgetById(templateBudgetId);
      if (!templateResult.isSuccess) {
        return Result.error('الموازنة المرجعية غير موجودة');
      }

      final template = templateResult.data!;

      // إنشاء بنود جديدة بناءً على القالب
      List<BudgetLine> newLines = [];
      for (var templateLine in template.lines) {
        final adjustedAmount =
            templateLine.budgetAmount * (1 + adjustmentPercentage / 100);

        newLines.add(
          BudgetLine(
            budgetId: 0, // سيتم تحديثه عند الإنشاء
            accountId: templateLine.accountId,
            budgetAmount: adjustedAmount,
            notes: templateLine.notes,
          ),
        );
      }

      // إنشاء الموازنة الجديدة
      final newBudget = Budget(
        code: code,
        name: name,
        description: 'موازنة منشأة من قالب: ${template.name}',
        type: type,
        period: period,
        startDate: startDate,
        endDate: endDate,
        lines: newLines,
      );

      return await createBudget(newBudget);
    } catch (e) {
      return Result.error('خطأ في إنشاء الموازنة من القالب: ${e.toString()}');
    }
  }
}
