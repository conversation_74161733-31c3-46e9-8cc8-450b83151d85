/// خدمة إدارة المشاريع والتكاليف
/// Project and Cost Management Service for Smart Ledger
library;

import '../models/project.dart';
import '../models/project_cost.dart';
import '../dao/project_dao.dart';

class ProjectService {
  static final ProjectService _instance = ProjectService._internal();
  factory ProjectService() => _instance;
  ProjectService._internal();

  final ProjectDao _projectDao = ProjectDao();

  /// الحصول على ProjectDao للوصول المباشر
  ProjectDao get projectDao => _projectDao;

  /// إنشاء مشروع جديد
  Future<Result<Project>> createProject(Project project) async {
    try {
      // التحقق من عدم تكرار الكود
      final existingProject = await _projectDao.getProjectByCode(project.code);
      if (existingProject != null) {
        return Result.error('كود المشروع موجود مسبقاً');
      }

      // التحقق من صحة البيانات
      final validation = _validateProject(project);
      if (!validation.isSuccess) {
        return validation;
      }

      final projectId = await _projectDao.createProject(project);
      final createdProject = await _projectDao.getProjectById(projectId);

      if (createdProject != null) {
        return Result.success(createdProject);
      } else {
        return Result.error('فشل في إنشاء المشروع');
      }
    } catch (e) {
      return Result.error('خطأ في إنشاء المشروع: ${e.toString()}');
    }
  }

  /// تحديث مشروع
  Future<Result<Project>> updateProject(Project project) async {
    try {
      // التحقق من وجود المشروع
      final existingProject = await _projectDao.getProjectById(project.id!);
      if (existingProject == null) {
        return Result.error('المشروع غير موجود');
      }

      // التحقق من عدم تكرار الكود (إذا تم تغييره)
      if (project.code != existingProject.code) {
        final duplicateProject = await _projectDao.getProjectByCode(
          project.code,
        );
        if (duplicateProject != null) {
          return Result.error('كود المشروع موجود مسبقاً');
        }
      }

      // التحقق من صحة البيانات
      final validation = _validateProject(project);
      if (!validation.isSuccess) {
        return validation;
      }

      await _projectDao.updateProject(project);
      final updatedProject = await _projectDao.getProjectById(project.id!);

      if (updatedProject != null) {
        return Result.success(updatedProject);
      } else {
        return Result.error('فشل في تحديث المشروع');
      }
    } catch (e) {
      return Result.error('خطأ في تحديث المشروع: ${e.toString()}');
    }
  }

  /// حذف مشروع
  Future<Result<void>> deleteProject(int projectId) async {
    try {
      // التحقق من وجود المشروع
      final project = await _projectDao.getProjectById(projectId);
      if (project == null) {
        return Result.error('المشروع غير موجود');
      }

      // التحقق من عدم وجود تكاليف مرتبطة
      final costs = await _projectDao.getProjectCosts(projectId);
      if (costs.isNotEmpty) {
        return Result.error('لا يمكن حذف المشروع لوجود تكاليف مرتبطة به');
      }

      await _projectDao.deleteProject(projectId);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف المشروع: ${e.toString()}');
    }
  }

  /// الحصول على مشروع بالمعرف
  Future<Result<Project>> getProjectById(int id) async {
    try {
      final project = await _projectDao.getProjectById(id);
      if (project != null) {
        return Result.success(project);
      } else {
        return Result.error('المشروع غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب المشروع: ${e.toString()}');
    }
  }

  /// الحصول على جميع المشاريع
  Future<Result<List<Project>>> getAllProjects({
    bool activeOnly = false,
    ProjectStatus? status,
    int? customerId,
    String? searchTerm,
  }) async {
    try {
      final projects = await _projectDao.getAllProjects(
        activeOnly: activeOnly,
        status: status,
        customerId: customerId,
        searchTerm: searchTerm,
      );
      return Result.success(projects);
    } catch (e) {
      return Result.error('خطأ في جلب المشاريع: ${e.toString()}');
    }
  }

  /// إضافة تكلفة للمشروع
  Future<Result<ProjectCost>> addProjectCost(ProjectCost cost) async {
    try {
      // التحقق من وجود المشروع
      final project = await _projectDao.getProjectById(cost.projectId);
      if (project == null) {
        return Result.error('المشروع غير موجود');
      }

      // التحقق من صحة البيانات
      final validation = _validateProjectCost(cost);
      if (!validation.isSuccess) {
        return validation;
      }

      final costId = await _projectDao.createProjectCost(cost);

      // تحديث التكلفة الفعلية للمشروع
      await _updateProjectActualCost(cost.projectId);

      // إنشاء قيد محاسبي للتكلفة
      await _createCostJournalEntry(cost);

      final createdCost = ProjectCost(
        id: costId,
        projectId: cost.projectId,
        phaseId: cost.phaseId,
        description: cost.description,
        costType: cost.costType,
        category: cost.category,
        amount: cost.amount,
        quantity: cost.quantity,
        unitCost: cost.unitCost,
        date: cost.date,
        accountId: cost.accountId,
        itemId: cost.itemId,
        supplierId: cost.supplierId,
        reference: cost.reference,
        isBillable: cost.isBillable,
        isApproved: cost.isApproved,
        approvedBy: cost.approvedBy,
        approvedAt: cost.approvedAt,
        notes: cost.notes,
      );

      return Result.success(createdCost);
    } catch (e) {
      return Result.error('خطأ في إضافة التكلفة: ${e.toString()}');
    }
  }

  /// تحديث تكلفة المشروع
  Future<Result<ProjectCost>> updateProjectCost(ProjectCost cost) async {
    try {
      // التحقق من وجود التكلفة
      final existingCosts = await _projectDao.getProjectCosts(cost.projectId);
      final existingCost = existingCosts
          .where((c) => c.id == cost.id)
          .firstOrNull;
      if (existingCost == null) {
        return Result.error('التكلفة غير موجودة');
      }

      // التحقق من صحة البيانات
      final validation = _validateProjectCost(cost);
      if (!validation.isSuccess) {
        return validation;
      }

      await _projectDao.updateProjectCost(cost);

      // تحديث التكلفة الفعلية للمشروع
      await _updateProjectActualCost(cost.projectId);

      return Result.success(cost);
    } catch (e) {
      return Result.error('خطأ في تحديث التكلفة: ${e.toString()}');
    }
  }

  /// حذف تكلفة المشروع
  Future<Result<void>> deleteProjectCost(int costId, int projectId) async {
    try {
      await _projectDao.deleteProjectCost(costId);

      // تحديث التكلفة الفعلية للمشروع
      await _updateProjectActualCost(projectId);

      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف التكلفة: ${e.toString()}');
    }
  }

  /// حساب ربحية المشروع
  Future<Result<ProjectProfitability>> calculateProjectProfitability(
    int projectId,
  ) async {
    try {
      final project = await _projectDao.getProjectById(projectId);
      if (project == null) {
        return Result.error('المشروع غير موجود');
      }

      // حساب إجمالي الإيرادات من الفواتير المرتبطة بالمشروع
      final revenues = await _calculateProjectRevenues(projectId);

      // حساب إجمالي التكاليف
      final totalCosts = await _projectDao.calculateProjectTotalCost(projectId);

      // حساب التكاليف حسب النوع
      final costsByType = await _projectDao.calculateProjectCostsByType(
        projectId,
      );

      final profitability = ProjectProfitability(
        projectId: projectId,
        totalRevenue: revenues,
        totalCosts: totalCosts,
        grossProfit: revenues - totalCosts,
        profitMargin: revenues > 0
            ? ((revenues - totalCosts) / revenues) * 100
            : 0,
        costsByType: costsByType,
        budgetVariance: project.budgetAmount - totalCosts,
        budgetVariancePercentage: project.budgetAmount > 0
            ? ((project.budgetAmount - totalCosts) / project.budgetAmount) * 100
            : 0,
      );

      return Result.success(profitability);
    } catch (e) {
      return Result.error('خطأ في حساب الربحية: ${e.toString()}');
    }
  }

  /// إنشاء مرحلة مشروع
  Future<Result<ProjectPhase>> createProjectPhase(ProjectPhase phase) async {
    try {
      // التحقق من وجود المشروع
      final project = await _projectDao.getProjectById(phase.projectId);
      if (project == null) {
        return Result.error('المشروع غير موجود');
      }

      final phaseId = await _projectDao.createProjectPhase(phase);
      final createdPhase = ProjectPhase(
        id: phaseId,
        projectId: phase.projectId,
        name: phase.name,
        description: phase.description,
        startDate: phase.startDate,
        endDate: phase.endDate,
        actualEndDate: phase.actualEndDate,
        budgetAmount: phase.budgetAmount,
        actualCost: phase.actualCost,
        weight: phase.weight,
        completionPercentage: phase.completionPercentage,
        status: phase.status,
        notes: phase.notes,
      );

      return Result.success(createdPhase);
    } catch (e) {
      return Result.error('خطأ في إنشاء المرحلة: ${e.toString()}');
    }
  }

  /// تحديث مرحلة مشروع
  Future<Result<ProjectPhase>> updateProjectPhase(ProjectPhase phase) async {
    try {
      // التحقق من وجود المرحلة
      final existingPhases = await _projectDao.getProjectPhases(
        phase.projectId,
      );
      final existingPhase = existingPhases
          .where((p) => p.id == phase.id)
          .firstOrNull;
      if (existingPhase == null) {
        return Result.error('المرحلة غير موجودة');
      }

      await _projectDao.updateProjectPhase(phase);
      return Result.success(phase);
    } catch (e) {
      return Result.error('خطأ في تحديث المرحلة: ${e.toString()}');
    }
  }

  /// حذف مرحلة مشروع
  Future<Result<void>> deleteProjectPhase(int phaseId) async {
    try {
      await _projectDao.deleteProjectPhase(phaseId);
      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف المرحلة: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات المشاريع
  Future<Result<Map<String, dynamic>>> getProjectStatistics() async {
    try {
      final statistics = await _projectDao.getProjectStatistics();
      return Result.success(statistics);
    } catch (e) {
      return Result.error('خطأ في جلب الإحصائيات: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات المشروع
  Result<Project> _validateProject(Project project) {
    if (project.code.trim().isEmpty) {
      return Result.error('كود المشروع مطلوب');
    }

    if (project.name.trim().isEmpty) {
      return Result.error('اسم المشروع مطلوب');
    }

    if (project.budgetAmount < 0) {
      return Result.error('ميزانية المشروع يجب أن تكون أكبر من أو تساوي صفر');
    }

    if (project.endDate != null &&
        project.endDate!.isBefore(project.startDate)) {
      return Result.error('تاريخ انتهاء المشروع يجب أن يكون بعد تاريخ البداية');
    }

    return Result.success(project);
  }

  /// التحقق من صحة بيانات تكلفة المشروع
  Result<ProjectCost> _validateProjectCost(ProjectCost cost) {
    if (cost.description.trim().isEmpty) {
      return Result.error('وصف التكلفة مطلوب');
    }

    if (cost.amount <= 0) {
      return Result.error('مبلغ التكلفة يجب أن يكون أكبر من صفر');
    }

    if (cost.quantity <= 0) {
      return Result.error('الكمية يجب أن تكون أكبر من صفر');
    }

    return Result.success(cost);
  }

  /// تحديث التكلفة الفعلية للمشروع
  Future<void> _updateProjectActualCost(int projectId) async {
    final totalCost = await _projectDao.calculateProjectTotalCost(projectId);
    final project = await _projectDao.getProjectById(projectId);

    if (project != null) {
      final updatedProject = project.copyWith(actualCost: totalCost);
      await _projectDao.updateProject(updatedProject);
    }
  }

  /// حساب إيرادات المشروع
  Future<double> _calculateProjectRevenues(int projectId) async {
    // هنا يمكن إضافة منطق حساب الإيرادات من الفواتير المرتبطة بالمشروع
    // في المستقبل عندما نضيف حقل project_id للفواتير
    return 0.0;
  }

  /// إنشاء قيد محاسبي للتكلفة
  Future<void> _createCostJournalEntry(ProjectCost cost) async {
    // هنا يمكن إضافة منطق إنشاء القيود المحاسبية للتكاليف
    // في المستقبل عندما نربط التكاليف بالحسابات المحاسبية
  }

  // ==================== إدارة مهام المشروع ====================

  /// إنشاء مهمة جديدة
  Future<Result<ProjectTask>> createProjectTask(ProjectTask task) async {
    try {
      // التحقق من صحة البيانات
      final validation = _validateProjectTask(task);
      if (!validation.isSuccess) {
        return validation;
      }

      final taskId = await _projectDao.createProjectTask(task);
      final createdTask = await _projectDao.getProjectTaskById(taskId);

      if (createdTask != null) {
        return Result.success(createdTask);
      } else {
        return Result.error('فشل في إنشاء المهمة');
      }
    } catch (e) {
      return Result.error('خطأ في إنشاء المهمة: ${e.toString()}');
    }
  }

  /// تحديث مهمة
  Future<Result<ProjectTask>> updateProjectTask(ProjectTask task) async {
    try {
      // التحقق من وجود المهمة
      final existingTask = await _projectDao.getProjectTaskById(task.id!);
      if (existingTask == null) {
        return Result.error('المهمة غير موجودة');
      }

      // التحقق من صحة البيانات
      final validation = _validateProjectTask(task);
      if (!validation.isSuccess) {
        return validation;
      }

      await _projectDao.updateProjectTask(task);
      final updatedTask = await _projectDao.getProjectTaskById(task.id!);

      if (updatedTask != null) {
        return Result.success(updatedTask);
      } else {
        return Result.error('فشل في تحديث المهمة');
      }
    } catch (e) {
      return Result.error('خطأ في تحديث المهمة: ${e.toString()}');
    }
  }

  /// حذف مهمة
  Future<Result<bool>> deleteProjectTask(int taskId) async {
    try {
      // التحقق من وجود المهمة
      final existingTask = await _projectDao.getProjectTaskById(taskId);
      if (existingTask == null) {
        return Result.error('المهمة غير موجودة');
      }

      final result = await _projectDao.deleteProjectTask(taskId);
      return Result.success(result > 0);
    } catch (e) {
      return Result.error('خطأ في حذف المهمة: ${e.toString()}');
    }
  }

  /// الحصول على مهمة بالمعرف
  Future<Result<ProjectTask>> getProjectTaskById(int id) async {
    try {
      final task = await _projectDao.getProjectTaskById(id);
      if (task != null) {
        return Result.success(task);
      } else {
        return Result.error('المهمة غير موجودة');
      }
    } catch (e) {
      return Result.error('خطأ في جلب المهمة: ${e.toString()}');
    }
  }

  /// الحصول على مهام المشروع
  Future<Result<List<ProjectTask>>> getProjectTasks(
    int projectId, {
    int? phaseId,
    TaskStatus? status,
    TaskPriority? priority,
    int? assignedTo,
    String? searchTerm,
  }) async {
    try {
      final tasks = await _projectDao.getProjectTasks(
        projectId,
        phaseId: phaseId,
        status: status,
        priority: priority,
        assignedTo: assignedTo,
        searchTerm: searchTerm,
      );
      return Result.success(tasks);
    } catch (e) {
      return Result.error('خطأ في جلب المهام: ${e.toString()}');
    }
  }

  /// تحديث حالة المهمة
  Future<Result<bool>> updateTaskStatus(int taskId, TaskStatus status) async {
    try {
      final result = await _projectDao.updateTaskStatus(taskId, status);
      return Result.success(result > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث حالة المهمة: ${e.toString()}');
    }
  }

  /// تحديث الساعات الفعلية للمهمة
  Future<Result<bool>> updateTaskActualHours(
    int taskId,
    double actualHours,
  ) async {
    try {
      final result = await _projectDao.updateTaskActualHours(
        taskId,
        actualHours,
      );
      return Result.success(result > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث الساعات الفعلية: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات مهام المشروع
  Future<Result<Map<String, dynamic>>> getProjectTasksStatistics(
    int projectId,
  ) async {
    try {
      final statistics = await _projectDao.getProjectTasksStatistics(projectId);
      return Result.success(statistics);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات المهام: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات المهمة
  Result<ProjectTask> _validateProjectTask(ProjectTask task) {
    if (task.name.trim().isEmpty) {
      return Result.error('اسم المهمة مطلوب');
    }

    if (task.estimatedHours < 0) {
      return Result.error('الساعات المقدرة لا يمكن أن تكون سالبة');
    }

    if (task.actualHours < 0) {
      return Result.error('الساعات الفعلية لا يمكن أن تكون سالبة');
    }

    if (task.startDate != null && task.endDate != null) {
      if (task.startDate!.isAfter(task.endDate!)) {
        return Result.error('تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية');
      }
    }

    return Result.success(task);
  }
}

/// نتيجة العملية
class Result<T> {
  final bool isSuccess;
  final T? data;
  final String? error;

  Result.success(this.data) : isSuccess = true, error = null;
  Result.error(this.error) : isSuccess = false, data = null;
}

/// نموذج ربحية المشروع
class ProjectProfitability {
  final int projectId;
  final double totalRevenue;
  final double totalCosts;
  final double grossProfit;
  final double profitMargin;
  final Map<String, double> costsByType;
  final double budgetVariance;
  final double budgetVariancePercentage;

  ProjectProfitability({
    required this.projectId,
    required this.totalRevenue,
    required this.totalCosts,
    required this.grossProfit,
    required this.profitMargin,
    required this.costsByType,
    required this.budgetVariance,
    required this.budgetVariancePercentage,
  });
}
