/// نموذج العملة
class Currency {
  final int? id;
  final String code;
  final String symbol;
  final String nameAr;
  final String nameEn;
  final bool isActive;
  final bool isBaseCurrency;
  final int decimalPlaces;
  final DateTime createdAt;
  final DateTime updatedAt;

  Currency({
    this.id,
    required this.code,
    required this.symbol,
    required this.nameAr,
    required this.nameEn,
    this.isActive = true,
    this.isBaseCurrency = false,
    this.decimalPlaces = 2,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from database map
  factory Currency.fromMap(Map<String, dynamic> map) {
    return Currency(
      id: map['id'] as int?,
      code: map['code'] as String,
      symbol: map['symbol'] as String,
      nameAr: map['name_ar'] as String,
      nameEn: map['name_en'] as String,
      isActive: (map['is_active'] as int) == 1,
      isBaseCurrency: (map['is_base_currency'] as int) == 1,
      decimalPlaces: map['decimal_places'] as int? ?? 2,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'symbol': symbol,
      'name_ar': nameAr,
      'name_en': nameEn,
      'is_active': isActive ? 1 : 0,
      'is_base_currency': isBaseCurrency ? 1 : 0,
      'decimal_places': decimalPlaces,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  Currency copyWith({
    int? id,
    String? code,
    String? symbol,
    String? nameAr,
    String? nameEn,
    bool? isActive,
    bool? isBaseCurrency,
    int? decimalPlaces,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Currency(
      id: id ?? this.id,
      code: code ?? this.code,
      symbol: symbol ?? this.symbol,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      isActive: isActive ?? this.isActive,
      isBaseCurrency: isBaseCurrency ?? this.isBaseCurrency,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  String get displayName => nameAr;
  String get displayNameEn => nameEn;
  String get fullDisplayName => '$nameAr ($code)';

  String formatAmount(double amount) {
    return '${amount.toStringAsFixed(decimalPlaces)} $symbol';
  }

  @override
  String toString() => fullDisplayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Currency &&
          runtimeType == other.runtimeType &&
          code == other.code;

  @override
  int get hashCode => code.hashCode;

  // Predefined currencies
  static final List<Currency> predefinedCurrencies = [
    Currency(
      code: 'SAR',
      symbol: 'ر.س',
      nameAr: 'ريال سعودي',
      nameEn: 'Saudi Riyal',
      isBaseCurrency: true,
    ),
    Currency(
      code: 'USD',
      symbol: '\$',
      nameAr: 'دولار أمريكي',
      nameEn: 'US Dollar',
    ),
    Currency(code: 'EUR', symbol: '€', nameAr: 'يورو', nameEn: 'Euro'),
    Currency(
      code: 'GBP',
      symbol: '£',
      nameAr: 'جنيه إسترليني',
      nameEn: 'British Pound',
    ),
    Currency(
      code: 'AED',
      symbol: 'د.إ',
      nameAr: 'درهم إماراتي',
      nameEn: 'UAE Dirham',
    ),
    Currency(
      code: 'KWD',
      symbol: 'د.ك',
      nameAr: 'دينار كويتي',
      nameEn: 'Kuwaiti Dinar',
      decimalPlaces: 3,
    ),
    Currency(
      code: 'QAR',
      symbol: 'ر.ق',
      nameAr: 'ريال قطري',
      nameEn: 'Qatari Riyal',
    ),
    Currency(
      code: 'BHD',
      symbol: 'د.ب',
      nameAr: 'دينار بحريني',
      nameEn: 'Bahraini Dinar',
      decimalPlaces: 3,
    ),
    Currency(
      code: 'OMR',
      symbol: 'ر.ع',
      nameAr: 'ريال عماني',
      nameEn: 'Omani Rial',
      decimalPlaces: 3,
    ),
    Currency(
      code: 'JOD',
      symbol: 'د.أ',
      nameAr: 'دينار أردني',
      nameEn: 'Jordanian Dinar',
      decimalPlaces: 3,
    ),
    Currency(
      code: 'EGP',
      symbol: 'ج.م',
      nameAr: 'جنيه مصري',
      nameEn: 'Egyptian Pound',
    ),
    Currency(
      code: 'LBP',
      symbol: 'ل.ل',
      nameAr: 'ليرة لبنانية',
      nameEn: 'Lebanese Pound',
    ),
    Currency(
      code: 'SYP',
      symbol: 'ل.س',
      nameAr: 'ليرة سورية',
      nameEn: 'Syrian Pound',
    ),
    Currency(
      code: 'IQD',
      symbol: 'د.ع',
      nameAr: 'دينار عراقي',
      nameEn: 'Iraqi Dinar',
    ),
    Currency(
      code: 'MAD',
      symbol: 'د.م',
      nameAr: 'درهم مغربي',
      nameEn: 'Moroccan Dirham',
    ),
    Currency(
      code: 'TND',
      symbol: 'د.ت',
      nameAr: 'دينار تونسي',
      nameEn: 'Tunisian Dinar',
      decimalPlaces: 3,
    ),
    Currency(
      code: 'DZD',
      symbol: 'د.ج',
      nameAr: 'دينار جزائري',
      nameEn: 'Algerian Dinar',
    ),
    Currency(
      code: 'LYD',
      symbol: 'د.ل',
      nameAr: 'دينار ليبي',
      nameEn: 'Libyan Dinar',
      decimalPlaces: 3,
    ),
    Currency(
      code: 'SDG',
      symbol: 'ج.س',
      nameAr: 'جنيه سوداني',
      nameEn: 'Sudanese Pound',
    ),
    Currency(
      code: 'YER',
      symbol: 'ر.ي',
      nameAr: 'ريال يمني',
      nameEn: 'Yemeni Rial',
    ),
  ];

  static Currency? findByCode(String code) {
    try {
      return predefinedCurrencies.firstWhere(
        (currency) => currency.code == code,
      );
    } catch (e) {
      return null;
    }
  }

  static Currency get defaultCurrency => predefinedCurrencies.first;
}
