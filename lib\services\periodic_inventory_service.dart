/// خدمة الجرد الدوري
/// Periodic Inventory Service for Smart Ledger
library;

import '../models/periodic_inventory.dart';
import '../models/stock_balance.dart';
import '../models/stock_movement.dart';
import '../database/periodic_inventory_dao.dart';
import '../database/stock_balance_dao.dart';
import '../services/warehouse_service.dart';
import '../utils/result.dart';

class PeriodicInventoryService {
  static final PeriodicInventoryService _instance =
      PeriodicInventoryService._internal();
  factory PeriodicInventoryService() => _instance;
  PeriodicInventoryService._internal();

  final PeriodicInventoryDao _inventoryDao = PeriodicInventoryDao();
  final StockBalanceDao _balanceDao = StockBalanceDao();
  final WarehouseService _warehouseService = WarehouseService();

  /// Get all periodic inventories
  Future<Result<List<PeriodicInventory>>> getAllInventories() async {
    try {
      final inventories = await _inventoryDao.getAllInventories();
      return Result.success(inventories);
    } catch (e) {
      return Result.error('خطأ في جلب الجرد الدوري: ${e.toString()}');
    }
  }

  /// Get inventory by ID
  Future<Result<PeriodicInventory?>> getInventoryById(int id) async {
    try {
      final inventory = await _inventoryDao.getInventoryById(id);
      return Result.success(inventory);
    } catch (e) {
      return Result.error('خطأ في جلب الجرد: ${e.toString()}');
    }
  }

  /// Create new inventory
  Future<Result<int>> createInventory(PeriodicInventory inventory) async {
    try {
      // Validate inventory data
      final validation = _validateInventory(inventory);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      // Generate inventory number if not provided
      if (inventory.inventoryNumber.isEmpty) {
        final inventoryNumber = await _generateInventoryNumber();
        inventory = inventory.copyWith(inventoryNumber: inventoryNumber);
      }

      final id = await _inventoryDao.insertInventory(inventory);

      // Generate inventory items based on current stock
      await _generateInventoryItems(id, inventory);

      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إنشاء الجرد: ${e.toString()}');
    }
  }

  /// Update inventory
  Future<Result<bool>> updateInventory(PeriodicInventory inventory) async {
    try {
      final validation = _validateInventory(inventory);
      if (!validation.isSuccess) {
        return Result.error(validation.error!);
      }

      final rowsAffected = await _inventoryDao.updateInventory(inventory);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث الجرد: ${e.toString()}');
    }
  }

  /// Start inventory
  Future<Result<bool>> startInventory(int inventoryId) async {
    try {
      final inventory = await _inventoryDao.getInventoryById(inventoryId);
      if (inventory == null) {
        return Result.error('الجرد غير موجود');
      }

      if (inventory.status != InventoryStatus.planned) {
        return Result.error('لا يمكن بدء هذا الجرد');
      }

      final updatedInventory = inventory.copyWith(
        status: InventoryStatus.inProgress,
        startDate: DateTime.now(),
      );

      await _inventoryDao.updateInventory(updatedInventory);
      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في بدء الجرد: ${e.toString()}');
    }
  }

  /// Complete inventory
  Future<Result<bool>> completeInventory(int inventoryId) async {
    try {
      final inventory = await _inventoryDao.getInventoryById(inventoryId);
      if (inventory == null) {
        return Result.error('الجرد غير موجود');
      }

      if (inventory.status != InventoryStatus.inProgress) {
        return Result.error('لا يمكن إكمال هذا الجرد');
      }

      // Check if all items are counted
      final uncounteditems = await _inventoryDao.getUncounteditems(inventoryId);
      if (uncounteditems.isNotEmpty) {
        return Result.error('يجب عد جميع الأصناف قبل إكمال الجرد');
      }

      final updatedInventory = inventory.copyWith(
        status: InventoryStatus.completed,
        endDate: DateTime.now(),
      );

      await _inventoryDao.updateInventory(updatedInventory);
      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إكمال الجرد: ${e.toString()}');
    }
  }

  /// Cancel inventory
  Future<Result<bool>> cancelInventory(int inventoryId) async {
    try {
      final inventory = await _inventoryDao.getInventoryById(inventoryId);
      if (inventory == null) {
        return Result.error('الجرد غير موجود');
      }

      if (inventory.status == InventoryStatus.completed) {
        return Result.error('لا يمكن إلغاء جرد مكتمل');
      }

      final updatedInventory = inventory.copyWith(
        status: InventoryStatus.cancelled,
      );

      await _inventoryDao.updateInventory(updatedInventory);
      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في إلغاء الجرد: ${e.toString()}');
    }
  }

  /// Get inventory items
  Future<Result<List<InventoryItem>>> getInventoryItems(int inventoryId) async {
    try {
      final items = await _inventoryDao.getInventoryItems(inventoryId);
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في جلب أصناف الجرد: ${e.toString()}');
    }
  }

  /// Update inventory item count
  Future<Result<bool>> updateItemCount({
    required int inventoryItemId,
    required double countedQuantity,
    String? notes,
    required int countedBy,
  }) async {
    try {
      final item = await _inventoryDao.getInventoryItemById(inventoryItemId);
      if (item == null) {
        return Result.error('صنف الجرد غير موجود');
      }

      final variance = countedQuantity - item.systemQuantity;
      final totalVarianceCost = variance * (item.unitCost ?? 0);

      final updatedItem = item.copyWith(
        countedQuantity: countedQuantity,
        variance: variance,
        totalVarianceCost: totalVarianceCost,
        notes: notes,
        countedBy: countedBy,
        countedAt: DateTime.now(),
      );

      await _inventoryDao.updateInventoryItem(updatedItem);
      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في تحديث عد الصنف: ${e.toString()}');
    }
  }

  /// Reconcile inventory variances
  Future<Result<bool>> reconcileInventory(int inventoryId) async {
    try {
      final inventory = await _inventoryDao.getInventoryById(inventoryId);
      if (inventory == null) {
        return Result.error('الجرد غير موجود');
      }

      if (inventory.status != InventoryStatus.completed) {
        return Result.error('يجب إكمال الجرد أولاً');
      }

      final items = await _inventoryDao.getInventoryItemsWithVariance(
        inventoryId,
      );

      for (final item in items) {
        if (item.hasVariance && !item.isReconciled) {
          // Create stock adjustment movement
          await _createAdjustmentMovement(item);

          // Mark item as reconciled
          final reconciledItem = item.copyWith(isReconciled: true);
          await _inventoryDao.updateInventoryItem(reconciledItem);
        }
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في تسوية الجرد: ${e.toString()}');
    }
  }

  /// Get inventory summary
  Future<Result<Map<String, dynamic>>> getInventorySummary(
    int inventoryId,
  ) async {
    try {
      final summary = await _inventoryDao.getInventorySummary(inventoryId);
      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب ملخص الجرد: ${e.toString()}');
    }
  }

  /// Search inventories
  Future<Result<List<PeriodicInventory>>> searchInventories(
    String query,
  ) async {
    try {
      final inventories = await _inventoryDao.searchInventories(query);
      return Result.success(inventories);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  /// Validate inventory data
  Result<bool> _validateInventory(PeriodicInventory inventory) {
    if (inventory.inventoryNumber.trim().isEmpty) {
      return Result.error('رقم الجرد مطلوب');
    }

    if (inventory.name.trim().isEmpty) {
      return Result.error('اسم الجرد مطلوب');
    }

    if (inventory.plannedDate.isBefore(
      DateTime.now().subtract(const Duration(days: 1)),
    )) {
      return Result.error('تاريخ الجرد لا يمكن أن يكون في الماضي');
    }

    return Result.success(true);
  }

  /// Generate inventory number
  Future<String> _generateInventoryNumber() async {
    final count = await _inventoryDao.getInventoryCount();
    final year = DateTime.now().year;
    return 'INV$year${(count + 1).toString().padLeft(6, '0')}';
  }

  /// Generate inventory items based on current stock
  Future<void> _generateInventoryItems(
    int inventoryId,
    PeriodicInventory inventory,
  ) async {
    List<StockBalance> balances;

    if (inventory.warehouseId != null) {
      // Specific warehouse
      balances = await _balanceDao.getBalancesByWarehouse(
        inventory.warehouseId!,
      );
    } else {
      // All warehouses
      balances = await _balanceDao.getAllBalances();
    }

    // Filter by location if specified
    if (inventory.locationId != null) {
      balances = balances
          .where((b) => b.locationId == inventory.locationId)
          .toList();
    }

    // Create inventory items
    for (final balance in balances) {
      if (balance.quantity > 0) {
        final inventoryItem = InventoryItem(
          inventoryId: inventoryId,
          itemId: balance.itemId,
          warehouseId: balance.warehouseId,
          locationId: balance.locationId,
          batchNumber: balance.batchNumber,
          serialNumber: balance.serialNumber,
          systemQuantity: balance.quantity,
          unitCost: balance.averageCost,
        );

        await _inventoryDao.insertInventoryItem(inventoryItem);
      }
    }
  }

  /// Create adjustment movement for variance
  Future<void> _createAdjustmentMovement(InventoryItem item) async {
    if (!item.hasVariance) return;

    // For inventory adjustments, we always use MovementType.adjustment
    // The quantity will be positive for increases and negative for decreases
    final movementType = MovementType.adjustment;

    final movement = StockMovement(
      documentNumber: 'ADJ-${item.inventoryId}-${item.itemId}',
      type: movementType,
      status: MovementStatus.completed,
      reason: MovementReason.adjustment,
      itemId: item.itemId,
      warehouseId: item.warehouseId,
      locationId: item.locationId,
      quantity: item.calculatedVariance.abs(),
      unitCost: item.unitCost ?? 0,
      totalCost: item.calculatedVarianceCost.abs(),
      batchNumber: item.batchNumber,
      serialNumber: item.serialNumber,
      notes: 'تسوية جرد - ${item.notes ?? ''}',
      referenceDocument: 'INV-${item.inventoryId}',
      movementDate: DateTime.now(),
    );

    await _warehouseService.addStockMovement(movement);
  }
}
