import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🛒 نظام إدارة المبيعات والمشتريات المتقدم
/// Advanced Sales and Purchase Management System
///
/// هذا الملف يحتوي على نظام إدارة المبيعات والمشتريات المتقدم لا مثيل له في التاريخ
/// This file contains unprecedented advanced sales and purchase management system in history

/// 🌟 لوحة إدارة المبيعات والمشتريات المتقدمة
/// Advanced Sales and Purchase Management Dashboard
class AdvancedSalesPurchaseDashboard extends StatefulWidget {
  const AdvancedSalesPurchaseDashboard({super.key});

  @override
  State<AdvancedSalesPurchaseDashboard> createState() =>
      _AdvancedSalesPurchaseDashboardState();
}

class _AdvancedSalesPurchaseDashboardState
    extends State<AdvancedSalesPurchaseDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _mainAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  int _selectedTab = 0;

  final List<SalesTransaction> _salesTransactions = [
    SalesTransaction(
      id: 'INV-2024-001',
      customerName: 'شركة الأندلس للتجارة',
      amount: 125000.0,
      date: DateTime.now().subtract(const Duration(days: 1)),
      status: TransactionStatus.completed,
      items: 15,
    ),
    SalesTransaction(
      id: 'INV-2024-002',
      customerName: 'مؤسسة النور للمقاولات',
      amount: 85000.0,
      date: DateTime.now().subtract(const Duration(days: 3)),
      status: TransactionStatus.pending,
      items: 8,
    ),
    SalesTransaction(
      id: 'INV-2024-003',
      customerName: 'شركة الفجر الجديد',
      amount: 45000.0,
      date: DateTime.now().subtract(const Duration(days: 5)),
      status: TransactionStatus.completed,
      items: 12,
    ),
  ];

  final List<PurchaseTransaction> _purchaseTransactions = [
    PurchaseTransaction(
      id: 'PO-2024-001',
      supplierName: 'شركة المواد الأولية المحدودة',
      amount: 75000.0,
      date: DateTime.now().subtract(const Duration(days: 2)),
      status: TransactionStatus.completed,
      items: 20,
    ),
    PurchaseTransaction(
      id: 'PO-2024-002',
      supplierName: 'مؤسسة التوريدات الذكية',
      amount: 120000.0,
      date: DateTime.now().subtract(const Duration(days: 4)),
      status: TransactionStatus.pending,
      items: 25,
    ),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _mainController.repeat(reverse: true);
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _pulseAnimation,
        _rotationAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.0 + (_mainAnimation.value * 0.5),
          child: QuantumEnergyEffect(
            intensity: 1.8 + (_pulseAnimation.value * 0.2),
            primaryColor: const Color(0xFF4CAF50),
            secondaryColor: const Color(0xFF66BB6A),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF1B5E20).withValues(alpha: 0.9),
                    const Color(0xFF2E7D32).withValues(alpha: 0.8),
                    const Color(0xFF388E3C).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس نظام المبيعات والمشتريات
                  Row(
                    children: [
                      Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Transform.rotate(
                          angle: _rotationAnimation.value * 0.1,
                          child: Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF4CAF50,
                                  ).withValues(alpha: 0.9),
                                  const Color(
                                    0xFF66BB6A,
                                  ).withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.5),
                                width: 3,
                              ),
                            ),
                            child: const Icon(
                              Icons.shopping_cart_rounded,
                              color: Colors.white,
                              size: 36,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🛒 إدارة المبيعات والمشتريات',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'نظام متطور لإدارة العمليات التجارية',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // تبويبات المبيعات والمشتريات
                  _buildTabSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المحتوى حسب التبويب المحدد
                  _selectedTab == 0 ? _buildSalesView() : _buildPurchasesView(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد التبويبات
  Widget _buildTabSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 0),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 0
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.trending_up_rounded,
                      color: _selectedTab == 0
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'المبيعات',
                      style: TextStyle(
                        color: _selectedTab == 0
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTab = 1),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedTab == 1
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.trending_down_rounded,
                      color: _selectedTab == 1
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'المشتريات',
                      style: TextStyle(
                        color: _selectedTab == 1
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.6),
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض المبيعات
  Widget _buildSalesView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات المبيعات
        _buildSalesStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة المبيعات
        Text(
          '📋 فواتير المبيعات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_salesTransactions.length, (index) {
          return _buildSalesTransactionCard(_salesTransactions[index]);
        }),
      ],
    );
  }

  /// بناء عرض المشتريات
  Widget _buildPurchasesView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // إحصائيات المشتريات
        _buildPurchaseStats(),

        const SizedBox(height: AppTheme.spacingLarge),

        // قائمة المشتريات
        Text(
          '🏭 أوامر الشراء',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_purchaseTransactions.length, (index) {
          return _buildPurchaseTransactionCard(_purchaseTransactions[index]);
        }),
      ],
    );
  }

  /// بناء إحصائيات المبيعات
  Widget _buildSalesStats() {
    final totalSales = _salesTransactions.fold(
      0.0,
      (sum, transaction) => sum + transaction.amount,
    );
    final completedSales = _salesTransactions
        .where((t) => t.status == TransactionStatus.completed)
        .length;
    final totalItems = _salesTransactions.fold(
      0,
      (sum, transaction) => sum + transaction.items,
    );

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي المبيعات',
            '${totalSales.toStringAsFixed(0)} ر.س',
            Icons.trending_up_rounded,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'الفواتير المكتملة',
            completedSales.toString(),
            Icons.check_circle_rounded,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي الأصناف',
            totalItems.toString(),
            Icons.inventory_rounded,
            const Color(0xFFFF9800),
          ),
        ),
      ],
    );
  }

  /// بناء إحصائيات المشتريات
  Widget _buildPurchaseStats() {
    final totalPurchases = _purchaseTransactions.fold(
      0.0,
      (sum, transaction) => sum + transaction.amount,
    );
    final completedPurchases = _purchaseTransactions
        .where((t) => t.status == TransactionStatus.completed)
        .length;
    final totalItems = _purchaseTransactions.fold(
      0,
      (sum, transaction) => sum + transaction.items,
    );

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي المشتريات',
            '${totalPurchases.toStringAsFixed(0)} ر.س',
            Icons.trending_down_rounded,
            const Color(0xFFF44336),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'الطلبات المكتملة',
            completedPurchases.toString(),
            Icons.check_circle_rounded,
            const Color(0xFF9C27B0),
          ),
        ),
        const SizedBox(width: AppTheme.spacingSmall),
        Expanded(
          child: _buildStatCard(
            'إجمالي الأصناف',
            totalItems.toString(),
            Icons.shopping_basket_rounded,
            const Color(0xFF00BCD4),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXSmall),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معاملة المبيعات
  Widget _buildSalesTransactionCard(SalesTransaction transaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getStatusColor(transaction.status).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    transaction.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.receipt_long_rounded,
                  color: _getStatusColor(transaction.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction.id,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      transaction.customerName,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    transaction.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getStatusText(transaction.status),
                  style: TextStyle(
                    color: _getStatusColor(transaction.status),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات إضافية
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'المبلغ',
                  '${transaction.amount.toStringAsFixed(2)} ر.س',
                  Icons.attach_money_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'الأصناف',
                  '${transaction.items} صنف',
                  Icons.inventory_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'التاريخ',
                  _formatDate(transaction.date),
                  Icons.calendar_today_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معاملة المشتريات
  Widget _buildPurchaseTransactionCard(PurchaseTransaction transaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: _getStatusColor(transaction.status).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    transaction.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  Icons.shopping_cart_rounded,
                  color: _getStatusColor(transaction.status),
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction.id,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      transaction.supplierName,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    transaction.status,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getStatusText(transaction.status),
                  style: TextStyle(
                    color: _getStatusColor(transaction.status),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          // معلومات إضافية
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'المبلغ',
                  '${transaction.amount.toStringAsFixed(2)} ر.س',
                  Icons.attach_money_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'الأصناف',
                  '${transaction.items} صنف',
                  Icons.shopping_basket_rounded,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'التاريخ',
                  _formatDate(transaction.date),
                  Icons.calendar_today_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.6), size: 14),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.6),
                  fontSize: 10,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return const Color(0xFF4CAF50);
      case TransactionStatus.pending:
        return const Color(0xFFFF9800);
      case TransactionStatus.cancelled:
        return const Color(0xFFF44336);
    }
  }

  String _getStatusText(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return 'مكتمل';
      case TransactionStatus.pending:
        return 'معلق';
      case TransactionStatus.cancelled:
        return 'ملغي';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'اليوم';
    } else if (difference == 1) {
      return 'أمس';
    } else {
      return 'منذ $difference أيام';
    }
  }
}

/// نموذج معاملة المبيعات
class SalesTransaction {
  final String id;
  final String customerName;
  final double amount;
  final DateTime date;
  final TransactionStatus status;
  final int items;

  SalesTransaction({
    required this.id,
    required this.customerName,
    required this.amount,
    required this.date,
    required this.status,
    required this.items,
  });
}

/// نموذج معاملة المشتريات
class PurchaseTransaction {
  final String id;
  final String supplierName;
  final double amount;
  final DateTime date;
  final TransactionStatus status;
  final int items;

  PurchaseTransaction({
    required this.id,
    required this.supplierName,
    required this.amount,
    required this.date,
    required this.status,
    required this.items,
  });
}

/// حالة المعاملة
enum TransactionStatus { completed, pending, cancelled }
