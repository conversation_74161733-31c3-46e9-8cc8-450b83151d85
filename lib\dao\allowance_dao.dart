import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/payroll_components.dart';

/// DAO للبدلات
/// Allowance Data Access Object
class AllowanceDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدراج بدل جديد
  Future<int> insertAllowance(Allowance allowance) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      DatabaseSchema.tableAllowances,
      allowance.toMap(),
    );
  }

  /// تحديث بدل
  Future<int> updateAllowance(Allowance allowance) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableAllowances,
      allowance.toMap(),
      where: 'id = ?',
      whereArgs: [allowance.id],
    );
  }

  /// حذف بدل
  Future<int> deleteAllowance(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      DatabaseSchema.tableAllowances,
      {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على بدل بالمعرف
  Future<Allowance?> getAllowanceById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAllowances,
      where: 'id = ? AND is_active = 1',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Allowance.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على بدل بالرمز
  Future<Allowance?> getAllowanceByCode(String code) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAllowances,
      where: 'code = ? AND is_active = 1',
      whereArgs: [code],
    );

    if (maps.isNotEmpty) {
      return Allowance.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على جميع البدلات
  Future<List<Allowance>> getAllAllowances() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAllowances,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Allowance.fromMap(maps[i]));
  }

  /// البحث في البدلات
  Future<List<Allowance>> searchAllowances(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAllowances,
      where: '''
        is_active = 1 AND (
          LOWER(code) LIKE LOWER(?) OR 
          LOWER(name) LIKE LOWER(?) OR 
          LOWER(description) LIKE LOWER(?)
        )
      ''',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Allowance.fromMap(maps[i]));
  }

  /// الحصول على البدلات حسب النوع
  Future<List<Allowance>> getAllowancesByType(AllowanceType type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAllowances,
      where: 'type = ? AND is_active = 1',
      whereArgs: [type.name],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Allowance.fromMap(maps[i]));
  }

  /// الحصول على البدلات حسب نوع الحساب
  Future<List<Allowance>> getAllowancesByCalculationType(
      AllowanceCalculationType calculationType) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAllowances,
      where: 'calculation_type = ? AND is_active = 1',
      whereArgs: [calculationType.name],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Allowance.fromMap(maps[i]));
  }

  /// التحقق من تفرد رمز البدل
  Future<bool> isAllowanceCodeUnique(String code, {int? excludeId}) async {
    final db = await _databaseHelper.database;
    String whereClause = 'code = ? AND is_active = 1';
    List<dynamic> whereArgs = [code];

    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseSchema.tableAllowances,
      where: whereClause,
      whereArgs: whereArgs,
    );

    return maps.isEmpty;
  }

  /// الحصول على إحصائيات البدلات
  Future<Map<String, dynamic>> getAllowanceStatistics() async {
    final db = await _databaseHelper.database;
    
    final totalResult = await db.rawQuery('''
      SELECT COUNT(*) as total_allowances
      FROM ${DatabaseSchema.tableAllowances}
      WHERE is_active = 1
    ''');

    final typeStatsResult = await db.rawQuery('''
      SELECT type, COUNT(*) as count
      FROM ${DatabaseSchema.tableAllowances}
      WHERE is_active = 1
      GROUP BY type
    ''');

    final calculationTypeStatsResult = await db.rawQuery('''
      SELECT calculation_type, COUNT(*) as count
      FROM ${DatabaseSchema.tableAllowances}
      WHERE is_active = 1
      GROUP BY calculation_type
    ''');

    return {
      'total_allowances': totalResult.first['total_allowances'] ?? 0,
      'by_type': {
        for (var row in typeStatsResult)
          row['type']: row['count']
      },
      'by_calculation_type': {
        for (var row in calculationTypeStatsResult)
          row['calculation_type']: row['count']
      },
    };
  }

  /// الحصول على البدلات المستخدمة
  Future<List<Allowance>> getUsedAllowances() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT a.*
      FROM ${DatabaseSchema.tableAllowances} a
      INNER JOIN ${DatabaseSchema.tableEmployeeAllowances} ea ON a.id = ea.allowance_id
      WHERE a.is_active = 1 AND ea.is_active = 1
      ORDER BY a.name ASC
    ''');

    return List.generate(maps.length, (i) => Allowance.fromMap(maps[i]));
  }

  /// الحصول على البدلات غير المستخدمة
  Future<List<Allowance>> getUnusedAllowances() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT a.*
      FROM ${DatabaseSchema.tableAllowances} a
      LEFT JOIN ${DatabaseSchema.tableEmployeeAllowances} ea ON a.id = ea.allowance_id AND ea.is_active = 1
      WHERE a.is_active = 1 AND ea.allowance_id IS NULL
      ORDER BY a.name ASC
    ''');

    return List.generate(maps.length, (i) => Allowance.fromMap(maps[i]));
  }

  /// الحصول على عدد الموظفين المستفيدين من البدل
  Future<int> getAllowanceEmployeeCount(int allowanceId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT COUNT(DISTINCT employee_code) as count
      FROM ${DatabaseSchema.tableEmployeeAllowances}
      WHERE allowance_id = ? AND is_active = 1
    ''', [allowanceId]);

    return result.first['count'] as int? ?? 0;
  }

  /// الحصول على إجمالي مبلغ البدل لجميع الموظفين
  Future<double> getAllowanceTotalAmount(int allowanceId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT SUM(amount) as total
      FROM ${DatabaseSchema.tableEmployeeAllowances}
      WHERE allowance_id = ? AND is_active = 1
    ''', [allowanceId]);

    return (result.first['total'] as num?)?.toDouble() ?? 0.0;
  }

  /// الحصول على متوسط مبلغ البدل
  Future<double> getAllowanceAverageAmount(int allowanceId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT AVG(amount) as average
      FROM ${DatabaseSchema.tableEmployeeAllowances}
      WHERE allowance_id = ? AND is_active = 1
    ''', [allowanceId]);

    return (result.first['average'] as num?)?.toDouble() ?? 0.0;
  }

  /// الحصول على البدلات مع تفاصيل الاستخدام
  Future<List<Map<String, dynamic>>> getAllowancesWithUsageDetails() async {
    final db = await _databaseHelper.database;
    return await db.rawQuery('''
      SELECT 
        a.*,
        COUNT(DISTINCT ea.employee_code) as employee_count,
        COALESCE(SUM(ea.amount), 0) as total_amount,
        COALESCE(AVG(ea.amount), 0) as average_amount
      FROM ${DatabaseSchema.tableAllowances} a
      LEFT JOIN ${DatabaseSchema.tableEmployeeAllowances} ea ON a.id = ea.allowance_id AND ea.is_active = 1
      WHERE a.is_active = 1
      GROUP BY a.id
      ORDER BY a.name ASC
    ''');
  }
}
