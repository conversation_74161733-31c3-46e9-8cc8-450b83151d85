/// نموذج الحساب البنكي
/// Bank Account Model for Smart Ledger
library;

enum BankAccountType {
  current('current', 'حساب جاري'),
  savings('savings', 'حساب توفير'),
  fixedDeposit('fixed_deposit', 'وديعة ثابتة'),
  creditCard('credit_card', 'بطاقة ائتمان'),
  loan('loan', 'قرض');

  const BankAccountType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum BankAccountStatus {
  active('active', 'نشط'),
  inactive('inactive', 'غير نشط'),
  closed('closed', 'مغلق'),
  frozen('frozen', 'مجمد');

  const BankAccountStatus(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

class BankAccount {
  final int? id;
  final String accountNumber;
  final String accountName;
  final String bankName;
  final String bankCode;
  final String? branchName;
  final String? branchCode;
  final String? iban;
  final String? swiftCode;
  final BankAccountType accountType;
  final BankAccountStatus status;
  final String currency;
  final double openingBalance;
  final double currentBalance;
  final double? creditLimit;
  final String? notes;
  final int? accountId; // Link to chart of accounts
  final DateTime createdAt;
  final DateTime updatedAt;

  BankAccount({
    this.id,
    required this.accountNumber,
    required this.accountName,
    required this.bankName,
    required this.bankCode,
    this.branchName,
    this.branchCode,
    this.iban,
    this.swiftCode,
    this.accountType = BankAccountType.current,
    this.status = BankAccountStatus.active,
    this.currency = 'SAR',
    this.openingBalance = 0.0,
    this.currentBalance = 0.0,
    this.creditLimit,
    this.notes,
    this.accountId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory BankAccount.fromMap(Map<String, dynamic> map) {
    return BankAccount(
      id: map['id'] as int?,
      accountNumber: map['account_number'] as String,
      accountName: map['account_name'] as String,
      bankName: map['bank_name'] as String,
      bankCode: map['bank_code'] as String,
      branchName: map['branch_name'] as String?,
      branchCode: map['branch_code'] as String?,
      iban: map['iban'] as String?,
      swiftCode: map['swift_code'] as String?,
      accountType: BankAccountType.values.firstWhere(
        (e) => e.value == map['account_type'],
        orElse: () => BankAccountType.current,
      ),
      status: BankAccountStatus.values.firstWhere(
        (e) => e.value == map['status'],
        orElse: () => BankAccountStatus.active,
      ),
      currency: map['currency'] as String? ?? 'SAR',
      openingBalance: (map['opening_balance'] as num?)?.toDouble() ?? 0.0,
      currentBalance: (map['current_balance'] as num?)?.toDouble() ?? 0.0,
      creditLimit: (map['credit_limit'] as num?)?.toDouble(),
      notes: map['notes'] as String?,
      accountId: map['account_id'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'account_number': accountNumber,
      'account_name': accountName,
      'bank_name': bankName,
      'bank_code': bankCode,
      'branch_name': branchName,
      'branch_code': branchCode,
      'iban': iban,
      'swift_code': swiftCode,
      'account_type': accountType.value,
      'status': status.value,
      'currency': currency,
      'opening_balance': openingBalance,
      'current_balance': currentBalance,
      'credit_limit': creditLimit,
      'notes': notes,
      'account_id': accountId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  BankAccount copyWith({
    int? id,
    String? accountNumber,
    String? accountName,
    String? bankName,
    String? bankCode,
    String? branchName,
    String? branchCode,
    String? iban,
    String? swiftCode,
    BankAccountType? accountType,
    BankAccountStatus? status,
    String? currency,
    double? openingBalance,
    double? currentBalance,
    double? creditLimit,
    String? notes,
    int? accountId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankAccount(
      id: id ?? this.id,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      bankName: bankName ?? this.bankName,
      bankCode: bankCode ?? this.bankCode,
      branchName: branchName ?? this.branchName,
      branchCode: branchCode ?? this.branchCode,
      iban: iban ?? this.iban,
      swiftCode: swiftCode ?? this.swiftCode,
      accountType: accountType ?? this.accountType,
      status: status ?? this.status,
      currency: currency ?? this.currency,
      openingBalance: openingBalance ?? this.openingBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      creditLimit: creditLimit ?? this.creditLimit,
      notes: notes ?? this.notes,
      accountId: accountId ?? this.accountId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'BankAccount{accountNumber: $accountNumber, accountName: $accountName, bankName: $bankName}';
  }

  /// Get available balance (current balance + credit limit for credit accounts)
  double get availableBalance {
    if (accountType == BankAccountType.creditCard && creditLimit != null) {
      return currentBalance + creditLimit!;
    }
    return currentBalance;
  }

  /// Check if account is overdrawn
  bool get isOverdrawn {
    return currentBalance < 0;
  }

  /// Get account display name
  String get displayName {
    return '$accountName ($accountNumber)';
  }
}

/// نموذج معاملة بنكية
/// Bank Transaction Model
enum BankTransactionType {
  deposit('deposit', 'إيداع'),
  withdrawal('withdrawal', 'سحب'),
  transfer('transfer', 'تحويل'),
  fee('fee', 'رسوم'),
  interest('interest', 'فوائد'),
  check('check', 'شيك'),
  directDebit('direct_debit', 'خصم مباشر'),
  standingOrder('standing_order', 'أمر دائم');

  const BankTransactionType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum BankTransactionStatus {
  pending('pending', 'معلق'),
  completed('completed', 'مكتمل'),
  cancelled('cancelled', 'ملغي'),
  failed('failed', 'فاشل');

  const BankTransactionStatus(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

class BankTransaction {
  final int? id;
  final int bankAccountId;
  final String transactionNumber;
  final BankTransactionType type;
  final BankTransactionStatus status;
  final double amount;
  final String currency;
  final String description;
  final String? reference;
  final String? checkNumber;
  final int? toBankAccountId; // For transfers
  final int? journalEntryId; // Link to journal entry
  final DateTime transactionDate;
  final DateTime? valueDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  BankAccount? bankAccount;
  BankAccount? toBankAccount;

  BankTransaction({
    this.id,
    required this.bankAccountId,
    required this.transactionNumber,
    required this.type,
    this.status = BankTransactionStatus.pending,
    required this.amount,
    this.currency = 'SAR',
    required this.description,
    this.reference,
    this.checkNumber,
    this.toBankAccountId,
    this.journalEntryId,
    DateTime? transactionDate,
    this.valueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.bankAccount,
    this.toBankAccount,
  }) : transactionDate = transactionDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory BankTransaction.fromMap(Map<String, dynamic> map) {
    return BankTransaction(
      id: map['id'] as int?,
      bankAccountId: map['bank_account_id'] as int,
      transactionNumber: map['transaction_number'] as String,
      type: BankTransactionType.values.firstWhere(
        (e) => e.value == map['type'],
        orElse: () => BankTransactionType.deposit,
      ),
      status: BankTransactionStatus.values.firstWhere(
        (e) => e.value == map['status'],
        orElse: () => BankTransactionStatus.pending,
      ),
      amount: (map['amount'] as num).toDouble(),
      currency: map['currency'] as String? ?? 'SAR',
      description: map['description'] as String,
      reference: map['reference'] as String?,
      checkNumber: map['check_number'] as String?,
      toBankAccountId: map['to_bank_account_id'] as int?,
      journalEntryId: map['journal_entry_id'] as int?,
      transactionDate: DateTime.parse(map['transaction_date'] as String),
      valueDate: map['value_date'] != null 
          ? DateTime.parse(map['value_date'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bank_account_id': bankAccountId,
      'transaction_number': transactionNumber,
      'type': type.value,
      'status': status.value,
      'amount': amount,
      'currency': currency,
      'description': description,
      'reference': reference,
      'check_number': checkNumber,
      'to_bank_account_id': toBankAccountId,
      'journal_entry_id': journalEntryId,
      'transaction_date': transactionDate.toIso8601String(),
      'value_date': valueDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'BankTransaction{transactionNumber: $transactionNumber, type: ${type.arabicName}, amount: $amount}';
  }

  /// Get signed amount (negative for withdrawals, positive for deposits)
  double get signedAmount {
    switch (type) {
      case BankTransactionType.withdrawal:
      case BankTransactionType.fee:
      case BankTransactionType.transfer:
        return -amount;
      case BankTransactionType.deposit:
      case BankTransactionType.interest:
        return amount;
      default:
        return amount;
    }
  }
}
