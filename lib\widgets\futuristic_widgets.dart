import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 🚀 مجموعة الويدجت المستقبلية الخارقة
/// Futuristic Extraordinary Widgets Collection
///
/// هذا الملف يحتوي على ويدجت مستقبلية لا مثيل لها في التاريخ
/// This file contains unprecedented futuristic widgets in history

/// 🌟 بطاقة مالية ثلاثية الأبعاد مع تأثيرات هولوجرافية
/// 3D Financial Card with Holographic Effects
class FuturisticFinancialCard extends StatefulWidget {
  final String title;
  final String amount;
  final String subtitle;
  final IconData icon;
  final List<Color> gradientColors;
  final VoidCallback? onTap;

  const FuturisticFinancialCard({
    super.key,
    required this.title,
    required this.amount,
    required this.subtitle,
    required this.icon,
    required this.gradientColors,
    this.onTap,
  });

  @override
  State<FuturisticFinancialCard> createState() =>
      _FuturisticFinancialCardState();
}

class _FuturisticFinancialCardState extends State<FuturisticFinancialCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pulseController;
  late Animation<double> _hoverAnimation;
  late Animation<double> _pulseAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();

    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _hoverAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverAnimation, _pulseAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _hoverAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _onHover(true),
            onExit: (_) => _onHover(false),
            child: GestureDetector(
              onTap: widget.onTap,
              child: HologramEffect(
                intensity: _isHovered ? 2.0 : 1.0,
                child: QuantumEnergyEffect(
                  intensity: 1.0 + (_pulseAnimation.value * 0.5),
                  primaryColor: widget.gradientColors.first,
                  secondaryColor: widget.gradientColors.last,
                  child: Container(
                    height: 160,
                    margin: const EdgeInsets.all(AppTheme.spacingMedium),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: widget.gradientColors,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: widget.gradientColors.first.withValues(
                            alpha: 0.4,
                          ),
                          blurRadius: 20 + (_pulseAnimation.value * 10),
                          offset: const Offset(0, 10),
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // خلفية الجسيمات المتحركة
                        _buildAnimatedParticles(),

                        // المحتوى الرئيسي
                        Padding(
                          padding: const EdgeInsets.all(AppTheme.spacingLarge),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  // أيقونة مع تأثير النيون
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(
                                        alpha: 0.2,
                                      ),
                                      borderRadius: BorderRadius.circular(15),
                                      border: Border.all(
                                        color: Colors.white.withValues(
                                          alpha: 0.3,
                                        ),
                                        width: 2,
                                      ),
                                    ),
                                    child: Icon(
                                      widget.icon,
                                      color: Colors.white,
                                      size: 28,
                                    ),
                                  ),
                                  const Spacer(),
                                  // مؤشر الحالة
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: Colors.greenAccent,
                                      borderRadius: BorderRadius.circular(6),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.greenAccent.withValues(
                                            alpha: 0.6,
                                          ),
                                          blurRadius: 8,
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: AppTheme.spacingLarge),

                              // العنوان
                              Text(
                                widget.title,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                      fontWeight: FontWeight.w300,
                                      letterSpacing: 0.5,
                                    ),
                              ),

                              const SizedBox(height: AppTheme.spacingSmall),

                              // المبلغ
                              Text(
                                widget.amount,
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineMedium
                                    ?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 1.2,
                                      shadows: [
                                        Shadow(
                                          color: Colors.white.withValues(
                                            alpha: 0.5,
                                          ),
                                          blurRadius: 10,
                                        ),
                                      ],
                                    ),
                              ),

                              const SizedBox(height: AppTheme.spacingXSmall),

                              // العنوان الفرعي
                              Text(
                                widget.subtitle,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: Colors.white.withValues(
                                        alpha: 0.8,
                                      ),
                                      fontWeight: FontWeight.w300,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  Widget _buildAnimatedParticles() {
    return Positioned.fill(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: Stack(
          children: List.generate(6, (index) {
            return Positioned(
              left: (index * 60.0) % 200,
              top: (index * 40.0) % 120,
              child: AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: 0.3 + (_pulseAnimation.value * 0.4),
                    child: Container(
                      width: 3,
                      height: 3,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(1.5),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.6),
                            blurRadius: 6,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          }),
        ),
      ),
    );
  }
}

/// 🎯 زر مستقبلي مع تأثيرات كمية
/// Futuristic Button with Quantum Effects
class QuantumButton extends StatefulWidget {
  final String label;
  final IconData icon;
  final VoidCallback? onPressed;
  final Color primaryColor;
  final Color secondaryColor;
  final double size;

  const QuantumButton({
    super.key,
    required this.label,
    required this.icon,
    this.onPressed,
    this.primaryColor = const Color(0xFF2196F3),
    this.secondaryColor = const Color(0xFF21CBF3),
    this.size = 100,
  });

  @override
  State<QuantumButton> createState() => _QuantumButtonState();
}

class _QuantumButtonState extends State<QuantumButton>
    with TickerProviderStateMixin {
  late AnimationController _pressController;
  late AnimationController _energyController;
  late Animation<double> _pressAnimation;
  late Animation<double> _energyAnimation;

  @override
  void initState() {
    super.initState();

    _pressController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _energyController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pressAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _pressController, curve: Curves.easeInOut),
    );

    _energyAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _energyController, curve: Curves.easeInOut),
    );

    _energyController.repeat();
  }

  @override
  void dispose() {
    _pressController.dispose();
    _energyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_pressAnimation, _energyAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pressAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _onPressDown(),
            onTapUp: (_) => _onPressUp(),
            onTapCancel: () => _onPressUp(),
            onTap: widget.onPressed,
            child: ParticleExplosionEffect(
              particleCount: 12,
              explosionRadius: widget.size * 0.6,
              child: QuantumEnergyEffect(
                intensity: 1.0 + (_energyAnimation.value * 0.8),
                primaryColor: widget.primaryColor,
                secondaryColor: widget.secondaryColor,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      colors: [
                        widget.primaryColor.withValues(alpha: 0.9),
                        widget.secondaryColor.withValues(alpha: 0.7),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(widget.size / 4),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.4),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: widget.primaryColor.withValues(alpha: 0.5),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        widget.icon,
                        color: Colors.white,
                        size: widget.size * 0.3,
                      ),
                      SizedBox(height: widget.size * 0.05),
                      Text(
                        widget.label,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: widget.size * 0.12,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onPressDown() {
    _pressController.forward();
  }

  void _onPressUp() {
    _pressController.reverse();
  }
}
