import 'account.dart';

class Customer {
  final int? id;
  final String code;
  final String name;
  final String? phone;
  final String? email;
  final String? address;
  final String? taxNumber;
  final String? notes;
  final double creditLimit;
  final double currentBalance;
  final int? accountId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Account? account;

  Customer({
    this.id,
    required this.code,
    required this.name,
    this.phone,
    this.email,
    this.address,
    this.taxNumber,
    this.notes,
    this.creditLimit = 0.0,
    this.currentBalance = 0.0,
    this.accountId,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.account,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor from database map
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      taxNumber: map['tax_number'] as String?,
      notes: map['notes'] as String?,
      creditLimit: (map['credit_limit'] as num?)?.toDouble() ?? 0.0,
      currentBalance: (map['current_balance'] as num?)?.toDouble() ?? 0.0,
      accountId: map['account_id'] as int?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  // Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'tax_number': taxNumber,
      'notes': notes,
      'credit_limit': creditLimit,
      'current_balance': currentBalance,
      'account_id': accountId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Copy with method for updates
  Customer copyWith({
    int? id,
    String? code,
    String? name,
    String? phone,
    String? email,
    String? address,
    String? taxNumber,
    String? notes,
    double? creditLimit,
    double? currentBalance,
    int? accountId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Account? account,
  }) {
    return Customer(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      taxNumber: taxNumber ?? this.taxNumber,
      notes: notes ?? this.notes,
      creditLimit: creditLimit ?? this.creditLimit,
      currentBalance: currentBalance ?? this.currentBalance,
      accountId: accountId ?? this.accountId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      account: account ?? this.account,
    );
  }

  // Helper methods
  bool get hasDebitBalance => currentBalance > 0;
  bool get hasCreditBalance => currentBalance < 0;
  bool get isBalanceZero => currentBalance == 0;

  double get availableCredit => creditLimit - currentBalance;
  bool get isCreditExceeded => currentBalance > creditLimit && creditLimit > 0;

  String get balanceStatus {
    if (isBalanceZero) return 'متوازن';
    if (hasDebitBalance) return 'مدين';
    return 'دائن';
  }

  String get displayBalance {
    if (isBalanceZero) return '0.00';
    return '${currentBalance.abs().toStringAsFixed(2)} $balanceStatus';
  }

  // Validation
  List<String> validate() {
    List<String> errors = [];

    if (code.trim().isEmpty) {
      errors.add('رمز العميل مطلوب');
    }

    if (name.trim().isEmpty) {
      errors.add('اسم العميل مطلوب');
    }

    if (email != null && email!.isNotEmpty) {
      if (!_isValidEmail(email!)) {
        errors.add('البريد الإلكتروني غير صحيح');
      }
    }

    if (phone != null && phone!.isNotEmpty) {
      if (!_isValidPhone(phone!)) {
        errors.add('رقم الهاتف غير صحيح');
      }
    }

    if (creditLimit < 0) {
      errors.add('حد الائتمان لا يمكن أن يكون سالباً');
    }

    return errors;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPhone(String phone) {
    // Simple phone validation - adjust based on your requirements
    return RegExp(r'^[\+]?[0-9\-\s\(\)]{7,15}$').hasMatch(phone);
  }

  @override
  String toString() {
    return 'Customer{id: $id, code: $code, name: $name, balance: $currentBalance}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Customer && other.id == id && other.code == code;
  }

  @override
  int get hashCode => id.hashCode ^ code.hashCode;
}

// Customer summary for reports
class CustomerSummary {
  final int customerId;
  final String customerCode;
  final String customerName;
  final double openingBalance;
  final double totalDebits;
  final double totalCredits;
  final double closingBalance;
  final int transactionCount;

  CustomerSummary({
    required this.customerId,
    required this.customerCode,
    required this.customerName,
    required this.openingBalance,
    required this.totalDebits,
    required this.totalCredits,
    required this.closingBalance,
    required this.transactionCount,
  });

  factory CustomerSummary.fromMap(Map<String, dynamic> map) {
    return CustomerSummary(
      customerId: map['customer_id'] as int,
      customerCode: map['customer_code'] as String,
      customerName: map['customer_name'] as String,
      openingBalance: (map['opening_balance'] as num?)?.toDouble() ?? 0.0,
      totalDebits: (map['total_debits'] as num?)?.toDouble() ?? 0.0,
      totalCredits: (map['total_credits'] as num?)?.toDouble() ?? 0.0,
      closingBalance: (map['closing_balance'] as num?)?.toDouble() ?? 0.0,
      transactionCount: map['transaction_count'] as int? ?? 0,
    );
  }

  double get netMovement => totalDebits - totalCredits;

  String get balanceStatus {
    if (closingBalance == 0) return 'متوازن';
    if (closingBalance > 0) return 'مدين';
    return 'دائن';
  }

  @override
  String toString() {
    return 'CustomerSummary{id: $customerId, name: $customerName, balance: $closingBalance}';
  }
}

// Customer transaction for statement
class CustomerTransaction {
  final int? id;
  final DateTime date;
  final String reference;
  final String description;
  final double debitAmount;
  final double creditAmount;
  final double runningBalance;
  final String transactionType; // 'invoice', 'payment', 'adjustment', etc.

  CustomerTransaction({
    this.id,
    required this.date,
    required this.reference,
    required this.description,
    required this.debitAmount,
    required this.creditAmount,
    required this.runningBalance,
    required this.transactionType,
  });

  factory CustomerTransaction.fromMap(Map<String, dynamic> map) {
    return CustomerTransaction(
      id: map['id'] as int?,
      date: DateTime.parse(map['date'] as String),
      reference: map['reference'] as String,
      description: map['description'] as String,
      debitAmount: (map['debit_amount'] as num?)?.toDouble() ?? 0.0,
      creditAmount: (map['credit_amount'] as num?)?.toDouble() ?? 0.0,
      runningBalance: (map['running_balance'] as num?)?.toDouble() ?? 0.0,
      transactionType: map['transaction_type'] as String,
    );
  }

  double get amount => debitAmount > 0 ? debitAmount : creditAmount;
  bool get isDebit => debitAmount > 0;
  bool get isCredit => creditAmount > 0;
  String get type => isDebit ? 'مدين' : 'دائن';

  @override
  String toString() {
    return 'CustomerTransaction{date: $date, ref: $reference, amount: $amount, type: $type}';
  }
}
