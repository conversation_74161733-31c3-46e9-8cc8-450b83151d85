import 'package:flutter/material.dart';
import '../../models/supplier.dart';
import '../../services/supplier_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/beautiful_card.dart';
import '../../widgets/smart_notifications.dart';
import '../../animations/app_animations.dart';
import 'add_supplier_screen.dart';
import 'supplier_details_screen.dart';

/// شاشة إدارة الموردين
class SuppliersScreen extends StatefulWidget {
  const SuppliersScreen({super.key});

  @override
  State<SuppliersScreen> createState() => _SuppliersScreenState();
}

class _SuppliersScreenState extends State<SuppliersScreen> {
  final SupplierService _supplierService = SupplierService();
  List<Supplier> _suppliers = [];
  List<Supplier> _filteredSuppliers = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSuppliers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadSuppliers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _supplierService.getAllSuppliers();
      if (result.isSuccess) {
        setState(() {
          _suppliers = result.data!;
          _filteredSuppliers = _suppliers;
          _isLoading = false;
        });
      } else {
        if (!mounted) return;
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: result.error ?? 'فشل في تحميل الموردين',
        );
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      SmartNotificationManager.showError(
        context,
        title: 'خطأ',
        message: 'حدث خطأ أثناء تحميل الموردين: ${e.toString()}',
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterSuppliers(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredSuppliers = _suppliers;
      } else {
        _filteredSuppliers = _suppliers.where((supplier) {
          return supplier.name.toLowerCase().contains(query.toLowerCase()) ||
              supplier.code.toLowerCase().contains(query.toLowerCase()) ||
              (supplier.phone?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
              (supplier.email?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
              (supplier.category?.toLowerCase().contains(query.toLowerCase()) ?? false);
        }).toList();
      }
    });
  }

  Future<void> _deleteSupplier(Supplier supplier) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المورد "${supplier.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await _supplierService.deleteSupplier(supplier.id!);
      if (!mounted) return;
      
      if (result.isSuccess) {
        SmartNotificationManager.showSuccess(
          context,
          title: 'تم الحذف',
          message: 'تم حذف المورد بنجاح',
        );
        _loadSuppliers();
      } else {
        SmartNotificationManager.showError(
          context,
          title: 'خطأ',
          message: result.error ?? 'فشل في حذف المورد',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الموردين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSuppliers,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMedium),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في الموردين...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterSuppliers('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                ),
              ),
              onChanged: _filterSuppliers,
            ),
          ),

          // قائمة الموردين
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredSuppliers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.business_outlined,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: AppTheme.spacingMedium),
                            Text(
                              _searchQuery.isEmpty
                                  ? 'لا توجد موردين مسجلين'
                                  : 'لا توجد نتائج للبحث',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingSmall),
                            Text(
                              _searchQuery.isEmpty
                                  ? 'اضغط على زر + لإضافة مورد جديد'
                                  : 'جرب البحث بكلمات مختلفة',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(AppTheme.spacingMedium),
                        itemCount: _filteredSuppliers.length,
                        itemBuilder: (context, index) {
                          final supplier = _filteredSuppliers[index];
                          return StaggeredAnimation(
                            index: index,
                            child: _buildSupplierCard(supplier),
                          );
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AddSupplierScreen(),
            ),
          );
          if (result == true) {
            _loadSuppliers();
          }
        },
        icon: const Icon(Icons.add),
        label: const Text('إضافة مورد'),
      ),
    );
  }

  Widget _buildSupplierCard(Supplier supplier) {
    return BeautifulCard(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: Text(
            supplier.name.isNotEmpty ? supplier.name[0].toUpperCase() : 'م',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          supplier.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرمز: ${supplier.code}'),
            if (supplier.category != null && supplier.category!.isNotEmpty)
              Text('الفئة: ${supplier.category}'),
            if (supplier.phone != null && supplier.phone!.isNotEmpty)
              Text('الهاتف: ${supplier.phone}'),
            if (supplier.email != null && supplier.email!.isNotEmpty)
              Text('البريد: ${supplier.email}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'view':
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => SupplierDetailsScreen(supplier: supplier),
                  ),
                );
                break;
              case 'edit':
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => AddSupplierScreen(supplier: supplier),
                  ),
                ).then((result) {
                  if (result == true) {
                    _loadSuppliers();
                  }
                });
                break;
              case 'delete':
                _deleteSupplier(supplier);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: ListTile(
                leading: Icon(Icons.visibility),
                title: Text('عرض التفاصيل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('تعديل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => SupplierDetailsScreen(supplier: supplier),
            ),
          );
        },
      ),
    );
  }
}
