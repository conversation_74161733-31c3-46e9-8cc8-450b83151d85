/// نموذج الخزينة النقدية
/// Cash Vault Model for Smart Ledger
library;

enum CashVaultType {
  main('main', 'خزينة رئيسية'),
  branch('branch', 'خزينة فرع'),
  petty('petty', 'خزينة صغيرة'),
  foreign('foreign', 'خزينة عملات أجنبية');

  const CashVaultType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum CashVaultStatus {
  active('active', 'نشطة'),
  inactive('inactive', 'غير نشطة'),
  closed('closed', 'مغلقة');

  const CashVaultStatus(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

class CashVault {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final CashVaultType type;
  final CashVaultStatus status;
  final String currency;
  final double openingBalance;
  final double currentBalance;
  final double? maxLimit;
  final String? location;
  final String? responsiblePerson;
  final int? accountId; // Link to chart of accounts
  final DateTime createdAt;
  final DateTime updatedAt;

  CashVault({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.type = CashVaultType.main,
    this.status = CashVaultStatus.active,
    this.currency = 'SAR',
    this.openingBalance = 0.0,
    this.currentBalance = 0.0,
    this.maxLimit,
    this.location,
    this.responsiblePerson,
    this.accountId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory CashVault.fromMap(Map<String, dynamic> map) {
    return CashVault(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      type: CashVaultType.values.firstWhere(
        (e) => e.value == map['type'],
        orElse: () => CashVaultType.main,
      ),
      status: CashVaultStatus.values.firstWhere(
        (e) => e.value == map['status'],
        orElse: () => CashVaultStatus.active,
      ),
      currency: map['currency'] as String? ?? 'SAR',
      openingBalance: (map['opening_balance'] as num?)?.toDouble() ?? 0.0,
      currentBalance: (map['current_balance'] as num?)?.toDouble() ?? 0.0,
      maxLimit: (map['max_limit'] as num?)?.toDouble(),
      location: map['location'] as String?,
      responsiblePerson: map['responsible_person'] as String?,
      accountId: map['account_id'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'type': type.value,
      'status': status.value,
      'currency': currency,
      'opening_balance': openingBalance,
      'current_balance': currentBalance,
      'max_limit': maxLimit,
      'location': location,
      'responsible_person': responsiblePerson,
      'account_id': accountId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  CashVault copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    CashVaultType? type,
    CashVaultStatus? status,
    String? currency,
    double? openingBalance,
    double? currentBalance,
    double? maxLimit,
    String? location,
    String? responsiblePerson,
    int? accountId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashVault(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      currency: currency ?? this.currency,
      openingBalance: openingBalance ?? this.openingBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      maxLimit: maxLimit ?? this.maxLimit,
      location: location ?? this.location,
      responsiblePerson: responsiblePerson ?? this.responsiblePerson,
      accountId: accountId ?? this.accountId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CashVault{code: $code, name: $name, currentBalance: $currentBalance}';
  }

  /// Check if vault is near limit
  bool get isNearLimit {
    if (maxLimit == null) return false;
    return currentBalance >= (maxLimit! * 0.9);
  }

  /// Check if vault is over limit
  bool get isOverLimit {
    if (maxLimit == null) return false;
    return currentBalance > maxLimit!;
  }

  /// Get vault display name
  String get displayName {
    return '$name ($code)';
  }
}

/// نموذج معاملة نقدية
/// Cash Transaction Model
enum CashTransactionType {
  receipt('receipt', 'قبض'),
  payment('payment', 'دفع'),
  transfer('transfer', 'تحويل'),
  deposit('deposit', 'إيداع في البنك'),
  withdrawal('withdrawal', 'سحب من البنك'),
  exchange('exchange', 'صرافة'),
  adjustment('adjustment', 'تسوية');

  const CashTransactionType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum CashTransactionStatus {
  pending('pending', 'معلق'),
  completed('completed', 'مكتمل'),
  cancelled('cancelled', 'ملغي');

  const CashTransactionStatus(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

class CashTransaction {
  final int? id;
  final int cashVaultId;
  final String transactionNumber;
  final CashTransactionType type;
  final CashTransactionStatus status;
  final double amount;
  final String currency;
  final String description;
  final String? reference;
  final String? receivedFrom; // For receipts
  final String? paidTo; // For payments
  final int? toCashVaultId; // For transfers
  final int? bankAccountId; // For bank deposits/withdrawals
  final int? journalEntryId; // Link to journal entry
  final DateTime transactionDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  CashVault? cashVault;
  CashVault? toCashVault;

  CashTransaction({
    this.id,
    required this.cashVaultId,
    required this.transactionNumber,
    required this.type,
    this.status = CashTransactionStatus.pending,
    required this.amount,
    this.currency = 'SAR',
    required this.description,
    this.reference,
    this.receivedFrom,
    this.paidTo,
    this.toCashVaultId,
    this.bankAccountId,
    this.journalEntryId,
    DateTime? transactionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.cashVault,
    this.toCashVault,
  }) : transactionDate = transactionDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory CashTransaction.fromMap(Map<String, dynamic> map) {
    return CashTransaction(
      id: map['id'] as int?,
      cashVaultId: map['cash_vault_id'] as int,
      transactionNumber: map['transaction_number'] as String,
      type: CashTransactionType.values.firstWhere(
        (e) => e.value == map['type'],
        orElse: () => CashTransactionType.receipt,
      ),
      status: CashTransactionStatus.values.firstWhere(
        (e) => e.value == map['status'],
        orElse: () => CashTransactionStatus.pending,
      ),
      amount: (map['amount'] as num).toDouble(),
      currency: map['currency'] as String? ?? 'SAR',
      description: map['description'] as String,
      reference: map['reference'] as String?,
      receivedFrom: map['received_from'] as String?,
      paidTo: map['paid_to'] as String?,
      toCashVaultId: map['to_cash_vault_id'] as int?,
      bankAccountId: map['bank_account_id'] as int?,
      journalEntryId: map['journal_entry_id'] as int?,
      transactionDate: DateTime.parse(map['transaction_date'] as String),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cash_vault_id': cashVaultId,
      'transaction_number': transactionNumber,
      'type': type.value,
      'status': status.value,
      'amount': amount,
      'currency': currency,
      'description': description,
      'reference': reference,
      'received_from': receivedFrom,
      'paid_to': paidTo,
      'to_cash_vault_id': toCashVaultId,
      'bank_account_id': bankAccountId,
      'journal_entry_id': journalEntryId,
      'transaction_date': transactionDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'CashTransaction{transactionNumber: $transactionNumber, type: ${type.arabicName}, amount: $amount}';
  }

  /// Get signed amount (negative for payments, positive for receipts)
  double get signedAmount {
    switch (type) {
      case CashTransactionType.payment:
      case CashTransactionType.transfer:
      case CashTransactionType.deposit:
        return -amount;
      case CashTransactionType.receipt:
      case CashTransactionType.withdrawal:
        return amount;
      case CashTransactionType.exchange:
      case CashTransactionType.adjustment:
        return amount; // Can be positive or negative based on context
    }
  }
}
