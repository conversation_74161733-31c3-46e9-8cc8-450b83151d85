import 'employee.dart';

/// نموذج القسم
/// Department Model
class Department {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final int? parentDepartmentId;
  final String? managerId;
  final double? budget;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Department? parentDepartment;
  Employee? manager;
  List<Department> subDepartments = [];
  List<Employee> employees = [];

  Department({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.parentDepartmentId,
    this.managerId,
    this.budget,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// عدد الموظفين في القسم
  int get employeeCount => employees.length;

  /// عدد الأقسام الفرعية
  int get subDepartmentCount => subDepartments.length;

  /// إجمالي رواتب القسم
  double get totalSalaries {
    return employees.fold(0.0, (sum, employee) => sum + employee.basicSalary);
  }

  /// Factory constructor from database map
  factory Department.fromMap(Map<String, dynamic> map) {
    return Department(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      parentDepartmentId: map['parent_department_id'] as int?,
      managerId: map['manager_id'] as String?,
      budget: map['budget'] != null ? (map['budget'] as num).toDouble() : null,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'parent_department_id': parentDepartmentId,
      'manager_id': managerId,
      'budget': budget,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  Department copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    int? parentDepartmentId,
    String? managerId,
    double? budget,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Department(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      parentDepartmentId: parentDepartmentId ?? this.parentDepartmentId,
      managerId: managerId ?? this.managerId,
      budget: budget ?? this.budget,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج المنصب
/// Position Model
class Position {
  final int? id;
  final String code;
  final String title;
  final String? description;
  final int departmentId;
  final PositionLevel level;
  final double? minSalary;
  final double? maxSalary;
  final List<String> responsibilities;
  final List<String> requirements;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Department? department;
  List<Employee> employees = [];

  Position({
    this.id,
    required this.code,
    required this.title,
    this.description,
    required this.departmentId,
    this.level = PositionLevel.junior,
    this.minSalary,
    this.maxSalary,
    this.responsibilities = const [],
    this.requirements = const [],
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// عدد الموظفين في هذا المنصب
  int get employeeCount => employees.length;

  /// متوسط الراتب للمنصب
  double get averageSalary {
    if (employees.isEmpty) return 0.0;
    final totalSalary = employees.fold(
      0.0,
      (sum, emp) => sum + emp.basicSalary,
    );
    return totalSalary / employees.length;
  }

  /// Factory constructor from database map
  factory Position.fromMap(Map<String, dynamic> map) {
    return Position(
      id: map['id'] as int?,
      code: map['code'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      departmentId: map['department_id'] as int,
      level: PositionLevel.values.firstWhere(
        (e) => e.name == map['level'],
        orElse: () => PositionLevel.junior,
      ),
      minSalary: map['min_salary'] != null
          ? (map['min_salary'] as num).toDouble()
          : null,
      maxSalary: map['max_salary'] != null
          ? (map['max_salary'] as num).toDouble()
          : null,
      responsibilities: map['responsibilities'] != null
          ? (map['responsibilities'] as String)
                .split('|')
                .where((s) => s.isNotEmpty)
                .toList()
          : [],
      requirements: map['requirements'] != null
          ? (map['requirements'] as String)
                .split('|')
                .where((s) => s.isNotEmpty)
                .toList()
          : [],
      isActive: (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'title': title,
      'description': description,
      'department_id': departmentId,
      'level': level.name,
      'min_salary': minSalary,
      'max_salary': maxSalary,
      'responsibilities': responsibilities.join('|'),
      'requirements': requirements.join('|'),
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  Position copyWith({
    int? id,
    String? code,
    String? title,
    String? description,
    int? departmentId,
    PositionLevel? level,
    double? minSalary,
    double? maxSalary,
    List<String>? responsibilities,
    List<String>? requirements,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Position(
      id: id ?? this.id,
      code: code ?? this.code,
      title: title ?? this.title,
      description: description ?? this.description,
      departmentId: departmentId ?? this.departmentId,
      level: level ?? this.level,
      minSalary: minSalary ?? this.minSalary,
      maxSalary: maxSalary ?? this.maxSalary,
      responsibilities: responsibilities ?? this.responsibilities,
      requirements: requirements ?? this.requirements,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// مستويات المناصب
enum PositionLevel {
  intern('متدرب'),
  junior('مبتدئ'),
  mid('متوسط'),
  senior('كبير'),
  lead('قائد فريق'),
  manager('مدير'),
  director('مدير عام'),
  executive('تنفيذي');

  const PositionLevel(this.displayName);
  final String displayName;
}
