import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'revolutionary_effects.dart';

/// 📊 نظام التقارير المالية المتقدم
/// Advanced Financial Reports System
///
/// هذا الملف يحتوي على نظام تقارير مالية متقدم لا مثيل له في التاريخ
/// This file contains unprecedented advanced financial reports system in history

/// 🌟 لوحة التقارير المالية المتقدمة
/// Advanced Financial Reports Dashboard
class AdvancedReportsDashboard extends StatefulWidget {
  const AdvancedReportsDashboard({super.key});

  @override
  State<AdvancedReportsDashboard> createState() =>
      _AdvancedReportsDashboardState();
}

class _AdvancedReportsDashboardState extends State<AdvancedReportsDashboard>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _chartController;
  late AnimationController _dataController;
  late Animation<double> _mainAnimation;
  late Animation<double> _chartAnimation;
  late Animation<double> _dataAnimation;

  int _selectedReportType = 0;

  final List<String> _reportTypes = [
    'الميزانية العمومية',
    'قائمة الدخل',
    'قائمة التدفقات النقدية',
    'تقرير الأرباح والخسائر',
  ];

  final List<FinancialData> _financialData = [
    FinancialData('الإيرادات', 850000, const Color(0xFF4CAF50), 0.85),
    FinancialData('المصروفات', 620000, const Color(0xFFF44336), 0.62),
    FinancialData('صافي الربح', 230000, const Color(0xFF2196F3), 0.23),
    FinancialData('الضرائب', 45000, const Color(0xFFFF9800), 0.045),
  ];

  @override
  void initState() {
    super.initState();

    _mainController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _chartController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _dataController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _mainAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
    );

    _chartAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(parent: _chartController, curve: Curves.linear));

    _dataAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _dataController, curve: Curves.elasticOut),
    );

    _mainController.repeat(reverse: true);
    _chartController.repeat();
    _dataController.forward();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _chartController.dispose();
    _dataController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _mainAnimation,
        _chartAnimation,
        _dataAnimation,
      ]),
      builder: (context, child) {
        return HologramEffect(
          intensity: 2.2 + (_mainAnimation.value * 0.8),
          child: QuantumEnergyEffect(
            intensity: 2.5 + (_dataAnimation.value * 0.5),
            primaryColor: const Color(0xFFFF5722),
            secondaryColor: const Color(0xFFFF7043),
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFBF360C).withValues(alpha: 0.9),
                    const Color(0xFFD84315).withValues(alpha: 0.8),
                    const Color(0xFFFF5722).withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: const Color(0xFFFF5722).withValues(alpha: 0.6),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFF5722).withValues(alpha: 0.5),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 8,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // رأس التقارير المالية
                  Row(
                    children: [
                      Transform.rotate(
                        angle: _chartAnimation.value * 0.1,
                        child: Container(
                          padding: const EdgeInsets.all(18),
                          decoration: BoxDecoration(
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFFFF5722).withValues(alpha: 0.9),
                                const Color(0xFFFF7043).withValues(alpha: 0.6),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.5),
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.analytics_rounded,
                            color: Colors.white,
                            size: 36,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '📊 التقارير المالية المتقدمة',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.8,
                                    fontSize: 22,
                                  ),
                            ),
                            Text(
                              'تحليل مالي شامل ومتطور',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // محدد نوع التقرير
                  _buildReportTypeSelector(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // المخطط البياني المتقدم
                  _buildAdvancedChart(),

                  const SizedBox(height: AppTheme.spacingLarge),

                  // البيانات المالية التفصيلية
                  _buildDetailedFinancialData(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محدد نوع التقرير
  Widget _buildReportTypeSelector() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _reportTypes.length,
        itemBuilder: (context, index) {
          final isSelected = _selectedReportType == index;
          return GestureDetector(
            onTap: () => setState(() => _selectedReportType = index),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? const Color(0xFFFF5722).withValues(alpha: 0.3)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: isSelected
                    ? Border.all(color: Colors.white.withValues(alpha: 0.3))
                    : null,
              ),
              child: Center(
                child: Text(
                  _reportTypes[index],
                  style: TextStyle(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.6),
                    fontWeight: isSelected
                        ? FontWeight.bold
                        : FontWeight.normal,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء المخطط البياني المتقدم
  Widget _buildAdvancedChart() {
    return Container(
      height: 250,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: const Color(0xFFFF5722).withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // الخلفية المتدرجة
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                colors: [
                  const Color(0xFFFF5722).withValues(alpha: 0.1),
                  const Color(0xFFFF7043).withValues(alpha: 0.05),
                  Colors.transparent,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
            ),
          ),

          // الشبكة
          CustomPaint(
            size: const Size(double.infinity, 250),
            painter: GridPainter(),
          ),

          // المخطط البياني
          Padding(
            padding: const EdgeInsets.all(20),
            child: CustomPaint(
              size: const Size(double.infinity, 210),
              painter: AdvancedChartPainter(
                data: _financialData,
                animationValue: _dataAnimation.value,
                chartType: _selectedReportType,
              ),
            ),
          ),

          // مؤشرات القيم
          Positioned(
            top: 15,
            right: 15,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    _reportTypes[_selectedReportType],
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                ...List.generate(_financialData.length, (index) {
                  final data = _financialData[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: data.color,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          data.label,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء البيانات المالية التفصيلية
  Widget _buildDetailedFinancialData() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '💰 البيانات المالية التفصيلية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),

        ...List.generate(_financialData.length, (index) {
          final data = _financialData[index];
          return _buildFinancialDataCard(data, index);
        }),

        const SizedBox(height: AppTheme.spacingMedium),

        // ملخص الأداء المالي
        _buildPerformanceSummary(),
      ],
    );
  }

  Widget _buildFinancialDataCard(FinancialData data, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: data.color.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: data.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  _getIconForDataType(index),
                  color: data.color,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.label,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${data.value.toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        color: data.color,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              // مؤشر النسبة المئوية
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: data.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(data.percentage * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: data.color,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          // شريط التقدم
          LinearProgressIndicator(
            value: data.percentage * _dataAnimation.value,
            backgroundColor: Colors.white.withValues(alpha: 0.1),
            valueColor: AlwaysStoppedAnimation<Color>(data.color),
          ),
        ],
      ),
    );
  }

  /// بناء ملخص الأداء المالي
  Widget _buildPerformanceSummary() {
    final totalRevenue = _financialData[0].value;
    final totalExpenses = _financialData[1].value;
    final netProfit = _financialData[2].value;
    final profitMargin = (netProfit / totalRevenue) * 100;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.trending_up_rounded,
                color: Color(0xFF4CAF50),
                size: 24,
              ),
              const SizedBox(width: AppTheme.spacingSmall),
              const Text(
                '📈 ملخص الأداء المالي',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'هامش الربح',
                  '${profitMargin.toStringAsFixed(1)}%',
                  profitMargin > 20
                      ? const Color(0xFF4CAF50)
                      : const Color(0xFFFF9800),
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'نسبة المصروفات',
                  '${((totalExpenses / totalRevenue) * 100).toStringAsFixed(1)}%',
                  const Color(0xFFF44336),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMedium),

          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'العائد على الاستثمار',
                  '${((netProfit / totalRevenue) * 100).toStringAsFixed(1)}%',
                  const Color(0xFF2196F3),
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'معدل النمو',
                  '+12.5%',
                  const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  IconData _getIconForDataType(int index) {
    switch (index) {
      case 0:
        return Icons.trending_up_rounded;
      case 1:
        return Icons.trending_down_rounded;
      case 2:
        return Icons.account_balance_wallet_rounded;
      case 3:
        return Icons.receipt_long_rounded;
      default:
        return Icons.analytics_rounded;
    }
  }
}

/// رسام الشبكة
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..strokeWidth = 0.5;

    // خطوط أفقية
    for (int i = 0; i <= 10; i++) {
      final y = (size.height / 10) * i;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // خطوط عمودية
    for (int i = 0; i <= 10; i++) {
      final x = (size.width / 10) * i;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام المخطط البياني المتقدم
class AdvancedChartPainter extends CustomPainter {
  final List<FinancialData> data;
  final double animationValue;
  final int chartType;

  AdvancedChartPainter({
    required this.data,
    required this.animationValue,
    required this.chartType,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (chartType == 0 || chartType == 1) {
      _drawBarChart(canvas, size);
    } else {
      _drawLineChart(canvas, size);
    }
  }

  void _drawBarChart(Canvas canvas, Size size) {
    final barWidth = size.width / (data.length * 2);
    final maxValue = data.map((d) => d.value).reduce(math.max);

    for (int i = 0; i < data.length; i++) {
      final barHeight =
          (data[i].value / maxValue) * size.height * 0.8 * animationValue;
      final x = (i * 2 + 1) * barWidth;
      final y = size.height - barHeight;

      final paint = Paint()
        ..color = data[i].color
        ..style = PaintingStyle.fill;

      final rect = Rect.fromLTWH(x, y, barWidth, barHeight);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(8)),
        paint,
      );

      // رسم القيمة فوق العمود
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${(data[i].value / 1000).toStringAsFixed(0)}K',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x + (barWidth - textPainter.width) / 2, y - 20),
      );
    }
  }

  void _drawLineChart(Canvas canvas, Size size) {
    final path = Path();
    final paint = Paint()
      ..color = const Color(0xFF2196F3)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final maxValue = data.map((d) => d.value).reduce(math.max);
    final stepX = size.width / (data.length - 1);

    for (int i = 0; i < data.length; i++) {
      final x = i * stepX;
      final y =
          size.height -
          (data[i].value / maxValue) * size.height * 0.8 * animationValue;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // رسم النقاط
      canvas.drawCircle(Offset(x, y), 4, Paint()..color = data[i].color);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// نموذج البيانات المالية
class FinancialData {
  final String label;
  final double value;
  final Color color;
  final double percentage;

  FinancialData(this.label, this.value, this.color, this.percentage);
}
