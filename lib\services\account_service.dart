import '../models/account.dart';
import '../database/account_dao.dart';

class AccountService {
  final AccountDao _accountDao = AccountDao();

  // Create a new account
  Future<Result<Account>> createAccount(Account account) async {
    try {
      // Validate account data
      List<String> errors = account.validate();
      if (errors.isNotEmpty) {
        return Result.error(errors.join(', '));
      }

      // Check if code already exists
      bool codeExists = await _accountDao.isCodeExists(account.code);
      if (codeExists) {
        return Result.error('رمز الحساب موجود مسبقاً');
      }

      // If parent account is specified, validate it exists and is of compatible type
      if (account.parentId != null) {
        Account? parentAccount = await _accountDao.getAccountById(
          account.parentId!,
        );
        if (parentAccount == null) {
          return Result.error('الحساب الأب غير موجود');
        }

        if (parentAccount.accountType != account.accountType) {
          return Result.error('نوع الحساب يجب أن يطابق نوع الحساب الأب');
        }
      }

      // Create the account
      int accountId = await _accountDao.insertAccount(account);
      Account createdAccount = account.copyWith(id: accountId);

      return Result.success(createdAccount);
    } catch (e) {
      return Result.error('خطأ في إنشاء الحساب: ${e.toString()}');
    }
  }

  // Update an existing account
  Future<Result<Account>> updateAccount(Account account) async {
    try {
      if (account.id == null) {
        return Result.error('معرف الحساب مطلوب للتحديث');
      }

      // Validate account data
      List<String> errors = account.validate();
      if (errors.isNotEmpty) {
        return Result.error(errors.join(', '));
      }

      // Check if code already exists (excluding current account)
      bool codeExists = await _accountDao.isCodeExists(
        account.code,
        excludeId: account.id,
      );
      if (codeExists) {
        return Result.error('رمز الحساب موجود مسبقاً');
      }

      // Check if account has transactions before allowing certain changes
      bool hasTransactions = await _accountDao.hasTransactions(account.id!);
      if (hasTransactions) {
        Account? existingAccount = await _accountDao.getAccountById(
          account.id!,
        );
        if (existingAccount != null &&
            existingAccount.accountType != account.accountType) {
          return Result.error('لا يمكن تغيير نوع الحساب بعد وجود حركات عليه');
        }
      }

      // Update the account
      await _accountDao.updateAccount(account);

      return Result.success(account);
    } catch (e) {
      return Result.error('خطأ في تحديث الحساب: ${e.toString()}');
    }
  }

  // Delete an account
  Future<Result<bool>> deleteAccount(int accountId) async {
    try {
      // Check if account exists
      Account? account = await _accountDao.getAccountById(accountId);
      if (account == null) {
        return Result.error('الحساب غير موجود');
      }

      // Check if account has children
      bool hasChildren = await _accountDao.hasChildren(accountId);
      if (hasChildren) {
        return Result.error('لا يمكن حذف حساب له حسابات فرعية');
      }

      // Check if account has transactions
      bool hasTransactions = await _accountDao.hasTransactions(accountId);
      if (hasTransactions) {
        return Result.error('لا يمكن حذف حساب له حركات محاسبية');
      }

      // Soft delete (deactivate) the account
      await _accountDao.deactivateAccount(accountId);

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في حذف الحساب: ${e.toString()}');
    }
  }

  // Get account by ID
  Future<Result<Account>> getAccountById(int accountId) async {
    try {
      Account? account = await _accountDao.getAccountById(accountId);
      if (account == null) {
        return Result.error('الحساب غير موجود');
      }
      return Result.success(account);
    } catch (e) {
      return Result.error('خطأ في جلب الحساب: ${e.toString()}');
    }
  }

  // Get account by code
  Future<Result<Account>> getAccountByCode(String code) async {
    try {
      Account? account = await _accountDao.getAccountByCode(code);
      if (account == null) {
        return Result.error('الحساب غير موجود');
      }
      return Result.success(account);
    } catch (e) {
      return Result.error('خطأ في جلب الحساب: ${e.toString()}');
    }
  }

  // Get all accounts
  Future<Result<List<Account>>> getAllAccounts() async {
    try {
      List<Account> accounts = await _accountDao.getAllAccounts();
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في جلب الحسابات: ${e.toString()}');
    }
  }

  // Get active accounts only
  Future<Result<List<Account>>> getActiveAccounts() async {
    try {
      List<Account> accounts = await _accountDao.getActiveAccounts();
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في جلب الحسابات النشطة: ${e.toString()}');
    }
  }

  // Get accounts by type
  Future<Result<List<Account>>> getAccountsByType(AccountType type) async {
    try {
      List<Account> accounts = await _accountDao.getAccountsByType(type);
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في جلب الحسابات: ${e.toString()}');
    }
  }

  // Search accounts
  Future<Result<List<Account>>> searchAccounts(String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getActiveAccounts();
      }

      List<Account> accounts = await _accountDao.searchAccounts(query);
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  // Get account tree structure
  Future<Result<AccountTree>> getAccountTree() async {
    try {
      AccountTree tree = await _accountDao.getAccountTree();
      return Result.success(tree);
    } catch (e) {
      return Result.error('خطأ في جلب شجرة الحسابات: ${e.toString()}');
    }
  }

  // Get next available account code
  Future<Result<String>> getNextAccountCode(String parentCode) async {
    try {
      String nextCode = await _accountDao.getNextAccountCode(parentCode);
      return Result.success(nextCode);
    } catch (e) {
      return Result.error('خطأ في توليد رمز الحساب: ${e.toString()}');
    }
  }

  // Get balance summary by account type
  Future<Result<Map<AccountType, double>>> getBalanceSummary() async {
    try {
      Map<AccountType, double> summary = await _accountDao
          .getBalanceSummaryByType();
      return Result.success(summary);
    } catch (e) {
      return Result.error('خطأ في جلب ملخص الأرصدة: ${e.toString()}');
    }
  }

  // Get accounts with non-zero balances
  Future<Result<List<Account>>> getAccountsWithBalance() async {
    try {
      List<Account> accounts = await _accountDao.getAccountsWithBalance();
      return Result.success(accounts);
    } catch (e) {
      return Result.error('خطأ في جلب الحسابات ذات الأرصدة: ${e.toString()}');
    }
  }

  // Validate account hierarchy
  Future<Result<bool>> validateAccountHierarchy(
    int accountId,
    int? newParentId,
  ) async {
    try {
      if (newParentId == null) {
        return Result.success(true); // Root account is always valid
      }

      if (accountId == newParentId) {
        return Result.error('لا يمكن أن يكون الحساب أباً لنفسه');
      }

      // Check if new parent exists
      Account? parentAccount = await _accountDao.getAccountById(newParentId);
      if (parentAccount == null) {
        return Result.error('الحساب الأب غير موجود');
      }

      // Check if this would create a circular reference
      Account? currentParent = parentAccount;
      while (currentParent?.parentId != null) {
        if (currentParent!.parentId == accountId) {
          return Result.error('لا يمكن إنشاء مرجع دائري في شجرة الحسابات');
        }
        currentParent = await _accountDao.getAccountById(
          currentParent.parentId!,
        );
      }

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في التحقق من هيكل الحسابات: ${e.toString()}');
    }
  }

  // Get account statistics
  Future<Result<Map<String, dynamic>>> getAccountStatistics() async {
    try {
      Map<String, dynamic> stats = await _accountDao.getAccountStatistics();
      return Result.success(stats);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات الحسابات: ${e.toString()}');
    }
  }

  // Initialize default chart of accounts
  Future<Result<bool>> initializeDefaultAccounts() async {
    try {
      // Check if accounts already exist
      List<Account> existingAccounts = await _accountDao.getAllAccounts();
      if (existingAccounts.isNotEmpty) {
        return Result.error('دليل الحسابات موجود مسبقاً');
      }

      // Create default accounts based on the schema
      List<Account> defaultAccounts = _createDefaultAccounts();

      await _accountDao.insertAccountsBatch(defaultAccounts);

      return Result.success(true);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء دليل الحسابات الافتراضي: ${e.toString()}',
      );
    }
  }

  // Create default chart of accounts
  List<Account> _createDefaultAccounts() {
    return [
      // Assets (الأصول)
      Account(code: '1', name: 'الأصول', accountType: AccountType.asset),
      Account(
        code: '11',
        name: 'الأصول المتداولة',
        accountType: AccountType.asset,
        parentId: 1,
      ),
      Account(
        code: '111',
        name: 'النقدية',
        accountType: AccountType.asset,
        parentId: 2,
      ),
      Account(
        code: '1111',
        name: 'الصندوق',
        accountType: AccountType.asset,
        parentId: 3,
      ),
      Account(
        code: '1112',
        name: 'البنك',
        accountType: AccountType.asset,
        parentId: 3,
      ),
      Account(
        code: '112',
        name: 'العملاء',
        accountType: AccountType.asset,
        parentId: 2,
      ),
      Account(
        code: '113',
        name: 'المخزون',
        accountType: AccountType.asset,
        parentId: 2,
      ),

      // Liabilities (الخصوم)
      Account(code: '2', name: 'الخصوم', accountType: AccountType.liability),
      Account(
        code: '21',
        name: 'الخصوم المتداولة',
        accountType: AccountType.liability,
        parentId: 8,
      ),
      Account(
        code: '211',
        name: 'الموردون',
        accountType: AccountType.liability,
        parentId: 9,
      ),
      Account(
        code: '212',
        name: 'المصروفات المستحقة',
        accountType: AccountType.liability,
        parentId: 9,
      ),

      // Equity (حقوق الملكية)
      Account(code: '3', name: 'حقوق الملكية', accountType: AccountType.equity),
      Account(
        code: '31',
        name: 'رأس المال',
        accountType: AccountType.equity,
        parentId: 12,
      ),
      Account(
        code: '32',
        name: 'الأرباح المحتجزة',
        accountType: AccountType.equity,
        parentId: 12,
      ),

      // Revenue (الإيرادات)
      Account(code: '4', name: 'الإيرادات', accountType: AccountType.revenue),
      Account(
        code: '41',
        name: 'إيرادات المبيعات',
        accountType: AccountType.revenue,
        parentId: 15,
      ),
      Account(
        code: '42',
        name: 'إيرادات أخرى',
        accountType: AccountType.revenue,
        parentId: 15,
      ),

      // Expenses (المصروفات)
      Account(code: '5', name: 'المصروفات', accountType: AccountType.expense),
      Account(
        code: '51',
        name: 'تكلفة البضاعة المباعة',
        accountType: AccountType.expense,
        parentId: 18,
      ),
      Account(
        code: '52',
        name: 'مصروفات التشغيل',
        accountType: AccountType.expense,
        parentId: 18,
      ),
      Account(
        code: '521',
        name: 'مصروفات الرواتب',
        accountType: AccountType.expense,
        parentId: 20,
      ),
      Account(
        code: '522',
        name: 'مصروفات الإيجار',
        accountType: AccountType.expense,
        parentId: 20,
      ),
      Account(
        code: '523',
        name: 'مصروفات الكهرباء',
        accountType: AccountType.expense,
        parentId: 20,
      ),
    ];
  }
}

// Result class for handling success/error states
class Result<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  Result._(this.data, this.error, this.isSuccess);

  factory Result.success(T data) => Result._(data, null, true);
  factory Result.error(String error) => Result._(null, error, false);

  bool get isError => !isSuccess;
}
