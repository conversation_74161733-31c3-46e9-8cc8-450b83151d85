/// خدمة التقارير المالية
/// Financial Report Service for Smart Ledger
library;

import 'dart:io';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import '../models/financial_report.dart';
import '../models/financial_reports.dart' as reports;
import '../models/account.dart';

import '../database/account_dao.dart';
import '../database/journal_entry_dao.dart';
import '../database/bank_account_dao.dart';
import '../database/cash_vault_dao.dart';
import '../utils/result.dart';

class FinancialReportService {
  static final FinancialReportService _instance =
      FinancialReportService._internal();
  factory FinancialReportService() => _instance;
  FinancialReportService._internal();

  final AccountDao _accountDao = AccountDao();
  final JournalEntryDao _journalDao = JournalEntryDao();
  final BankAccountDao _bankDao = BankAccountDao();
  final CashVaultDao _cashDao = CashVaultDao();

  /// Generate Balance Sheet
  Future<Result<FinancialReport>> generateBalanceSheet({
    required DateTime asOfDate,
    bool includeZeroBalances = false,
  }) async {
    try {
      final accounts = await _accountDao.getAllAccounts();
      final balances = await _getAccountBalances(accounts, asOfDate);

      // Assets Section
      final assetLines = await _buildAccountLines(
        balances
            .where((b) => b.account.accountType == AccountType.asset)
            .toList(),
        includeZeroBalances,
      );
      final assetsSection = ReportSection(
        title: 'الأصول',
        order: 1,
        lines: assetLines,
        totals: {
          'total': assetLines.fold(0.0, (sum, line) => sum + line.amount),
        },
      );

      // Liabilities Section
      final liabilityLines = await _buildAccountLines(
        balances
            .where((b) => b.account.accountType == AccountType.liability)
            .toList(),
        includeZeroBalances,
      );
      final liabilitiesSection = ReportSection(
        title: 'الخصوم',
        order: 2,
        lines: liabilityLines,
        totals: {
          'total': liabilityLines.fold(0.0, (sum, line) => sum + line.amount),
        },
      );

      // Equity Section
      final equityLines = await _buildAccountLines(
        balances
            .where((b) => b.account.accountType == AccountType.equity)
            .toList(),
        includeZeroBalances,
      );
      final equitySection = ReportSection(
        title: 'حقوق الملكية',
        order: 3,
        lines: equityLines,
        totals: {
          'total': equityLines.fold(0.0, (sum, line) => sum + line.amount),
        },
      );

      final totalAssets = assetsSection.totals['total'] as double;
      final totalLiabilitiesEquity =
          (liabilitiesSection.totals['total'] as double) +
          (equitySection.totals['total'] as double);

      final report = FinancialReport(
        type: ReportType.balanceSheet,
        title: 'الميزانية العمومية',
        subtitle: 'كما في ${_formatDate(asOfDate)}',
        fromDate: asOfDate,
        toDate: asOfDate,
        sections: [assetsSection, liabilitiesSection, equitySection],
        summary: {
          'total_assets': totalAssets,
          'total_liabilities_equity': totalLiabilitiesEquity,
          'is_balanced': (totalAssets - totalLiabilitiesEquity).abs() < 0.01,
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء الميزانية العمومية: ${e.toString()}');
    }
  }

  /// Generate Income Statement
  Future<Result<FinancialReport>> generateIncomeStatement({
    required DateTime fromDate,
    required DateTime toDate,
    bool includeZeroBalances = false,
  }) async {
    try {
      final accounts = await _accountDao.getAllAccounts();
      final balances = await _getAccountBalancesPeriod(
        accounts,
        fromDate,
        toDate,
      );

      // Revenue Section
      final revenueLines = await _buildAccountLines(
        balances
            .where((b) => b.account.accountType == AccountType.revenue)
            .toList(),
        includeZeroBalances,
      );
      final revenueSection = ReportSection(
        title: 'الإيرادات',
        order: 1,
        lines: revenueLines,
        totals: {
          'total': revenueLines.fold(0.0, (sum, line) => sum + line.amount),
        },
      );

      // Expense Section
      final expenseLines = await _buildAccountLines(
        balances
            .where((b) => b.account.accountType == AccountType.expense)
            .toList(),
        includeZeroBalances,
      );
      final expenseSection = ReportSection(
        title: 'المصروفات',
        order: 2,
        lines: expenseLines,
        totals: {
          'total': expenseLines.fold(0.0, (sum, line) => sum + line.amount),
        },
      );

      final totalRevenue = revenueSection.totals['total'] as double;
      final totalExpenses = expenseSection.totals['total'] as double;
      final netIncome = totalRevenue - totalExpenses;

      final report = FinancialReport(
        type: ReportType.incomeStatement,
        title: 'قائمة الدخل',
        subtitle:
            'للفترة من ${_formatDate(fromDate)} إلى ${_formatDate(toDate)}',
        fromDate: fromDate,
        toDate: toDate,
        sections: [revenueSection, expenseSection],
        summary: {
          'total_revenue': totalRevenue,
          'total_expenses': totalExpenses,
          'net_income': netIncome,
          'profit_margin': totalRevenue != 0
              ? (netIncome / totalRevenue) * 100
              : 0,
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء قائمة الدخل: ${e.toString()}');
    }
  }

  /// Generate Trial Balance
  Future<Result<TrialBalance>> generateTrialBalance({
    required DateTime asOfDate,
    bool includeZeroBalances = false,
  }) async {
    try {
      final accounts = await _accountDao.getAllAccounts();
      final balances = await _getAccountBalances(accounts, asOfDate);

      final lines = <TrialBalanceLine>[];

      for (final balance in balances) {
        if (!includeZeroBalances && balance.balance == 0) continue;

        final debitBalance = balance.balance > 0 ? balance.balance : 0.0;
        final creditBalance = balance.balance < 0 ? balance.balance.abs() : 0.0;

        lines.add(
          TrialBalanceLine(
            accountCode: balance.account.code,
            accountName: balance.account.name,
            accountType: balance.account.accountType.toString(),
            debitBalance: debitBalance,
            creditBalance: creditBalance,
          ),
        );
      }

      // Sort by account code
      lines.sort((a, b) => a.accountCode.compareTo(b.accountCode));

      final trialBalance = TrialBalance(asOfDate: asOfDate, lines: lines);

      return Result.success(trialBalance);
    } catch (e) {
      return Result.error('خطأ في إنشاء ميزان المراجعة: ${e.toString()}');
    }
  }

  /// Generate Cash Flow Statement
  Future<Result<CashFlowStatement>> generateCashFlowStatement({
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    try {
      // Get cash accounts from chart of accounts
      final cashAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      final cashAccountIds = cashAccounts
          .where(
            (a) =>
                a.name.contains('نقد') ||
                a.name.contains('بنك') ||
                a.name.contains('خزينة'),
          )
          .map((a) => a.id!)
          .toList();

      // Also get bank accounts for more accurate cash reporting
      final bankAccounts = await _bankDao.getActiveBankAccounts();
      final bankAccountIds = bankAccounts
          .map((ba) => ba.accountId)
          .where((id) => id != null)
          .cast<int>()
          .toList();

      // Get cash vaults for more accurate cash reporting
      final cashVaults = await _cashDao.getActiveCashVaults();
      final cashVaultAccountIds = cashVaults
          .map((cv) => cv.accountId)
          .where((id) => id != null)
          .cast<int>()
          .toList();

      // Combine cash, bank, and vault account IDs
      final allCashAccountIds = {
        ...cashAccountIds,
        ...bankAccountIds,
        ...cashVaultAccountIds,
      }.toList();

      // Calculate beginning and ending cash
      final beginningDate = fromDate.subtract(const Duration(days: 1));
      final beginningCash = await _calculateCashBalance(
        allCashAccountIds,
        beginningDate,
      );
      final endingCash = await _calculateCashBalance(allCashAccountIds, toDate);

      // Operating Activities
      final operatingItems = await _getOperatingCashFlows(fromDate, toDate);
      final operatingSection = CashFlowSection(
        title: 'الأنشطة التشغيلية',
        items: operatingItems,
      );

      // Investing Activities
      final investingItems = await _getInvestingCashFlows(fromDate, toDate);
      final investingSection = CashFlowSection(
        title: 'الأنشطة الاستثمارية',
        items: investingItems,
      );

      // Financing Activities
      final financingItems = await _getFinancingCashFlows(fromDate, toDate);
      final financingSection = CashFlowSection(
        title: 'الأنشطة التمويلية',
        items: financingItems,
      );

      final cashFlow = CashFlowStatement(
        fromDate: fromDate,
        toDate: toDate,
        operatingActivities: operatingSection,
        investingActivities: investingSection,
        financingActivities: financingSection,
        beginningCash: beginningCash,
        endingCash: endingCash,
      );

      return Result.success(cashFlow);
    } catch (e) {
      return Result.error('خطأ في إنشاء قائمة التدفق النقدي: ${e.toString()}');
    }
  }

  /// Generate Account Statement
  Future<Result<FinancialReport>> generateAccountStatement({
    required int accountId,
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    try {
      final account = await _accountDao.getAccountById(accountId);
      if (account == null) {
        return Result.error('الحساب غير موجود');
      }

      final entries = await _journalDao.getJournalEntriesByDateRange(
        fromDate,
        toDate,
      );

      final lines = <ReportLine>[];
      double runningBalance = await _getAccountBalance(
        accountId,
        fromDate.subtract(const Duration(days: 1)),
      );

      for (final entry in entries) {
        final line = entry.lines.firstWhere((l) => l.accountId == accountId);
        final amount = line.debitAmount - line.creditAmount;
        runningBalance += amount;

        lines.add(
          ReportLine(
            accountCode: entry.entryNumber,
            accountName: entry.description,
            amount: amount,
            details: {
              'date': entry.date,
              'running_balance': runningBalance,
              'reference': entry.reference,
            },
          ),
        );
      }

      final section = ReportSection(
        title: 'كشف حساب ${account.name}',
        order: 1,
        lines: lines,
        totals: {
          'beginning_balance':
              runningBalance -
              lines.fold(0.0, (sum, line) => sum + line.amount),
          'ending_balance': runningBalance,
          'total_debits': lines
              .where((l) => l.amount > 0)
              .fold(0.0, (sum, line) => sum + line.amount),
          'total_credits': lines
              .where((l) => l.amount < 0)
              .fold(0.0, (sum, line) => sum + line.amount.abs()),
        },
      );

      final report = FinancialReport(
        type: ReportType.accountStatement,
        title: 'كشف حساب',
        subtitle: '${account.name} (${account.code})',
        fromDate: fromDate,
        toDate: toDate,
        sections: [section],
        summary: section.totals,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء كشف الحساب: ${e.toString()}');
    }
  }

  /// Generate Bank Reconciliation Report
  Future<Result<FinancialReport>> generateBankReconciliationReport({
    required int bankAccountId,
    required DateTime asOfDate,
  }) async {
    try {
      final bankAccount = await _bankDao.getBankAccountById(bankAccountId);
      if (bankAccount == null) {
        return Result.error('الحساب البنكي غير موجود');
      }

      // Get bank account balance from bank records
      final bankBalance = bankAccount.currentBalance;

      // Get book balance from chart of accounts
      final bookBalance = bankAccount.accountId != null
          ? await _getAccountBalance(bankAccount.accountId!, asOfDate)
          : 0.0;

      final lines = <ReportLine>[
        ReportLine(
          accountCode: 'BANK',
          accountName: 'رصيد البنك حسب كشف الحساب',
          amount: bankBalance,
        ),
        ReportLine(
          accountCode: 'BOOK',
          accountName: 'رصيد الدفاتر',
          amount: bookBalance,
        ),
        ReportLine(
          accountCode: 'DIFF',
          accountName: 'الفرق',
          amount: bankBalance - bookBalance,
        ),
      ];

      final section = ReportSection(
        title: 'تسوية الحساب البنكي - ${bankAccount.accountName}',
        order: 1,
        lines: lines,
        totals: {
          'bank_balance': bankBalance,
          'book_balance': bookBalance,
          'difference': bankBalance - bookBalance,
          'is_reconciled': (bankBalance - bookBalance).abs() < 0.01,
        },
      );

      final report = FinancialReport(
        type: ReportType.accountStatement,
        title: 'تقرير تسوية الحساب البنكي',
        subtitle: '${bankAccount.accountName} - ${bankAccount.bankName}',
        fromDate: asOfDate,
        toDate: asOfDate,
        sections: [section],
        summary: section.totals,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error(
        'خطأ في إنشاء تقرير التسوية البنكية: ${e.toString()}',
      );
    }
  }

  /// Generate General Ledger
  Future<Result<reports.GeneralLedgerReport>> generateGeneralLedger({
    required DateTime fromDate,
    required DateTime toDate,
    List<int>? accountIds,
  }) async {
    try {
      // Get accounts to include
      List<Account> accounts;
      if (accountIds != null && accountIds.isNotEmpty) {
        accounts = [];
        for (int id in accountIds) {
          final account = await _accountDao.getAccountById(id);
          if (account != null) {
            accounts.add(account);
          }
        }
      } else {
        accounts = await _accountDao.getAllAccounts();
      }

      final accountStatements = <reports.AccountStatementReport>[];

      for (final account in accounts) {
        // Generate account statement for each account
        final entries = await _journalDao.getJournalEntriesByDateRange(
          fromDate,
          toDate,
        );

        final items = <reports.AccountStatementItem>[];
        final transactions = <reports.AccountStatementTransaction>[];
        double runningBalance = await _getAccountBalance(
          account.id!,
          fromDate.subtract(const Duration(days: 1)),
        );
        double totalDebits = 0.0;
        double totalCredits = 0.0;

        for (final entry in entries) {
          for (final line in entry.lines) {
            if (line.accountId == account.id) {
              final debitAmount = line.debitAmount;
              final creditAmount = line.creditAmount;
              runningBalance += debitAmount - creditAmount;
              totalDebits += debitAmount;
              totalCredits += creditAmount;

              items.add(
                reports.AccountStatementItem(
                  date: entry.date,
                  entryNumber: entry.entryNumber,
                  description: entry.description,
                  reference: entry.reference,
                  debitAmount: debitAmount,
                  creditAmount: creditAmount,
                  balance: runningBalance,
                ),
              );

              transactions.add(
                reports.AccountStatementTransaction(
                  date: entry.date,
                  description: entry.description,
                  debitAmount: debitAmount,
                  creditAmount: creditAmount,
                  balance: runningBalance,
                ),
              );
            }
          }
        }

        final accountStatement = reports.AccountStatementReport(
          account: account,
          startDate: fromDate,
          endDate: toDate,
          openingBalance: runningBalance - (totalDebits - totalCredits),
          closingBalance: runningBalance,
          items: items,
          transactions: transactions,
          totalDebits: totalDebits,
          totalCredits: totalCredits,
        );

        accountStatements.add(accountStatement);
      }

      final report = reports.GeneralLedgerReport(
        startDate: fromDate,
        endDate: toDate,
        accountStatements: accountStatements,
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في إنشاء دفتر الأستاذ العام: ${e.toString()}');
    }
  }

  /// Get account balances as of date
  Future<List<AccountBalance>> _getAccountBalances(
    List<Account> accounts,
    DateTime asOfDate,
  ) async {
    final balances = <AccountBalance>[];

    for (final account in accounts) {
      final balance = await _getAccountBalance(account.id!, asOfDate);
      balances.add(AccountBalance(account: account, balance: balance));
    }

    return balances;
  }

  /// Get account balances for period
  Future<List<AccountBalance>> _getAccountBalancesPeriod(
    List<Account> accounts,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    final balances = <AccountBalance>[];

    for (final account in accounts) {
      final balance = await _getAccountBalancePeriod(
        account.id!,
        fromDate,
        toDate,
      );
      balances.add(AccountBalance(account: account, balance: balance));
    }

    return balances;
  }

  /// Get account balance as of date
  Future<double> _getAccountBalance(int accountId, DateTime asOfDate) async {
    try {
      // Get the account to determine its type for balance calculation
      final account = await _accountDao.getAccountById(accountId);
      if (account == null) return 0.0;

      // Get all transactions for this account
      final transactions = await _journalDao.getAccountTransactions(accountId);

      double totalDebit = 0.0;
      double totalCredit = 0.0;

      // Filter transactions up to the specified date and sum debits/credits
      for (final transaction in transactions) {
        // We need to get the journal entry to check the date and posted status
        final journalEntry = await _journalDao.getJournalEntryById(
          transaction.journalEntryId,
        );

        if (journalEntry != null &&
            journalEntry.date.isBefore(
              asOfDate.add(Duration(days: 1)),
            ) && // Include same day
            journalEntry.isPosted) {
          totalDebit += transaction.debitAmount;
          totalCredit += transaction.creditAmount;
        }
      }

      // Calculate balance based on account type
      // For debit normal accounts (Assets, Expenses): Debit - Credit
      // For credit normal accounts (Liabilities, Equity, Revenue): Credit - Debit
      if (account.accountType.isDebitNormal) {
        return totalDebit - totalCredit;
      } else {
        return totalCredit - totalDebit;
      }
    } catch (e) {
      // Return 0.0 for balance calculation errors to avoid breaking reports
      // The error will be logged at the service level if needed
      return 0.0;
    }
  }

  /// Get account balance for period
  Future<double> _getAccountBalancePeriod(
    int accountId,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      // Get the account to determine its type for balance calculation
      final account = await _accountDao.getAccountById(accountId);
      if (account == null) return 0.0;

      // Get all transactions for this account
      final transactions = await _journalDao.getAccountTransactions(accountId);

      double totalDebit = 0.0;
      double totalCredit = 0.0;

      // Filter transactions within the specified period and sum debits/credits
      for (final transaction in transactions) {
        // We need to get the journal entry to check the date and posted status
        final journalEntry = await _journalDao.getJournalEntryById(
          transaction.journalEntryId,
        );

        if (journalEntry != null &&
            journalEntry.date.isAfter(
              fromDate.subtract(Duration(days: 1)),
            ) && // Include fromDate
            journalEntry.date.isBefore(
              toDate.add(Duration(days: 1)),
            ) && // Include toDate
            journalEntry.isPosted) {
          totalDebit += transaction.debitAmount;
          totalCredit += transaction.creditAmount;
        }
      }

      // Calculate balance based on account type
      // For debit normal accounts (Assets, Expenses): Debit - Credit
      // For credit normal accounts (Liabilities, Equity, Revenue): Credit - Debit
      if (account.accountType.isDebitNormal) {
        return totalDebit - totalCredit;
      } else {
        return totalCredit - totalDebit;
      }
    } catch (e) {
      // Return 0.0 for balance calculation errors to avoid breaking reports
      // The error will be logged at the service level if needed
      return 0.0;
    }
  }

  /// Build account lines for report
  Future<List<ReportLine>> _buildAccountLines(
    List<AccountBalance> balances,
    bool includeZeroBalances,
  ) async {
    final lines = <ReportLine>[];

    for (final balance in balances) {
      if (!includeZeroBalances && balance.balance == 0) continue;

      lines.add(
        ReportLine(
          accountCode: balance.account.code,
          accountName: balance.account.name,
          amount: balance.balance,
          level: _getAccountLevel(balance.account.code),
        ),
      );
    }

    return lines;
  }

  /// Get account level based on code
  int _getAccountLevel(String code) {
    return code.length ~/ 2; // Assuming 2 digits per level
  }

  /// Calculate cash balance
  Future<double> _calculateCashBalance(
    List<int> cashAccountIds,
    DateTime asOfDate,
  ) async {
    double total = 0.0;
    for (final accountId in cashAccountIds) {
      total += await _getAccountBalance(accountId, asOfDate);
    }
    return total;
  }

  /// Get operating cash flows
  Future<List<CashFlowItem>> _getOperatingCashFlows(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // Implementation would analyze cash flows from operations
    return [
      CashFlowItem(description: 'صافي الدخل', amount: 0.0),
      CashFlowItem(description: 'تعديلات للبنود غير النقدية', amount: 0.0),
    ];
  }

  /// Get investing cash flows
  Future<List<CashFlowItem>> _getInvestingCashFlows(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // Implementation would analyze investing activities
    return [
      CashFlowItem(description: 'شراء أصول ثابتة', amount: 0.0),
      CashFlowItem(description: 'بيع أصول ثابتة', amount: 0.0),
    ];
  }

  /// Get financing cash flows
  Future<List<CashFlowItem>> _getFinancingCashFlows(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // Implementation would analyze financing activities
    return [
      CashFlowItem(description: 'قروض جديدة', amount: 0.0),
      CashFlowItem(description: 'سداد قروض', amount: 0.0),
    ];
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Export report to different formats
  Future<Result<String>> exportReport({
    required FinancialReport report,
    required ReportFormat format,
  }) async {
    try {
      switch (format) {
        case ReportFormat.pdf:
          return await _exportToPdf(report);
        case ReportFormat.excel:
          return await _exportToExcel(report);
        case ReportFormat.csv:
          return await _exportToCsv(report);
        case ReportFormat.html:
          return await _exportToHtml(report);
      }
    } catch (e) {
      return Result.error('خطأ في تصدير التقرير: ${e.toString()}');
    }
  }

  /// Export to PDF
  Future<Result<String>> _exportToPdf(FinancialReport report) async {
    try {
      // Create PDF document
      final pdf = pw.Document();

      // Load Arabic font for proper text rendering
      final arabicFont = await _loadArabicFont();

      // Add pages to PDF
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: pw.ThemeData.withFont(base: arabicFont),
          build: (pw.Context context) => _buildReportPages(report, arabicFont),
        ),
      );

      // Save PDF file
      final output = await getApplicationDocumentsDirectory();
      final fileName =
          'financial_report_${report.type.value}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${output.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      return Result.success(file.path);
    } catch (e) {
      return Result.error('خطأ في تصدير PDF: ${e.toString()}');
    }
  }

  /// Load Arabic font for PDF
  Future<pw.Font> _loadArabicFont() async {
    try {
      // Try to load a system Arabic font or use a default font
      // For now, we'll use the default font which should support Arabic
      return pw.Font.helvetica();
    } catch (e) {
      // Fallback to default font
      return pw.Font.helvetica();
    }
  }

  /// Build report pages for PDF
  List<pw.Widget> _buildReportPages(
    FinancialReport report,
    pw.Font arabicFont,
  ) {
    final dateFormat = DateFormat('yyyy/MM/dd');

    return [
      // Report header
      _buildReportHeader(report, arabicFont, dateFormat),
      pw.SizedBox(height: 20),

      // Report sections
      ...report.sections.map(
        (section) => _buildReportSection(section, arabicFont),
      ),

      // Report summary if available
      if (report.summary.isNotEmpty) ...[
        pw.SizedBox(height: 20),
        _buildReportSummary(report, arabicFont),
      ],

      // Report footer
      pw.SizedBox(height: 20),
      _buildReportFooter(report, arabicFont, dateFormat),
    ];
  }

  /// Build report header
  pw.Widget _buildReportHeader(
    FinancialReport report,
    pw.Font arabicFont,
    DateFormat dateFormat,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Report title
        pw.Text(
          report.title,
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
          ),
          textDirection: pw.TextDirection.rtl,
        ),

        // Subtitle if available
        if (report.subtitle != null) ...[
          pw.SizedBox(height: 5),
          pw.Text(
            report.subtitle!,
            style: pw.TextStyle(font: arabicFont, fontSize: 14),
            textDirection: pw.TextDirection.rtl,
          ),
        ],

        pw.SizedBox(height: 10),

        // Date range
        pw.Text(
          'من ${dateFormat.format(report.fromDate)} إلى ${dateFormat.format(report.toDate)}',
          style: pw.TextStyle(font: arabicFont, fontSize: 12),
          textDirection: pw.TextDirection.rtl,
        ),

        // Divider
        pw.SizedBox(height: 10),
        pw.Divider(),
      ],
    );
  }

  /// Build report section
  pw.Widget _buildReportSection(ReportSection section, pw.Font arabicFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title
        pw.Text(
          section.title,
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
          ),
          textDirection: pw.TextDirection.rtl,
        ),

        // Subtitle if available
        if (section.subtitle != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            section.subtitle!,
            style: pw.TextStyle(font: arabicFont, fontSize: 12),
            textDirection: pw.TextDirection.rtl,
          ),
        ],

        pw.SizedBox(height: 10),

        // Section table
        if (section.lines.isNotEmpty) _buildSectionTable(section, arabicFont),

        pw.SizedBox(height: 15),
      ],
    );
  }

  /// Build section table
  pw.Widget _buildSectionTable(ReportSection section, pw.Font arabicFont) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1), // Account Code
        1: const pw.FlexColumnWidth(3), // Account Name
        2: const pw.FlexColumnWidth(2), // Amount
        3: const pw.FlexColumnWidth(2), // Previous Amount (if available)
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('رمز الحساب', arabicFont, isHeader: true),
            _buildTableCell('اسم الحساب', arabicFont, isHeader: true),
            _buildTableCell('المبلغ', arabicFont, isHeader: true),
            if (section.lines.any((line) => line.previousAmount != null))
              _buildTableCell('المبلغ السابق', arabicFont, isHeader: true),
          ],
        ),

        // Data rows
        ...section.lines.map(
          (line) => pw.TableRow(
            children: [
              _buildTableCell(
                line.accountCode,
                arabicFont,
                isBold: line.isBold,
              ),
              _buildTableCell(
                line.accountName,
                arabicFont,
                isBold: line.isBold,
                level: line.level,
              ),
              _buildTableCell(
                _formatAmount(line.amount),
                arabicFont,
                isBold: line.isBold,
                isAmount: true,
              ),
              if (section.lines.any((l) => l.previousAmount != null))
                _buildTableCell(
                  line.previousAmount != null
                      ? _formatAmount(line.previousAmount!)
                      : '-',
                  arabicFont,
                  isBold: line.isBold,
                  isAmount: true,
                ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build table cell
  pw.Widget _buildTableCell(
    String text,
    pw.Font arabicFont, {
    bool isHeader = false,
    bool isBold = false,
    bool isAmount = false,
    int level = 0,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        '${' ' * (level * 2)}$text', // Add indentation for levels
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: isHeader ? 12 : 10,
          fontWeight: (isHeader || isBold)
              ? pw.FontWeight.bold
              : pw.FontWeight.normal,
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: isAmount ? pw.TextAlign.right : pw.TextAlign.right,
      ),
    );
  }

  /// Build report summary
  pw.Widget _buildReportSummary(FinancialReport report, pw.Font arabicFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'ملخص التقرير',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 10),

        // Summary items
        ...report.summary.entries.map(
          (entry) => pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 5),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  entry.key,
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  entry.value.toString(),
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build report footer
  pw.Widget _buildReportFooter(
    FinancialReport report,
    pw.Font arabicFont,
    DateFormat dateFormat,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Divider(),
        pw.SizedBox(height: 10),

        // Generation info
        pw.Text(
          'تم إنشاء التقرير في: ${dateFormat.format(report.generatedAt)}',
          style: pw.TextStyle(font: arabicFont, fontSize: 10),
          textDirection: pw.TextDirection.rtl,
        ),

        if (report.generatedBy != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            'بواسطة: ${report.generatedBy}',
            style: pw.TextStyle(font: arabicFont, fontSize: 10),
            textDirection: pw.TextDirection.rtl,
          ),
        ],

        if (report.notes != null) ...[
          pw.SizedBox(height: 10),
          pw.Text(
            'ملاحظات:',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            report.notes!,
            style: pw.TextStyle(font: arabicFont, fontSize: 10),
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ],
    );
  }

  /// Format amount for display
  String _formatAmount(double amount) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return formatter.format(amount);
  }

  /// Export to Excel
  Future<Result<String>> _exportToExcel(FinancialReport report) async {
    try {
      // Create Excel workbook
      final excel = Excel.createExcel();

      // Remove default sheet and create a new one with Arabic name
      excel.delete('Sheet1');
      final sheet = excel['التقرير المالي'];

      // Set RTL direction for Arabic text
      sheet.isRTL = true;

      int currentRow = 0;

      // Add report header
      _addExcelHeader(sheet, report, currentRow);
      currentRow += 5; // Skip header rows

      // Add report sections
      for (final section in report.sections) {
        currentRow = _addExcelSection(sheet, section, currentRow);
        currentRow += 2; // Add spacing between sections
      }

      // Add summary if available
      if (report.summary.isNotEmpty) {
        currentRow = _addExcelSummary(sheet, report.summary, currentRow);
      }

      // Save file
      final output = await getApplicationDocumentsDirectory();
      final fileName =
          'report_${report.type.value}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${output.path}/$fileName';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        return Result.success(filePath);
      } else {
        return Result.error('فشل في حفظ ملف Excel');
      }
    } catch (e) {
      return Result.error('خطأ في إنشاء ملف Excel: ${e.toString()}');
    }
  }

  /// Add header information to Excel sheet
  void _addExcelHeader(Sheet sheet, FinancialReport report, int startRow) {
    // Report title
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow))
        .value = TextCellValue(
      report.title,
    );

    // Report subtitle
    if (report.subtitle != null) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 1),
          )
          .value = TextCellValue(
        report.subtitle!,
      );
    }

    // Date range
    sheet
        .cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 2),
        )
        .value = TextCellValue(
      'من تاريخ: ${_formatDate(report.fromDate)}',
    );

    sheet
        .cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 3),
        )
        .value = TextCellValue(
      'إلى تاريخ: ${_formatDate(report.toDate)}',
    );

    // Generated date
    sheet
        .cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: startRow + 4),
        )
        .value = TextCellValue(
      'تاريخ الإنشاء: ${_formatDate(report.generatedAt)}',
    );
  }

  /// Add a report section to Excel sheet
  int _addExcelSection(Sheet sheet, ReportSection section, int startRow) {
    int currentRow = startRow;

    // Section title
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      section.title,
    );
    currentRow++;

    // Headers
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'رمز الحساب',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow))
        .value = TextCellValue(
      'اسم الحساب',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow))
        .value = TextCellValue(
      'المبلغ',
    );
    currentRow++;

    // Data rows
    for (final line in section.lines) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        line.accountCode,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
          )
          .value = TextCellValue(
        line.accountName,
      );
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow),
          )
          .value = DoubleCellValue(
        line.amount,
      );
      currentRow++;
    }

    // Section totals
    if (section.totals.isNotEmpty) {
      currentRow++; // Add spacing
      for (final entry in section.totals.entries) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
            )
            .value = TextCellValue(
          entry.key,
        );
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow),
            )
            .value = DoubleCellValue(
          entry.value as double,
        );
        currentRow++;
      }
    }

    return currentRow;
  }

  /// Add summary section to Excel sheet
  int _addExcelSummary(
    Sheet sheet,
    Map<String, dynamic> summary,
    int startRow,
  ) {
    int currentRow = startRow;

    // Summary title
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        .value = TextCellValue(
      'الملخص',
    );
    currentRow++;

    // Summary items
    for (final entry in summary.entries) {
      sheet
          .cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
          )
          .value = TextCellValue(
        entry.key,
      );

      if (entry.value is double) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
            )
            .value = DoubleCellValue(
          entry.value,
        );
      } else {
        sheet
            .cell(
              CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow),
            )
            .value = TextCellValue(
          entry.value.toString(),
        );
      }
      currentRow++;
    }

    return currentRow;
  }

  /// Export to CSV
  Future<Result<String>> _exportToCsv(FinancialReport report) async {
    try {
      final buffer = StringBuffer();

      // Header
      buffer.writeln('${report.title},${report.subtitle ?? ''}');
      buffer.writeln('من تاريخ,${_formatDate(report.fromDate)}');
      buffer.writeln('إلى تاريخ,${_formatDate(report.toDate)}');
      buffer.writeln('');

      // Sections
      for (final section in report.sections) {
        buffer.writeln('section.title}');
        buffer.writeln('رمز الحساب,اسم الحساب,المبلغ');

        for (final line in section.lines) {
          buffer.writeln(
            '${line.accountCode},${line.accountName},${line.amount}',
          );
        }

        buffer.writeln('');
      }

      return Result.success(buffer.toString());
    } catch (e) {
      return Result.error('خطأ في تصدير CSV: ${e.toString()}');
    }
  }

  /// Export to HTML
  Future<Result<String>> _exportToHtml(FinancialReport report) async {
    try {
      final buffer = StringBuffer();

      buffer.writeln('<!DOCTYPE html>');
      buffer.writeln('<html dir="rtl" lang="ar">');
      buffer.writeln('<head>');
      buffer.writeln('<meta charset="UTF-8">');
      buffer.writeln('<title>${report.title}</title>');
      buffer.writeln('<style>');
      buffer.writeln('body { font-family: Arial, sans-serif; margin: 20px; }');
      buffer.writeln(
        'table { width: 100%; border-collapse: collapse; margin: 20px 0; }',
      );
      buffer.writeln(
        'th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }',
      );
      buffer.writeln('th { background-color: #f2f2f2; }');
      buffer.writeln('h1, h2 { color: #333; }');
      buffer.writeln('</style>');
      buffer.writeln('</head>');
      buffer.writeln('<body>');

      // Report header
      buffer.writeln('<h1>${report.title}</h1>');
      if (report.subtitle != null) {
        buffer.writeln('<h2>${report.subtitle}</h2>');
      }
      buffer.writeln('<p>من تاريخ: ${_formatDate(report.fromDate)}</p>');
      buffer.writeln('<p>إلى تاريخ: ${_formatDate(report.toDate)}</p>');

      // Report sections
      for (final section in report.sections) {
        buffer.writeln('<h3>${section.title}</h3>');
        buffer.writeln('<table>');
        buffer.writeln('<tr>');
        buffer.writeln('<th>رمز الحساب</th>');
        buffer.writeln('<th>اسم الحساب</th>');
        buffer.writeln('<th>المبلغ</th>');
        buffer.writeln('</tr>');

        for (final line in section.lines) {
          buffer.writeln('<tr>');
          buffer.writeln('<td>${line.accountCode}</td>');
          buffer.writeln('<td>${line.accountName}</td>');
          buffer.writeln('<td>${line.amount}</td>');
          buffer.writeln('</tr>');
        }

        buffer.writeln('</table>');
      }

      buffer.writeln('</body>');
      buffer.writeln('</html>');

      return Result.success(buffer.toString());
    } catch (e) {
      return Result.error('خطأ في تصدير HTML: ${e.toString()}');
    }
  }
}

/// Helper class for account balance
class AccountBalance {
  final Account account;
  final double balance;

  AccountBalance({required this.account, required this.balance});
}
