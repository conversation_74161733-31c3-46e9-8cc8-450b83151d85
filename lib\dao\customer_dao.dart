/// طبقة الوصول لبيانات العملاء
/// Customer Data Access Object for Smart Ledger
library;


import '../database/database_helper.dart';
import '../models/customer.dart';
import '../utils/result.dart';

/// طبقة الوصول لبيانات العملاء
class CustomerDao {
  static const String tableName = 'customers';

  /// إنشاء عميل جديد
  Future<Result<Customer>> createCustomer(Customer customer) async {
    try {
      final db = await DatabaseHelper().database;
      final id = await db.insert(tableName, customer.toMap());
      final newCustomer = customer.copyWith(id: id);
      return Result.success(newCustomer);
    } catch (e) {
      return Result.error('فشل في إنشاء العميل: ${e.toString()}');
    }
  }

  /// الحصول على عميل بالمعرف
  Future<Result<Customer?>> getCustomerById(int id) async {
    try {
      final db = await DatabaseHelper().database;
      final maps = await db.query(tableName, where: 'id = ?', whereArgs: [id]);

      if (maps.isEmpty) {
        return Result.success(null);
      }

      final customer = Customer.fromMap(maps.first);
      return Result.success(customer);
    } catch (e) {
      return Result.error('فشل في جلب العميل: ${e.toString()}');
    }
  }

  /// الحصول على جميع العملاء
  Future<Result<List<Customer>>> getAllCustomers() async {
    try {
      final db = await DatabaseHelper().database;
      final maps = await db.query(tableName, orderBy: 'name ASC');

      final customers = maps.map((map) => Customer.fromMap(map)).toList();
      return Result.success(customers);
    } catch (e) {
      return Result.error('فشل في جلب العملاء: ${e.toString()}');
    }
  }

  /// البحث في العملاء
  Future<Result<List<Customer>>> searchCustomers(String query) async {
    try {
      final db = await DatabaseHelper().database;
      final maps = await db.query(
        tableName,
        where: 'name LIKE ? OR email LIKE ? OR phone LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'name ASC',
      );

      final customers = maps.map((map) => Customer.fromMap(map)).toList();
      return Result.success(customers);
    } catch (e) {
      return Result.error('فشل في البحث عن العملاء: ${e.toString()}');
    }
  }

  /// تحديث عميل
  Future<Result<Customer>> updateCustomer(Customer customer) async {
    try {
      final db = await DatabaseHelper().database;
      await db.update(
        tableName,
        customer.toMap(),
        where: 'id = ?',
        whereArgs: [customer.id],
      );
      return Result.success(customer);
    } catch (e) {
      return Result.error('فشل في تحديث العميل: ${e.toString()}');
    }
  }

  /// حذف عميل
  Future<Result<void>> deleteCustomer(int id) async {
    try {
      final db = await DatabaseHelper().database;
      await db.delete(tableName, where: 'id = ?', whereArgs: [id]);
      return Result.success(null);
    } catch (e) {
      return Result.error('فشل في حذف العميل: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات العملاء
  Future<Result<Map<String, dynamic>>> getCustomerStatistics() async {
    try {
      final db = await DatabaseHelper().database;

      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $tableName',
      );
      final total = totalResult.first['count'] as int;

      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $tableName WHERE is_active = 1',
      );
      final active = activeResult.first['count'] as int;

      return Result.success({
        'total': total,
        'active': active,
        'inactive': total - active,
      });
    } catch (e) {
      return Result.error('فشل في جلب إحصائيات العملاء: ${e.toString()}');
    }
  }
}
