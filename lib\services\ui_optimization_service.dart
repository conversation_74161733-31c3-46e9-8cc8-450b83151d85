import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';

/// 🎨 خدمة تحسين واجهة المستخدم
/// UI Optimization Service
class UIOptimizationService {
  static final UIOptimizationService _instance =
      UIOptimizationService._internal();
  factory UIOptimizationService() => _instance;
  UIOptimizationService._internal();

  // إعدادات التحسين
  bool _reducedAnimations = false;
  bool _highContrastMode = false;
  bool _reducedMotion = false;
  double _animationScale = 1.0;

  /// تحسين الرسوم المتحركة
  void optimizeAnimations({
    bool reduceAnimations = false,
    double animationScale = 1.0,
  }) {
    _reducedAnimations = reduceAnimations;
    _animationScale = animationScale;

    // تطبيق إعدادات الرسوم المتحركة
    if (_reducedAnimations) {
      // تقليل مدة الرسوم المتحركة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        SemanticsService.announce(
          'تم تفعيل وضع الرسوم المتحركة المحدودة',
          TextDirection.rtl,
        );
      });
    }
  }

  /// تحسين الألوان والتباين
  void optimizeColors({bool highContrast = false, Brightness? brightness}) {
    _highContrastMode = highContrast;

    if (_highContrastMode) {
      // تطبيق وضع التباين العالي
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: brightness == Brightness.dark
              ? Colors.black
              : Colors.white,
          statusBarIconBrightness: brightness == Brightness.dark
              ? Brightness.light
              : Brightness.dark,
          systemNavigationBarColor: brightness == Brightness.dark
              ? Colors.black
              : Colors.white,
          systemNavigationBarIconBrightness: brightness == Brightness.dark
              ? Brightness.light
              : Brightness.dark,
        ),
      );
    }
  }

  /// تحسين الخطوط والنصوص
  TextStyle optimizeTextStyle(
    TextStyle original, {
    bool increaseFontSize = false,
    bool boldText = false,
  }) {
    double fontSize = original.fontSize ?? 14.0;
    FontWeight fontWeight = original.fontWeight ?? FontWeight.normal;

    if (increaseFontSize) {
      fontSize *= 1.2;
    }

    if (boldText) {
      fontWeight = FontWeight.bold;
    }

    return original.copyWith(fontSize: fontSize, fontWeight: fontWeight);
  }

  /// تحسين التخطيط للشاشات الصغيرة
  EdgeInsets optimizePadding(EdgeInsets original, BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (isSmallScreen) {
      return EdgeInsets.all(original.left * 0.8);
    }

    return original;
  }

  /// تحسين أحجام الأيقونات
  double optimizeIconSize(double original, BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (isSmallScreen) {
      return original * 0.9;
    }

    return original;
  }

  /// تحسين الظلال والتأثيرات
  List<BoxShadow> optimizeShadows(List<BoxShadow> original) {
    if (_reducedAnimations) {
      // تقليل الظلال في وضع الأداء المحسن
      return original
          .map(
            (shadow) => shadow.copyWith(
              blurRadius: shadow.blurRadius * 0.5,
              spreadRadius: shadow.spreadRadius * 0.5,
            ),
          )
          .toList();
    }

    return original;
  }

  /// تحسين الحدود والزوايا
  BorderRadius optimizeBorderRadius(BorderRadius original) {
    if (_reducedAnimations) {
      // تقليل انحناء الزوايا لتحسين الأداء
      return BorderRadius.circular(original.topLeft.x * 0.7);
    }

    return original;
  }

  /// تحسين التدرجات اللونية
  Gradient? optimizeGradient(Gradient? original) {
    if (_reducedAnimations && original != null) {
      // استخدام لون واحد بدلاً من التدرج
      if (original is LinearGradient && original.colors.isNotEmpty) {
        return null; // إرجاع null لاستخدام لون واحد
      }
    }

    return original;
  }

  /// تحسين الشفافية
  double optimizeOpacity(double original) {
    if (_highContrastMode) {
      // تقليل الشفافية في وضع التباين العالي
      return (original * 1.2).clamp(0.0, 1.0);
    }

    return original;
  }

  /// إنشاء ثيم محسن
  ThemeData createOptimizedTheme(ThemeData baseTheme) {
    return baseTheme.copyWith(
      // تحسين الألوان
      colorScheme: _highContrastMode
          ? baseTheme.colorScheme.copyWith(
              primary: _highContrastMode
                  ? Colors.black
                  : baseTheme.colorScheme.primary,
              onPrimary: _highContrastMode
                  ? Colors.white
                  : baseTheme.colorScheme.onPrimary,
            )
          : baseTheme.colorScheme,

      // تحسين النصوص
      textTheme: baseTheme.textTheme.copyWith(
        bodyLarge: optimizeTextStyle(
          baseTheme.textTheme.bodyLarge ?? const TextStyle(),
          increaseFontSize: _highContrastMode,
          boldText: _highContrastMode,
        ),
        bodyMedium: optimizeTextStyle(
          baseTheme.textTheme.bodyMedium ?? const TextStyle(),
          increaseFontSize: _highContrastMode,
          boldText: _highContrastMode,
        ),
      ),

      // تحسين الرسوم المتحركة
      pageTransitionsTheme: _reducedAnimations
          ? const PageTransitionsTheme(
              builders: {
                TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
              },
            )
          : baseTheme.pageTransitionsTheme,

      // تحسين الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: _reducedAnimations ? 2 : 4,
          shadowColor: _reducedAnimations ? Colors.transparent : null,
        ),
      ),

      // تحسين البطاقات
      cardTheme: baseTheme.cardTheme.copyWith(
        elevation: _reducedAnimations ? 2 : baseTheme.cardTheme.elevation,
        shadowColor: _reducedAnimations
            ? Colors.transparent
            : baseTheme.cardTheme.shadowColor,
      ),
    );
  }

  /// تحسين الرسوم المتحركة المخصصة
  AnimationController createOptimizedAnimationController({
    required Duration duration,
    required TickerProvider vsync,
  }) {
    final optimizedDuration = _reducedAnimations
        ? Duration(milliseconds: (duration.inMilliseconds * 0.5).round())
        : duration;

    return AnimationController(duration: optimizedDuration, vsync: vsync);
  }

  /// تحسين منحنيات الرسوم المتحركة
  Curve optimizeCurve(Curve original) {
    if (_reducedAnimations) {
      return Curves.linear; // استخدام منحنى خطي للأداء الأفضل
    }

    return original;
  }

  /// تحسين التخطيط المرن
  Widget optimizeFlexibleLayout({
    required Widget child,
    required BuildContext context,
  }) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    if (isTablet) {
      return Row(
        children: [
          Expanded(flex: 2, child: child),
          const Expanded(flex: 1, child: SizedBox()),
        ],
      );
    }

    return child;
  }

  /// تحسين القوائم الطويلة
  Widget optimizeListView({
    required List<Widget> children,
    required BuildContext context,
  }) {
    if (children.length > 50) {
      // استخدام ListView.builder للقوائم الطويلة
      return ListView.builder(
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
        cacheExtent: 100, // تحسين التخزين المؤقت
      );
    }

    return ListView(children: children);
  }

  /// تحسين الصور
  Widget optimizeImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      cacheWidth: width?.round(),
      cacheHeight: height?.round(),
      filterQuality: _reducedAnimations
          ? FilterQuality.low
          : FilterQuality.medium,
    );
  }

  /// الحصول على إعدادات التحسين الحالية
  Map<String, dynamic> getOptimizationSettings() {
    return {
      'reduced_animations': _reducedAnimations,
      'high_contrast_mode': _highContrastMode,
      'reduced_motion': _reducedMotion,
      'animation_scale': _animationScale,
    };
  }

  /// تطبيق إعدادات التحسين
  void applyOptimizationSettings(Map<String, dynamic> settings) {
    _reducedAnimations = settings['reduced_animations'] ?? false;
    _highContrastMode = settings['high_contrast_mode'] ?? false;
    _reducedMotion = settings['reduced_motion'] ?? false;
    _animationScale = settings['animation_scale'] ?? 1.0;
  }

  /// إعادة تعيين إعدادات التحسين
  void resetOptimizationSettings() {
    _reducedAnimations = false;
    _highContrastMode = false;
    _reducedMotion = false;
    _animationScale = 1.0;
  }
}
