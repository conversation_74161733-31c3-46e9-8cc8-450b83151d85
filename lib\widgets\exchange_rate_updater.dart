import 'package:flutter/material.dart';
import '../services/exchange_rate_service.dart';
import '../models/exchange_rate.dart';

/// ويدجت لتحديث أسعار الصرف من API
class ExchangeRateUpdater extends StatefulWidget {
  const ExchangeRateUpdater({super.key});

  @override
  State<ExchangeRateUpdater> createState() => _ExchangeRateUpdaterState();
}

class _ExchangeRateUpdaterState extends State<ExchangeRateUpdater> {
  final ExchangeRateService _exchangeRateService = ExchangeRateService();
  bool _isLoading = false;
  bool _isConnected = false;
  List<String> _supportedCurrencies = [];
  List<ExchangeRate> _updatedRates = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkConnectivity();
  }

  Future<void> _checkConnectivity() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _exchangeRateService.checkAPIConnectivity();
      if (result.isSuccess) {
        setState(() {
          _isConnected = result.data!;
        });

        if (_isConnected) {
          await _loadSupportedCurrencies();
        }
      } else {
        setState(() {
          _errorMessage = result.error;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في التحقق من الاتصال: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSupportedCurrencies() async {
    try {
      final result = await _exchangeRateService.getSupportedCurrenciesFromAPI();
      if (result.isSuccess) {
        setState(() {
          _supportedCurrencies = result.data!;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في جلب العملات المدعومة: ${e.toString()}';
      });
    }
  }

  Future<void> _updateAllRates() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _exchangeRateService.updateExchangeRatesFromAPI(
        baseCurrency: 'USD',
        targetCurrencies: ['EUR', 'GBP', 'JPY', 'SAR', 'AED', 'EGP'],
      );

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث أسعار الصرف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        await _loadUpdatedRates();
      } else {
        setState(() {
          _errorMessage = result.error;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحديث أسعار الصرف: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadUpdatedRates() async {
    try {
      final rates = await _exchangeRateService.getActiveExchangeRates();
      final apiRates = rates
          .where((rate) => rate.source == ExchangeRateSource.api)
          .toList();
      setState(() {
        _updatedRates = apiRates;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في جلب أسعار الصرف المحدثة: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.currency_exchange, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'تحديث أسعار الصرف من API',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Icon(
                  _isConnected ? Icons.wifi : Icons.wifi_off,
                  color: _isConnected ? Colors.green : Colors.red,
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                  ],
                ),
              ),

            if (_errorMessage != null) const SizedBox(height: 16),

            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _checkConnectivity,
                  icon: const Icon(Icons.refresh),
                  label: const Text('فحص الاتصال'),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: (_isLoading || !_isConnected)
                      ? null
                      : _updateAllRates,
                  icon: const Icon(Icons.download),
                  label: const Text('تحديث الأسعار'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            if (_isLoading)
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Center(child: CircularProgressIndicator()),
              ),

            if (_supportedCurrencies.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'العملات المدعومة (${_supportedCurrencies.length}):',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: _supportedCurrencies.take(20).map((currency) {
                  return Chip(
                    label: Text(currency),
                    backgroundColor: Colors.blue.shade50,
                  );
                }).toList(),
              ),
            ],

            if (_updatedRates.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'أسعار الصرف المحدثة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...(_updatedRates.take(10).map((rate) {
                return ListTile(
                  dense: true,
                  leading: CircleAvatar(
                    backgroundColor: Colors.green.shade100,
                    child: Text(
                      rate.toCurrencyCode.substring(0, 2),
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  title: Text(rate.currencyPair),
                  subtitle: Text(
                    'آخر تحديث: ${rate.updatedAt.toString().split(' ')[0]}',
                    style: const TextStyle(fontSize: 12),
                  ),
                  trailing: Text(
                    rate.rate.toStringAsFixed(4),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                );
              }).toList()),
            ],
          ],
        ),
      ),
    );
  }
}
