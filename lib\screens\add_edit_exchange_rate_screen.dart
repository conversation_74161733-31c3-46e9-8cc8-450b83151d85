import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/exchange_rate.dart';
import '../models/currency.dart';
import '../services/exchange_rate_service.dart';
import '../services/currency_service.dart';
import '../widgets/quantum_card.dart';
import '../widgets/quantum_button.dart';
import '../widgets/quantum_text_field.dart';
import '../widgets/quantum_dropdown.dart';

/// شاشة إضافة/تعديل سعر الصرف
class AddEditExchangeRateScreen extends StatefulWidget {
  final ExchangeRate? exchangeRate;

  const AddEditExchangeRateScreen({super.key, this.exchangeRate});

  @override
  State<AddEditExchangeRateScreen> createState() =>
      _AddEditExchangeRateScreenState();
}

class _AddEditExchangeRateScreenState extends State<AddEditExchangeRateScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final ExchangeRateService _exchangeRateService = ExchangeRateService();
  final CurrencyService _currencyService = CurrencyService();

  late TextEditingController _rateController;
  late TextEditingController _notesController;

  String? _fromCurrency;
  String? _toCurrency;
  DateTime _effectiveDate = DateTime.now();
  DateTime? _expiryDate;
  ExchangeRateSource _source = ExchangeRateSource.manual;
  bool _isActive = true;
  bool _isLoading = false;

  List<Currency> _currencies = [];

  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupAnimations();
    _loadCurrencies();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rateController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _rateController = TextEditingController(
      text: widget.exchangeRate?.rate.toString() ?? '',
    );
    _notesController = TextEditingController(
      text: widget.exchangeRate?.notes ?? '',
    );

    if (widget.exchangeRate != null) {
      _fromCurrency = widget.exchangeRate!.fromCurrencyCode;
      _toCurrency = widget.exchangeRate!.toCurrencyCode;
      _effectiveDate = widget.exchangeRate!.effectiveDate;
      _expiryDate = widget.exchangeRate!.expiryDate;
      _source = widget.exchangeRate!.source;
      _isActive = widget.exchangeRate!.isActive;
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  Future<void> _loadCurrencies() async {
    try {
      final currencies = await _currencyService.getActiveCurrencies();
      setState(() => _currencies = currencies);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل العملات: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isEffectiveDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isEffectiveDate
          ? _effectiveDate
          : (_expiryDate ?? DateTime.now()),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (picked != null) {
      setState(() {
        if (isEffectiveDate) {
          _effectiveDate = picked;
        } else {
          _expiryDate = picked;
        }
      });
    }
  }

  Future<void> _saveExchangeRate() async {
    if (!_formKey.currentState!.validate()) return;

    if (_fromCurrency == null || _toCurrency == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى اختيار العملات')));
      return;
    }

    if (_fromCurrency == _toCurrency) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن أن تكون العملة المصدر والمستهدفة متشابهتين'),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final exchangeRate = ExchangeRate(
        id: widget.exchangeRate?.id,
        fromCurrencyCode: _fromCurrency!,
        toCurrencyCode: _toCurrency!,
        rate: double.parse(_rateController.text),
        effectiveDate: _effectiveDate,
        expiryDate: _expiryDate,
        source: _source,
        isActive: _isActive,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        createdAt: widget.exchangeRate?.createdAt,
        updatedAt: DateTime.now(),
      );

      final result = widget.exchangeRate == null
          ? await _exchangeRateService.addExchangeRate(exchangeRate)
          : await _exchangeRateService.updateExchangeRate(exchangeRate);

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.exchangeRate == null
                    ? 'تم إضافة سعر الصرف بنجاح'
                    : 'تم تحديث سعر الصرف بنجاح',
              ),
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(result.error!)));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: ${e.toString()}')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.exchangeRate == null ? 'إضافة سعر صرف' : 'تعديل سعر الصرف',
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, _slideAnimation.value),
            child: FadeTransition(opacity: _fadeAnimation, child: _buildForm()),
          );
        },
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildCurrencySelectionCard(),
          const SizedBox(height: 16),
          _buildRateCard(),
          const SizedBox(height: 16),
          _buildDatesCard(),
          const SizedBox(height: 16),
          _buildSettingsCard(),
          const SizedBox(height: 16),
          _buildPreviewCard(),
          const SizedBox(height: 24),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildCurrencySelectionCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختيار العملات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: QuantumDropdown<String>(
                    value: _fromCurrency,
                    items: _currencies
                        .map(
                          (currency) => DropdownMenuItem(
                            value: currency.code,
                            child: Text(
                              '${currency.nameAr} (${currency.code})',
                            ),
                          ),
                        )
                        .toList(),
                    onChanged: (value) => setState(() => _fromCurrency = value),
                    labelText: 'من العملة',
                  ),
                ),
                const SizedBox(width: 16),
                const Icon(Icons.arrow_forward, color: Colors.grey),
                const SizedBox(width: 16),
                Expanded(
                  child: QuantumDropdown<String>(
                    value: _toCurrency,
                    items: _currencies
                        .map(
                          (currency) => DropdownMenuItem(
                            value: currency.code,
                            child: Text(
                              '${currency.nameAr} (${currency.code})',
                            ),
                          ),
                        )
                        .toList(),
                    onChanged: (value) => setState(() => _toCurrency = value),
                    labelText: 'إلى العملة',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRateCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'سعر الصرف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _rateController,
              labelText: 'السعر',
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'سعر الصرف مطلوب';
                }
                final rate = double.tryParse(value);
                if (rate == null || rate <= 0) {
                  return 'سعر الصرف يجب أن يكون أكبر من الصفر';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatesCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التواريخ',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('تاريخ السريان'),
              subtitle: Text(_effectiveDate.toString().split(' ')[0]),
              trailing: const Icon(Icons.calendar_today),
              onTap: () => _selectDate(context, true),
            ),
            ListTile(
              title: const Text('تاريخ الانتهاء (اختياري)'),
              subtitle: Text(
                _expiryDate?.toString().split(' ')[0] ?? 'غير محدد',
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_expiryDate != null)
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => setState(() => _expiryDate = null),
                    ),
                  const Icon(Icons.calendar_today),
                ],
              ),
              onTap: () => _selectDate(context, false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCard() {
    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإعدادات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            QuantumDropdown<ExchangeRateSource>(
              value: _source,
              items: ExchangeRateSource.values
                  .map(
                    (source) => DropdownMenuItem(
                      value: source,
                      child: Text(source.displayName),
                    ),
                  )
                  .toList(),
              onChanged: (value) => setState(() => _source = value!),
              labelText: 'مصدر السعر',
            ),
            const SizedBox(height: 16),
            QuantumTextField(
              controller: _notesController,
              labelText: 'ملاحظات (اختياري)',
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('سعر صرف نشط'),
              subtitle: const Text('هل سعر الصرف متاح للاستخدام؟'),
              value: _isActive,
              onChanged: (value) => setState(() => _isActive = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewCard() {
    if (_fromCurrency == null ||
        _toCurrency == null ||
        _rateController.text.isEmpty) {
      return const SizedBox.shrink();
    }

    final rate = double.tryParse(_rateController.text);
    if (rate == null) return const SizedBox.shrink();

    return QuantumCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معاينة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '1 $_fromCurrency = ${rate.toStringAsFixed(4)} $_toCurrency',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1 $_toCurrency = ${(1 / rate).toStringAsFixed(4)} $_fromCurrency',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'مثال: 100 $_fromCurrency = ${(100 * rate).toStringAsFixed(2)} $_toCurrency',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: QuantumButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            variant: QuantumButtonVariant.outline,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: QuantumButton(
            text: widget.exchangeRate == null ? 'إضافة' : 'تحديث',
            onPressed: _isLoading ? null : _saveExchangeRate,
            isLoading: _isLoading,
          ),
        ),
      ],
    );
  }
}
