/// خدمة التقارير المالية المتقدمة
/// Advanced Financial Reports Service for Smart Ledger
library;

import '../models/advanced_financial_reports.dart';
import '../models/financial_report.dart' as basic_reports;
import '../models/account.dart';
import '../models/invoice.dart';
import '../models/stock_movement.dart';
import '../database/account_dao.dart';
import '../database/journal_entry_dao.dart';
import '../database/invoice_dao.dart';
import '../database/item_dao.dart';
import '../database/stock_movement_dao.dart';
import '../utils/result.dart';
import '../services/financial_report_service.dart';

/// خدمة التقارير المالية المتقدمة
class AdvancedFinancialReportsService {
  final AccountDao _accountDao;
  final JournalEntryDao _journalEntryDao;
  final InvoiceDao _invoiceDao;
  final ItemDao _itemDao;
  final StockMovementDao _stockMovementDao;
  final FinancialReportService _basicReportService;

  AdvancedFinancialReportsService({
    required AccountDao accountDao,
    required JournalEntryDao journalEntryDao,
    required InvoiceDao invoiceDao,
    required ItemDao itemDao,
    required StockMovementDao stockMovementDao,
    required FinancialReportService basicReportService,
  }) : _accountDao = accountDao,
       _journalEntryDao = journalEntryDao,
       _invoiceDao = invoiceDao,
       _itemDao = itemDao,
       _stockMovementDao = stockMovementDao,
       _basicReportService = basicReportService;

  /// إنشاء تقرير مالي متقدم
  Future<Result<AdvancedFinancialReport>> generateAdvancedReport({
    required AnalysisType analysisType,
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    Map<String, dynamic> parameters = const {},
  }) async {
    try {
      switch (analysisType) {
        case AnalysisType.trend:
          return await _generateTrendAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
        case AnalysisType.ratio:
          return await _generateRatioAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
        case AnalysisType.variance:
          return await _generateVarianceAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
        case AnalysisType.comparative:
          return await _generateComparativeAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
        case AnalysisType.forecast:
          return await _generateForecastAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
        case AnalysisType.performance:
          return await _generatePerformanceAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
        case AnalysisType.liquidity:
          return await _generateLiquidityAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
        case AnalysisType.profitability:
          return await _generateProfitabilityAnalysis(
            period: period,
            detailLevel: detailLevel,
            fromDate: fromDate,
            toDate: toDate,
            parameters: parameters,
          );
      }
    } catch (e) {
      return Result.error('خطأ في إنشاء التقرير المتقدم: ${e.toString()}');
    }
  }

  /// تحليل الاتجاهات
  Future<Result<AdvancedFinancialReport>> _generateTrendAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      // تحديد الفترات للمقارنة
      final periods = _generatePeriods(fromDate, toDate, period);
      final sections = <ReportSection>[];
      final metrics = <FinancialMetric>[];
      final chartData = <ChartData>[];
      final insights = <Insight>[];

      // تحليل الإيرادات عبر الفترات
      final revenueData = await _analyzeRevenueTrend(periods);
      sections.add(revenueData.section);
      chartData.add(revenueData.chart);
      metrics.addAll(revenueData.metrics);

      // تحليل المصروفات عبر الفترات
      final expenseData = await _analyzeExpenseTrend(periods);
      sections.add(expenseData.section);
      chartData.add(expenseData.chart);
      metrics.addAll(expenseData.metrics);

      // تحليل الربحية عبر الفترات
      final profitData = await _analyzeProfitTrend(periods);
      sections.add(profitData.section);
      chartData.add(profitData.chart);
      metrics.addAll(profitData.metrics);

      // إنشاء الرؤى والتوصيات
      insights.addAll(
        await _generateTrendInsights(revenueData, expenseData, profitData),
      );

      final report = AdvancedFinancialReport(
        title: 'تحليل الاتجاهات المالية',
        subtitle:
            'تحليل الاتجاهات للفترة من ${_formatDate(fromDate)} إلى ${_formatDate(toDate)}',
        analysisType: AnalysisType.trend,
        period: period,
        detailLevel: detailLevel,
        fromDate: fromDate,
        toDate: toDate,
        parameters: parameters,
        sections: sections,
        metrics: metrics,
        chartData: chartData,
        insights: insights,
        summary: {
          'total_periods': periods.length,
          'revenue_trend': _calculateTrendDirection(revenueData.values),
          'expense_trend': _calculateTrendDirection(expenseData.values),
          'profit_trend': _calculateTrendDirection(profitData.values),
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في تحليل الاتجاهات: ${e.toString()}');
    }
  }

  /// تحليل النسب المالية
  Future<Result<AdvancedFinancialReport>> _generateRatioAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      final sections = <ReportSection>[];
      final metrics = <FinancialMetric>[];
      final chartData = <ChartData>[];
      final insights = <Insight>[];

      // الحصول على البيانات المالية الأساسية
      final balanceSheetResult = await _basicReportService.generateBalanceSheet(
        asOfDate: toDate,
        includeZeroBalances: false,
      );

      final incomeStatementResult = await _basicReportService
          .generateIncomeStatement(
            fromDate: fromDate,
            toDate: toDate,
            includeZeroBalances: false,
          );

      if (!balanceSheetResult.isSuccess || !incomeStatementResult.isSuccess) {
        return Result.error('خطأ في الحصول على البيانات المالية الأساسية');
      }

      final balanceSheet = balanceSheetResult.data!;
      final incomeStatement = incomeStatementResult.data!;

      // حساب نسب السيولة
      final liquidityRatios = await _calculateLiquidityRatios(balanceSheet);
      sections.add(liquidityRatios.section);
      metrics.addAll(liquidityRatios.metrics);
      chartData.add(liquidityRatios.chart);

      // حساب نسب الربحية
      final profitabilityRatios = await _calculateProfitabilityRatios(
        balanceSheet,
        incomeStatement,
      );
      sections.add(profitabilityRatios.section);
      metrics.addAll(profitabilityRatios.metrics);
      chartData.add(profitabilityRatios.chart);

      // حساب نسب النشاط
      final activityRatios = await _calculateActivityRatios(
        balanceSheet,
        incomeStatement,
      );
      sections.add(activityRatios.section);
      metrics.addAll(activityRatios.metrics);
      chartData.add(activityRatios.chart);

      // حساب نسب الرافعة المالية
      final leverageRatios = await _calculateLeverageRatios(balanceSheet);
      sections.add(leverageRatios.section);
      metrics.addAll(leverageRatios.metrics);
      chartData.add(leverageRatios.chart);

      // إنشاء الرؤى والتوصيات
      insights.addAll(await _generateRatioInsights(metrics));

      final report = AdvancedFinancialReport(
        title: 'تحليل النسب المالية',
        subtitle: 'تحليل شامل للنسب المالية كما في ${_formatDate(toDate)}',
        analysisType: AnalysisType.ratio,
        period: period,
        detailLevel: detailLevel,
        fromDate: fromDate,
        toDate: toDate,
        parameters: parameters,
        sections: sections,
        metrics: metrics,
        chartData: chartData,
        insights: insights,
        summary: {
          'liquidity_score': _calculateOverallScore(liquidityRatios.metrics),
          'profitability_score': _calculateOverallScore(
            profitabilityRatios.metrics,
          ),
          'activity_score': _calculateOverallScore(activityRatios.metrics),
          'leverage_score': _calculateOverallScore(leverageRatios.metrics),
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في تحليل النسب المالية: ${e.toString()}');
    }
  }

  /// تحليل التباين (مقارنة الفعلي مع المخطط)
  Future<Result<AdvancedFinancialReport>> _generateVarianceAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      // سيتم تطوير هذه الطريقة لاحقاً عند إضافة نظام الموازنات
      return Result.error(
        'تحليل التباين غير متاح حالياً - يتطلب نظام الموازنات',
      );
    } catch (e) {
      return Result.error('خطأ في تحليل التباين: ${e.toString()}');
    }
  }

  /// التحليل المقارن
  Future<Result<AdvancedFinancialReport>> _generateComparativeAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      // مقارنة الفترة الحالية مع الفترة السابقة
      final currentPeriodDuration = toDate.difference(fromDate);
      final previousFromDate = fromDate.subtract(currentPeriodDuration);
      final previousToDate = fromDate.subtract(const Duration(days: 1));

      // الحصول على بيانات الفترة الحالية
      final currentIncomeResult = await _basicReportService
          .generateIncomeStatement(
            fromDate: fromDate,
            toDate: toDate,
            includeZeroBalances: false,
          );

      // الحصول على بيانات الفترة السابقة
      final previousIncomeResult = await _basicReportService
          .generateIncomeStatement(
            fromDate: previousFromDate,
            toDate: previousToDate,
            includeZeroBalances: false,
          );

      if (!currentIncomeResult.isSuccess || !previousIncomeResult.isSuccess) {
        return Result.error('خطأ في الحصول على البيانات للمقارنة');
      }

      final sections = <ReportSection>[];
      final metrics = <FinancialMetric>[];
      final chartData = <ChartData>[];
      final insights = <Insight>[];

      // مقارنة الإيرادات
      final revenueComparison = _compareRevenueData(
        currentIncomeResult.data!,
        previousIncomeResult.data!,
      );
      sections.add(revenueComparison.section);
      metrics.addAll(revenueComparison.metrics);
      chartData.add(revenueComparison.chart);

      // مقارنة المصروفات
      final expenseComparison = _compareExpenseData(
        currentIncomeResult.data!,
        previousIncomeResult.data!,
      );
      sections.add(expenseComparison.section);
      metrics.addAll(expenseComparison.metrics);
      chartData.add(expenseComparison.chart);

      // إنشاء الرؤى والتوصيات
      insights.addAll(
        await _generateComparativeInsights(
          revenueComparison,
          expenseComparison,
        ),
      );

      final report = AdvancedFinancialReport(
        title: 'التحليل المقارن',
        subtitle: 'مقارنة الأداء المالي بين الفترات',
        analysisType: AnalysisType.comparative,
        period: period,
        detailLevel: detailLevel,
        fromDate: fromDate,
        toDate: toDate,
        parameters: parameters,
        sections: sections,
        metrics: metrics,
        chartData: chartData,
        insights: insights,
        summary: {
          'revenue_change': revenueComparison.changePercentage,
          'expense_change': expenseComparison.changePercentage,
          'profit_change':
              revenueComparison.changePercentage -
              expenseComparison.changePercentage,
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في التحليل المقارن: ${e.toString()}');
    }
  }

  /// التحليل التنبؤي
  Future<Result<AdvancedFinancialReport>> _generateForecastAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      // سيتم تطوير هذه الطريقة لاحقاً مع إضافة خوارزميات التنبؤ
      return Result.error('التحليل التنبؤي غير متاح حالياً');
    } catch (e) {
      return Result.error('خطأ في التحليل التنبؤي: ${e.toString()}');
    }
  }

  /// تحليل الأداء
  Future<Result<AdvancedFinancialReport>> _generatePerformanceAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      final sections = <ReportSection>[];
      final metrics = <FinancialMetric>[];
      final chartData = <ChartData>[];
      final insights = <Insight>[];

      // تحليل أداء المبيعات
      final salesPerformance = await _analyzeSalesPerformance(fromDate, toDate);
      sections.add(salesPerformance.section);
      metrics.addAll(salesPerformance.metrics);
      chartData.add(salesPerformance.chart);

      // تحليل أداء المخزون
      final inventoryPerformance = await _analyzeInventoryPerformance(
        fromDate,
        toDate,
      );
      sections.add(inventoryPerformance.section);
      metrics.addAll(inventoryPerformance.metrics);
      chartData.add(inventoryPerformance.chart);

      // تحليل أداء العملاء
      final customerPerformance = await _analyzeCustomerPerformance(
        fromDate,
        toDate,
      );
      sections.add(customerPerformance.section);
      metrics.addAll(customerPerformance.metrics);
      chartData.add(customerPerformance.chart);

      // إنشاء الرؤى والتوصيات
      insights.addAll(
        await _generatePerformanceInsights(
          salesPerformance,
          inventoryPerformance,
          customerPerformance,
        ),
      );

      final report = AdvancedFinancialReport(
        title: 'تحليل الأداء',
        subtitle:
            'تحليل شامل لأداء الأعمال للفترة من ${_formatDate(fromDate)} إلى ${_formatDate(toDate)}',
        analysisType: AnalysisType.performance,
        period: period,
        detailLevel: detailLevel,
        fromDate: fromDate,
        toDate: toDate,
        parameters: parameters,
        sections: sections,
        metrics: metrics,
        chartData: chartData,
        insights: insights,
        summary: {
          'sales_performance_score': _calculateOverallScore(
            salesPerformance.metrics,
          ),
          'inventory_performance_score': _calculateOverallScore(
            inventoryPerformance.metrics,
          ),
          'customer_performance_score': _calculateOverallScore(
            customerPerformance.metrics,
          ),
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في تحليل الأداء: ${e.toString()}');
    }
  }

  /// تحليل السيولة
  Future<Result<AdvancedFinancialReport>> _generateLiquidityAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      final sections = <ReportSection>[];
      final metrics = <FinancialMetric>[];
      final chartData = <ChartData>[];
      final insights = <Insight>[];

      // الحصول على الميزانية العمومية
      final balanceSheetResult = await _basicReportService.generateBalanceSheet(
        asOfDate: toDate,
        includeZeroBalances: false,
      );

      if (!balanceSheetResult.isSuccess) {
        return Result.error('خطأ في الحصول على بيانات الميزانية العمومية');
      }

      // تحليل السيولة قصيرة المدى
      final shortTermLiquidity = await _analyzeShortTermLiquidity(
        balanceSheetResult.data!,
      );
      sections.add(shortTermLiquidity.section);
      metrics.addAll(shortTermLiquidity.metrics);
      chartData.add(shortTermLiquidity.chart);

      // تحليل السيولة طويلة المدى
      final longTermLiquidity = await _analyzeLongTermLiquidity(
        balanceSheetResult.data!,
      );
      sections.add(longTermLiquidity.section);
      metrics.addAll(longTermLiquidity.metrics);
      chartData.add(longTermLiquidity.chart);

      // تحليل التدفق النقدي
      final cashFlowAnalysis = await _analyzeCashFlow(fromDate, toDate);
      sections.add(cashFlowAnalysis.section);
      metrics.addAll(cashFlowAnalysis.metrics);
      chartData.add(cashFlowAnalysis.chart);

      // إنشاء الرؤى والتوصيات
      insights.addAll(
        await _generateLiquidityInsights(
          shortTermLiquidity,
          longTermLiquidity,
          cashFlowAnalysis,
        ),
      );

      final report = AdvancedFinancialReport(
        title: 'تحليل السيولة',
        subtitle:
            'تحليل شامل للسيولة والتدفق النقدي كما في ${_formatDate(toDate)}',
        analysisType: AnalysisType.liquidity,
        period: period,
        detailLevel: detailLevel,
        fromDate: fromDate,
        toDate: toDate,
        parameters: parameters,
        sections: sections,
        metrics: metrics,
        chartData: chartData,
        insights: insights,
        summary: {
          'short_term_liquidity_score': _calculateOverallScore(
            shortTermLiquidity.metrics,
          ),
          'long_term_liquidity_score': _calculateOverallScore(
            longTermLiquidity.metrics,
          ),
          'cash_flow_score': _calculateOverallScore(cashFlowAnalysis.metrics),
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في تحليل السيولة: ${e.toString()}');
    }
  }

  /// تحليل الربحية
  Future<Result<AdvancedFinancialReport>> _generateProfitabilityAnalysis({
    required ReportPeriod period,
    required DetailLevel detailLevel,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, dynamic> parameters,
  }) async {
    try {
      final sections = <ReportSection>[];
      final metrics = <FinancialMetric>[];
      final chartData = <ChartData>[];
      final insights = <Insight>[];

      // الحصول على قائمة الدخل
      final incomeStatementResult = await _basicReportService
          .generateIncomeStatement(
            fromDate: fromDate,
            toDate: toDate,
            includeZeroBalances: false,
          );

      // الحصول على الميزانية العمومية
      final balanceSheetResult = await _basicReportService.generateBalanceSheet(
        asOfDate: toDate,
        includeZeroBalances: false,
      );

      if (!incomeStatementResult.isSuccess || !balanceSheetResult.isSuccess) {
        return Result.error('خطأ في الحصول على البيانات المالية');
      }

      // تحليل هوامش الربح
      final profitMargins = await _analyzeProfitMargins(
        incomeStatementResult.data!,
      );
      sections.add(profitMargins.section);
      metrics.addAll(profitMargins.metrics);
      chartData.add(profitMargins.chart);

      // تحليل العائد على الاستثمار
      final returnAnalysis = await _analyzeReturns(
        incomeStatementResult.data!,
        balanceSheetResult.data!,
      );
      sections.add(returnAnalysis.section);
      metrics.addAll(returnAnalysis.metrics);
      chartData.add(returnAnalysis.chart);

      // تحليل ربحية المنتجات
      final productProfitability = await _analyzeProductProfitability(
        fromDate,
        toDate,
      );
      sections.add(productProfitability.section);
      metrics.addAll(productProfitability.metrics);
      chartData.add(productProfitability.chart);

      // إنشاء الرؤى والتوصيات
      insights.addAll(
        await _generateProfitabilityInsights(
          profitMargins,
          returnAnalysis,
          productProfitability,
        ),
      );

      final report = AdvancedFinancialReport(
        title: 'تحليل الربحية',
        subtitle:
            'تحليل شامل للربحية والعوائد للفترة من ${_formatDate(fromDate)} إلى ${_formatDate(toDate)}',
        analysisType: AnalysisType.profitability,
        period: period,
        detailLevel: detailLevel,
        fromDate: fromDate,
        toDate: toDate,
        parameters: parameters,
        sections: sections,
        metrics: metrics,
        chartData: chartData,
        insights: insights,
        summary: {
          'profit_margin_score': _calculateOverallScore(profitMargins.metrics),
          'return_score': _calculateOverallScore(returnAnalysis.metrics),
          'product_profitability_score': _calculateOverallScore(
            productProfitability.metrics,
          ),
        },
      );

      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في تحليل الربحية: ${e.toString()}');
    }
  }

  /// طرق مساعدة

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// إنشاء فترات زمنية للتحليل
  List<DateRange> _generatePeriods(
    DateTime fromDate,
    DateTime toDate,
    ReportPeriod period,
  ) {
    final periods = <DateRange>[];
    DateTime currentStart = fromDate;

    while (currentStart.isBefore(toDate)) {
      DateTime currentEnd;
      String label;

      switch (period) {
        case ReportPeriod.monthly:
          currentEnd = DateTime(currentStart.year, currentStart.month + 1, 0);
          if (currentEnd.isAfter(toDate)) currentEnd = toDate;
          label = '${currentStart.month}/${currentStart.year}';
          break;
        case ReportPeriod.quarterly:
          final quarter = ((currentStart.month - 1) ~/ 3) + 1;
          currentEnd = DateTime(currentStart.year, quarter * 3 + 1, 0);
          if (currentEnd.isAfter(toDate)) currentEnd = toDate;
          label = 'Q$quarter ${currentStart.year}';
          break;
        case ReportPeriod.yearly:
          currentEnd = DateTime(currentStart.year + 1, 1, 0);
          if (currentEnd.isAfter(toDate)) currentEnd = toDate;
          label = '${currentStart.year}';
          break;
        default:
          currentEnd = toDate;
          label = 'الفترة الكاملة';
      }

      periods.add(
        DateRange(start: currentStart, end: currentEnd, label: label),
      );

      if (period == ReportPeriod.custom ||
          period == ReportPeriod.daily ||
          period == ReportPeriod.weekly) {
        break;
      }

      currentStart = DateTime(
        currentEnd.year,
        currentEnd.month,
        currentEnd.day + 1,
      );
    }

    return periods;
  }

  /// حساب اتجاه البيانات
  String _calculateTrendDirection(List<double> values) {
    if (values.length < 2) return 'مستقر';

    final firstHalf = values.take(values.length ~/ 2).toList();
    final secondHalf = values.skip(values.length ~/ 2).toList();

    final firstAvg = firstHalf.reduce((a, b) => a + b) / firstHalf.length;
    final secondAvg = secondHalf.reduce((a, b) => a + b) / secondHalf.length;

    if (secondAvg > firstAvg * 1.05) return 'صاعد';
    if (secondAvg < firstAvg * 0.95) return 'هابط';
    return 'مستقر';
  }

  /// حساب النتيجة الإجمالية للمؤشرات
  double _calculateOverallScore(List<FinancialMetric> metrics) {
    if (metrics.isEmpty) return 0;

    double totalScore = 0;
    for (final metric in metrics) {
      switch (metric.status) {
        case MetricStatus.excellent:
          totalScore += 5;
          break;
        case MetricStatus.good:
          totalScore += 4;
          break;
        case MetricStatus.normal:
          totalScore += 3;
          break;
        case MetricStatus.warning:
          totalScore += 2;
          break;
        case MetricStatus.critical:
          totalScore += 1;
          break;
      }
    }

    return totalScore / metrics.length;
  }

  /// طرق التحليل المساعدة (ستتم إضافتها لاحقاً)

  Future<AnalysisData> _analyzeRevenueTrend(List<DateRange> periods) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      double totalRevenue = 0.0;
      double previousPeriodRevenue = 0.0;

      for (int i = 0; i < periods.length; i++) {
        final period = periods[i];

        // الحصول على إجمالي المبيعات للفترة
        final periodRevenue = await _invoiceDao.getTotalSales(
          startDate: period.start,
          endDate: period.end,
        );

        values.add(periodRevenue);
        dataPoints.add(DataPoint(label: period.label, value: periodRevenue));

        totalRevenue += periodRevenue;

        // حساب معدل النمو للفترة الحالية مقارنة بالسابقة
        if (i > 0) {
          final growthRate = previousPeriodRevenue > 0
              ? ((periodRevenue - previousPeriodRevenue) /
                        previousPeriodRevenue) *
                    100
              : 0.0;

          metrics.add(
            FinancialMetric(
              name: 'معدل نمو الإيرادات - ${period.label}',
              category: 'اتجاه الإيرادات',
              value: growthRate,
              unit: '%',
              status: _evaluateGrowthRate(growthRate),
              description: 'معدل نمو الإيرادات مقارنة بالفترة السابقة',
            ),
          );
        }

        previousPeriodRevenue = periodRevenue;
      }

      // متوسط الإيرادات
      final avgRevenue = periods.isNotEmpty
          ? totalRevenue / periods.length
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'متوسط الإيرادات',
          category: 'اتجاه الإيرادات',
          value: avgRevenue,
          unit: 'ريال',
          status: _evaluateSalesAmount(avgRevenue),
          description: 'متوسط الإيرادات عبر جميع الفترات',
        ),
      );

      // إجمالي الإيرادات
      metrics.add(
        FinancialMetric(
          name: 'إجمالي الإيرادات',
          category: 'اتجاه الإيرادات',
          value: totalRevenue,
          unit: 'ريال',
          status: _evaluateSalesAmount(totalRevenue),
          description: 'إجمالي الإيرادات لجميع الفترات',
        ),
      );

      return AnalysisData(
        section: ReportSection(
          title: 'تحليل الإيرادات',
          order: 1,
          subtitle: 'تحليل اتجاه الإيرادات عبر الفترات الزمنية',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'اتجاه الإيرادات',
          type: ChartType.line,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'تحليل الإيرادات', order: 1),
        metrics: [],
        chart: ChartData(
          title: 'اتجاه الإيرادات',
          type: ChartType.line,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeExpenseTrend(List<DateRange> periods) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      double totalExpenses = 0.0;
      double previousPeriodExpenses = 0.0;

      for (int i = 0; i < periods.length; i++) {
        final period = periods[i];

        // الحصول على إجمالي المشتريات للفترة
        final periodExpenses = await _invoiceDao.getTotalPurchases(
          startDate: period.start,
          endDate: period.end,
        );

        values.add(periodExpenses);
        dataPoints.add(DataPoint(label: period.label, value: periodExpenses));

        totalExpenses += periodExpenses;

        // حساب معدل النمو للفترة الحالية مقارنة بالسابقة
        if (i > 0) {
          final growthRate = previousPeriodExpenses > 0
              ? ((periodExpenses - previousPeriodExpenses) /
                        previousPeriodExpenses) *
                    100
              : 0.0;

          metrics.add(
            FinancialMetric(
              name: 'معدل نمو المصروفات - ${period.label}',
              category: 'اتجاه المصروفات',
              value: growthRate,
              unit: '%',
              status: _evaluateExpenseGrowthRate(growthRate),
              description: 'معدل نمو المصروفات مقارنة بالفترة السابقة',
            ),
          );
        }

        previousPeriodExpenses = periodExpenses;
      }

      // متوسط المصروفات
      final avgExpenses = periods.isNotEmpty
          ? totalExpenses / periods.length
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'متوسط المصروفات',
          category: 'اتجاه المصروفات',
          value: avgExpenses,
          unit: 'ريال',
          status: _evaluatePurchaseAmount(avgExpenses),
          description: 'متوسط المصروفات عبر جميع الفترات',
        ),
      );

      // إجمالي المصروفات
      metrics.add(
        FinancialMetric(
          name: 'إجمالي المصروفات',
          category: 'اتجاه المصروفات',
          value: totalExpenses,
          unit: 'ريال',
          status: _evaluatePurchaseAmount(totalExpenses),
          description: 'إجمالي المصروفات لجميع الفترات',
        ),
      );

      return AnalysisData(
        section: ReportSection(
          title: 'تحليل المصروفات',
          order: 2,
          subtitle: 'تحليل اتجاه المصروفات عبر الفترات الزمنية',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'اتجاه المصروفات',
          type: ChartType.line,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'تحليل المصروفات', order: 2),
        metrics: [],
        chart: ChartData(
          title: 'اتجاه المصروفات',
          type: ChartType.line,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeProfitTrend(List<DateRange> periods) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      double totalProfit = 0.0;
      double previousPeriodProfit = 0.0;

      for (int i = 0; i < periods.length; i++) {
        final period = periods[i];

        // الحصول على إجمالي المبيعات والمشتريات للفترة
        final periodRevenue = await _invoiceDao.getTotalSales(
          startDate: period.start,
          endDate: period.end,
        );
        final periodExpenses = await _invoiceDao.getTotalPurchases(
          startDate: period.start,
          endDate: period.end,
        );

        final periodProfit = periodRevenue - periodExpenses;
        values.add(periodProfit);
        dataPoints.add(DataPoint(label: period.label, value: periodProfit));

        totalProfit += periodProfit;

        // حساب معدل النمو للفترة الحالية مقارنة بالسابقة
        if (i > 0) {
          final growthRate = previousPeriodProfit != 0
              ? ((periodProfit - previousPeriodProfit) /
                        previousPeriodProfit.abs()) *
                    100
              : 0.0;

          metrics.add(
            FinancialMetric(
              name: 'معدل نمو الأرباح - ${period.label}',
              category: 'اتجاه الأرباح',
              value: growthRate,
              unit: '%',
              status: _evaluateGrowthRate(growthRate),
              description: 'معدل نمو الأرباح مقارنة بالفترة السابقة',
            ),
          );
        }

        // هامش الربح للفترة
        final profitMargin = periodRevenue > 0
            ? (periodProfit / periodRevenue) * 100
            : 0.0;
        metrics.add(
          FinancialMetric(
            name: 'هامش الربح - ${period.label}',
            category: 'اتجاه الأرباح',
            value: profitMargin,
            unit: '%',
            status: _evaluateProfitMargin(profitMargin),
            description: 'هامش الربح للفترة',
          ),
        );

        previousPeriodProfit = periodProfit;
      }

      // متوسط الأرباح
      final avgProfit = periods.isNotEmpty ? totalProfit / periods.length : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'متوسط الأرباح',
          category: 'اتجاه الأرباح',
          value: avgProfit,
          unit: 'ريال',
          status: _evaluateNetCashFlow(avgProfit),
          description: 'متوسط الأرباح عبر جميع الفترات',
        ),
      );

      // إجمالي الأرباح
      metrics.add(
        FinancialMetric(
          name: 'إجمالي الأرباح',
          category: 'اتجاه الأرباح',
          value: totalProfit,
          unit: 'ريال',
          status: _evaluateNetCashFlow(totalProfit),
          description: 'إجمالي الأرباح لجميع الفترات',
        ),
      );

      return AnalysisData(
        section: ReportSection(
          title: 'تحليل الأرباح',
          order: 3,
          subtitle: 'تحليل اتجاه الأرباح وهوامش الربح عبر الفترات الزمنية',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'اتجاه الأرباح',
          type: ChartType.line,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'تحليل الأرباح', order: 3),
        metrics: [],
        chart: ChartData(
          title: 'اتجاه الأرباح',
          type: ChartType.line,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<List<Insight>> _generateTrendInsights(
    AnalysisData revenueData,
    AnalysisData expenseData,
    AnalysisData profitData,
  ) async {
    final insights = <Insight>[];

    try {
      // تحليل اتجاه الإيرادات
      final revenueDirection = _calculateTrendDirection(revenueData.values);
      if (revenueDirection == 'صاعد') {
        insights.add(
          Insight(
            title: 'نمو إيجابي في الإيرادات',
            description:
                'تظهر الإيرادات اتجاهاً صاعداً مما يشير إلى نمو الأعمال',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            recommendations: ['استمر في الاستراتيجيات الحالية وفكر في التوسع'],
          ),
        );
      } else if (revenueDirection == 'هابط') {
        insights.add(
          Insight(
            title: 'انخفاض في الإيرادات',
            description: 'تظهر الإيرادات اتجاهاً هابطاً يتطلب اهتماماً فورياً',
            type: InsightType.risk,
            priority: InsightPriority.high,
            recommendations: [
              'راجع استراتيجيات المبيعات والتسويق وحدد أسباب الانخفاض',
            ],
          ),
        );
      }

      // تحليل اتجاه المصروفات
      final expenseDirection = _calculateTrendDirection(expenseData.values);
      if (expenseDirection == 'صاعد') {
        insights.add(
          Insight(
            title: 'ارتفاع في المصروفات',
            description: 'تظهر المصروفات اتجاهاً صاعداً قد يؤثر على الربحية',
            type: InsightType.risk,
            priority: InsightPriority.medium,
            recommendations: ['راجع بنود المصروفات وابحث عن فرص للتوفير'],
          ),
        );
      } else if (expenseDirection == 'هابط') {
        insights.add(
          Insight(
            title: 'انخفاض في المصروفات',
            description: 'تظهر المصروفات اتجاهاً هابطاً مما يحسن الربحية',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            recommendations: ['استمر في سياسات التحكم في التكاليف'],
          ),
        );
      }

      // تحليل اتجاه الأرباح
      final profitDirection = _calculateTrendDirection(profitData.values);
      if (profitDirection == 'صاعد') {
        insights.add(
          Insight(
            title: 'نمو في الأرباح',
            description: 'تظهر الأرباح اتجاهاً صاعداً يعكس أداءً مالياً قوياً',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            recommendations: ['استثمر الأرباح الإضافية في نمو الأعمال'],
          ),
        );
      } else if (profitDirection == 'هابط') {
        insights.add(
          Insight(
            title: 'انخفاض في الأرباح',
            description: 'تظهر الأرباح اتجاهاً هابطاً يتطلب مراجعة شاملة',
            type: InsightType.risk,
            priority: InsightPriority.critical,
            recommendations: ['راجع هيكل التكاليف واستراتيجيات التسعير'],
          ),
        );
      }

      // تحليل العلاقة بين الإيرادات والمصروفات
      if (revenueDirection == 'صاعد' && expenseDirection == 'صاعد') {
        insights.add(
          Insight(
            title: 'نمو متوازي في الإيرادات والمصروفات',
            description: 'كل من الإيرادات والمصروفات في ازدياد',
            type: InsightType.trend,
            priority: InsightPriority.medium,
            recommendations: ['راقب هوامش الربح للتأكد من استدامة النمو'],
          ),
        );
      }

      return insights;
    } catch (e) {
      return [];
    }
  }

  Future<AnalysisData> _calculateLiquidityRatios(
    basic_reports.FinancialReport balanceSheet,
  ) async {
    try {
      // الحصول على الأصول المتداولة
      final currentAssets = await _getCurrentAssets();

      // الحصول على الخصوم المتداولة
      final currentLiabilities = await _getCurrentLiabilities();

      // الحصول على النقد والاستثمارات قصيرة المدى
      final cashAndEquivalents = await _getCashAndEquivalents();

      // حساب النسب
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];

      // نسبة التداول (Current Ratio)
      final currentRatio = currentLiabilities > 0
          ? (currentAssets / currentLiabilities).toDouble()
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة التداول',
          category: 'نسب السيولة',
          value: currentRatio,
          unit: 'مرة',
          status: _evaluateCurrentRatio(currentRatio),
          description: 'قدرة الشركة على سداد التزاماتها قصيرة المدى',
        ),
      );
      dataPoints.add(DataPoint(label: 'نسبة التداول', value: currentRatio));

      // النسبة السريعة (Quick Ratio)
      final quickRatio = currentLiabilities > 0
          ? ((currentAssets - await _getInventoryValue()) / currentLiabilities)
                .toDouble()
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'النسبة السريعة',
          category: 'نسب السيولة',
          value: quickRatio,
          unit: 'مرة',
          status: _evaluateQuickRatio(quickRatio),
          description:
              'قدرة الشركة على سداد التزاماتها دون الاعتماد على المخزون',
        ),
      );
      dataPoints.add(DataPoint(label: 'النسبة السريعة', value: quickRatio));

      // النسبة النقدية (Cash Ratio)
      final cashRatio = currentLiabilities > 0
          ? (cashAndEquivalents / currentLiabilities).toDouble()
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'النسبة النقدية',
          category: 'نسب السيولة',
          value: cashRatio,
          unit: 'مرة',
          status: _evaluateCashRatio(cashRatio),
          description: 'قدرة الشركة على سداد التزاماتها باستخدام النقد فقط',
        ),
      );
      dataPoints.add(DataPoint(label: 'النسبة النقدية', value: cashRatio));

      return AnalysisData(
        section: ReportSection(
          title: 'نسب السيولة',
          order: 1,
          subtitle: 'تحليل قدرة الشركة على الوفاء بالتزاماتها قصيرة المدى',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'نسب السيولة',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: [currentRatio, quickRatio, cashRatio],
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'نسب السيولة', order: 1),
        metrics: [],
        chart: ChartData(
          title: 'نسب السيولة',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _calculateProfitabilityRatios(
    basic_reports.FinancialReport balanceSheet,
    basic_reports.FinancialReport incomeStatement,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // الحصول على البيانات من التقارير
      double revenue = 0.0;
      double grossProfit = 0.0;
      double netIncome = 0.0;
      double totalAssets = 0.0;
      double totalEquity = 0.0;

      // استخراج الإيرادات والأرباح من قائمة الدخل
      for (final section in incomeStatement.sections) {
        for (final line in section.lines) {
          if (line.accountName.contains('مبيعات') ||
              line.accountName.contains('إيرادات')) {
            revenue += line.amount;
          }
          if (line.accountName.contains('مجمل الربح')) {
            grossProfit += line.amount;
          }
          if (line.accountName.contains('صافي الربح') ||
              line.accountName.contains('صافي الدخل')) {
            netIncome += line.amount;
          }
        }
      }

      // استخراج الأصول وحقوق الملكية من الميزانية
      for (final section in balanceSheet.sections) {
        for (final line in section.lines) {
          if (section.title.contains('الأصول')) {
            totalAssets += line.amount;
          }
          if (section.title.contains('حقوق الملكية')) {
            totalEquity += line.amount;
          }
        }
      }

      // هامش الربح الإجمالي
      final grossProfitMargin = revenue > 0
          ? (grossProfit / revenue) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'هامش الربح الإجمالي',
          category: 'نسب الربحية',
          value: grossProfitMargin,
          unit: '%',
          status: _evaluateProfitMargin(grossProfitMargin),
          description: 'نسبة الربح الإجمالي إلى المبيعات',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'هامش الربح الإجمالي', value: grossProfitMargin),
      );
      values.add(grossProfitMargin);

      // هامش الربح الصافي
      final netProfitMargin = revenue > 0 ? (netIncome / revenue) * 100 : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'هامش الربح الصافي',
          category: 'نسب الربحية',
          value: netProfitMargin,
          unit: '%',
          status: _evaluateProfitMargin(netProfitMargin),
          description: 'نسبة الربح الصافي إلى المبيعات',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'هامش الربح الصافي', value: netProfitMargin),
      );
      values.add(netProfitMargin);

      // العائد على الأصول (ROA)
      final roa = totalAssets > 0 ? (netIncome / totalAssets) * 100 : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'العائد على الأصول',
          category: 'نسب الربحية',
          value: roa,
          unit: '%',
          status: _evaluateROA(roa),
          description: 'نسبة الربح الصافي إلى إجمالي الأصول',
        ),
      );
      dataPoints.add(DataPoint(label: 'العائد على الأصول', value: roa));
      values.add(roa);

      // العائد على حقوق الملكية (ROE)
      final roe = totalEquity > 0 ? (netIncome / totalEquity) * 100 : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'العائد على حقوق الملكية',
          category: 'نسب الربحية',
          value: roe,
          unit: '%',
          status: _evaluateROE(roe),
          description: 'نسبة الربح الصافي إلى حقوق الملكية',
        ),
      );
      dataPoints.add(DataPoint(label: 'العائد على حقوق الملكية', value: roe));
      values.add(roe);

      return AnalysisData(
        section: ReportSection(
          title: 'نسب الربحية',
          order: 2,
          subtitle: 'تحليل قدرة الشركة على تحقيق الأرباح',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'نسب الربحية',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'نسب الربحية', order: 2),
        metrics: [],
        chart: ChartData(
          title: 'نسب الربحية',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _calculateActivityRatios(
    basic_reports.FinancialReport balanceSheet,
    basic_reports.FinancialReport incomeStatement,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // الحصول على البيانات من التقارير
      double revenue = 0.0;
      double totalAssets = 0.0;
      double inventory = 0.0;
      double accountsReceivable = 0.0;
      double accountsPayable = 0.0;

      // استخراج الإيرادات من قائمة الدخل
      for (final section in incomeStatement.sections) {
        for (final line in section.lines) {
          if (line.accountName.contains('مبيعات') ||
              line.accountName.contains('إيرادات')) {
            revenue += line.amount;
          }
        }
      }

      // استخراج البيانات من الميزانية
      for (final section in balanceSheet.sections) {
        for (final line in section.lines) {
          if (section.title.contains('الأصول')) {
            totalAssets += line.amount;
            if (line.accountName.contains('مخزون')) {
              inventory += line.amount;
            }
            if (line.accountName.contains('ذمم مدينة') ||
                line.accountName.contains('عملاء')) {
              accountsReceivable += line.amount;
            }
          }
          if (line.accountName.contains('ذمم دائنة') ||
              line.accountName.contains('موردين')) {
            accountsPayable += line.amount;
          }
        }
      }

      // معدل دوران الأصول
      final assetTurnover = totalAssets > 0 ? revenue / totalAssets : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'معدل دوران الأصول',
          category: 'نسب النشاط',
          value: assetTurnover,
          unit: 'مرة',
          status: _evaluateAssetTurnover(assetTurnover),
          description: 'كفاءة استخدام الأصول في توليد المبيعات',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'معدل دوران الأصول', value: assetTurnover),
      );
      values.add(assetTurnover);

      // معدل دوران المخزون
      final inventoryTurnover = inventory > 0 ? revenue / inventory : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'معدل دوران المخزون',
          category: 'نسب النشاط',
          value: inventoryTurnover,
          unit: 'مرة',
          status: _evaluateInventoryTurnover(inventoryTurnover),
          description: 'سرعة تحويل المخزون إلى مبيعات',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'معدل دوران المخزون', value: inventoryTurnover),
      );
      values.add(inventoryTurnover);

      // معدل دوران الذمم المدينة
      final receivablesTurnover = accountsReceivable > 0
          ? revenue / accountsReceivable
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'معدل دوران الذمم المدينة',
          category: 'نسب النشاط',
          value: receivablesTurnover,
          unit: 'مرة',
          status: _evaluateReceivablesTurnover(receivablesTurnover),
          description: 'سرعة تحصيل الذمم المدينة',
        ),
      );
      dataPoints.add(
        DataPoint(
          label: 'معدل دوران الذمم المدينة',
          value: receivablesTurnover,
        ),
      );
      values.add(receivablesTurnover);

      // فترة التحصيل (بالأيام)
      final collectionPeriod = receivablesTurnover > 0
          ? 365 / receivablesTurnover
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'فترة التحصيل',
          category: 'نسب النشاط',
          value: collectionPeriod,
          unit: 'يوم',
          status: _evaluateCollectionPeriod(collectionPeriod),
          description: 'متوسط عدد الأيام لتحصيل الذمم المدينة',
        ),
      );
      dataPoints.add(DataPoint(label: 'فترة التحصيل', value: collectionPeriod));
      values.add(collectionPeriod);

      return AnalysisData(
        section: ReportSection(
          title: 'نسب النشاط',
          order: 3,
          subtitle: 'تحليل كفاءة استخدام الأصول والموارد',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'نسب النشاط',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'نسب النشاط', order: 3),
        metrics: [],
        chart: ChartData(
          title: 'نسب النشاط',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _calculateLeverageRatios(
    basic_reports.FinancialReport balanceSheet,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // الحصول على البيانات من الميزانية
      double totalAssets = 0.0;
      double totalLiabilities = 0.0;
      double totalEquity = 0.0;
      double longTermDebt = 0.0;

      for (final section in balanceSheet.sections) {
        for (final line in section.lines) {
          if (section.title.contains('الأصول')) {
            totalAssets += line.amount;
          } else if (section.title.contains('الخصوم') ||
              section.title.contains('الالتزامات')) {
            totalLiabilities += line.amount;
            if (line.accountName.contains('طويلة الأجل') ||
                line.accountName.contains('قروض')) {
              longTermDebt += line.amount;
            }
          } else if (section.title.contains('حقوق الملكية')) {
            totalEquity += line.amount;
          }
        }
      }

      // نسبة الدين إلى الأصول
      final debtToAssets = totalAssets > 0
          ? (totalLiabilities / totalAssets) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة الدين إلى الأصول',
          category: 'نسب الرافعة المالية',
          value: debtToAssets,
          unit: '%',
          status: _evaluateDebtToAssets(debtToAssets),
          description: 'نسبة إجمالي الديون إلى إجمالي الأصول',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'نسبة الدين إلى الأصول', value: debtToAssets),
      );
      values.add(debtToAssets);

      // نسبة الدين إلى حقوق الملكية
      final debtToEquity = totalEquity > 0
          ? (totalLiabilities / totalEquity) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة الدين إلى حقوق الملكية',
          category: 'نسب الرافعة المالية',
          value: debtToEquity,
          unit: '%',
          status: _evaluateDebtToEquity(debtToEquity),
          description: 'نسبة إجمالي الديون إلى حقوق الملكية',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'نسبة الدين إلى حقوق الملكية', value: debtToEquity),
      );
      values.add(debtToEquity);

      // نسبة حقوق الملكية إلى الأصول
      final equityToAssets = totalAssets > 0
          ? (totalEquity / totalAssets) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة حقوق الملكية إلى الأصول',
          category: 'نسب الرافعة المالية',
          value: equityToAssets,
          unit: '%',
          status: _evaluateEquityToAssets(equityToAssets),
          description: 'نسبة حقوق الملكية إلى إجمالي الأصول',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'نسبة حقوق الملكية إلى الأصول', value: equityToAssets),
      );
      values.add(equityToAssets);

      // الرافعة المالية
      final financialLeverage = totalEquity > 0
          ? totalAssets / totalEquity
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'الرافعة المالية',
          category: 'نسب الرافعة المالية',
          value: financialLeverage,
          unit: 'مرة',
          status: _evaluateFinancialLeverage(financialLeverage),
          description: 'مضاعف حقوق الملكية',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'الرافعة المالية', value: financialLeverage),
      );
      values.add(financialLeverage);

      return AnalysisData(
        section: ReportSection(
          title: 'نسب الرافعة المالية',
          order: 4,
          subtitle: 'تحليل هيكل رأس المال والمخاطر المالية',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'نسب الرافعة المالية',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'نسب الرافعة المالية', order: 4),
        metrics: [],
        chart: ChartData(
          title: 'نسب الرافعة المالية',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<List<Insight>> _generateRatioInsights(
    List<FinancialMetric> metrics,
  ) async {
    final insights = <Insight>[];

    try {
      // تحليل نسب الربحية
      final profitabilityMetrics = metrics
          .where((m) => m.category == 'نسب الربحية')
          .toList();
      for (final metric in profitabilityMetrics) {
        if (metric.status == MetricStatus.excellent) {
          insights.add(
            Insight(
              title: 'أداء ممتاز في ${metric.name}',
              description:
                  '${metric.name} تظهر أداءً ممتازاً بقيمة ${metric.value.toStringAsFixed(2)}${metric.unit}',
              type: InsightType.opportunity,
              priority: InsightPriority.medium,
              recommendations: [
                'استمر في الاستراتيجيات الحالية',
                'فكر في التوسع والاستثمار',
              ],
            ),
          );
        } else if (metric.status == MetricStatus.critical) {
          insights.add(
            Insight(
              title: 'تحذير: انخفاض في ${metric.name}',
              description:
                  '${metric.name} تظهر أداءً ضعيفاً بقيمة ${metric.value.toStringAsFixed(2)}${metric.unit}',
              type: InsightType.risk,
              priority: InsightPriority.high,
              recommendations: [
                'راجع استراتيجيات التسعير',
                'قلل التكاليف التشغيلية',
                'حسن كفاءة العمليات',
              ],
            ),
          );
        }
      }

      // تحليل نسب النشاط
      final activityMetrics = metrics
          .where((m) => m.category == 'نسب النشاط')
          .toList();
      for (final metric in activityMetrics) {
        if (metric.name.contains('دوران المخزون') &&
            metric.status == MetricStatus.critical) {
          insights.add(
            Insight(
              title: 'بطء في دوران المخزون',
              description:
                  'معدل دوران المخزون منخفض مما يشير إلى تراكم المخزون',
              type: InsightType.risk,
              priority: InsightPriority.high,
              recommendations: [
                'راجع سياسات المخزون',
                'حسن التنبؤ بالطلب',
                'فعل حملات تسويقية للمخزون البطيء',
              ],
            ),
          );
        }
        if (metric.name.contains('فترة التحصيل') &&
            metric.status == MetricStatus.warning) {
          insights.add(
            Insight(
              title: 'تأخير في تحصيل الذمم المدينة',
              description: 'فترة التحصيل أطول من المعدل المطلوب',
              type: InsightType.risk,
              priority: InsightPriority.medium,
              recommendations: [
                'راجع سياسات الائتمان',
                'حسن إجراءات التحصيل',
                'قدم خصومات للدفع المبكر',
              ],
            ),
          );
        }
      }

      // تحليل نسب الرافعة المالية
      final leverageMetrics = metrics
          .where((m) => m.category == 'نسب الرافعة المالية')
          .toList();
      for (final metric in leverageMetrics) {
        if (metric.name.contains('الدين إلى الأصول') &&
            metric.status == MetricStatus.critical) {
          insights.add(
            Insight(
              title: 'مخاطر مالية عالية',
              description:
                  'نسبة الدين إلى الأصول مرتفعة جداً مما يزيد المخاطر المالية',
              type: InsightType.risk,
              priority: InsightPriority.critical,
              recommendations: [
                'قلل الديون',
                'زد رأس المال',
                'حسن التدفق النقدي',
              ],
            ),
          );
        }
        if (metric.name.contains('حقوق الملكية إلى الأصول') &&
            metric.status == MetricStatus.excellent) {
          insights.add(
            Insight(
              title: 'هيكل مالي قوي',
              description:
                  'نسبة حقوق الملكية إلى الأصول ممتازة تعكس استقراراً مالياً',
              type: InsightType.opportunity,
              priority: InsightPriority.medium,
              recommendations: [
                'استغل القوة المالية للتوسع',
                'فكر في استثمارات جديدة',
              ],
            ),
          );
        }
      }

      // تحليل شامل للأداء
      final excellentCount = metrics
          .where((m) => m.status == MetricStatus.excellent)
          .length;
      final criticalCount = metrics
          .where((m) => m.status == MetricStatus.critical)
          .length;
      final totalCount = metrics.length;

      if (excellentCount > totalCount * 0.6) {
        insights.add(
          Insight(
            title: 'أداء مالي ممتاز',
            description: 'معظم النسب المالية تظهر أداءً ممتازاً',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            recommendations: [
              'استمر في الاستراتيجيات الحالية',
              'فكر في التوسع',
              'استثمر في النمو',
            ],
          ),
        );
      } else if (criticalCount > totalCount * 0.4) {
        insights.add(
          Insight(
            title: 'تحذير: تدهور في الأداء المالي',
            description: 'عدد كبير من النسب المالية يظهر أداءً ضعيفاً',
            type: InsightType.risk,
            priority: InsightPriority.critical,
            recommendations: [
              'راجع الاستراتيجية المالية',
              'قلل التكاليف',
              'حسن الكفاءة التشغيلية',
            ],
          ),
        );
      }

      return insights;
    } catch (e) {
      return [];
    }
  }

  // طرق التحليل المقارن
  AnalysisData _compareRevenueData(
    basic_reports.FinancialReport current,
    basic_reports.FinancialReport previous,
  ) {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // استخراج بيانات الإيرادات من التقارير
      final currentRevenue = current.summary['total_revenue'] as double? ?? 0.0;
      final previousRevenue =
          previous.summary['total_revenue'] as double? ?? 0.0;

      // حساب التغيير المطلق والنسبي
      final absoluteChange = currentRevenue - previousRevenue;
      final percentageChange = previousRevenue != 0
          ? (absoluteChange / previousRevenue) * 100
          : 0.0;

      // معدل النمو السنوي
      final growthRate = previousRevenue != 0
          ? ((currentRevenue / previousRevenue) - 1) * 100
          : 0.0;

      // إضافة المقاييس المالية
      metrics.add(
        FinancialMetric(
          name: 'الإيرادات الحالية',
          category: 'مقارنة الإيرادات',
          value: currentRevenue,
          unit: 'ريال',
          status: _evaluateRevenueStatus(currentRevenue),
          description: 'إجمالي الإيرادات للفترة الحالية',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'الإيرادات السابقة',
          category: 'مقارنة الإيرادات',
          value: previousRevenue,
          unit: 'ريال',
          status: MetricStatus.normal,
          description: 'إجمالي الإيرادات للفترة السابقة',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'التغيير المطلق',
          category: 'مقارنة الإيرادات',
          value: absoluteChange,
          unit: 'ريال',
          status: _evaluateChangeStatus(absoluteChange),
          description: 'الفرق بين الإيرادات الحالية والسابقة',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'نسبة التغيير',
          category: 'مقارنة الإيرادات',
          value: percentageChange,
          unit: '%',
          status: _evaluatePercentageChangeStatus(percentageChange),
          description: 'النسبة المئوية للتغيير في الإيرادات',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'معدل النمو',
          category: 'مقارنة الإيرادات',
          value: growthRate,
          unit: '%',
          status: _evaluateGrowthRateStatus(growthRate),
          description: 'معدل نمو الإيرادات مقارنة بالفترة السابقة',
        ),
      );

      // إضافة نقاط البيانات للرسم البياني
      dataPoints.add(
        DataPoint(label: 'الفترة السابقة', value: previousRevenue),
      );
      dataPoints.add(DataPoint(label: 'الفترة الحالية', value: currentRevenue));
      dataPoints.add(
        DataPoint(label: 'التغيير المطلق', value: absoluteChange.abs()),
      );

      // إضافة القيم للمعالجة اللاحقة
      values.addAll([
        currentRevenue,
        previousRevenue,
        absoluteChange,
        percentageChange,
        growthRate,
      ]);

      return AnalysisData(
        section: ReportSection(
          title: 'مقارنة الإيرادات',
          order: 1,
          subtitle: 'مقارنة شاملة للإيرادات بين الفترة الحالية والسابقة',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مقارنة الإيرادات',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
        changePercentage: percentageChange,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'مقارنة الإيرادات', order: 1),
        metrics: [],
        chart: ChartData(
          title: 'مقارنة الإيرادات',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  AnalysisData _compareExpenseData(
    basic_reports.FinancialReport current,
    basic_reports.FinancialReport previous,
  ) {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // استخراج بيانات المصروفات من التقارير
      final currentExpenses =
          current.summary['total_expenses'] as double? ?? 0.0;
      final previousExpenses =
          previous.summary['total_expenses'] as double? ?? 0.0;

      // حساب التغيير المطلق والنسبي
      final absoluteChange = currentExpenses - previousExpenses;
      final percentageChange = previousExpenses != 0
          ? (absoluteChange / previousExpenses) * 100
          : 0.0;

      // معدل النمو السنوي (إذا كانت الفترة أقل من سنة، يتم تحويلها لمعدل سنوي)
      final growthRate = percentageChange; // يمكن تعديلها حسب طول الفترة

      // إضافة المؤشرات
      metrics.add(
        FinancialMetric(
          name: 'المصروفات الحالية',
          category: 'مقارنة المصروفات',
          value: currentExpenses,
          unit: 'ريال',
          status: _evaluatePurchaseAmount(currentExpenses),
          description: 'إجمالي المصروفات للفترة الحالية',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'المصروفات السابقة',
          category: 'مقارنة المصروفات',
          value: previousExpenses,
          unit: 'ريال',
          status: _evaluatePurchaseAmount(previousExpenses),
          description: 'إجمالي المصروفات للفترة السابقة',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'التغيير المطلق',
          category: 'مقارنة المصروفات',
          value: absoluteChange,
          unit: 'ريال',
          status: _evaluateExpenseChangeStatus(absoluteChange),
          description: 'الفرق المطلق في المصروفات بين الفترتين',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'نسبة التغيير',
          category: 'مقارنة المصروفات',
          value: percentageChange,
          unit: '%',
          status: _evaluateExpensePercentageChangeStatus(percentageChange),
          description: 'النسبة المئوية للتغيير في المصروفات',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'معدل النمو',
          category: 'مقارنة المصروفات',
          value: growthRate,
          unit: '%',
          status: _evaluateExpenseGrowthRateStatus(growthRate),
          description: 'معدل نمو المصروفات مقارنة بالفترة السابقة',
        ),
      );

      // إضافة نقاط البيانات للرسم البياني
      dataPoints.add(
        DataPoint(label: 'الفترة السابقة', value: previousExpenses),
      );
      dataPoints.add(
        DataPoint(label: 'الفترة الحالية', value: currentExpenses),
      );
      dataPoints.add(
        DataPoint(label: 'التغيير المطلق', value: absoluteChange.abs()),
      );

      // إضافة القيم للمعالجة اللاحقة
      values.addAll([
        currentExpenses,
        previousExpenses,
        absoluteChange,
        percentageChange,
        growthRate,
      ]);

      return AnalysisData(
        section: ReportSection(
          title: 'مقارنة المصروفات',
          order: 2,
          subtitle: 'مقارنة شاملة للمصروفات بين الفترة الحالية والسابقة',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مقارنة المصروفات',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
        changePercentage: percentageChange,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'مقارنة المصروفات', order: 2),
        metrics: [],
        chart: ChartData(
          title: 'مقارنة المصروفات',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<List<Insight>> _generateComparativeInsights(
    AnalysisData revenueComparison,
    AnalysisData expenseComparison,
  ) async {
    final insights = <Insight>[];

    try {
      // تحليل تغيير الإيرادات
      final revenueChange = revenueComparison.changePercentage;
      if (revenueChange > 10) {
        insights.add(
          Insight(
            title: 'نمو قوي في الإيرادات',
            description:
                'ارتفعت الإيرادات بنسبة ${revenueChange.toStringAsFixed(1)}% مقارنة بالفترة السابقة',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            impact: revenueChange,
            recommendations: [
              'استمر في الاستراتيجيات الحالية التي أدت لهذا النمو',
              'فكر في التوسع والاستثمار في المزيد من الفرص',
              'حافظ على جودة الخدمة مع زيادة الحجم',
            ],
            data: {'revenue_change': revenueChange},
          ),
        );
      } else if (revenueChange > 5) {
        insights.add(
          Insight(
            title: 'نمو متوسط في الإيرادات',
            description:
                'ارتفعت الإيرادات بنسبة ${revenueChange.toStringAsFixed(1)}% مقارنة بالفترة السابقة',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            impact: revenueChange,
            recommendations: [
              'ابحث عن فرص لتسريع النمو',
              'راجع استراتيجيات التسويق والمبيعات',
              'حلل أسباب النمو وكررها',
            ],
            data: {'revenue_change': revenueChange},
          ),
        );
      } else if (revenueChange < -10) {
        insights.add(
          Insight(
            title: 'انخفاض حاد في الإيرادات',
            description:
                'انخفضت الإيرادات بنسبة ${revenueChange.abs().toStringAsFixed(1)}% مقارنة بالفترة السابقة',
            type: InsightType.risk,
            priority: InsightPriority.critical,
            impact: revenueChange.abs(),
            recommendations: [
              'راجع استراتيجية المبيعات والتسويق فوراً',
              'حلل أسباب الانخفاض وضع خطة للتعافي',
              'فكر في منتجات أو خدمات جديدة',
              'راجع أسعارك مقارنة بالمنافسين',
            ],
            data: {'revenue_change': revenueChange},
          ),
        );
      } else if (revenueChange < -5) {
        insights.add(
          Insight(
            title: 'انخفاض في الإيرادات',
            description:
                'انخفضت الإيرادات بنسبة ${revenueChange.abs().toStringAsFixed(1)}% مقارنة بالفترة السابقة',
            type: InsightType.risk,
            priority: InsightPriority.high,
            impact: revenueChange.abs(),
            recommendations: [
              'راجع استراتيجيات المبيعات',
              'حلل اتجاهات السوق والمنافسة',
              'فكر في حملات تسويقية جديدة',
            ],
            data: {'revenue_change': revenueChange},
          ),
        );
      }

      // تحليل تغيير المصروفات
      final expenseChange = expenseComparison.changePercentage;
      if (expenseChange > 15) {
        insights.add(
          Insight(
            title: 'ارتفاع كبير في المصروفات',
            description:
                'ارتفعت المصروفات بنسبة ${expenseChange.toStringAsFixed(1)}% مقارنة بالفترة السابقة',
            type: InsightType.risk,
            priority: InsightPriority.high,
            impact: expenseChange,
            recommendations: [
              'راجع جميع بنود المصروفات وحدد الزيادات غير المبررة',
              'ابحث عن فرص لتقليل التكاليف',
              'تأكد من أن الزيادة مرتبطة بنمو الإيرادات',
              'راجع عقود الموردين والخدمات',
            ],
            data: {'expense_change': expenseChange},
          ),
        );
      } else if (expenseChange < -10) {
        insights.add(
          Insight(
            title: 'تحسن ممتاز في إدارة التكاليف',
            description:
                'انخفضت المصروفات بنسبة ${expenseChange.abs().toStringAsFixed(1)}% مقارنة بالفترة السابقة',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            impact: expenseChange.abs(),
            recommendations: [
              'استمر في استراتيجيات تقليل التكاليف الحالية',
              'شارك أفضل الممارسات مع باقي الأقسام',
              'استثمر الوفورات في نمو الأعمال',
            ],
            data: {'expense_change': expenseChange},
          ),
        );
      }

      // تحليل العلاقة بين الإيرادات والمصروفات
      final profitChange = revenueChange - expenseChange;
      if (revenueChange > 0 && expenseChange > revenueChange) {
        insights.add(
          Insight(
            title: 'تحذير: نمو المصروفات يفوق نمو الإيرادات',
            description:
                'رغم نمو الإيرادات، إلا أن المصروفات نمت بوتيرة أسرع مما يقلل الربحية',
            type: InsightType.risk,
            priority: InsightPriority.high,
            impact: expenseChange - revenueChange,
            recommendations: [
              'راجع هيكل التكاليف وابحث عن فرص للتوفير',
              'تأكد من كفاءة العمليات التشغيلية',
              'راجع استراتيجيات التسعير',
            ],
            data: {
              'revenue_change': revenueChange,
              'expense_change': expenseChange,
              'profit_impact': profitChange,
            },
          ),
        );
      } else if (revenueChange > 0 && expenseChange < 0) {
        insights.add(
          Insight(
            title: 'أداء مالي ممتاز',
            description:
                'نمو في الإيرادات مع انخفاض في المصروفات - مزيج مثالي للربحية',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            impact: profitChange,
            recommendations: [
              'استمر في هذا الأداء الممتاز',
              'استثمر الأرباح الإضافية في التوسع',
              'وثق أفضل الممارسات للمستقبل',
            ],
            data: {
              'revenue_change': revenueChange,
              'expense_change': expenseChange,
              'profit_impact': profitChange,
            },
          ),
        );
      }

      // تحليل الاستقرار المالي
      if (revenueChange.abs() < 5 && expenseChange.abs() < 5) {
        insights.add(
          Insight(
            title: 'استقرار مالي',
            description:
                'تظهر الإيرادات والمصروفات استقراراً نسبياً مقارنة بالفترة السابقة',
            type: InsightType.trend,
            priority: InsightPriority.medium,
            recommendations: [
              'فكر في استراتيجيات للنمو المستدام',
              'ابحث عن فرص جديدة للتوسع',
              'راجع خطط التطوير طويلة المدى',
            ],
            data: {
              'revenue_change': revenueChange,
              'expense_change': expenseChange,
            },
          ),
        );
      }

      // تحليل الكفاءة التشغيلية
      final revenueMetrics = revenueComparison.metrics;
      final expenseMetrics = expenseComparison.metrics;

      if (revenueMetrics.isNotEmpty && expenseMetrics.isNotEmpty) {
        final excellentRevenueMetrics = revenueMetrics
            .where((m) => m.status == MetricStatus.excellent)
            .length;
        final criticalExpenseMetrics = expenseMetrics
            .where((m) => m.status == MetricStatus.critical)
            .length;

        if (excellentRevenueMetrics > revenueMetrics.length * 0.6) {
          insights.add(
            Insight(
              title: 'أداء ممتاز في مؤشرات الإيرادات',
              description:
                  'معظم مؤشرات الإيرادات تظهر أداءً ممتازاً في المقارنة',
              type: InsightType.opportunity,
              priority: InsightPriority.medium,
              recommendations: [
                'استمر في الاستراتيجيات الناجحة',
                'شارك أفضل الممارسات',
                'فكر في التوسع',
              ],
            ),
          );
        }

        if (criticalExpenseMetrics > expenseMetrics.length * 0.4) {
          insights.add(
            Insight(
              title: 'تحذير: مؤشرات المصروفات تحتاج مراجعة',
              description: 'عدد كبير من مؤشرات المصروفات يظهر أداءً ضعيفاً',
              type: InsightType.risk,
              priority: InsightPriority.high,
              recommendations: [
                'راجع بنود المصروفات الرئيسية',
                'ابحث عن فرص لتحسين الكفاءة',
                'قارن مع معايير الصناعة',
              ],
            ),
          );
        }
      }

      return insights;
    } catch (e) {
      return [];
    }
  }

  // طرق تحليل الأداء
  Future<AnalysisData> _analyzeSalesPerformance(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];

      // الحصول على ملخص المبيعات
      final salesSummary = await _invoiceDao.getSalesSummary(
        startDate: fromDate,
        endDate: toDate,
      );

      // إجمالي المبيعات
      final totalSales = salesSummary['total_sales'] ?? 0.0;
      metrics.add(
        FinancialMetric(
          name: 'إجمالي المبيعات',
          category: 'أداء المبيعات',
          value: totalSales,
          unit: 'ريال',
          status: _evaluateSalesAmount(totalSales),
          description: 'إجمالي قيمة المبيعات للفترة المحددة',
        ),
      );
      dataPoints.add(DataPoint(label: 'إجمالي المبيعات', value: totalSales));

      // عدد الفواتير
      final invoiceCount = salesSummary['invoice_count'] ?? 0.0;
      metrics.add(
        FinancialMetric(
          name: 'عدد فواتير المبيعات',
          category: 'أداء المبيعات',
          value: invoiceCount,
          unit: 'فاتورة',
          status: _evaluateInvoiceCount(invoiceCount.toInt()),
          description: 'عدد فواتير المبيعات المصدرة خلال الفترة',
        ),
      );
      dataPoints.add(DataPoint(label: 'عدد الفواتير', value: invoiceCount));

      // متوسط قيمة الفاتورة
      final avgInvoiceValue = invoiceCount > 0
          ? totalSales / invoiceCount
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'متوسط قيمة الفاتورة',
          category: 'أداء المبيعات',
          value: avgInvoiceValue,
          unit: 'ريال',
          status: _evaluateAverageInvoiceValue(avgInvoiceValue),
          description: 'متوسط قيمة فاتورة المبيعات',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'متوسط قيمة الفاتورة', value: avgInvoiceValue),
      );

      return AnalysisData(
        section: ReportSection(
          title: 'أداء المبيعات',
          order: 1,
          subtitle: 'تحليل شامل لأداء المبيعات خلال الفترة المحددة',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات أداء المبيعات',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: [totalSales, invoiceCount, avgInvoiceValue],
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'أداء المبيعات', order: 1),
        metrics: [],
        chart: ChartData(title: 'أداء المبيعات', type: ChartType.line),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeInventoryPerformance(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];

      // الحصول على إحصائيات حركات المخزون للفترة المحددة
      final movementStats = await _stockMovementDao.getMovementStatistics(
        startDate: fromDate,
        endDate: toDate,
      );

      // الحصول على حركات المخزون للفترة
      final movements = await _stockMovementDao.getMovementsByDateRange(
        fromDate,
        toDate,
      );

      // إجمالي عدد الحركات
      final totalMovements = movementStats['total'] ?? 0;
      metrics.add(
        FinancialMetric(
          name: 'إجمالي حركات المخزون',
          category: 'أداء المخزون',
          value: totalMovements.toDouble(),
          unit: 'حركة',
          status: _evaluateMovementCount(totalMovements),
          description: 'إجمالي عدد حركات المخزون خلال الفترة المحددة',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'إجمالي الحركات', value: totalMovements.toDouble()),
      );

      // إجمالي قيمة الحركات
      final totalValue = movementStats['total_value'] ?? 0.0;
      metrics.add(
        FinancialMetric(
          name: 'إجمالي قيمة حركات المخزون',
          category: 'أداء المخزون',
          value: totalValue,
          unit: 'ريال',
          status: _evaluateInventoryValue(totalValue),
          description: 'إجمالي قيمة حركات المخزون خلال الفترة',
        ),
      );
      dataPoints.add(DataPoint(label: 'إجمالي القيمة', value: totalValue));

      // حساب حركات الاستلام والصرف
      final receiptMovements = movements
          .where((m) => m.type == MovementType.receipt)
          .length;
      final issueMovements = movements
          .where((m) => m.type == MovementType.issue)
          .length;

      // نسبة دوران المخزون (تقريبية)
      final turnoverRatio = receiptMovements > 0
          ? (issueMovements / receiptMovements).toDouble()
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة دوران المخزون',
          category: 'أداء المخزون',
          value: turnoverRatio,
          unit: 'مرة',
          status: _evaluateTurnoverRatio(turnoverRatio),
          description: 'نسبة حركات الصرف إلى حركات الاستلام',
        ),
      );
      dataPoints.add(DataPoint(label: 'نسبة الدوران', value: turnoverRatio));

      // متوسط قيمة الحركة
      final avgMovementValue = totalMovements > 0
          ? totalValue / totalMovements
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'متوسط قيمة الحركة',
          category: 'أداء المخزون',
          value: avgMovementValue,
          unit: 'ريال',
          status: _evaluateAverageMovementValue(avgMovementValue),
          description: 'متوسط قيمة حركة المخزون الواحدة',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'متوسط قيمة الحركة', value: avgMovementValue),
      );

      return AnalysisData(
        section: ReportSection(
          title: 'أداء المخزون',
          order: 2,
          subtitle: 'تحليل شامل لأداء وحركات المخزون خلال الفترة المحددة',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات أداء المخزون',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: [
          totalMovements.toDouble(),
          totalValue,
          turnoverRatio,
          avgMovementValue,
        ],
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'أداء المخزون', order: 2),
        metrics: [],
        chart: ChartData(title: 'أداء المخزون', type: ChartType.bar),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeCustomerPerformance(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];

      // الحصول على الفواتير للفترة المحددة
      final invoices = await _invoiceDao.getInvoicesByDateRange(
        fromDate,
        toDate,
      );
      final salesInvoices = invoices
          .where((i) => i.invoiceType == InvoiceType.sales)
          .toList();

      // تحليل العملاء
      final customerSales = <int, double>{};
      final customerCounts = <int, int>{};

      for (final invoice in salesInvoices) {
        if (invoice.customerId != null) {
          customerSales[invoice.customerId!] =
              (customerSales[invoice.customerId!] ?? 0) + invoice.totalAmount;
          customerCounts[invoice.customerId!] =
              (customerCounts[invoice.customerId!] ?? 0) + 1;
        }
      }

      // عدد العملاء النشطين
      final activeCustomers = customerSales.length;
      metrics.add(
        FinancialMetric(
          name: 'عدد العملاء النشطين',
          category: 'أداء العملاء',
          value: activeCustomers.toDouble(),
          unit: 'عميل',
          status: _evaluateActiveCustomers(activeCustomers),
          description: 'عدد العملاء الذين قاموا بعمليات شراء خلال الفترة',
        ),
      );

      // متوسط المبيعات لكل عميل
      final avgSalesPerCustomer = activeCustomers > 0
          ? customerSales.values.reduce((a, b) => a + b) / activeCustomers
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'متوسط المبيعات لكل عميل',
          category: 'أداء العملاء',
          value: avgSalesPerCustomer,
          unit: 'ريال',
          status: _evaluateAverageCustomerSales(avgSalesPerCustomer),
          description: 'متوسط قيمة المبيعات لكل عميل نشط',
        ),
      );

      // أعلى قيمة مبيعات لعميل واحد
      final maxCustomerSales = customerSales.isNotEmpty
          ? customerSales.values.reduce((a, b) => a > b ? a : b)
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'أعلى مبيعات لعميل',
          category: 'أداء العملاء',
          value: maxCustomerSales,
          unit: 'ريال',
          status: _evaluateMaxCustomerSales(maxCustomerSales),
          description: 'أعلى قيمة مبيعات لعميل واحد خلال الفترة',
        ),
      );

      // إضافة نقاط البيانات للرسم البياني
      dataPoints.add(
        DataPoint(label: 'العملاء النشطين', value: activeCustomers.toDouble()),
      );
      dataPoints.add(
        DataPoint(label: 'متوسط المبيعات', value: avgSalesPerCustomer),
      );
      dataPoints.add(DataPoint(label: 'أعلى مبيعات', value: maxCustomerSales));

      return AnalysisData(
        section: ReportSection(
          title: 'أداء العملاء',
          order: 3,
          subtitle: 'تحليل أداء العملاء وأنماط الشراء',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات أداء العملاء',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: [
          activeCustomers.toDouble(),
          avgSalesPerCustomer,
          maxCustomerSales,
        ],
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'أداء العملاء', order: 3),
        metrics: [],
        chart: ChartData(title: 'أداء العملاء', type: ChartType.pie),
        values: [],
      );
    }
  }

  Future<List<Insight>> _generatePerformanceInsights(
    AnalysisData salesPerformance,
    AnalysisData inventoryPerformance,
    AnalysisData customerPerformance,
  ) async {
    final List<Insight> insights = [];

    try {
      // تحليل أداء المبيعات
      await _analyzeSalesPerformanceInsights(salesPerformance, insights);

      // تحليل أداء المخزون
      await _analyzeInventoryPerformanceInsights(
        inventoryPerformance,
        insights,
      );

      // تحليل أداء العملاء
      await _analyzeCustomerPerformanceInsights(customerPerformance, insights);

      // تحليل شامل للأداء العام
      await _analyzeOverallPerformanceInsights(
        salesPerformance,
        inventoryPerformance,
        customerPerformance,
        insights,
      );

      return insights;
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع قائمة فارغة
      return [];
    }
  }

  /// تحليل رؤى أداء المبيعات
  Future<void> _analyzeSalesPerformanceInsights(
    AnalysisData salesPerformance,
    List<Insight> insights,
  ) async {
    // تحليل اتجاه المبيعات
    if (salesPerformance.changePercentage > 20) {
      insights.add(
        Insight(
          title: 'نمو ممتاز في المبيعات',
          description:
              'المبيعات تظهر نمواً قوياً بنسبة ${salesPerformance.changePercentage.toStringAsFixed(1)}%',
          type: InsightType.opportunity,
          priority: InsightPriority.high,
          impact: salesPerformance.changePercentage,
          recommendations: [
            'استمر في الاستراتيجيات الحالية للمبيعات',
            'فكر في زيادة الاستثمار في التسويق',
            'توسع في الأسواق الجديدة',
            'زد من المخزون لتلبية الطلب المتزايد',
          ],
          data: {
            'change_percentage': salesPerformance.changePercentage,
            'category': 'مبيعات',
          },
        ),
      );
    } else if (salesPerformance.changePercentage < -10) {
      insights.add(
        Insight(
          title: 'تراجع في أداء المبيعات',
          description:
              'المبيعات تظهر تراجعاً بنسبة ${salesPerformance.changePercentage.abs().toStringAsFixed(1)}%',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          impact: salesPerformance.changePercentage.abs(),
          recommendations: [
            'راجع استراتيجية التسويق والمبيعات',
            'حلل أسباب تراجع المبيعات',
            'قدم عروض وخصومات جذابة',
            'حسن من خدمة العملاء',
            'ادرس السوق والمنافسين',
          ],
          data: {
            'change_percentage': salesPerformance.changePercentage,
            'category': 'مبيعات',
          },
        ),
      );
    }

    // تحليل مؤشرات المبيعات
    final salesMetrics = salesPerformance.metrics;
    final excellentSalesMetrics = salesMetrics
        .where((m) => m.status == MetricStatus.excellent)
        .length;
    final criticalSalesMetrics = salesMetrics
        .where((m) => m.status == MetricStatus.critical)
        .length;

    if (excellentSalesMetrics > salesMetrics.length * 0.7) {
      insights.add(
        Insight(
          title: 'أداء مبيعات متميز',
          description: 'معظم مؤشرات المبيعات تظهر أداءً ممتازاً',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          recommendations: [
            'حافظ على مستوى الأداء الحالي',
            'شارك أفضل الممارسات مع الفرق الأخرى',
            'استثمر في تدريب فريق المبيعات',
          ],
          data: {
            'excellent_metrics': excellentSalesMetrics,
            'total_metrics': salesMetrics.length,
            'category': 'مبيعات',
          },
        ),
      );
    } else if (criticalSalesMetrics > salesMetrics.length * 0.3) {
      insights.add(
        Insight(
          title: 'تحذير: ضعف في مؤشرات المبيعات',
          description: 'عدد كبير من مؤشرات المبيعات يحتاج إلى تحسين',
          type: InsightType.risk,
          priority: InsightPriority.high,
          recommendations: [
            'راجع عمليات المبيعات',
            'حسن من تدريب فريق المبيعات',
            'طور استراتيجيات مبيعات جديدة',
            'حلل أداء المنتجات والخدمات',
          ],
          data: {
            'critical_metrics': criticalSalesMetrics,
            'total_metrics': salesMetrics.length,
            'category': 'مبيعات',
          },
        ),
      );
    }
  }

  /// تحليل رؤى أداء المخزون
  Future<void> _analyzeInventoryPerformanceInsights(
    AnalysisData inventoryPerformance,
    List<Insight> insights,
  ) async {
    // تحليل اتجاه المخزون
    if (inventoryPerformance.changePercentage > 15) {
      insights.add(
        Insight(
          title: 'زيادة كبيرة في المخزون',
          description:
              'المخزون يظهر زيادة بنسبة ${inventoryPerformance.changePercentage.toStringAsFixed(1)}%',
          type: InsightType.risk,
          priority: InsightPriority.medium,
          impact: inventoryPerformance.changePercentage,
          recommendations: [
            'راجع سياسات الشراء والتخزين',
            'حلل معدل دوران المخزون',
            'فكر في عروض لتصريف المخزون الزائد',
            'حسن من التنبؤ بالطلب',
          ],
          data: {
            'change_percentage': inventoryPerformance.changePercentage,
            'category': 'مخزون',
          },
        ),
      );
    } else if (inventoryPerformance.changePercentage < -20) {
      insights.add(
        Insight(
          title: 'انخفاض حاد في المخزون',
          description:
              'المخزون يظهر انخفاضاً بنسبة ${inventoryPerformance.changePercentage.abs().toStringAsFixed(1)}%',
          type: InsightType.risk,
          priority: InsightPriority.high,
          impact: inventoryPerformance.changePercentage.abs(),
          recommendations: [
            'تأكد من توفر المخزون الكافي',
            'راجع سلسلة التوريد',
            'حسن من عمليات الشراء',
            'تجنب نفاد المخزون',
          ],
          data: {
            'change_percentage': inventoryPerformance.changePercentage,
            'category': 'مخزون',
          },
        ),
      );
    }

    // تحليل مؤشرات المخزون
    final inventoryMetrics = inventoryPerformance.metrics;
    for (final metric in inventoryMetrics) {
      if (metric.name.contains('دوران المخزون') && metric.value < 4) {
        insights.add(
          Insight(
            title: 'بطء في دوران المخزون',
            description:
                'معدل دوران المخزون منخفض: ${metric.value.toStringAsFixed(2)} مرة في السنة',
            type: InsightType.risk,
            priority: InsightPriority.medium,
            recommendations: [
              'حسن من استراتيجيات التسويق',
              'راجع أسعار المنتجات',
              'قدم عروض وخصومات',
              'حلل المنتجات بطيئة الحركة',
            ],
            data: {'turnover_rate': metric.value, 'category': 'مخزون'},
          ),
        );
      }
    }
  }

  /// تحليل رؤى أداء العملاء
  Future<void> _analyzeCustomerPerformanceInsights(
    AnalysisData customerPerformance,
    List<Insight> insights,
  ) async {
    // تحليل اتجاه أداء العملاء
    if (customerPerformance.changePercentage > 25) {
      insights.add(
        Insight(
          title: 'نمو ممتاز في قاعدة العملاء',
          description:
              'أداء العملاء يظهر نمواً قوياً بنسبة ${customerPerformance.changePercentage.toStringAsFixed(1)}%',
          type: InsightType.opportunity,
          priority: InsightPriority.high,
          impact: customerPerformance.changePercentage,
          recommendations: [
            'استثمر في برامج ولاء العملاء',
            'طور خدمات إضافية للعملاء الحاليين',
            'وسع استراتيجيات اكتساب عملاء جدد',
            'حسن من تجربة العملاء',
          ],
          data: {
            'change_percentage': customerPerformance.changePercentage,
            'category': 'عملاء',
          },
        ),
      );
    } else if (customerPerformance.changePercentage < -15) {
      insights.add(
        Insight(
          title: 'تراجع في أداء العملاء',
          description:
              'أداء العملاء يظهر تراجعاً بنسبة ${customerPerformance.changePercentage.abs().toStringAsFixed(1)}%',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          impact: customerPerformance.changePercentage.abs(),
          recommendations: [
            'حلل أسباب فقدان العملاء',
            'حسن من خدمة العملاء',
            'راجع استراتيجية التسعير',
            'طور برامج استرداد العملاء',
            'ادرس رضا العملاء',
          ],
          data: {
            'change_percentage': customerPerformance.changePercentage,
            'category': 'عملاء',
          },
        ),
      );
    }

    // تحليل مؤشرات العملاء
    final customerMetrics = customerPerformance.metrics;
    for (final metric in customerMetrics) {
      if (metric.name.contains('متوسط قيمة العميل') && metric.value > 1000) {
        insights.add(
          Insight(
            title: 'قيمة عالية للعملاء',
            description:
                'متوسط قيمة العميل مرتفعة: ${metric.value.toStringAsFixed(0)} ${metric.unit}',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            recommendations: [
              'ركز على الاحتفاظ بالعملاء عالي القيمة',
              'طور منتجات وخدمات متميزة',
              'استثمر في التسويق المخصص',
            ],
            data: {'customer_value': metric.value, 'category': 'عملاء'},
          ),
        );
      }

      if (metric.name.contains('معدل الاحتفاظ') && metric.value < 70) {
        insights.add(
          Insight(
            title: 'انخفاض في معدل الاحتفاظ بالعملاء',
            description:
                'معدل الاحتفاظ بالعملاء منخفض: ${metric.value.toStringAsFixed(1)}%',
            type: InsightType.risk,
            priority: InsightPriority.high,
            recommendations: [
              'طور برامج ولاء العملاء',
              'حسن من خدمة ما بعد البيع',
              'راجع جودة المنتجات والخدمات',
              'تواصل بانتظام مع العملاء',
            ],
            data: {'retention_rate': metric.value, 'category': 'عملاء'},
          ),
        );
      }
    }
  }

  /// تحليل شامل للأداء العام
  Future<void> _analyzeOverallPerformanceInsights(
    AnalysisData salesPerformance,
    AnalysisData inventoryPerformance,
    AnalysisData customerPerformance,
    List<Insight> insights,
  ) async {
    // حساب متوسط الأداء العام
    final averagePerformance =
        (salesPerformance.changePercentage +
            inventoryPerformance.changePercentage +
            customerPerformance.changePercentage) /
        3;

    // تحليل الأداء العام
    if (averagePerformance > 15) {
      insights.add(
        Insight(
          title: 'أداء عام ممتاز',
          description: 'جميع مجالات الأداء تظهر نمواً إيجابياً قوياً',
          type: InsightType.opportunity,
          priority: InsightPriority.high,
          impact: averagePerformance,
          recommendations: [
            'استمر في الاستراتيجيات الحالية',
            'فكر في التوسع والاستثمار',
            'طور خطط نمو طموحة',
            'استثمر في التكنولوجيا والابتكار',
          ],
          data: {
            'average_performance': averagePerformance,
            'sales_performance': salesPerformance.changePercentage,
            'inventory_performance': inventoryPerformance.changePercentage,
            'customer_performance': customerPerformance.changePercentage,
            'category': 'أداء عام',
          },
        ),
      );
    } else if (averagePerformance < -10) {
      insights.add(
        Insight(
          title: 'تحذير: تراجع في الأداء العام',
          description: 'معظم مجالات الأداء تظهر تراجعاً يحتاج إلى اهتمام',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          impact: averagePerformance.abs(),
          recommendations: [
            'راجع الاستراتيجية العامة للشركة',
            'حلل العوامل الخارجية المؤثرة',
            'طور خطة تعافي شاملة',
            'ركز على تحسين الكفاءة التشغيلية',
          ],
          data: {
            'average_performance': averagePerformance,
            'sales_performance': salesPerformance.changePercentage,
            'inventory_performance': inventoryPerformance.changePercentage,
            'customer_performance': customerPerformance.changePercentage,
            'category': 'أداء عام',
          },
        ),
      );
    }

    // تحليل التوازن بين المجالات
    final performanceVariance = _calculateVariance([
      salesPerformance.changePercentage,
      inventoryPerformance.changePercentage,
      customerPerformance.changePercentage,
    ]);

    if (performanceVariance > 400) {
      // تباين عالي
      insights.add(
        Insight(
          title: 'عدم توازن في الأداء',
          description: 'هناك تفاوت كبير في الأداء بين المجالات المختلفة',
          type: InsightType.recommendation,
          priority: InsightPriority.medium,
          recommendations: [
            'ركز على المجالات ضعيفة الأداء',
            'انقل أفضل الممارسات بين الأقسام',
            'وازن الاستثمارات بين المجالات',
            'طور استراتيجية متكاملة',
          ],
          data: {
            'performance_variance': performanceVariance,
            'category': 'أداء عام',
          },
        ),
      );
    }

    // تحليل الاتجاهات المتسقة
    final positiveAreas = [
      if (salesPerformance.changePercentage > 0) 'المبيعات',
      if (inventoryPerformance.changePercentage > 0) 'المخزون',
      if (customerPerformance.changePercentage > 0) 'العملاء',
    ];

    if (positiveAreas.length >= 2) {
      insights.add(
        Insight(
          title: 'اتجاهات إيجابية متعددة',
          description:
              'عدة مجالات تظهر أداءً إيجابياً: ${positiveAreas.join(', ')}',
          type: InsightType.trend,
          priority: InsightPriority.medium,
          recommendations: [
            'استغل الزخم الإيجابي',
            'ادعم المجالات الناجحة',
            'تعلم من النجاحات المحققة',
          ],
          data: {'positive_areas': positiveAreas, 'category': 'أداء عام'},
        ),
      );
    }
  }

  /// حساب التباين للقائمة
  double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0;

    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDifferences = values.map(
      (value) => (value - mean) * (value - mean),
    );
    return squaredDifferences.reduce((a, b) => a + b) / values.length;
  }

  // طرق تحليل السيولة
  Future<AnalysisData> _analyzeShortTermLiquidity(
    basic_reports.FinancialReport balanceSheet,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // استخراج البيانات من الميزانية العمومية
      double currentAssets = 0.0;
      double currentLiabilities = 0.0;
      double cash = 0.0;
      double shortTermInvestments = 0.0;
      double accountsReceivable = 0.0;
      double inventory = 0.0;
      double prepaidExpenses = 0.0;

      for (final section in balanceSheet.sections) {
        for (final line in section.lines) {
          if (section.title.contains('الأصول')) {
            // الأصول المتداولة
            if (line.accountName.contains('نقدية') ||
                line.accountName.contains('صندوق') ||
                line.accountName.contains('بنك')) {
              cash += line.amount;
              currentAssets += line.amount;
            } else if (line.accountName.contains('استثمارات قصيرة') ||
                line.accountName.contains('أوراق مالية')) {
              shortTermInvestments += line.amount;
              currentAssets += line.amount;
            } else if (line.accountName.contains('ذمم مدينة') ||
                line.accountName.contains('عملاء') ||
                line.accountName.contains('مدينون')) {
              accountsReceivable += line.amount;
              currentAssets += line.amount;
            } else if (line.accountName.contains('مخزون') ||
                line.accountName.contains('بضاعة')) {
              inventory += line.amount;
              currentAssets += line.amount;
            } else if (line.accountName.contains('مصروفات مدفوعة مقدماً') ||
                line.accountName.contains('مقدم')) {
              prepaidExpenses += line.amount;
              currentAssets += line.amount;
            }
          } else if (section.title.contains('الخصوم') ||
              section.title.contains('الالتزامات')) {
            // الخصوم المتداولة
            if (line.accountName.contains('قصيرة الأجل') ||
                line.accountName.contains('دائنون') ||
                line.accountName.contains('موردون') ||
                line.accountName.contains('مستحقات') ||
                line.accountName.contains('أوراق دفع') ||
                (!line.accountName.contains('طويلة الأجل') &&
                    !line.accountName.contains('رأس المال'))) {
              currentLiabilities += line.amount;
            }
          }
        }
      }

      // 1. نسبة السيولة الجارية (Current Ratio)
      final currentRatio = currentLiabilities > 0
          ? currentAssets / currentLiabilities
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة السيولة الجارية',
          category: 'نسب السيولة قصيرة المدى',
          value: currentRatio,
          unit: 'مرة',
          status: _evaluateCurrentRatio(currentRatio),
          description: 'قدرة الشركة على سداد التزاماتها قصيرة الأجل',
          formula: 'الأصول المتداولة ÷ الخصوم المتداولة',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'نسبة السيولة الجارية', value: currentRatio),
      );
      values.add(currentRatio);

      // 2. نسبة السيولة السريعة (Quick Ratio/Acid Test)
      final quickAssets = cash + shortTermInvestments + accountsReceivable;
      final quickRatio = currentLiabilities > 0
          ? quickAssets / currentLiabilities
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة السيولة السريعة',
          category: 'نسب السيولة قصيرة المدى',
          value: quickRatio,
          unit: 'مرة',
          status: _evaluateQuickRatio(quickRatio),
          description:
              'قدرة الشركة على سداد التزاماتها دون الاعتماد على المخزون',
          formula:
              '(النقدية + الاستثمارات قصيرة الأجل + الذمم المدينة) ÷ الخصوم المتداولة',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'نسبة السيولة السريعة', value: quickRatio),
      );
      values.add(quickRatio);

      // 3. نسبة السيولة النقدية (Cash Ratio)
      final cashRatio = currentLiabilities > 0
          ? (cash + shortTermInvestments) / currentLiabilities
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة السيولة النقدية',
          category: 'نسب السيولة قصيرة المدى',
          value: cashRatio,
          unit: 'مرة',
          status: _evaluateCashRatio(cashRatio),
          description:
              'قدرة الشركة على سداد التزاماتها بالنقد والاستثمارات السائلة فقط',
          formula: '(النقدية + الاستثمارات قصيرة الأجل) ÷ الخصوم المتداولة',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'نسبة السيولة النقدية', value: cashRatio),
      );
      values.add(cashRatio);

      // 4. رأس المال العامل (Working Capital)
      final workingCapital = currentAssets - currentLiabilities;
      metrics.add(
        FinancialMetric(
          name: 'رأس المال العامل',
          category: 'نسب السيولة قصيرة المدى',
          value: workingCapital,
          unit: 'ريال',
          status: _evaluateWorkingCapital(workingCapital),
          description: 'الفائض في الأصول المتداولة بعد تغطية الخصوم المتداولة',
          formula: 'الأصول المتداولة - الخصوم المتداولة',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'رأس المال العامل', value: workingCapital),
      );
      values.add(workingCapital);

      // 5. نسبة النقدية إلى إجمالي الأصول المتداولة
      final cashToCurrentAssets = currentAssets > 0
          ? (cash / currentAssets) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة النقدية إلى الأصول المتداولة',
          category: 'نسب السيولة قصيرة المدى',
          value: cashToCurrentAssets,
          unit: '%',
          status: _evaluateCashToCurrentAssets(cashToCurrentAssets),
          description: 'نسبة النقدية من إجمالي الأصول المتداولة',
          formula: '(النقدية ÷ الأصول المتداولة) × 100',
        ),
      );
      dataPoints.add(
        DataPoint(
          label: 'نسبة النقدية للأصول المتداولة',
          value: cashToCurrentAssets,
        ),
      );
      values.add(cashToCurrentAssets);

      return AnalysisData(
        section: ReportSection(
          title: 'السيولة قصيرة المدى',
          order: 1,
          subtitle: 'تحليل قدرة الشركة على الوفاء بالتزاماتها قصيرة الأجل',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات السيولة قصيرة المدى',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'السيولة قصيرة المدى', order: 1),
        metrics: [],
        chart: ChartData(
          title: 'السيولة قصيرة المدى',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeLongTermLiquidity(
    basic_reports.FinancialReport balanceSheet,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // استخراج البيانات من الميزانية العمومية
      double totalAssets = 0.0;
      double currentAssets = 0.0;
      double nonCurrentAssets = 0.0;
      double totalLiabilities = 0.0;
      double currentLiabilities = 0.0;
      double longTermLiabilities = 0.0;
      double totalEquity = 0.0;
      double cash = 0.0;
      double inventory = 0.0;

      for (final section in balanceSheet.sections) {
        for (final line in section.lines) {
          final accountName = line.accountName.toLowerCase();
          final amount = line.amount.abs();

          // تصنيف الأصول
          if (accountName.contains('أصول') || accountName.contains('assets')) {
            totalAssets += amount;
            if (accountName.contains('متداول') ||
                accountName.contains('current')) {
              currentAssets += amount;
            } else if (accountName.contains('ثابت') ||
                accountName.contains('fixed') ||
                accountName.contains('طويل') ||
                accountName.contains('long')) {
              nonCurrentAssets += amount;
            }
          }

          // تصنيف الخصوم
          if (accountName.contains('خصوم') ||
              accountName.contains('liabilities')) {
            totalLiabilities += amount;
            if (accountName.contains('متداول') ||
                accountName.contains('current')) {
              currentLiabilities += amount;
            } else if (accountName.contains('طويل') ||
                accountName.contains('long')) {
              longTermLiabilities += amount;
            }
          }

          // حقوق الملكية
          if (accountName.contains('حقوق') ||
              accountName.contains('equity') ||
              accountName.contains('رأس المال') ||
              accountName.contains('capital')) {
            totalEquity += amount;
          }

          // النقدية
          if (accountName.contains('نقد') ||
              accountName.contains('cash') ||
              accountName.contains('بنك') ||
              accountName.contains('bank')) {
            cash += amount;
          }

          // المخزون
          if (accountName.contains('مخزون') ||
              accountName.contains('inventory') ||
              accountName.contains('بضاعة') ||
              accountName.contains('stock')) {
            inventory += amount;
          }
        }
      }

      // 1. نسبة الأصول الثابتة إلى حقوق الملكية (Fixed Assets to Equity Ratio)
      final fixedAssetsToEquity = totalEquity > 0
          ? (nonCurrentAssets / totalEquity) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة الأصول الثابتة إلى حقوق الملكية',
          category: 'نسب السيولة طويلة المدى',
          value: fixedAssetsToEquity,
          unit: '%',
          status: _evaluateFixedAssetsToEquity(fixedAssetsToEquity),
          description: 'نسبة الأصول الثابتة المموّلة من حقوق الملكية',
          formula: '(الأصول الثابتة ÷ حقوق الملكية) × 100',
        ),
      );
      dataPoints.add(
        DataPoint(
          label: 'الأصول الثابتة/حقوق الملكية',
          value: fixedAssetsToEquity,
        ),
      );
      values.add(fixedAssetsToEquity);

      // 2. نسبة رأس المال العامل إلى إجمالي الأصول (Working Capital to Total Assets)
      final workingCapital = currentAssets - currentLiabilities;
      final workingCapitalToAssets = totalAssets > 0
          ? (workingCapital / totalAssets) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة رأس المال العامل إلى الأصول',
          category: 'نسب السيولة طويلة المدى',
          value: workingCapitalToAssets,
          unit: '%',
          status: _evaluateWorkingCapitalToAssets(workingCapitalToAssets),
          description: 'نسبة رأس المال العامل من إجمالي الأصول',
          formula:
              '((الأصول المتداولة - الخصوم المتداولة) ÷ إجمالي الأصول) × 100',
        ),
      );
      dataPoints.add(
        DataPoint(
          label: 'رأس المال العامل/الأصول',
          value: workingCapitalToAssets,
        ),
      );
      values.add(workingCapitalToAssets);

      // 3. نسبة الديون طويلة الأجل إلى إجمالي الأصول (Long-term Debt to Assets)
      final longTermDebtToAssets = totalAssets > 0
          ? (longTermLiabilities / totalAssets) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة الديون طويلة الأجل إلى الأصول',
          category: 'نسب السيولة طويلة المدى',
          value: longTermDebtToAssets,
          unit: '%',
          status: _evaluateLongTermDebtToAssets(longTermDebtToAssets),
          description: 'نسبة الديون طويلة الأجل من إجمالي الأصول',
          formula: '(الديون طويلة الأجل ÷ إجمالي الأصول) × 100',
        ),
      );
      dataPoints.add(
        DataPoint(
          label: 'الديون طويلة الأجل/الأصول',
          value: longTermDebtToAssets,
        ),
      );
      values.add(longTermDebtToAssets);

      // 4. نسبة الأصول المتداولة إلى إجمالي الأصول (Current Assets to Total Assets)
      final currentAssetsToTotal = totalAssets > 0
          ? (currentAssets / totalAssets) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة الأصول المتداولة إلى إجمالي الأصول',
          category: 'نسب السيولة طويلة المدى',
          value: currentAssetsToTotal,
          unit: '%',
          status: _evaluateCurrentAssetsToTotal(currentAssetsToTotal),
          description: 'نسبة الأصول المتداولة من إجمالي الأصول',
          formula: '(الأصول المتداولة ÷ إجمالي الأصول) × 100',
        ),
      );
      dataPoints.add(
        DataPoint(
          label: 'الأصول المتداولة/إجمالي الأصول',
          value: currentAssetsToTotal,
        ),
      );
      values.add(currentAssetsToTotal);

      return AnalysisData(
        section: ReportSection(
          title: 'السيولة طويلة المدى',
          order: 2,
          subtitle:
              'تحليل قدرة الشركة على الوفاء بالتزاماتها طويلة الأجل وإدارة هيكل رأس المال',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات السيولة طويلة المدى',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'السيولة طويلة المدى', order: 2),
        metrics: [],
        chart: ChartData(
          title: 'السيولة طويلة المدى',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeCashFlow(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];

      // الحصول على إجمالي المبيعات والمشتريات
      final totalSales = await _invoiceDao.getTotalSales(
        startDate: fromDate,
        endDate: toDate,
      );
      final totalPurchases = await _invoiceDao.getTotalPurchases(
        startDate: fromDate,
        endDate: toDate,
      );

      // الحصول على المبالغ المستحقة
      final totalOutstanding = await _invoiceDao.getTotalOutstanding();

      // حساب صافي التدفق النقدي
      final netCashFlow = totalSales - totalPurchases;
      metrics.add(
        FinancialMetric(
          name: 'صافي التدفق النقدي',
          category: 'التدفق النقدي',
          value: netCashFlow,
          unit: 'ريال',
          status: _evaluateNetCashFlow(netCashFlow),
          description: 'الفرق بين إجمالي المبيعات والمشتريات',
        ),
      );

      // إجمالي المبيعات
      metrics.add(
        FinancialMetric(
          name: 'إجمالي المبيعات',
          category: 'التدفق النقدي',
          value: totalSales,
          unit: 'ريال',
          status: _evaluateSalesAmount(totalSales),
          description: 'إجمالي قيمة المبيعات للفترة',
        ),
      );

      // إجمالي المشتريات
      metrics.add(
        FinancialMetric(
          name: 'إجمالي المشتريات',
          category: 'التدفق النقدي',
          value: totalPurchases,
          unit: 'ريال',
          status: _evaluatePurchaseAmount(totalPurchases),
          description: 'إجمالي قيمة المشتريات للفترة',
        ),
      );

      // المبالغ المستحقة
      metrics.add(
        FinancialMetric(
          name: 'المبالغ المستحقة',
          category: 'التدفق النقدي',
          value: totalOutstanding,
          unit: 'ريال',
          status: _evaluateOutstandingAmount(totalOutstanding),
          description: 'إجمالي المبالغ المستحقة من العملاء',
        ),
      );

      // إضافة نقاط البيانات للرسم البياني
      dataPoints.add(DataPoint(label: 'المبيعات', value: totalSales));
      dataPoints.add(DataPoint(label: 'المشتريات', value: totalPurchases));
      dataPoints.add(DataPoint(label: 'صافي التدفق', value: netCashFlow));
      dataPoints.add(DataPoint(label: 'المستحقات', value: totalOutstanding));

      return AnalysisData(
        section: ReportSection(
          title: 'التدفق النقدي',
          order: 3,
          subtitle: 'تحليل التدفقات النقدية الداخلة والخارجة',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات التدفق النقدي',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: [totalSales, totalPurchases, netCashFlow, totalOutstanding],
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'التدفق النقدي', order: 3),
        metrics: [],
        chart: ChartData(title: 'التدفق النقدي', type: ChartType.line),
        values: [],
      );
    }
  }

  Future<List<Insight>> _generateLiquidityInsights(
    AnalysisData shortTermLiquidity,
    AnalysisData longTermLiquidity,
    AnalysisData cashFlowAnalysis,
  ) async {
    final List<Insight> insights = [];

    try {
      // تحليل السيولة قصيرة المدى
      await _analyzeShortTermLiquidityInsights(shortTermLiquidity, insights);

      // تحليل السيولة طويلة المدى
      await _analyzeLongTermLiquidityInsights(longTermLiquidity, insights);

      // تحليل التدفق النقدي
      await _analyzeCashFlowInsights(cashFlowAnalysis, insights);

      // تحليل شامل للسيولة العامة
      await _analyzeOverallLiquidityInsights(
        shortTermLiquidity,
        longTermLiquidity,
        cashFlowAnalysis,
        insights,
      );

      return insights;
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع قائمة فارغة
      return [];
    }
  }

  /// تحليل رؤى السيولة قصيرة المدى
  Future<void> _analyzeShortTermLiquidityInsights(
    AnalysisData shortTermLiquidity,
    List<Insight> insights,
  ) async {
    final metrics = shortTermLiquidity.metrics;

    // تحليل نسبة السيولة الجارية
    final currentRatioMetric = metrics.firstWhere(
      (m) => m.name == 'نسبة السيولة الجارية',
      orElse: () => FinancialMetric(
        name: 'نسبة السيولة الجارية',
        category: 'نسب السيولة قصيرة المدى',
        value: 0.0,
        unit: 'مرة',
        status: MetricStatus.critical,
      ),
    );

    if (currentRatioMetric.status == MetricStatus.excellent) {
      insights.add(
        Insight(
          title: 'سيولة جارية ممتازة',
          description:
              'نسبة السيولة الجارية ${currentRatioMetric.value.toStringAsFixed(2)} تظهر قدرة ممتازة على سداد الالتزامات قصيرة الأجل',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          impact: currentRatioMetric.value,
          recommendations: [
            'استمر في الحفاظ على مستوى السيولة الحالي',
            'فكر في استثمار الفائض النقدي',
            'راجع إمكانية التوسع في الأعمال',
          ],
          data: {
            'current_ratio': currentRatioMetric.value,
            'category': 'سيولة قصيرة المدى',
          },
        ),
      );
    } else if (currentRatioMetric.status == MetricStatus.critical) {
      insights.add(
        Insight(
          title: 'تحذير: ضعف في السيولة الجارية',
          description:
              'نسبة السيولة الجارية ${currentRatioMetric.value.toStringAsFixed(2)} تشير إلى صعوبة في سداد الالتزامات قصيرة الأجل',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          impact: currentRatioMetric.value,
          recommendations: [
            'زد من الأصول المتداولة (النقدية، المخزون)',
            'قلل من الخصوم المتداولة',
            'راجع شروط الدفع مع الموردين',
            'حسن من عملية تحصيل الذمم المدينة',
          ],
          data: {
            'current_ratio': currentRatioMetric.value,
            'category': 'سيولة قصيرة المدى',
          },
        ),
      );
    }

    // تحليل نسبة السيولة السريعة
    final quickRatioMetric = metrics.firstWhere(
      (m) => m.name == 'نسبة السيولة السريعة',
      orElse: () => FinancialMetric(
        name: 'نسبة السيولة السريعة',
        category: 'نسب السيولة قصيرة المدى',
        value: 0.0,
        unit: 'مرة',
        status: MetricStatus.critical,
      ),
    );

    if (quickRatioMetric.value >= 1.0 &&
        quickRatioMetric.status == MetricStatus.excellent) {
      insights.add(
        Insight(
          title: 'سيولة سريعة قوية',
          description:
              'نسبة السيولة السريعة ${quickRatioMetric.value.toStringAsFixed(2)} تظهر قدرة قوية على السداد دون الاعتماد على المخزون',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          recommendations: [
            'استمر في إدارة النقدية بكفاءة',
            'حافظ على مستوى الذمم المدينة',
            'راجع سياسات الائتمان',
          ],
          data: {
            'quick_ratio': quickRatioMetric.value,
            'category': 'سيولة قصيرة المدى',
          },
        ),
      );
    } else if (quickRatioMetric.value < 0.5) {
      insights.add(
        Insight(
          title: 'تحذير: ضعف في السيولة السريعة',
          description:
              'نسبة السيولة السريعة ${quickRatioMetric.value.toStringAsFixed(2)} منخفضة جداً، مما يشير إلى اعتماد كبير على المخزون',
          type: InsightType.risk,
          priority: InsightPriority.high,
          recommendations: [
            'زد من النقدية المتاحة',
            'حسن من عملية تحصيل الذمم',
            'قلل من الاعتماد على المخزون',
            'راجع سياسات إدارة النقدية',
          ],
          data: {
            'quick_ratio': quickRatioMetric.value,
            'category': 'سيولة قصيرة المدى',
          },
        ),
      );
    }

    // تحليل رأس المال العامل
    final workingCapitalMetric = metrics.firstWhere(
      (m) => m.name == 'رأس المال العامل',
      orElse: () => FinancialMetric(
        name: 'رأس المال العامل',
        category: 'نسب السيولة قصيرة المدى',
        value: 0.0,
        unit: 'ريال',
        status: MetricStatus.critical,
      ),
    );

    if (workingCapitalMetric.value > 0 &&
        workingCapitalMetric.status == MetricStatus.excellent) {
      insights.add(
        Insight(
          title: 'رأس مال عامل إيجابي',
          description:
              'رأس المال العامل الإيجابي ${workingCapitalMetric.value.toStringAsFixed(0)} ريال يشير إلى صحة مالية جيدة',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          recommendations: [
            'استثمر الفائض في فرص نمو',
            'حافظ على التوازن بين السيولة والربحية',
            'راجع إمكانية تحسين دورة التشغيل',
          ],
          data: {
            'working_capital': workingCapitalMetric.value,
            'category': 'سيولة قصيرة المدى',
          },
        ),
      );
    } else if (workingCapitalMetric.value < 0) {
      insights.add(
        Insight(
          title: 'تحذير: رأس مال عامل سالب',
          description:
              'رأس المال العامل السالب ${workingCapitalMetric.value.toStringAsFixed(0)} ريال يشير إلى مشاكل في السيولة',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          recommendations: [
            'زد من الأصول المتداولة فوراً',
            'أعد جدولة الديون قصيرة الأجل',
            'حسن من عملية التحصيل',
            'راجع استراتيجية التمويل',
          ],
          data: {
            'working_capital': workingCapitalMetric.value,
            'category': 'سيولة قصيرة المدى',
          },
        ),
      );
    }
  }

  /// تحليل رؤى السيولة طويلة المدى
  Future<void> _analyzeLongTermLiquidityInsights(
    AnalysisData longTermLiquidity,
    List<Insight> insights,
  ) async {
    final metrics = longTermLiquidity.metrics;

    // تحليل نسبة الأصول الثابتة إلى حقوق الملكية
    final fixedAssetsToEquityMetric = metrics.firstWhere(
      (m) => m.name == 'نسبة الأصول الثابتة إلى حقوق الملكية',
      orElse: () => FinancialMetric(
        name: 'نسبة الأصول الثابتة إلى حقوق الملكية',
        category: 'نسب السيولة طويلة المدى',
        value: 0.0,
        unit: '%',
        status: MetricStatus.critical,
      ),
    );

    if (fixedAssetsToEquityMetric.value <= 80 &&
        fixedAssetsToEquityMetric.status == MetricStatus.excellent) {
      insights.add(
        Insight(
          title: 'توازن جيد في هيكل الأصول',
          description:
              'نسبة الأصول الثابتة إلى حقوق الملكية ${fixedAssetsToEquityMetric.value.toStringAsFixed(1)}% تظهر توازناً صحياً',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          recommendations: [
            'حافظ على التوازن الحالي في هيكل الأصول',
            'راجع إمكانية الاستثمار في أصول إضافية',
            'فكر في تحسين كفاءة استخدام الأصول',
          ],
          data: {
            'fixed_assets_to_equity': fixedAssetsToEquityMetric.value,
            'category': 'سيولة طويلة المدى',
          },
        ),
      );
    } else if (fixedAssetsToEquityMetric.value > 100) {
      insights.add(
        Insight(
          title: 'تحذير: إفراط في الاستثمار بالأصول الثابتة',
          description:
              'نسبة الأصول الثابتة إلى حقوق الملكية ${fixedAssetsToEquityMetric.value.toStringAsFixed(1)}% مرتفعة جداً',
          type: InsightType.risk,
          priority: InsightPriority.high,
          recommendations: [
            'راجع استراتيجية الاستثمار في الأصول الثابتة',
            'فكر في بيع بعض الأصول غير المنتجة',
            'زد من حقوق الملكية',
            'حسن من عائد الأصول الثابتة',
          ],
          data: {
            'fixed_assets_to_equity': fixedAssetsToEquityMetric.value,
            'category': 'سيولة طويلة المدى',
          },
        ),
      );
    }

    // تحليل نسبة رأس المال العامل إلى الأصول
    final workingCapitalToAssetsMetric = metrics.firstWhere(
      (m) => m.name == 'نسبة رأس المال العامل إلى الأصول',
      orElse: () => FinancialMetric(
        name: 'نسبة رأس المال العامل إلى الأصول',
        category: 'نسب السيولة طويلة المدى',
        value: 0.0,
        unit: '%',
        status: MetricStatus.critical,
      ),
    );

    if (workingCapitalToAssetsMetric.value >= 10 &&
        workingCapitalToAssetsMetric.status == MetricStatus.excellent) {
      insights.add(
        Insight(
          title: 'كفاءة جيدة في إدارة رأس المال العامل',
          description:
              'نسبة رأس المال العامل إلى الأصول ${workingCapitalToAssetsMetric.value.toStringAsFixed(1)}% تظهر إدارة فعالة',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          recommendations: [
            'استمر في الإدارة الفعالة لرأس المال العامل',
            'راجع إمكانية تحسين دورة التشغيل',
            'فكر في استثمار الفائض',
          ],
          data: {
            'working_capital_to_assets': workingCapitalToAssetsMetric.value,
            'category': 'سيولة طويلة المدى',
          },
        ),
      );
    } else if (workingCapitalToAssetsMetric.value < 0) {
      insights.add(
        Insight(
          title: 'تحذير: رأس مال عامل سالب نسبة للأصول',
          description:
              'نسبة رأس المال العامل إلى الأصول ${workingCapitalToAssetsMetric.value.toStringAsFixed(1)}% سالبة',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          recommendations: [
            'زد من الأصول المتداولة فوراً',
            'قلل من الخصوم المتداولة',
            'راجع هيكل التمويل',
            'حسن من إدارة السيولة',
          ],
          data: {
            'working_capital_to_assets': workingCapitalToAssetsMetric.value,
            'category': 'سيولة طويلة المدى',
          },
        ),
      );
    }

    // تحليل نسبة الديون طويلة الأجل
    final longTermDebtMetric = metrics.firstWhere(
      (m) => m.name.contains('الديون طويلة الأجل'),
      orElse: () => FinancialMetric(
        name: 'نسبة الديون طويلة الأجل',
        category: 'نسب السيولة طويلة المدى',
        value: 0.0,
        unit: '%',
        status: MetricStatus.good,
      ),
    );

    if (longTermDebtMetric.value <= 30 &&
        longTermDebtMetric.status == MetricStatus.excellent) {
      insights.add(
        Insight(
          title: 'مستوى ديون طويلة الأجل صحي',
          description:
              'نسبة الديون طويلة الأجل ${longTermDebtMetric.value.toStringAsFixed(1)}% في المستوى الآمن',
          type: InsightType.opportunity,
          priority: InsightPriority.low,
          recommendations: [
            'حافظ على مستوى الديون الحالي',
            'راجع إمكانية الاستفادة من التمويل الإضافي للنمو',
            'فكر في تحسين شروط الديون الحالية',
          ],
          data: {
            'long_term_debt_ratio': longTermDebtMetric.value,
            'category': 'سيولة طويلة المدى',
          },
        ),
      );
    } else if (longTermDebtMetric.value > 60) {
      insights.add(
        Insight(
          title: 'تحذير: ارتفاع في الديون طويلة الأجل',
          description:
              'نسبة الديون طويلة الأجل ${longTermDebtMetric.value.toStringAsFixed(1)}% مرتفعة ومقلقة',
          type: InsightType.risk,
          priority: InsightPriority.high,
          recommendations: [
            'ضع خطة لتقليل الديون طويلة الأجل',
            'زد من حقوق الملكية',
            'حسن من الربحية لسداد الديون',
            'راجع شروط الديون وإمكانية إعادة التفاوض',
          ],
          data: {
            'long_term_debt_ratio': longTermDebtMetric.value,
            'category': 'سيولة طويلة المدى',
          },
        ),
      );
    }
  }

  /// تحليل رؤى التدفق النقدي
  Future<void> _analyzeCashFlowInsights(
    AnalysisData cashFlowAnalysis,
    List<Insight> insights,
  ) async {
    final metrics = cashFlowAnalysis.metrics;

    // تحليل صافي التدفق النقدي
    final netCashFlowMetric = metrics.firstWhere(
      (m) => m.name == 'صافي التدفق النقدي',
      orElse: () => FinancialMetric(
        name: 'صافي التدفق النقدي',
        category: 'التدفق النقدي',
        value: 0.0,
        unit: 'ريال',
        status: MetricStatus.critical,
      ),
    );

    if (netCashFlowMetric.value > 0 &&
        netCashFlowMetric.status == MetricStatus.excellent) {
      insights.add(
        Insight(
          title: 'تدفق نقدي إيجابي قوي',
          description:
              'صافي التدفق النقدي الإيجابي ${netCashFlowMetric.value.toStringAsFixed(0)} ريال يشير إلى صحة مالية جيدة',
          type: InsightType.opportunity,
          priority: InsightPriority.high,
          impact: netCashFlowMetric.value,
          recommendations: [
            'استثمر الفائض النقدي في فرص نمو',
            'فكر في توسيع الأعمال',
            'حافظ على مستوى السيولة الحالي',
            'راجع إمكانية سداد الديون مبكراً',
          ],
          data: {
            'net_cash_flow': netCashFlowMetric.value,
            'category': 'تدفق نقدي',
          },
        ),
      );
    } else if (netCashFlowMetric.value < 0) {
      insights.add(
        Insight(
          title: 'تحذير: تدفق نقدي سالب',
          description:
              'صافي التدفق النقدي السالب ${netCashFlowMetric.value.toStringAsFixed(0)} ريال يتطلب اهتماماً فورياً',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          impact: netCashFlowMetric.value,
          recommendations: [
            'زد من المبيعات فوراً',
            'قلل من المشتريات غير الضرورية',
            'حسن من عملية تحصيل الذمم',
            'راجع شروط الدفع مع الموردين',
            'فكر في مصادر تمويل إضافية',
          ],
          data: {
            'net_cash_flow': netCashFlowMetric.value,
            'category': 'تدفق نقدي',
          },
        ),
      );
    }

    // تحليل إجمالي المبيعات
    final totalSalesMetric = metrics.firstWhere(
      (m) => m.name == 'إجمالي المبيعات',
      orElse: () => FinancialMetric(
        name: 'إجمالي المبيعات',
        category: 'التدفق النقدي',
        value: 0.0,
        unit: 'ريال',
        status: MetricStatus.critical,
      ),
    );

    // تحليل إجمالي المشتريات
    final totalPurchasesMetric = metrics.firstWhere(
      (m) => m.name == 'إجمالي المشتريات',
      orElse: () => FinancialMetric(
        name: 'إجمالي المشتريات',
        category: 'التدفق النقدي',
        value: 0.0,
        unit: 'ريال',
        status: MetricStatus.critical,
      ),
    );

    // تحليل نسبة المبيعات إلى المشتريات
    if (totalSalesMetric.value > 0 && totalPurchasesMetric.value > 0) {
      final salesToPurchasesRatio =
          totalSalesMetric.value / totalPurchasesMetric.value;

      if (salesToPurchasesRatio >= 1.5) {
        insights.add(
          Insight(
            title: 'نسبة مبيعات إلى مشتريات ممتازة',
            description:
                'نسبة المبيعات إلى المشتريات ${salesToPurchasesRatio.toStringAsFixed(2)} تظهر كفاءة تشغيلية عالية',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            impact: salesToPurchasesRatio,
            recommendations: [
              'استمر في الاستراتيجيات الحالية',
              'فكر في زيادة حجم المبيعات',
              'راجع إمكانية تحسين هوامش الربح',
            ],
            data: {
              'sales_to_purchases_ratio': salesToPurchasesRatio,
              'total_sales': totalSalesMetric.value,
              'total_purchases': totalPurchasesMetric.value,
              'category': 'تدفق نقدي',
            },
          ),
        );
      } else if (salesToPurchasesRatio < 1.1) {
        insights.add(
          Insight(
            title: 'تحذير: نسبة مبيعات إلى مشتريات منخفضة',
            description:
                'نسبة المبيعات إلى المشتريات ${salesToPurchasesRatio.toStringAsFixed(2)} منخفضة ومقلقة',
            type: InsightType.risk,
            priority: InsightPriority.high,
            recommendations: [
              'زد من المبيعات',
              'قلل من المشتريات',
              'حسن من استراتيجية التسعير',
              'راجع كفاءة إدارة المخزون',
            ],
            data: {
              'sales_to_purchases_ratio': salesToPurchasesRatio,
              'total_sales': totalSalesMetric.value,
              'total_purchases': totalPurchasesMetric.value,
              'category': 'تدفق نقدي',
            },
          ),
        );
      }
    }

    // تحليل المبالغ المستحقة
    final outstandingMetric = metrics.firstWhere(
      (m) => m.name == 'المبالغ المستحقة',
      orElse: () => FinancialMetric(
        name: 'المبالغ المستحقة',
        category: 'التدفق النقدي',
        value: 0.0,
        unit: 'ريال',
        status: MetricStatus.good,
      ),
    );

    if (totalSalesMetric.value > 0) {
      final outstandingRatio =
          (outstandingMetric.value / totalSalesMetric.value) * 100;

      if (outstandingRatio > 30) {
        insights.add(
          Insight(
            title: 'تحذير: ارتفاع في المبالغ المستحقة',
            description:
                'المبالغ المستحقة تشكل ${outstandingRatio.toStringAsFixed(1)}% من إجمالي المبيعات',
            type: InsightType.risk,
            priority: InsightPriority.high,
            recommendations: [
              'حسن من عملية تحصيل الذمم',
              'راجع سياسات الائتمان',
              'فكر في حوافز للدفع المبكر',
              'راجع أعمار الذمم المدينة',
            ],
            data: {
              'outstanding_ratio': outstandingRatio,
              'outstanding_amount': outstandingMetric.value,
              'total_sales': totalSalesMetric.value,
              'category': 'تدفق نقدي',
            },
          ),
        );
      } else if (outstandingRatio <= 15) {
        insights.add(
          Insight(
            title: 'إدارة ممتازة للمبالغ المستحقة',
            description:
                'المبالغ المستحقة تشكل فقط ${outstandingRatio.toStringAsFixed(1)}% من إجمالي المبيعات',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            recommendations: [
              'استمر في سياسات التحصيل الحالية',
              'شارك أفضل الممارسات',
              'فكر في تحسين شروط الدفع',
            ],
            data: {
              'outstanding_ratio': outstandingRatio,
              'outstanding_amount': outstandingMetric.value,
              'total_sales': totalSalesMetric.value,
              'category': 'تدفق نقدي',
            },
          ),
        );
      }
    }
  }

  /// تحليل شامل للسيولة العامة
  Future<void> _analyzeOverallLiquidityInsights(
    AnalysisData shortTermLiquidity,
    AnalysisData longTermLiquidity,
    AnalysisData cashFlowAnalysis,
    List<Insight> insights,
  ) async {
    // تحليل الأداء العام للسيولة
    final shortTermMetrics = shortTermLiquidity.metrics;
    final longTermMetrics = longTermLiquidity.metrics;
    final cashFlowMetrics = cashFlowAnalysis.metrics;

    // حساب نقاط الأداء العام
    final excellentShortTerm = shortTermMetrics
        .where((m) => m.status == MetricStatus.excellent)
        .length;
    final excellentLongTerm = longTermMetrics
        .where((m) => m.status == MetricStatus.excellent)
        .length;
    final excellentCashFlow = cashFlowMetrics
        .where((m) => m.status == MetricStatus.excellent)
        .length;

    final criticalShortTerm = shortTermMetrics
        .where((m) => m.status == MetricStatus.critical)
        .length;
    final criticalLongTerm = longTermMetrics
        .where((m) => m.status == MetricStatus.critical)
        .length;
    final criticalCashFlow = cashFlowMetrics
        .where((m) => m.status == MetricStatus.critical)
        .length;

    final totalMetrics =
        shortTermMetrics.length +
        longTermMetrics.length +
        cashFlowMetrics.length;
    final totalExcellent =
        excellentShortTerm + excellentLongTerm + excellentCashFlow;
    final totalCritical =
        criticalShortTerm + criticalLongTerm + criticalCashFlow;

    // تحليل الأداء العام
    if (totalExcellent > totalMetrics * 0.7) {
      insights.add(
        Insight(
          title: 'وضع سيولة ممتاز شامل',
          description:
              'معظم مؤشرات السيولة تظهر أداءً ممتازاً عبر جميع المجالات',
          type: InsightType.opportunity,
          priority: InsightPriority.high,
          impact: (totalExcellent / totalMetrics) * 100,
          recommendations: [
            'استمر في الاستراتيجيات الحالية لإدارة السيولة',
            'فكر في استثمار الفائض النقدي',
            'راجع إمكانية التوسع في الأعمال',
            'شارك أفضل الممارسات مع الفرق الأخرى',
          ],
          data: {
            'excellent_metrics': totalExcellent,
            'total_metrics': totalMetrics,
            'excellent_percentage': (totalExcellent / totalMetrics) * 100,
            'category': 'سيولة شاملة',
          },
        ),
      );
    } else if (totalCritical > totalMetrics * 0.4) {
      insights.add(
        Insight(
          title: 'تحذير: مشاكل شاملة في السيولة',
          description: 'عدد كبير من مؤشرات السيولة يظهر أداءً ضعيفاً',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          impact: (totalCritical / totalMetrics) * 100,
          recommendations: [
            'ضع خطة طوارئ فورية لإدارة السيولة',
            'راجع جميع استراتيجيات التمويل',
            'قلل من المصروفات غير الضرورية',
            'حسن من عمليات التحصيل',
            'فكر في مصادر تمويل إضافية',
          ],
          data: {
            'critical_metrics': totalCritical,
            'total_metrics': totalMetrics,
            'critical_percentage': (totalCritical / totalMetrics) * 100,
            'category': 'سيولة شاملة',
          },
        ),
      );
    }

    // تحليل التوازن بين السيولة قصيرة وطويلة المدى
    final shortTermScore =
        excellentShortTerm /
        (shortTermMetrics.isNotEmpty ? shortTermMetrics.length : 1);
    final longTermScore =
        excellentLongTerm /
        (longTermMetrics.isNotEmpty ? longTermMetrics.length : 1);

    if (shortTermScore > 0.8 && longTermScore < 0.3) {
      insights.add(
        Insight(
          title: 'عدم توازن: سيولة قصيرة ممتازة وطويلة ضعيفة',
          description:
              'السيولة قصيرة المدى ممتازة لكن السيولة طويلة المدى تحتاج تحسين',
          type: InsightType.trend,
          priority: InsightPriority.medium,
          recommendations: [
            'ركز على تحسين السيولة طويلة المدى',
            'راجع هيكل التمويل',
            'فكر في زيادة حقوق الملكية',
            'حسن من إدارة الأصول الثابتة',
          ],
          data: {
            'short_term_score': shortTermScore,
            'long_term_score': longTermScore,
            'category': 'توازن السيولة',
          },
        ),
      );
    } else if (longTermScore > 0.8 && shortTermScore < 0.3) {
      insights.add(
        Insight(
          title: 'عدم توازن: سيولة طويلة ممتازة وقصيرة ضعيفة',
          description:
              'السيولة طويلة المدى ممتازة لكن السيولة قصيرة المدى تحتاج اهتمام فوري',
          type: InsightType.risk,
          priority: InsightPriority.high,
          recommendations: [
            'ركز فوراً على تحسين السيولة قصيرة المدى',
            'زد من النقدية المتاحة',
            'حسن من عملية التحصيل',
            'راجع شروط الدفع مع الموردين',
          ],
          data: {
            'short_term_score': shortTermScore,
            'long_term_score': longTermScore,
            'category': 'توازن السيولة',
          },
        ),
      );
    } else if (shortTermScore > 0.7 && longTermScore > 0.7) {
      insights.add(
        Insight(
          title: 'توازن ممتاز في السيولة',
          description:
              'السيولة قصيرة وطويلة المدى تظهران أداءً متوازناً وممتازاً',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          recommendations: [
            'حافظ على التوازن الحالي',
            'استثمر في فرص النمو',
            'فكر في التوسع الاستراتيجي',
            'طور خطط مالية طموحة',
          ],
          data: {
            'short_term_score': shortTermScore,
            'long_term_score': longTermScore,
            'category': 'توازن السيولة',
          },
        ),
      );
    }

    // تحليل اتجاهات التدفق النقدي
    final netCashFlowMetric = cashFlowMetrics.firstWhere(
      (m) => m.name == 'صافي التدفق النقدي',
      orElse: () => FinancialMetric(
        name: 'صافي التدفق النقدي',
        category: 'التدفق النقدي',
        value: 0.0,
        unit: 'ريال',
        status: MetricStatus.critical,
      ),
    );

    final currentRatioMetric = shortTermMetrics.firstWhere(
      (m) => m.name == 'نسبة السيولة الجارية',
      orElse: () => FinancialMetric(
        name: 'نسبة السيولة الجارية',
        category: 'نسب السيولة قصيرة المدى',
        value: 0.0,
        unit: 'مرة',
        status: MetricStatus.critical,
      ),
    );

    // تحليل التناسق بين التدفق النقدي والسيولة الجارية
    if (netCashFlowMetric.value > 0 && currentRatioMetric.value >= 1.5) {
      insights.add(
        Insight(
          title: 'تناسق إيجابي بين التدفق النقدي والسيولة',
          description: 'التدفق النقدي الإيجابي يدعم السيولة الجارية الممتازة',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          recommendations: [
            'استغل هذا الوضع الإيجابي للنمو',
            'فكر في استثمارات استراتيجية',
            'حافظ على هذا التناسق',
          ],
          data: {
            'net_cash_flow': netCashFlowMetric.value,
            'current_ratio': currentRatioMetric.value,
            'category': 'تناسق السيولة',
          },
        ),
      );
    } else if (netCashFlowMetric.value < 0 && currentRatioMetric.value < 1.0) {
      insights.add(
        Insight(
          title: 'تحذير حرج: تدفق نقدي وسيولة ضعيفان',
          description:
              'التدفق النقدي السالب مع ضعف السيولة الجارية يشكل خطراً كبيراً',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          recommendations: [
            'اتخذ إجراءات طوارئ فورية',
            'ابحث عن مصادر تمويل عاجلة',
            'قلل من جميع المصروفات غير الضرورية',
            'حسن من عمليات التحصيل بشكل عاجل',
          ],
          data: {
            'net_cash_flow': netCashFlowMetric.value,
            'current_ratio': currentRatioMetric.value,
            'category': 'تناسق السيولة',
          },
        ),
      );
    }
  }

  // طرق تحليل الربحية
  Future<AnalysisData> _analyzeProfitMargins(
    basic_reports.FinancialReport incomeStatement,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // الحصول على البيانات من قائمة الدخل
      double revenue = 0.0;
      double grossProfit = 0.0;
      double operatingProfit = 0.0;
      double netIncome = 0.0;
      double costOfGoodsSold = 0.0;
      double operatingExpenses = 0.0;
      double totalExpenses = 0.0;

      // استخراج البيانات من قائمة الدخل
      for (final section in incomeStatement.sections) {
        for (final line in section.lines) {
          final accountName = line.accountName.toLowerCase();

          // الإيرادات
          if (accountName.contains('مبيعات') ||
              accountName.contains('إيرادات') ||
              accountName.contains('دخل')) {
            revenue += line.amount;
          }

          // تكلفة البضاعة المباعة
          if (accountName.contains('تكلفة المبيعات') ||
              accountName.contains('تكلفة البضاعة') ||
              accountName.contains('كلفة البضاعة')) {
            costOfGoodsSold += line.amount.abs();
          }

          // مجمل الربح
          if (accountName.contains('مجمل الربح') ||
              accountName.contains('إجمالي الربح')) {
            grossProfit += line.amount;
          }

          // الربح التشغيلي
          if (accountName.contains('ربح تشغيلي') ||
              accountName.contains('الربح من العمليات')) {
            operatingProfit += line.amount;
          }

          // صافي الربح
          if (accountName.contains('صافي الربح') ||
              accountName.contains('صافي الدخل') ||
              accountName.contains('الربح الصافي')) {
            netIncome += line.amount;
          }

          // المصروفات التشغيلية
          if (accountName.contains('مصروفات') ||
              accountName.contains('مصاريف')) {
            if (!accountName.contains('تكلفة المبيعات')) {
              operatingExpenses += line.amount.abs();
              totalExpenses += line.amount.abs();
            }
          }
        }
      }

      // حساب مجمل الربح إذا لم يكن موجوداً
      if (grossProfit == 0.0 && revenue > 0 && costOfGoodsSold > 0) {
        grossProfit = revenue - costOfGoodsSold;
      }

      // حساب الربح التشغيلي إذا لم يكن موجوداً
      if (operatingProfit == 0.0 && grossProfit > 0) {
        operatingProfit = grossProfit - operatingExpenses;
      }

      // حساب صافي الربح إذا لم يكن موجوداً
      if (netIncome == 0.0 && revenue > 0) {
        netIncome = revenue - totalExpenses - costOfGoodsSold;
      }

      // 1. هامش الربح الإجمالي (Gross Profit Margin)
      final grossProfitMargin = revenue > 0
          ? (grossProfit / revenue) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'هامش الربح الإجمالي',
          category: 'هوامش الربح',
          value: grossProfitMargin,
          unit: '%',
          status: _evaluateProfitMargin(grossProfitMargin),
          description:
              'نسبة مجمل الربح إلى إجمالي المبيعات - يقيس كفاءة الإنتاج',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'هامش الربح الإجمالي', value: grossProfitMargin),
      );
      values.add(grossProfitMargin);

      // 2. هامش الربح التشغيلي (Operating Profit Margin)
      final operatingProfitMargin = revenue > 0
          ? (operatingProfit / revenue) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'هامش الربح التشغيلي',
          category: 'هوامش الربح',
          value: operatingProfitMargin,
          unit: '%',
          status: _evaluateProfitMargin(operatingProfitMargin),
          description:
              'نسبة الربح التشغيلي إلى المبيعات - يقيس كفاءة العمليات الأساسية',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'هامش الربح التشغيلي', value: operatingProfitMargin),
      );
      values.add(operatingProfitMargin);

      // 3. هامش الربح الصافي (Net Profit Margin)
      final netProfitMargin = revenue > 0 ? (netIncome / revenue) * 100 : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'هامش الربح الصافي',
          category: 'هوامش الربح',
          value: netProfitMargin,
          unit: '%',
          status: _evaluateProfitMargin(netProfitMargin),
          description: 'نسبة صافي الربح إلى المبيعات - يقيس الربحية الإجمالية',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'هامش الربح الصافي', value: netProfitMargin),
      );
      values.add(netProfitMargin);

      // 4. نسبة تكلفة المبيعات (Cost of Sales Ratio)
      final costOfSalesRatio = revenue > 0
          ? (costOfGoodsSold / revenue) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة تكلفة المبيعات',
          category: 'هوامش الربح',
          value: costOfSalesRatio,
          unit: '%',
          status: _evaluateCostRatio(costOfSalesRatio),
          description: 'نسبة تكلفة البضاعة المباعة إلى المبيعات',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'نسبة تكلفة المبيعات', value: costOfSalesRatio),
      );
      values.add(costOfSalesRatio);

      // 5. نسبة المصروفات التشغيلية (Operating Expenses Ratio)
      final operatingExpensesRatio = revenue > 0
          ? (operatingExpenses / revenue) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'نسبة المصروفات التشغيلية',
          category: 'هوامش الربح',
          value: operatingExpensesRatio,
          unit: '%',
          status: _evaluateExpenseRatio(operatingExpensesRatio),
          description: 'نسبة المصروفات التشغيلية إلى المبيعات',
        ),
      );
      dataPoints.add(
        DataPoint(
          label: 'نسبة المصروفات التشغيلية',
          value: operatingExpensesRatio,
        ),
      );
      values.add(operatingExpensesRatio);

      return AnalysisData(
        section: ReportSection(
          title: 'تحليل هوامش الربح',
          order: 1,
          subtitle: 'تحليل شامل لهوامش الربح ونسب التكاليف والمصروفات',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات هوامش الربح',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'تحليل هوامش الربح', order: 1),
        metrics: [],
        chart: ChartData(
          title: 'تحليل هوامش الربح',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeReturns(
    basic_reports.FinancialReport incomeStatement,
    basic_reports.FinancialReport balanceSheet,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // استخراج البيانات من التقارير المالية
      double netIncome = 0.0;
      double totalRevenue = 0.0;
      double totalAssets = 0.0;
      double totalEquity = 0.0;
      double totalLiabilities = 0.0;
      double operatingIncome = 0.0;
      double interestExpense = 0.0;

      // استخراج البيانات من قائمة الدخل
      for (final section in incomeStatement.sections) {
        for (final line in section.lines) {
          if (line.accountName.contains('صافي الربح') ||
              line.accountName.contains('الربح الصافي') ||
              line.accountName.contains('صافي الدخل')) {
            netIncome += line.amount;
          } else if (line.accountName.contains('مبيعات') ||
              line.accountName.contains('إيرادات') ||
              line.accountName.contains('الإيرادات')) {
            totalRevenue += line.amount;
          } else if (line.accountName.contains('الربح التشغيلي') ||
              line.accountName.contains('دخل التشغيل')) {
            operatingIncome += line.amount;
          } else if (line.accountName.contains('فوائد مدينة') ||
              line.accountName.contains('مصروفات الفوائد')) {
            interestExpense += line.amount.abs();
          }
        }
      }

      // استخراج البيانات من الميزانية العمومية
      for (final section in balanceSheet.sections) {
        for (final line in section.lines) {
          if (line.accountName.contains('إجمالي الأصول') ||
              line.accountName.contains('مجموع الأصول')) {
            totalAssets += line.amount;
          } else if (line.accountName.contains('إجمالي حقوق الملكية') ||
              line.accountName.contains('مجموع حقوق الملكية') ||
              line.accountName.contains('حقوق المساهمين')) {
            totalEquity += line.amount;
          } else if (line.accountName.contains('إجمالي الخصوم') ||
              line.accountName.contains('مجموع الخصوم') ||
              line.accountName.contains('إجمالي الالتزامات')) {
            totalLiabilities += line.amount;
          }
        }
      }

      // حساب العائد على الأصول (ROA)
      final roa = totalAssets > 0 ? (netIncome / totalAssets) * 100 : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'العائد على الأصول',
          category: 'مؤشرات العائد',
          value: roa,
          unit: '%',
          status: _evaluateROA(roa),
          description: 'قدرة الشركة على توليد الأرباح من أصولها',
          formula: '(صافي الربح ÷ إجمالي الأصول) × 100',
        ),
      );
      dataPoints.add(DataPoint(label: 'العائد على الأصول', value: roa));
      values.add(roa);

      // حساب العائد على حقوق الملكية (ROE)
      final roe = totalEquity > 0 ? (netIncome / totalEquity) * 100 : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'العائد على حقوق الملكية',
          category: 'مؤشرات العائد',
          value: roe,
          unit: '%',
          status: _evaluateROE(roe),
          description: 'عائد المساهمين على استثماراتهم',
          formula: '(صافي الربح ÷ حقوق الملكية) × 100',
        ),
      );
      dataPoints.add(DataPoint(label: 'العائد على حقوق الملكية', value: roe));
      values.add(roe);

      // حساب العائد على المبيعات (ROS)
      final ros = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'العائد على المبيعات',
          category: 'مؤشرات العائد',
          value: ros,
          unit: '%',
          status: _evaluateROS(ros),
          description: 'نسبة الربح الصافي من إجمالي المبيعات',
          formula: '(صافي الربح ÷ إجمالي المبيعات) × 100',
        ),
      );
      dataPoints.add(DataPoint(label: 'العائد على المبيعات', value: ros));
      values.add(ros);

      // حساب العائد على رأس المال المستثمر (ROIC)
      final investedCapital = totalEquity + totalLiabilities;
      final roic = investedCapital > 0
          ? (operatingIncome / investedCapital) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'العائد على رأس المال المستثمر',
          category: 'مؤشرات العائد',
          value: roic,
          unit: '%',
          status: _evaluateROIC(roic),
          description: 'كفاءة استخدام رأس المال المستثمر',
          formula: '(الربح التشغيلي ÷ رأس المال المستثمر) × 100',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'العائد على رأس المال المستثمر', value: roic),
      );
      values.add(roic);

      // حساب مضاعف حقوق الملكية (Equity Multiplier)
      final equityMultiplier = totalEquity > 0
          ? totalAssets / totalEquity
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'مضاعف حقوق الملكية',
          category: 'مؤشرات العائد',
          value: equityMultiplier,
          unit: 'مرة',
          status: _evaluateEquityMultiplier(equityMultiplier),
          description: 'مقدار الرافعة المالية المستخدمة',
          formula: 'إجمالي الأصول ÷ حقوق الملكية',
        ),
      );
      dataPoints.add(
        DataPoint(label: 'مضاعف حقوق الملكية', value: equityMultiplier),
      );
      values.add(equityMultiplier);

      return AnalysisData(
        section: ReportSection(
          title: 'تحليل العوائد',
          order: 2,
          subtitle: 'تحليل شامل لمؤشرات العائد والربحية',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'مؤشرات العائد',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'تحليل العوائد', order: 2),
        metrics: [],
        chart: ChartData(
          title: 'تحليل العوائد',
          type: ChartType.bar,
          dataPoints: [],
        ),
        values: [],
      );
    }
  }

  Future<AnalysisData> _analyzeProductProfitability(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final metrics = <FinancialMetric>[];
      final dataPoints = <DataPoint>[];
      final values = <double>[];

      // الحصول على فواتير المبيعات للفترة المحددة
      final salesInvoices = await _invoiceDao.getInvoicesByDateRange(
        fromDate,
        toDate,
      );
      final salesOnly = salesInvoices
          .where((i) => i.invoiceType == InvoiceType.sales)
          .toList();

      // تجميع بيانات المبيعات حسب المنتج
      final productSales = <int, ProductProfitabilityData>{};

      for (final invoice in salesOnly) {
        for (final line in invoice.lines) {
          if (!productSales.containsKey(line.itemId)) {
            // الحصول على بيانات المنتج
            final item = await _itemDao.getItemById(line.itemId);
            if (item != null) {
              productSales[line.itemId] = ProductProfitabilityData(
                itemId: line.itemId,
                itemName: item.name,
                itemCode: item.code,
                costPrice: item.costPrice,
                sellingPrice: item.sellingPrice,
              );
            }
          }

          final productData = productSales[line.itemId];
          if (productData != null) {
            productData.totalQuantitySold += line.quantity;
            productData.totalRevenue += line.lineTotal;
            productData.totalCost += line.quantity * productData.costPrice;
          }
        }
      }

      // حساب الربحية لكل منتج
      final profitableProducts = productSales.values.toList();
      profitableProducts.sort((a, b) => b.grossProfit.compareTo(a.grossProfit));

      // إحصائيات عامة
      double totalRevenue = 0;
      double totalCost = 0;
      double totalProfit = 0;
      int totalProductsSold = profitableProducts.length;

      for (final product in profitableProducts) {
        totalRevenue += product.totalRevenue;
        totalCost += product.totalCost;
        totalProfit += product.grossProfit;
      }

      // المقاييس المالية
      metrics.add(
        FinancialMetric(
          name: 'عدد المنتجات المباعة',
          category: 'ربحية المنتجات',
          value: totalProductsSold.toDouble(),
          unit: 'منتج',
          status: _evaluateProductCount(totalProductsSold),
          description: 'إجمالي عدد المنتجات التي تم بيعها خلال الفترة',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'إجمالي إيرادات المنتجات',
          category: 'ربحية المنتجات',
          value: totalRevenue,
          unit: 'ريال',
          status: _evaluateRevenueAmount(totalRevenue),
          description: 'إجمالي الإيرادات من جميع المنتجات المباعة',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'إجمالي تكلفة المنتجات',
          category: 'ربحية المنتجات',
          value: totalCost,
          unit: 'ريال',
          status: _evaluateCostAmount(totalCost),
          description: 'إجمالي تكلفة جميع المنتجات المباعة',
        ),
      );

      metrics.add(
        FinancialMetric(
          name: 'إجمالي ربح المنتجات',
          category: 'ربحية المنتجات',
          value: totalProfit,
          unit: 'ريال',
          status: _evaluateProfitAmount(totalProfit),
          description: 'إجمالي الربح من جميع المنتجات المباعة',
        ),
      );

      final overallProfitMargin = totalRevenue > 0
          ? (totalProfit / totalRevenue) * 100
          : 0.0;
      metrics.add(
        FinancialMetric(
          name: 'هامش الربح الإجمالي',
          category: 'ربحية المنتجات',
          value: overallProfitMargin,
          unit: '%',
          status: _evaluateProfitMargin(overallProfitMargin),
          description: 'متوسط هامش الربح لجميع المنتجات',
        ),
      );

      // أفضل 10 منتجات ربحية لعرضها في الرسم البياني
      final top10Products = profitableProducts.take(10).toList();
      for (final product in top10Products) {
        dataPoints.add(
          DataPoint(
            label: product.itemName.length > 20
                ? '${product.itemName.substring(0, 17)}...'
                : product.itemName,
            value: product.grossProfit,
          ),
        );
      }

      values.addAll([
        totalRevenue,
        totalCost,
        totalProfit,
        overallProfitMargin,
        totalProductsSold.toDouble(),
      ]);

      return AnalysisData(
        section: ReportSection(
          title: 'ربحية المنتجات',
          order: 3,
          subtitle: 'تحليل شامل لربحية المنتجات وأداء المبيعات',
        ),
        metrics: metrics,
        chart: ChartData(
          title: 'أفضل 10 منتجات ربحية',
          type: ChartType.bar,
          dataPoints: dataPoints,
        ),
        values: values,
      );
    } catch (e) {
      return AnalysisData(
        section: ReportSection(title: 'ربحية المنتجات', order: 3),
        metrics: [],
        chart: ChartData(title: 'ربحية المنتجات', type: ChartType.pie),
        values: [],
      );
    }
  }

  Future<List<Insight>> _generateProfitabilityInsights(
    AnalysisData profitMargins,
    AnalysisData returnAnalysis,
    AnalysisData productProfitability,
  ) async {
    final List<Insight> insights = [];

    try {
      // تحليل هوامش الربح
      await _analyzeProfitMarginsInsights(profitMargins, insights);

      // تحليل مؤشرات العائد
      await _analyzeReturnInsights(returnAnalysis, insights);

      // تحليل ربحية المنتجات
      await _analyzeProductProfitabilityInsights(
        productProfitability,
        insights,
      );

      // تحليل شامل للربحية العامة
      await _analyzeOverallProfitabilityInsights(
        profitMargins,
        returnAnalysis,
        productProfitability,
        insights,
      );

      return insights;
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع قائمة فارغة
      return [];
    }
  }

  /// تحليل رؤى هوامش الربح
  Future<void> _analyzeProfitMarginsInsights(
    AnalysisData profitMargins,
    List<Insight> insights,
  ) async {
    // تحليل هامش الربح الإجمالي
    final grossMarginMetric = profitMargins.metrics
        .where((m) => m.name.contains('هامش الربح الإجمالي'))
        .firstOrNull;

    if (grossMarginMetric != null) {
      if (grossMarginMetric.status == MetricStatus.excellent) {
        insights.add(
          Insight(
            title: 'هامش ربح إجمالي ممتاز',
            description:
                'هامش الربح الإجمالي يبلغ ${grossMarginMetric.value.toStringAsFixed(1)}% مما يدل على كفاءة عالية في التكاليف المباشرة',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            impact: grossMarginMetric.value,
            recommendations: [
              'استمر في استراتيجيات التحكم في التكاليف الحالية',
              'فكر في زيادة الأسعار تدريجياً للاستفادة من الكفاءة',
              'استثمر في تحسين جودة المنتجات',
              'وسع خطوط الإنتاج عالية الهامش',
            ],
            data: {
              'gross_margin': grossMarginMetric.value,
              'category': 'هوامش الربح',
            },
          ),
        );
      } else if (grossMarginMetric.status == MetricStatus.critical) {
        insights.add(
          Insight(
            title: 'تحذير: هامش ربح إجمالي منخفض',
            description:
                'هامش الربح الإجمالي ${grossMarginMetric.value.toStringAsFixed(1)}% أقل من المستوى المطلوب',
            type: InsightType.risk,
            priority: InsightPriority.critical,
            impact: grossMarginMetric.value,
            recommendations: [
              'راجع استراتيجية التسعير فوراً',
              'قلل تكاليف المواد الخام والإنتاج',
              'ابحث عن موردين أكثر كفاءة من ناحية التكلفة',
              'حسن كفاءة العمليات الإنتاجية',
              'فكر في إيقاف المنتجات غير المربحة',
            ],
            data: {
              'gross_margin': grossMarginMetric.value,
              'category': 'هوامش الربح',
            },
          ),
        );
      }
    }

    // تحليل هامش الربح التشغيلي
    final operatingMarginMetric = profitMargins.metrics
        .where((m) => m.name.contains('هامش الربح التشغيلي'))
        .firstOrNull;

    if (operatingMarginMetric != null) {
      if (operatingMarginMetric.status == MetricStatus.excellent) {
        insights.add(
          Insight(
            title: 'كفاءة تشغيلية عالية',
            description:
                'هامش الربح التشغيلي ${operatingMarginMetric.value.toStringAsFixed(1)}% يظهر إدارة ممتازة للمصروفات التشغيلية',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            impact: operatingMarginMetric.value,
            recommendations: [
              'حافظ على مستوى الكفاءة التشغيلية الحالي',
              'شارك أفضل الممارسات مع الأقسام الأخرى',
              'استثمر في أتمتة العمليات لزيادة الكفاءة',
              'فكر في التوسع مع الحفاظ على نفس مستوى الكفاءة',
            ],
            data: {
              'operating_margin': operatingMarginMetric.value,
              'category': 'هوامش الربح',
            },
          ),
        );
      } else if (operatingMarginMetric.status == MetricStatus.critical) {
        insights.add(
          Insight(
            title: 'مصروفات تشغيلية مرتفعة',
            description:
                'هامش الربح التشغيلي ${operatingMarginMetric.value.toStringAsFixed(1)}% يشير إلى ارتفاع المصروفات التشغيلية',
            type: InsightType.risk,
            priority: InsightPriority.high,
            impact: operatingMarginMetric.value,
            recommendations: [
              'راجع جميع المصروفات التشغيلية وقلل غير الضرورية',
              'حسن كفاءة العمليات الإدارية',
              'فكر في إعادة هيكلة الأقسام',
              'استخدم التكنولوجيا لتقليل التكاليف',
              'راجع عقود الخدمات والموردين',
            ],
            data: {
              'operating_margin': operatingMarginMetric.value,
              'category': 'هوامش الربح',
            },
          ),
        );
      }
    }

    // تحليل هامش الربح الصافي
    final netMarginMetric = profitMargins.metrics
        .where((m) => m.name.contains('هامش الربح الصافي'))
        .firstOrNull;

    if (netMarginMetric != null) {
      if (netMarginMetric.status == MetricStatus.excellent) {
        insights.add(
          Insight(
            title: 'ربحية إجمالية ممتازة',
            description:
                'هامش الربح الصافي ${netMarginMetric.value.toStringAsFixed(1)}% يدل على أداء مالي قوي شامل',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            impact: netMarginMetric.value,
            recommendations: [
              'استمر في الاستراتيجيات المالية الحالية',
              'فكر في التوسع والاستثمار في النمو',
              'وزع الأرباح أو أعد استثمارها',
              'طور منتجات وخدمات جديدة',
            ],
            data: {
              'net_margin': netMarginMetric.value,
              'category': 'هوامش الربح',
            },
          ),
        );
      } else if (netMarginMetric.status == MetricStatus.critical) {
        insights.add(
          Insight(
            title: 'تحذير: ربحية منخفضة',
            description:
                'هامش الربح الصافي ${netMarginMetric.value.toStringAsFixed(1)}% يتطلب مراجعة شاملة للاستراتيجية',
            type: InsightType.risk,
            priority: InsightPriority.critical,
            impact: netMarginMetric.value,
            recommendations: [
              'راجع الاستراتيجية المالية بالكامل',
              'قلل جميع أنواع التكاليف',
              'حسن استراتيجية التسعير',
              'ركز على المنتجات الأكثر ربحية',
              'فكر في إعادة هيكلة الأعمال',
            ],
            data: {
              'net_margin': netMarginMetric.value,
              'category': 'هوامش الربح',
            },
          ),
        );
      }
    }
  }

  /// تحليل رؤى مؤشرات العائد
  Future<void> _analyzeReturnInsights(
    AnalysisData returnAnalysis,
    List<Insight> insights,
  ) async {
    // تحليل العائد على الأصول (ROA)
    final roaMetric = returnAnalysis.metrics
        .where((m) => m.name.contains('العائد على الأصول'))
        .firstOrNull;

    if (roaMetric != null) {
      if (roaMetric.status == MetricStatus.excellent) {
        insights.add(
          Insight(
            title: 'عائد ممتاز على الأصول',
            description:
                'العائد على الأصول ${roaMetric.value.toStringAsFixed(1)}% يظهر استخداماً فعالاً للأصول',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            impact: roaMetric.value,
            recommendations: [
              'استمر في الاستراتيجيات الحالية لإدارة الأصول',
              'فكر في الاستثمار في أصول إضافية مربحة',
              'شارك أفضل الممارسات في إدارة الأصول',
              'طور خطط توسع مدروسة',
            ],
            data: {'roa': roaMetric.value, 'category': 'مؤشرات العائد'},
          ),
        );
      } else if (roaMetric.status == MetricStatus.critical) {
        insights.add(
          Insight(
            title: 'ضعف في استخدام الأصول',
            description:
                'العائد على الأصول ${roaMetric.value.toStringAsFixed(1)}% يشير إلى عدم الاستفادة الكاملة من الأصول',
            type: InsightType.risk,
            priority: InsightPriority.high,
            impact: roaMetric.value,
            recommendations: [
              'راجع كفاءة استخدام الأصول الحالية',
              'فكر في بيع الأصول غير المنتجة',
              'حسن العمليات لزيادة الإنتاجية',
              'استثمر في تدريب الموظفين لتحسين الكفاءة',
              'راجع استراتيجية الاستثمار في الأصول',
            ],
            data: {'roa': roaMetric.value, 'category': 'مؤشرات العائد'},
          ),
        );
      }
    }

    // تحليل العائد على حقوق الملكية (ROE)
    final roeMetric = returnAnalysis.metrics
        .where((m) => m.name.contains('العائد على حقوق الملكية'))
        .firstOrNull;

    if (roeMetric != null) {
      if (roeMetric.status == MetricStatus.excellent) {
        insights.add(
          Insight(
            title: 'عائد ممتاز على حقوق الملكية',
            description:
                'العائد على حقوق الملكية ${roeMetric.value.toStringAsFixed(1)}% يدل على قيمة عالية للمساهمين',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            impact: roeMetric.value,
            recommendations: [
              'استمر في الاستراتيجيات المالية الحالية',
              'فكر في زيادة رأس المال للتوسع',
              'وزع أرباحاً جيدة على المساهمين',
              'استثمر في مشاريع نمو جديدة',
            ],
            data: {'roe': roeMetric.value, 'category': 'مؤشرات العائد'},
          ),
        );
      } else if (roeMetric.status == MetricStatus.critical) {
        insights.add(
          Insight(
            title: 'انخفاض العائد على حقوق الملكية',
            description:
                'العائد على حقوق الملكية ${roeMetric.value.toStringAsFixed(1)}% أقل من التوقعات',
            type: InsightType.risk,
            priority: InsightPriority.critical,
            impact: roeMetric.value,
            recommendations: [
              'راجع الاستراتيجية المالية الشاملة',
              'حسن الربحية من خلال زيادة الإيرادات',
              'قلل التكاليف لتحسين الأرباح',
              'فكر في إعادة هيكلة رأس المال',
              'راجع سياسات التوزيع والاستثمار',
            ],
            data: {'roe': roeMetric.value, 'category': 'مؤشرات العائد'},
          ),
        );
      }
    }

    // تحليل العائد على الاستثمار (ROI)
    final roiMetric = returnAnalysis.metrics
        .where((m) => m.name.contains('العائد على الاستثمار'))
        .firstOrNull;

    if (roiMetric != null) {
      if (roiMetric.status == MetricStatus.excellent) {
        insights.add(
          Insight(
            title: 'استثمارات مربحة جداً',
            description:
                'العائد على الاستثمار ${roiMetric.value.toStringAsFixed(1)}% يظهر قرارات استثمارية ممتازة',
            type: InsightType.opportunity,
            priority: InsightPriority.medium,
            impact: roiMetric.value,
            recommendations: [
              'استمر في نفس معايير اختيار الاستثمارات',
              'زد من حجم الاستثمارات المشابهة',
              'شارك خبرات الاستثمار مع الفرق الأخرى',
              'طور محفظة استثمارية متنوعة',
            ],
            data: {'roi': roiMetric.value, 'category': 'مؤشرات العائد'},
          ),
        );
      } else if (roiMetric.status == MetricStatus.critical) {
        insights.add(
          Insight(
            title: 'ضعف في عوائد الاستثمار',
            description:
                'العائد على الاستثمار ${roiMetric.value.toStringAsFixed(1)}% يتطلب مراجعة استراتيجية الاستثمار',
            type: InsightType.risk,
            priority: InsightPriority.high,
            impact: roiMetric.value,
            recommendations: [
              'راجع معايير اختيار الاستثمارات',
              'قيم الاستثمارات الحالية وفكر في التخارج من غير المربحة',
              'حسن عمليات تقييم الفرص الاستثمارية',
              'استعن بخبراء استثماريين',
              'نوع محفظة الاستثمارات لتقليل المخاطر',
            ],
            data: {'roi': roiMetric.value, 'category': 'مؤشرات العائد'},
          ),
        );
      }
    }
  }

  /// تحليل رؤى ربحية المنتجات
  Future<void> _analyzeProductProfitabilityInsights(
    AnalysisData productProfitability,
    List<Insight> insights,
  ) async {
    // تحليل إجمالي الربحية للمنتجات
    if (productProfitability.values.isNotEmpty) {
      final totalRevenue = productProfitability.values.isNotEmpty
          ? productProfitability.values[0]
          : 0.0;
      final totalCost = productProfitability.values.length > 1
          ? productProfitability.values[1]
          : 0.0;
      final totalProfit = productProfitability.values.length > 2
          ? productProfitability.values[2]
          : 0.0;
      final overallMargin = productProfitability.values.length > 3
          ? productProfitability.values[3]
          : 0.0;

      // تحليل الهامش الإجمالي للمنتجات
      if (overallMargin > 25) {
        insights.add(
          Insight(
            title: 'ربحية منتجات ممتازة',
            description:
                'هامش الربح الإجمالي للمنتجات ${overallMargin.toStringAsFixed(1)}% يدل على أداء قوي',
            type: InsightType.opportunity,
            priority: InsightPriority.high,
            impact: overallMargin,
            recommendations: [
              'استمر في التركيز على المنتجات عالية الهامش',
              'طور منتجات مشابهة للمنتجات الأكثر ربحية',
              'زد من الاستثمار في التسويق للمنتجات المربحة',
              'فكر في زيادة الأسعار تدريجياً',
            ],
            data: {
              'overall_margin': overallMargin,
              'total_revenue': totalRevenue,
              'total_profit': totalProfit,
              'category': 'ربحية المنتجات',
            },
          ),
        );
      } else if (overallMargin < 10) {
        insights.add(
          Insight(
            title: 'تحذير: ربحية منتجات منخفضة',
            description:
                'هامش الربح الإجمالي للمنتجات ${overallMargin.toStringAsFixed(1)}% أقل من المستوى المطلوب',
            type: InsightType.risk,
            priority: InsightPriority.critical,
            impact: overallMargin,
            recommendations: [
              'راجع استراتيجية تسعير المنتجات',
              'قلل تكاليف الإنتاج والمواد الخام',
              'ركز على المنتجات الأكثر ربحية',
              'فكر في إيقاف المنتجات غير المربحة',
              'حسن كفاءة سلسلة التوريد',
            ],
            data: {
              'overall_margin': overallMargin,
              'total_revenue': totalRevenue,
              'total_profit': totalProfit,
              'category': 'ربحية المنتجات',
            },
          ),
        );
      }

      // تحليل حجم المبيعات
      if (totalRevenue > 0) {
        final revenueGrowthIndicator = totalRevenue / 1000; // مؤشر بسيط للنمو
        if (revenueGrowthIndicator > 100) {
          insights.add(
            Insight(
              title: 'حجم مبيعات قوي',
              description:
                  'إجمالي إيرادات المنتجات ${totalRevenue.toStringAsFixed(0)} يظهر أداء مبيعات جيد',
              type: InsightType.opportunity,
              priority: InsightPriority.medium,
              impact: totalRevenue,
              recommendations: [
                'استمر في استراتيجيات المبيعات الحالية',
                'وسع قنوات التوزيع',
                'زد من الاستثمار في التسويق',
                'طور منتجات جديدة لزيادة المبيعات',
              ],
              data: {
                'total_revenue': totalRevenue,
                'category': 'ربحية المنتجات',
              },
            ),
          );
        }
      }
    }

    // تحليل أداء المنتجات الفردية من خلال البيانات المخططية
    if (productProfitability.chart.dataPoints.isNotEmpty) {
      final topProducts =
          productProfitability.chart.dataPoints
              .where((dp) => dp.value > 0)
              .toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      if (topProducts.isNotEmpty) {
        final topProduct = topProducts.first;
        final averageProfit =
            topProducts.fold(0.0, (sum, dp) => sum + dp.value) /
            topProducts.length;

        if (topProduct.value > averageProfit * 2) {
          insights.add(
            Insight(
              title: 'منتج نجم في الربحية',
              description:
                  'المنتج "${topProduct.label}" يحقق ربحاً استثنائياً بقيمة ${topProduct.value.toStringAsFixed(0)}',
              type: InsightType.opportunity,
              priority: InsightPriority.high,
              impact: topProduct.value,
              recommendations: [
                'ركز على تسويق هذا المنتج بقوة',
                'زد من الإنتاج والمخزون لهذا المنتج',
                'طور منتجات مشابهة',
                'ادرس أسباب نجاح هذا المنتج وطبقها على منتجات أخرى',
              ],
              data: {
                'top_product': topProduct.label,
                'top_product_profit': topProduct.value,
                'average_profit': averageProfit,
                'category': 'ربحية المنتجات',
              },
            ),
          );
        }

        // تحليل المنتجات ضعيفة الأداء
        final poorProducts = topProducts
            .where((dp) => dp.value < averageProfit * 0.5)
            .toList();
        if (poorProducts.isNotEmpty) {
          insights.add(
            Insight(
              title: 'منتجات تحتاج مراجعة',
              description:
                  '${poorProducts.length} منتج يحقق أرباحاً أقل من المتوسط بكثير',
              type: InsightType.risk,
              priority: InsightPriority.medium,
              impact: poorProducts.length.toDouble(),
              recommendations: [
                'راجع استراتيجية تسعير هذه المنتجات',
                'قلل تكاليف إنتاج هذه المنتجات',
                'فكر في تحسين جودة أو مواصفات هذه المنتجات',
                'قيم إمكانية إيقاف المنتجات غير المربحة',
              ],
              data: {
                'poor_products_count': poorProducts.length,
                'average_profit': averageProfit,
                'category': 'ربحية المنتجات',
              },
            ),
          );
        }
      }
    }
  }

  /// تحليل شامل للربحية العامة
  Future<void> _analyzeOverallProfitabilityInsights(
    AnalysisData profitMargins,
    AnalysisData returnAnalysis,
    AnalysisData productProfitability,
    List<Insight> insights,
  ) async {
    // حساب النقاط الإجمالية للأداء
    int excellentCount = 0;
    int goodCount = 0;
    int criticalCount = 0;
    int totalMetrics = 0;

    // تجميع المقاييس من جميع التحليلات
    final allMetrics = [
      ...profitMargins.metrics,
      ...returnAnalysis.metrics,
      ...productProfitability.metrics,
    ];

    for (final metric in allMetrics) {
      totalMetrics++;
      switch (metric.status) {
        case MetricStatus.excellent:
          excellentCount++;
          break;
        case MetricStatus.good:
          goodCount++;
          break;
        case MetricStatus.critical:
          criticalCount++;
          break;
        default:
          break;
      }
    }

    if (totalMetrics == 0) return;

    final excellentPercentage = (excellentCount / totalMetrics) * 100;
    final criticalPercentage = (criticalCount / totalMetrics) * 100;

    // تحليل الأداء الشامل
    if (excellentPercentage > 70) {
      insights.add(
        Insight(
          title: 'أداء ربحية استثنائي شامل',
          description:
              '${excellentPercentage.toStringAsFixed(0)}% من مؤشرات الربحية تظهر أداءً ممتازاً',
          type: InsightType.opportunity,
          priority: InsightPriority.high,
          impact: excellentPercentage,
          recommendations: [
            'استمر في جميع الاستراتيجيات الحالية',
            'فكر في التوسع الطموح والاستثمار في النمو',
            'طور خطط استراتيجية طويلة المدى',
            'استثمر في الابتكار والتطوير',
            'فكر في الاستحواذ على شركات أخرى',
          ],
          data: {
            'excellent_percentage': excellentPercentage,
            'excellent_count': excellentCount,
            'total_metrics': totalMetrics,
            'category': 'ربحية شاملة',
          },
        ),
      );
    } else if (excellentPercentage > 50) {
      insights.add(
        Insight(
          title: 'أداء ربحية جيد عموماً',
          description:
              '${excellentPercentage.toStringAsFixed(0)}% من مؤشرات الربحية تظهر أداءً ممتازاً مع إمكانية للتحسين',
          type: InsightType.opportunity,
          priority: InsightPriority.medium,
          impact: excellentPercentage,
          recommendations: [
            'ركز على تحسين المؤشرات الضعيفة',
            'حافظ على الأداء الجيد في المجالات القوية',
            'طور خطط تحسين مستهدفة',
            'استثمر في التدريب والتطوير',
          ],
          data: {
            'excellent_percentage': excellentPercentage,
            'excellent_count': excellentCount,
            'total_metrics': totalMetrics,
            'category': 'ربحية شاملة',
          },
        ),
      );
    }

    if (criticalPercentage > 40) {
      insights.add(
        Insight(
          title: 'تحذير: تحديات ربحية كبيرة',
          description:
              '${criticalPercentage.toStringAsFixed(0)}% من مؤشرات الربحية تظهر أداءً ضعيفاً يتطلب تدخلاً فورياً',
          type: InsightType.risk,
          priority: InsightPriority.critical,
          impact: criticalPercentage,
          recommendations: [
            'ضع خطة طوارئ فورية لتحسين الربحية',
            'راجع جميع الاستراتيجيات المالية والتشغيلية',
            'قلل التكاليف بشكل جذري',
            'ركز على المنتجات والخدمات الأكثر ربحية فقط',
            'فكر في إعادة هيكلة الأعمال',
            'استعن بخبراء ماليين خارجيين',
          ],
          data: {
            'critical_percentage': criticalPercentage,
            'critical_count': criticalCount,
            'total_metrics': totalMetrics,
            'category': 'ربحية شاملة',
          },
        ),
      );
    } else if (criticalPercentage > 20) {
      insights.add(
        Insight(
          title: 'مؤشرات ربحية تحتاج انتباه',
          description:
              '${criticalPercentage.toStringAsFixed(0)}% من مؤشرات الربحية تحتاج تحسين',
          type: InsightType.risk,
          priority: InsightPriority.high,
          impact: criticalPercentage,
          recommendations: [
            'ضع خطة تحسين مستهدفة للمؤشرات الضعيفة',
            'راجع العمليات والاستراتيجيات ذات الصلة',
            'حسن كفاءة التكاليف',
            'راقب التقدم بشكل دوري',
          ],
          data: {
            'critical_percentage': criticalPercentage,
            'critical_count': criticalCount,
            'total_metrics': totalMetrics,
            'category': 'ربحية شاملة',
          },
        ),
      );
    }

    // تحليل التوازن بين المجالات المختلفة
    final marginMetrics = profitMargins.metrics.length;
    final returnMetrics = returnAnalysis.metrics.length;
    final productMetrics = productProfitability.metrics.length;

    if (marginMetrics > 0 && returnMetrics > 0 && productMetrics > 0) {
      final marginExcellent = profitMargins.metrics
          .where((m) => m.status == MetricStatus.excellent)
          .length;
      final returnExcellent = returnAnalysis.metrics
          .where((m) => m.status == MetricStatus.excellent)
          .length;
      final productExcellent = productProfitability.metrics
          .where((m) => m.status == MetricStatus.excellent)
          .length;

      final marginScore = marginMetrics > 0
          ? (marginExcellent / marginMetrics) * 100
          : 0;
      final returnScore = returnMetrics > 0
          ? (returnExcellent / returnMetrics) * 100
          : 0;
      final productScore = productMetrics > 0
          ? (productExcellent / productMetrics) * 100
          : 0;

      // تحديد نقاط القوة والضعف
      final scores = [
        {'name': 'هوامش الربح', 'score': marginScore},
        {'name': 'مؤشرات العائد', 'score': returnScore},
        {'name': 'ربحية المنتجات', 'score': productScore},
      ];

      scores.sort(
        (a, b) => (b['score'] as double).compareTo(a['score'] as double),
      );

      final strongest = scores.first;
      final weakest = scores.last;

      if ((strongest['score'] as double) - (weakest['score'] as double) > 30) {
        insights.add(
          Insight(
            title: 'عدم توازن في أداء الربحية',
            description:
                'هناك فجوة كبيرة بين أداء ${strongest['name']} (${(strongest['score'] as double).toStringAsFixed(0)}%) و ${weakest['name']} (${(weakest['score'] as double).toStringAsFixed(0)}%)',
            type: InsightType.trend,
            priority: InsightPriority.medium,
            impact:
                (strongest['score'] as double) - (weakest['score'] as double),
            recommendations: [
              'ركز على تحسين ${weakest['name']}',
              'انقل أفضل الممارسات من ${strongest['name']}',
              'ضع خطة متوازنة لتحسين جميع المجالات',
              'راجع الموارد المخصصة لكل مجال',
            ],
            data: {
              'strongest_area': strongest['name'],
              'strongest_score': strongest['score'],
              'weakest_area': weakest['name'],
              'weakest_score': weakest['score'],
              'gap':
                  (strongest['score'] as double) - (weakest['score'] as double),
              'category': 'ربحية شاملة',
            },
          ),
        );
      }
    }
  }

  /// طرق مساعدة لحساب البيانات المالية باستخدام AccountDao

  /// الحصول على إجمالي الأصول المتداولة
  Future<double> _getCurrentAssets() async {
    try {
      final currentAssetAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double total = 0.0;

      for (final account in currentAssetAccounts) {
        // تحديد الأصول المتداولة بناءً على رمز الحساب أو الاسم
        if (_isCurrentAsset(account)) {
          final balance = await _getAccountCurrentBalance(account.id!);
          total += balance;
        }
      }

      return total;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على إجمالي الخصوم المتداولة
  Future<double> _getCurrentLiabilities() async {
    try {
      final liabilityAccounts = await _accountDao.getAccountsByType(
        AccountType.liability,
      );
      double total = 0.0;

      for (final account in liabilityAccounts) {
        // تحديد الخصوم المتداولة بناءً على رمز الحساب أو الاسم
        if (_isCurrentLiability(account)) {
          final balance = await _getAccountCurrentBalance(account.id!);
          total += balance;
        }
      }

      return total;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على النقد والاستثمارات قصيرة المدى
  Future<double> _getCashAndEquivalents() async {
    try {
      final assetAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double total = 0.0;

      for (final account in assetAccounts) {
        // تحديد حسابات النقد والاستثمارات قصيرة المدى
        if (_isCashOrEquivalent(account)) {
          final balance = await _getAccountCurrentBalance(account.id!);
          total += balance;
        }
      }

      return total;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على قيمة المخزون
  Future<double> _getInventoryValue() async {
    try {
      final assetAccounts = await _accountDao.getAccountsByType(
        AccountType.asset,
      );
      double total = 0.0;

      for (final account in assetAccounts) {
        // تحديد حسابات المخزون
        if (_isInventoryAccount(account)) {
          final balance = await _getAccountCurrentBalance(account.id!);
          total += balance;
        }
      }

      return total;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على الرصيد الحالي للحساب
  Future<double> _getAccountCurrentBalance(int accountId) async {
    try {
      final transactions = await _journalEntryDao.getAccountTransactions(
        accountId,
      );
      double totalDebit = 0.0;
      double totalCredit = 0.0;

      for (final transaction in transactions) {
        final entry = await _journalEntryDao.getJournalEntryById(
          transaction.journalEntryId,
        );
        if (entry != null && entry.isPosted) {
          totalDebit += transaction.debitAmount;
          totalCredit += transaction.creditAmount;
        }
      }

      // حساب الرصيد بناءً على نوع الحساب
      final account = await _accountDao.getAccountById(accountId);
      if (account != null) {
        switch (account.accountType) {
          case AccountType.asset:
          case AccountType.expense:
            return totalDebit - totalCredit;
          case AccountType.liability:
          case AccountType.equity:
          case AccountType.revenue:
            return totalCredit - totalDebit;
        }
      }

      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// تحديد ما إذا كان الحساب من الأصول المتداولة
  bool _isCurrentAsset(Account account) {
    // تحديد الأصول المتداولة بناءً على رمز الحساب أو الاسم
    return account.code.startsWith('11') || // النقد
        account.code.startsWith('12') || // الذمم المدينة
        account.code.startsWith('13') || // المخزون
        account.code.startsWith('14') || // المصروفات المدفوعة مقدماً
        account.name.contains('نقد') ||
        account.name.contains('ذمم مدينة') ||
        account.name.contains('مخزون') ||
        account.name.contains('مدفوع مقدماً');
  }

  /// تحديد ما إذا كان الحساب من الخصوم المتداولة
  bool _isCurrentLiability(Account account) {
    // تحديد الخصوم المتداولة بناءً على رمز الحساب أو الاسم
    return account.code.startsWith('21') || // الذمم الدائنة
        account.code.startsWith('22') || // القروض قصيرة المدى
        account.code.startsWith('23') || // المستحقات
        account.name.contains('ذمم دائنة') ||
        account.name.contains('قرض قصير') ||
        account.name.contains('مستحق');
  }

  /// تحديد ما إذا كان الحساب من النقد أو ما يعادله
  bool _isCashOrEquivalent(Account account) {
    return account.code.startsWith('111') || // النقد في الصندوق
        account.code.startsWith('112') || // النقد في البنك
        account.code.startsWith('113') || // الاستثمارات قصيرة المدى
        account.name.contains('صندوق') ||
        account.name.contains('بنك') ||
        account.name.contains('نقد');
  }

  /// تحديد ما إذا كان الحساب من المخزون
  bool _isInventoryAccount(Account account) {
    return account.code.startsWith('13') ||
        account.name.contains('مخزون') ||
        account.name.contains('بضاعة');
  }

  /// تقييم نسبة التداول
  MetricStatus _evaluateCurrentRatio(double ratio) {
    if (ratio >= 2.0) return MetricStatus.excellent;
    if (ratio >= 1.5) return MetricStatus.good;
    if (ratio >= 1.0) return MetricStatus.normal;
    if (ratio >= 0.5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم النسبة السريعة
  MetricStatus _evaluateQuickRatio(double ratio) {
    if (ratio >= 1.5) return MetricStatus.excellent;
    if (ratio >= 1.0) return MetricStatus.good;
    if (ratio >= 0.8) return MetricStatus.normal;
    if (ratio >= 0.5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم النسبة النقدية
  MetricStatus _evaluateCashRatio(double ratio) {
    if (ratio >= 0.5) return MetricStatus.excellent;
    if (ratio >= 0.3) return MetricStatus.good;
    if (ratio >= 0.2) return MetricStatus.normal;
    if (ratio >= 0.1) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم مبلغ المبيعات
  MetricStatus _evaluateSalesAmount(double amount) {
    if (amount >= 1000000) return MetricStatus.excellent;
    if (amount >= 500000) return MetricStatus.good;
    if (amount >= 100000) return MetricStatus.normal;
    if (amount >= 50000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم عدد الفواتير
  MetricStatus _evaluateInvoiceCount(int count) {
    if (count >= 100) return MetricStatus.excellent;
    if (count >= 50) return MetricStatus.good;
    if (count >= 20) return MetricStatus.normal;
    if (count >= 10) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم متوسط قيمة الفاتورة
  MetricStatus _evaluateAverageInvoiceValue(double value) {
    if (value >= 10000) return MetricStatus.excellent;
    if (value >= 5000) return MetricStatus.good;
    if (value >= 2000) return MetricStatus.normal;
    if (value >= 1000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم عدد العملاء النشطين
  MetricStatus _evaluateActiveCustomers(int count) {
    if (count >= 50) return MetricStatus.excellent;
    if (count >= 25) return MetricStatus.good;
    if (count >= 10) return MetricStatus.normal;
    if (count >= 5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم متوسط المبيعات لكل عميل
  MetricStatus _evaluateAverageCustomerSales(double value) {
    if (value >= 50000) return MetricStatus.excellent;
    if (value >= 25000) return MetricStatus.good;
    if (value >= 10000) return MetricStatus.normal;
    if (value >= 5000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم أعلى مبيعات لعميل
  MetricStatus _evaluateMaxCustomerSales(double value) {
    if (value >= 100000) return MetricStatus.excellent;
    if (value >= 50000) return MetricStatus.good;
    if (value >= 25000) return MetricStatus.normal;
    if (value >= 10000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم صافي التدفق النقدي
  MetricStatus _evaluateNetCashFlow(double value) {
    if (value >= 500000) return MetricStatus.excellent;
    if (value >= 100000) return MetricStatus.good;
    if (value >= 0) return MetricStatus.normal;
    if (value >= -100000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم مبلغ المشتريات
  MetricStatus _evaluatePurchaseAmount(double amount) {
    // للمشتريات، المبالغ الأقل تعتبر أفضل (تحكم في التكاليف)
    if (amount <= 100000) return MetricStatus.excellent;
    if (amount <= 300000) return MetricStatus.good;
    if (amount <= 500000) return MetricStatus.normal;
    if (amount <= 1000000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم المبالغ المستحقة
  MetricStatus _evaluateOutstandingAmount(double amount) {
    // المبالغ المستحقة الأقل تعتبر أفضل
    if (amount <= 50000) return MetricStatus.excellent;
    if (amount <= 150000) return MetricStatus.good;
    if (amount <= 300000) return MetricStatus.normal;
    if (amount <= 500000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم عدد حركات المخزون
  MetricStatus _evaluateMovementCount(int count) {
    if (count >= 100) return MetricStatus.excellent;
    if (count >= 50) return MetricStatus.good;
    if (count >= 20) return MetricStatus.normal;
    if (count >= 5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم قيمة المخزون
  MetricStatus _evaluateInventoryValue(double value) {
    if (value >= 500000) return MetricStatus.excellent;
    if (value >= 200000) return MetricStatus.good;
    if (value >= 50000) return MetricStatus.normal;
    if (value >= 10000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة دوران المخزون
  MetricStatus _evaluateTurnoverRatio(double ratio) {
    if (ratio >= 1.2) return MetricStatus.excellent;
    if (ratio >= 1.0) return MetricStatus.good;
    if (ratio >= 0.8) return MetricStatus.normal;
    if (ratio >= 0.5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم متوسط قيمة حركة المخزون
  MetricStatus _evaluateAverageMovementValue(double value) {
    if (value >= 5000) return MetricStatus.excellent;
    if (value >= 2000) return MetricStatus.good;
    if (value >= 1000) return MetricStatus.normal;
    if (value >= 500) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم معدل النمو
  MetricStatus _evaluateGrowthRate(double rate) {
    if (rate >= 20) return MetricStatus.excellent;
    if (rate >= 10) return MetricStatus.good;
    if (rate >= 0) return MetricStatus.normal;
    if (rate >= -10) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم معدل نمو المصروفات (المعدل الأقل أفضل)
  MetricStatus _evaluateExpenseGrowthRate(double rate) {
    if (rate <= -10) return MetricStatus.excellent; // انخفاض المصروفات جيد
    if (rate <= 0) return MetricStatus.good;
    if (rate <= 10) return MetricStatus.normal;
    if (rate <= 20) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم هامش الربح
  MetricStatus _evaluateProfitMargin(double margin) {
    if (margin >= 30) return MetricStatus.excellent;
    if (margin >= 20) return MetricStatus.good;
    if (margin >= 10) return MetricStatus.normal;
    if (margin >= 0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم العائد على الأصول (ROA)
  MetricStatus _evaluateROA(double roa) {
    if (roa >= 15) return MetricStatus.excellent;
    if (roa >= 10) return MetricStatus.good;
    if (roa >= 5) return MetricStatus.normal;
    if (roa >= 0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم العائد على حقوق الملكية (ROE)
  MetricStatus _evaluateROE(double roe) {
    if (roe >= 20) return MetricStatus.excellent;
    if (roe >= 15) return MetricStatus.good;
    if (roe >= 10) return MetricStatus.normal;
    if (roe >= 0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم العائد على المبيعات (ROS)
  MetricStatus _evaluateROS(double ros) {
    if (ros >= 15) return MetricStatus.excellent;
    if (ros >= 10) return MetricStatus.good;
    if (ros >= 5) return MetricStatus.normal;
    if (ros >= 0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم العائد على رأس المال المستثمر (ROIC)
  MetricStatus _evaluateROIC(double roic) {
    if (roic >= 15) return MetricStatus.excellent;
    if (roic >= 12) return MetricStatus.good;
    if (roic >= 8) return MetricStatus.normal;
    if (roic >= 0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم مضاعف حقوق الملكية
  MetricStatus _evaluateEquityMultiplier(double multiplier) {
    if (multiplier <= 2.0) return MetricStatus.excellent;
    if (multiplier <= 3.0) return MetricStatus.good;
    if (multiplier <= 4.0) return MetricStatus.normal;
    if (multiplier <= 6.0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم معدل دوران الأصول
  MetricStatus _evaluateAssetTurnover(double turnover) {
    if (turnover >= 2.0) return MetricStatus.excellent;
    if (turnover >= 1.5) return MetricStatus.good;
    if (turnover >= 1.0) return MetricStatus.normal;
    if (turnover >= 0.5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم معدل دوران المخزون
  MetricStatus _evaluateInventoryTurnover(double turnover) {
    if (turnover >= 12) return MetricStatus.excellent;
    if (turnover >= 8) return MetricStatus.good;
    if (turnover >= 4) return MetricStatus.normal;
    if (turnover >= 2) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم معدل دوران الذمم المدينة
  MetricStatus _evaluateReceivablesTurnover(double turnover) {
    if (turnover >= 12) return MetricStatus.excellent;
    if (turnover >= 8) return MetricStatus.good;
    if (turnover >= 6) return MetricStatus.normal;
    if (turnover >= 4) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم فترة التحصيل
  MetricStatus _evaluateCollectionPeriod(double days) {
    if (days <= 30) return MetricStatus.excellent;
    if (days <= 45) return MetricStatus.good;
    if (days <= 60) return MetricStatus.normal;
    if (days <= 90) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة الدين إلى الأصول
  MetricStatus _evaluateDebtToAssets(double ratio) {
    if (ratio <= 30) return MetricStatus.excellent;
    if (ratio <= 50) return MetricStatus.good;
    if (ratio <= 70) return MetricStatus.normal;
    if (ratio <= 85) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة الدين إلى حقوق الملكية
  MetricStatus _evaluateDebtToEquity(double ratio) {
    if (ratio <= 50) return MetricStatus.excellent;
    if (ratio <= 100) return MetricStatus.good;
    if (ratio <= 200) return MetricStatus.normal;
    if (ratio <= 300) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة حقوق الملكية إلى الأصول
  MetricStatus _evaluateEquityToAssets(double ratio) {
    if (ratio >= 70) return MetricStatus.excellent;
    if (ratio >= 50) return MetricStatus.good;
    if (ratio >= 30) return MetricStatus.normal;
    if (ratio >= 15) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم الرافعة المالية
  MetricStatus _evaluateFinancialLeverage(double leverage) {
    if (leverage <= 1.5) return MetricStatus.excellent;
    if (leverage <= 2.0) return MetricStatus.good;
    if (leverage <= 3.0) return MetricStatus.normal;
    if (leverage <= 5.0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم حالة الإيرادات
  MetricStatus _evaluateRevenueStatus(double revenue) {
    if (revenue >= 1000000) return MetricStatus.excellent;
    if (revenue >= 500000) return MetricStatus.good;
    if (revenue >= 100000) return MetricStatus.normal;
    if (revenue >= 50000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم حالة التغيير
  MetricStatus _evaluateChangeStatus(double change) {
    if (change >= 100000) return MetricStatus.excellent;
    if (change >= 50000) return MetricStatus.good;
    if (change >= 0) return MetricStatus.normal;
    if (change >= -50000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة التغيير المئوية
  MetricStatus _evaluatePercentageChangeStatus(double percentage) {
    if (percentage >= 20) return MetricStatus.excellent;
    if (percentage >= 10) return MetricStatus.good;
    if (percentage >= 0) return MetricStatus.normal;
    if (percentage >= -10) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم معدل النمو
  MetricStatus _evaluateGrowthRateStatus(double growthRate) {
    if (growthRate >= 25) return MetricStatus.excellent;
    if (growthRate >= 15) return MetricStatus.good;
    if (growthRate >= 5) return MetricStatus.normal;
    if (growthRate >= 0) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم التغيير المطلق في المصروفات
  MetricStatus _evaluateExpenseChangeStatus(double change) {
    // للمصروفات، الانخفاض أفضل من الارتفاع
    if (change <= -100000) return MetricStatus.excellent; // انخفاض كبير
    if (change <= -50000) return MetricStatus.good; // انخفاض متوسط
    if (change <= 0) return MetricStatus.normal; // انخفاض طفيف أو ثبات
    if (change <= 50000) return MetricStatus.warning; // ارتفاع متوسط
    return MetricStatus.critical; // ارتفاع كبير
  }

  /// تقييم نسبة التغيير المئوية للمصروفات
  MetricStatus _evaluateExpensePercentageChangeStatus(double percentage) {
    // للمصروفات، النسب السالبة أفضل (تعني انخفاض المصروفات)
    if (percentage <= -20) return MetricStatus.excellent; // انخفاض كبير
    if (percentage <= -10) return MetricStatus.good; // انخفاض متوسط
    if (percentage <= 0) return MetricStatus.normal; // انخفاض طفيف أو ثبات
    if (percentage <= 10) return MetricStatus.warning; // ارتفاع متوسط
    return MetricStatus.critical; // ارتفاع كبير
  }

  /// تقييم معدل نمو المصروفات
  MetricStatus _evaluateExpenseGrowthRateStatus(double growthRate) {
    // للمصروفات، النمو السالب أفضل (يعني انخفاض المصروفات)
    if (growthRate <= -25) return MetricStatus.excellent; // انخفاض كبير
    if (growthRate <= -15) return MetricStatus.good; // انخفاض متوسط
    if (growthRate <= -5) return MetricStatus.normal; // انخفاض طفيف
    if (growthRate <= 0) return MetricStatus.warning; // ثبات
    return MetricStatus.critical; // ارتفاع
  }

  /// تقييم رأس المال العامل
  MetricStatus _evaluateWorkingCapital(double workingCapital) {
    if (workingCapital >= 500000) return MetricStatus.excellent;
    if (workingCapital >= 200000) return MetricStatus.good;
    if (workingCapital >= 0) return MetricStatus.normal;
    if (workingCapital >= -100000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة النقدية إلى الأصول المتداولة
  MetricStatus _evaluateCashToCurrentAssets(double percentage) {
    if (percentage >= 30) return MetricStatus.excellent;
    if (percentage >= 20) return MetricStatus.good;
    if (percentage >= 10) return MetricStatus.normal;
    if (percentage >= 5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة الأصول الثابتة إلى حقوق الملكية
  MetricStatus _evaluateFixedAssetsToEquity(double percentage) {
    if (percentage <= 60) return MetricStatus.excellent;
    if (percentage <= 80) return MetricStatus.good;
    if (percentage <= 100) return MetricStatus.normal;
    if (percentage <= 120) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة رأس المال العامل إلى الأصول
  MetricStatus _evaluateWorkingCapitalToAssets(double percentage) {
    if (percentage >= 20) return MetricStatus.excellent;
    if (percentage >= 15) return MetricStatus.good;
    if (percentage >= 10) return MetricStatus.normal;
    if (percentage >= 5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة الديون طويلة الأجل إلى الأصول
  MetricStatus _evaluateLongTermDebtToAssets(double percentage) {
    if (percentage <= 20) return MetricStatus.excellent;
    if (percentage <= 35) return MetricStatus.good;
    if (percentage <= 50) return MetricStatus.normal;
    if (percentage <= 70) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة الأصول المتداولة إلى إجمالي الأصول
  MetricStatus _evaluateCurrentAssetsToTotal(double percentage) {
    if (percentage >= 40) return MetricStatus.excellent;
    if (percentage >= 30) return MetricStatus.good;
    if (percentage >= 20) return MetricStatus.normal;
    if (percentage >= 10) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة تكلفة المبيعات
  MetricStatus _evaluateCostRatio(double ratio) {
    // نسبة أقل تعني ربحية أفضل
    if (ratio <= 40) return MetricStatus.excellent;
    if (ratio <= 50) return MetricStatus.good;
    if (ratio <= 60) return MetricStatus.normal;
    if (ratio <= 70) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم نسبة المصروفات التشغيلية
  MetricStatus _evaluateExpenseRatio(double ratio) {
    // نسبة أقل تعني كفاءة أفضل
    if (ratio <= 15) return MetricStatus.excellent;
    if (ratio <= 25) return MetricStatus.good;
    if (ratio <= 35) return MetricStatus.normal;
    if (ratio <= 45) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم عدد المنتجات
  MetricStatus _evaluateProductCount(int count) {
    if (count >= 50) return MetricStatus.excellent;
    if (count >= 25) return MetricStatus.good;
    if (count >= 10) return MetricStatus.normal;
    if (count >= 5) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم مبلغ الإيرادات
  MetricStatus _evaluateRevenueAmount(double amount) {
    if (amount >= 1000000) return MetricStatus.excellent;
    if (amount >= 500000) return MetricStatus.good;
    if (amount >= 100000) return MetricStatus.normal;
    if (amount >= 50000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم مبلغ التكلفة
  MetricStatus _evaluateCostAmount(double amount) {
    // للتكاليف، المبالغ الأقل تعتبر أفضل
    if (amount <= 100000) return MetricStatus.excellent;
    if (amount <= 300000) return MetricStatus.good;
    if (amount <= 500000) return MetricStatus.normal;
    if (amount <= 800000) return MetricStatus.warning;
    return MetricStatus.critical;
  }

  /// تقييم مبلغ الربح
  MetricStatus _evaluateProfitAmount(double amount) {
    if (amount >= 500000) return MetricStatus.excellent;
    if (amount >= 200000) return MetricStatus.good;
    if (amount >= 50000) return MetricStatus.normal;
    if (amount >= 10000) return MetricStatus.warning;
    return MetricStatus.critical;
  }
}

/// نموذج نطاق التاريخ
class DateRange {
  final DateTime start;
  final DateTime end;
  final String label;

  DateRange({required this.start, required this.end, required this.label});
}

/// نموذج بيانات التحليل
class AnalysisData {
  final ReportSection section;
  final List<FinancialMetric> metrics;
  final ChartData chart;
  final List<double> values;
  final double changePercentage;

  AnalysisData({
    required this.section,
    required this.metrics,
    required this.chart,
    required this.values,
    this.changePercentage = 0,
  });
}

/// نموذج بيانات ربحية المنتج
class ProductProfitabilityData {
  final int itemId;
  final String itemName;
  final String itemCode;
  final double costPrice;
  final double sellingPrice;
  double totalQuantitySold = 0.0;
  double totalRevenue = 0.0;
  double totalCost = 0.0;

  ProductProfitabilityData({
    required this.itemId,
    required this.itemName,
    required this.itemCode,
    required this.costPrice,
    required this.sellingPrice,
  });

  /// الربح الإجمالي
  double get grossProfit => totalRevenue - totalCost;

  /// هامش الربح
  double get profitMargin =>
      totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

  /// متوسط سعر البيع
  double get averageSellingPrice =>
      totalQuantitySold > 0 ? totalRevenue / totalQuantitySold : 0;

  /// متوسط التكلفة
  double get averageCostPrice =>
      totalQuantitySold > 0 ? totalCost / totalQuantitySold : 0;
}
