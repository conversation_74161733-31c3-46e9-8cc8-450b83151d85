/// شاشة إضافة/تعديل المشروع
/// Add/Edit Project Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/project.dart';
import '../../models/customer.dart';
import '../../services/project_service.dart';
import '../../dao/customer_dao.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/quantum_button.dart';
import '../../widgets/quantum_text_field.dart';
import '../../widgets/quantum_dropdown.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_text_styles.dart';

class AddEditProjectScreen extends StatefulWidget {
  final Project? project;

  const AddEditProjectScreen({super.key, this.project});

  @override
  State<AddEditProjectScreen> createState() => _AddEditProjectScreenState();
}

class _AddEditProjectScreenState extends State<AddEditProjectScreen> {
  final _formKey = GlobalKey<FormState>();
  final ProjectService _projectService = ProjectService();
  final CustomerDao _customerDao = CustomerDao();

  // Controllers
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _budgetController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // Form data
  ProjectType _selectedType = ProjectType.internal;
  ProjectStatus _selectedStatus = ProjectStatus.planning;
  ProjectPriority _selectedPriority = ProjectPriority.medium;
  Customer? _selectedCustomer;
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;

  List<Customer> _customers = [];
  bool _isLoading = false;
  bool get _isEditing => widget.project != null;

  @override
  void initState() {
    super.initState();
    _loadCustomers();
    if (_isEditing) {
      _populateFields();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _budgetController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _populateFields() {
    final project = widget.project!;
    _codeController.text = project.code;
    _nameController.text = project.name;
    _descriptionController.text = project.description ?? '';
    _budgetController.text = project.budgetAmount.toString();
    _notesController.text = project.notes ?? '';
    _selectedType = project.type;
    _selectedStatus = project.status;
    _selectedPriority = project.priority;
    _startDate = project.startDate;
    _endDate = project.endDate;

    // سيتم تحديد العميل بعد تحميل قائمة العملاء
  }

  Future<void> _loadCustomers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _customerDao.getAllCustomers();
      if (result.isSuccess) {
        setState(() {
          _customers = result.data!;

          // إذا كان في وضع التعديل، ابحث عن العميل المحدد
          if (_isEditing && widget.project!.customerId != null) {
            _selectedCustomer = _customers
                .where((c) => c.id == widget.project!.customerId)
                .firstOrNull;
          }
        });
      } else {
        _showErrorDialog(result.error ?? 'فشل في تحميل العملاء');
      }
    } catch (e) {
      _showErrorDialog('فشل في تحميل العملاء: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => ErrorDialog(message: message),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : (_endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // إذا كان تاريخ البداية بعد تاريخ النهاية، امسح تاريخ النهاية
          if (_endDate != null && _endDate!.isBefore(_startDate)) {
            _endDate = null;
          }
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _saveProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final project = Project(
        id: _isEditing ? widget.project!.id : null,
        code: _codeController.text.trim(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        type: _selectedType,
        status: _selectedStatus,
        priority: _selectedPriority,
        customerId: _selectedCustomer?.id,
        startDate: _startDate,
        endDate: _endDate,
        budgetAmount: double.parse(_budgetController.text),
        actualCost: _isEditing ? widget.project!.actualCost : 0.0,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isActive: true,
        createdAt: _isEditing ? widget.project!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = _isEditing
          ? await _projectService.updateProject(project)
          : await _projectService.createProject(project);

      if (!mounted) return;

      if (result.isSuccess) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث المشروع بنجاح' : 'تم إنشاء المشروع بنجاح',
            ),
          ),
        );
      } else {
        _showErrorDialog(result.error ?? 'فشل في حفظ المشروع');
      }
    } catch (e) {
      _showErrorDialog('خطأ في حفظ المشروع: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل المشروع' : 'إضافة مشروع جديد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _saveProject,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                QuantumCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المعلومات الأساسية',
                          style: AppTextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: QuantumTextField(
                                controller: _codeController,
                                labelText: 'كود المشروع *',
                                textAlign: TextAlign.right,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'كود المشروع مطلوب';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 2,
                              child: QuantumTextField(
                                controller: _nameController,
                                labelText: 'اسم المشروع *',
                                textAlign: TextAlign.right,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'اسم المشروع مطلوب';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        QuantumTextField(
                          controller: _descriptionController,
                          labelText: 'وصف المشروع',
                          textAlign: TextAlign.right,
                          maxLines: 3,
                        ),

                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: QuantumDropdown<ProjectType>(
                                value: _selectedType,
                                items: ProjectType.values
                                    .map(
                                      (type) => DropdownMenuItem(
                                        value: type,
                                        child: Text(type.displayName),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedType = value!;
                                  });
                                },
                                labelText: 'نوع المشروع',
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: QuantumDropdown<ProjectStatus>(
                                value: _selectedStatus,
                                items: ProjectStatus.values
                                    .map(
                                      (status) => DropdownMenuItem(
                                        value: status,
                                        child: Text(status.displayName),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedStatus = value!;
                                  });
                                },
                                labelText: 'حالة المشروع',
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: QuantumDropdown<ProjectPriority>(
                                value: _selectedPriority,
                                items: ProjectPriority.values
                                    .map(
                                      (priority) => DropdownMenuItem(
                                        value: priority,
                                        child: Text(priority.displayName),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedPriority = value!;
                                  });
                                },
                                labelText: 'أولوية المشروع',
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        QuantumDropdown<Customer>(
                          value: _selectedCustomer,
                          items: _customers
                              .map(
                                (customer) => DropdownMenuItem(
                                  value: customer,
                                  child: Text(
                                    '${customer.name} (${customer.code})',
                                  ),
                                ),
                              )
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCustomer = value;
                            });
                          },
                          labelText: 'العميل',
                          hintText: 'اختر العميل (اختياري)',
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                QuantumCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'التواريخ والميزانية',
                          style: AppTextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context, true),
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.border),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.calendar_today,
                                        color: AppColors.primary,
                                      ),
                                      const SizedBox(width: 8),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'تاريخ البداية',
                                            style: AppTextStyles.bodySmall
                                                .copyWith(
                                                  color:
                                                      AppColors.textSecondary,
                                                ),
                                          ),
                                          Text(
                                            '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                                            style: AppTextStyles.bodyMedium,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context, false),
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.border),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.calendar_today,
                                        color: AppColors.primary,
                                      ),
                                      const SizedBox(width: 8),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'تاريخ النهاية',
                                            style: AppTextStyles.bodySmall
                                                .copyWith(
                                                  color:
                                                      AppColors.textSecondary,
                                                ),
                                          ),
                                          Text(
                                            _endDate != null
                                                ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                                                : 'غير محدد',
                                            style: AppTextStyles.bodyMedium
                                                .copyWith(
                                                  color: _endDate != null
                                                      ? AppColors.textPrimary
                                                      : AppColors.textSecondary,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        QuantumTextField(
                          controller: _budgetController,
                          labelText: 'ميزانية المشروع *',
                          textAlign: TextAlign.right,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'^\d+\.?\d{0,2}'),
                            ),
                          ],
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'ميزانية المشروع مطلوبة';
                            }
                            final budget = double.tryParse(value);
                            if (budget == null || budget < 0) {
                              return 'يرجى إدخال ميزانية صحيحة';
                            }
                            return null;
                          },
                          suffixText: 'ر.س',
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                QuantumCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ملاحظات إضافية',
                          style: AppTextStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        QuantumTextField(
                          controller: _notesController,
                          labelText: 'ملاحظات',
                          textAlign: TextAlign.right,
                          maxLines: 4,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                Row(
                  children: [
                    Expanded(
                      child: QuantumButton(
                        onPressed: () => Navigator.pop(context),
                        text: 'إلغاء',
                        variant: QuantumButtonVariant.outline,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: QuantumButton(
                        onPressed: _saveProject,
                        text: _isEditing ? 'تحديث المشروع' : 'إنشاء المشروع',
                        variant: QuantumButtonVariant.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
