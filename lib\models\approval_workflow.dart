/// نماذج نظام الموافقات
/// Approval Workflow Models for Smart Ledger
library;

import 'user.dart';

/// نموذج الدور
/// Role Model
class Role {
  final int? id;
  final String name;
  final String? description;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Role({
    this.id,
    required this.name,
    this.description,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory Role.fromMap(Map<String, dynamic> map) {
    return Role(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'Role{name: $name}';
  }
}

enum ApprovalStatus {
  pending('pending', 'في انتظار الموافقة'),
  approved('approved', 'موافق عليه'),
  rejected('rejected', 'مرفوض'),
  cancelled('cancelled', 'ملغي');

  const ApprovalStatus(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum DocumentType {
  journalEntry('journal_entry', 'قيد يومية'),
  invoice('invoice', 'فاتورة'),
  expense('expense', 'مصروف'),
  stockMovement('stock_movement', 'حركة مخزون'),
  bankTransaction('bank_transaction', 'معاملة بنكية'),
  cashTransaction('cash_transaction', 'معاملة نقدية'),
  purchase('purchase', 'مشتريات'),
  sale('sale', 'مبيعات');

  const DocumentType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum ApprovalLevel {
  level1('level1', 'المستوى الأول'),
  level2('level2', 'المستوى الثاني'),
  level3('level3', 'المستوى الثالث'),
  final_('final', 'الموافقة النهائية');

  const ApprovalLevel(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// نموذج سير عمل الموافقة
/// Approval Workflow Model
class ApprovalWorkflow {
  final int? id;
  final String name;
  final String? description;
  final DocumentType documentType;
  final double? minAmount;
  final double? maxAmount;
  final bool isActive;
  final List<ApprovalStep> steps;
  final DateTime createdAt;
  final DateTime updatedAt;

  ApprovalWorkflow({
    this.id,
    required this.name,
    this.description,
    required this.documentType,
    this.minAmount,
    this.maxAmount,
    this.isActive = true,
    this.steps = const [],
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory ApprovalWorkflow.fromMap(Map<String, dynamic> map) {
    return ApprovalWorkflow(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      documentType: DocumentType.values.firstWhere(
        (e) => e.value == map['document_type'],
        orElse: () => DocumentType.journalEntry,
      ),
      minAmount: (map['min_amount'] as num?)?.toDouble(),
      maxAmount: (map['max_amount'] as num?)?.toDouble(),
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'document_type': documentType.value,
      'min_amount': minAmount,
      'max_amount': maxAmount,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  ApprovalWorkflow copyWith({
    int? id,
    String? name,
    String? description,
    DocumentType? documentType,
    double? minAmount,
    double? maxAmount,
    bool? isActive,
    List<ApprovalStep>? steps,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ApprovalWorkflow(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      documentType: documentType ?? this.documentType,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
      isActive: isActive ?? this.isActive,
      steps: steps ?? this.steps,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ApprovalWorkflow{name: $name, documentType: ${documentType.arabicName}}';
  }
}

/// نموذج خطوة الموافقة
/// Approval Step Model
class ApprovalStep {
  final int? id;
  final int workflowId;
  final int stepOrder;
  final String name;
  final ApprovalLevel level;
  final int? approverId;
  final int? roleId;
  final bool isRequired;
  final int? timeoutHours;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  User? approver;
  Role? role;

  ApprovalStep({
    this.id,
    required this.workflowId,
    required this.stepOrder,
    required this.name,
    required this.level,
    this.approverId,
    this.roleId,
    this.isRequired = true,
    this.timeoutHours,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.approver,
    this.role,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory ApprovalStep.fromMap(Map<String, dynamic> map) {
    return ApprovalStep(
      id: map['id'] as int?,
      workflowId: map['workflow_id'] as int,
      stepOrder: map['step_order'] as int,
      name: map['name'] as String,
      level: ApprovalLevel.values.firstWhere(
        (e) => e.value == map['level'],
        orElse: () => ApprovalLevel.level1,
      ),
      approverId: map['approver_id'] as int?,
      roleId: map['role_id'] as int?,
      isRequired: (map['is_required'] as int) == 1,
      timeoutHours: map['timeout_hours'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workflow_id': workflowId,
      'step_order': stepOrder,
      'name': name,
      'level': level.value,
      'approver_id': approverId,
      'role_id': roleId,
      'is_required': isRequired ? 1 : 0,
      'timeout_hours': timeoutHours,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'ApprovalStep{name: $name, level: ${level.arabicName}, order: $stepOrder}';
  }
}

/// نموذج طلب الموافقة
/// Approval Request Model
class ApprovalRequest {
  final int? id;
  final int workflowId;
  final DocumentType documentType;
  final int documentId;
  final String documentNumber;
  final double? amount;
  final String? description;
  final ApprovalStatus status;
  final int requestedBy;
  final DateTime requestedAt;
  final int? currentStepId;
  final String? rejectionReason;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  ApprovalWorkflow? workflow;
  User? requester;
  ApprovalStep? currentStep;
  List<ApprovalAction>? actions;

  ApprovalRequest({
    this.id,
    required this.workflowId,
    required this.documentType,
    required this.documentId,
    required this.documentNumber,
    this.amount,
    this.description,
    this.status = ApprovalStatus.pending,
    required this.requestedBy,
    DateTime? requestedAt,
    this.currentStepId,
    this.rejectionReason,
    this.completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.workflow,
    this.requester,
    this.currentStep,
    this.actions,
  }) : requestedAt = requestedAt ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory ApprovalRequest.fromMap(Map<String, dynamic> map) {
    return ApprovalRequest(
      id: map['id'] as int?,
      workflowId: map['workflow_id'] as int,
      documentType: DocumentType.values.firstWhere(
        (e) => e.value == map['document_type'],
        orElse: () => DocumentType.journalEntry,
      ),
      documentId: map['document_id'] as int,
      documentNumber: map['document_number'] as String,
      amount: (map['amount'] as num?)?.toDouble(),
      description: map['description'] as String?,
      status: ApprovalStatus.values.firstWhere(
        (e) => e.value == map['status'],
        orElse: () => ApprovalStatus.pending,
      ),
      requestedBy: map['requested_by'] as int,
      requestedAt: DateTime.parse(map['requested_at'] as String),
      currentStepId: map['current_step_id'] as int?,
      rejectionReason: map['rejection_reason'] as String?,
      completedAt: map['completed_at'] != null
          ? DateTime.parse(map['completed_at'] as String)
          : null,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workflow_id': workflowId,
      'document_type': documentType.value,
      'document_id': documentId,
      'document_number': documentNumber,
      'amount': amount,
      'description': description,
      'status': status.value,
      'requested_by': requestedBy,
      'requested_at': requestedAt.toIso8601String(),
      'current_step_id': currentStepId,
      'rejection_reason': rejectionReason,
      'completed_at': completedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Check if request is pending
  bool get isPending => status == ApprovalStatus.pending;

  /// Check if request is approved
  bool get isApproved => status == ApprovalStatus.approved;

  /// Check if request is rejected
  bool get isRejected => status == ApprovalStatus.rejected;

  /// Create a copy with updated fields
  ApprovalRequest copyWith({
    int? id,
    int? workflowId,
    DocumentType? documentType,
    int? documentId,
    String? documentNumber,
    double? amount,
    String? description,
    ApprovalStatus? status,
    int? requestedBy,
    DateTime? requestedAt,
    int? currentStepId,
    String? rejectionReason,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ApprovalRequest(
      id: id ?? this.id,
      workflowId: workflowId ?? this.workflowId,
      documentType: documentType ?? this.documentType,
      documentId: documentId ?? this.documentId,
      documentNumber: documentNumber ?? this.documentNumber,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      status: status ?? this.status,
      requestedBy: requestedBy ?? this.requestedBy,
      requestedAt: requestedAt ?? this.requestedAt,
      currentStepId: currentStepId ?? this.currentStepId,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ApprovalRequest{documentNumber: $documentNumber, status: ${status.arabicName}}';
  }
}

/// نموذج إجراء الموافقة
/// Approval Action Model
class ApprovalAction {
  final int? id;
  final int requestId;
  final int stepId;
  final ApprovalStatus action;
  final int actionBy;
  final DateTime actionAt;
  final String? comments;
  final DateTime createdAt;

  // Navigation properties
  ApprovalStep? step;
  User? actionUser;

  ApprovalAction({
    this.id,
    required this.requestId,
    required this.stepId,
    required this.action,
    required this.actionBy,
    DateTime? actionAt,
    this.comments,
    DateTime? createdAt,
    this.step,
    this.actionUser,
  }) : actionAt = actionAt ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now();

  /// Factory constructor from database map
  factory ApprovalAction.fromMap(Map<String, dynamic> map) {
    return ApprovalAction(
      id: map['id'] as int?,
      requestId: map['request_id'] as int,
      stepId: map['step_id'] as int,
      action: ApprovalStatus.values.firstWhere(
        (e) => e.value == map['action'],
        orElse: () => ApprovalStatus.pending,
      ),
      actionBy: map['action_by'] as int,
      actionAt: DateTime.parse(map['action_at'] as String),
      comments: map['comments'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'request_id': requestId,
      'step_id': stepId,
      'action': action.value,
      'action_by': actionBy,
      'action_at': actionAt.toIso8601String(),
      'comments': comments,
      'created_at': createdAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'ApprovalAction{action: ${action.arabicName}, actionBy: $actionBy}';
  }
}
