/// نماذج التقارير المالية
/// Financial Report Models for Smart Ledger
library;

enum ReportType {
  balanceSheet('balance_sheet', 'الميزانية العمومية'),
  incomeStatement('income_statement', 'قائمة الدخل'),
  cashFlow('cash_flow', 'قائمة التدفق النقدي'),
  trialBalance('trial_balance', 'ميزان المراجعة'),
  generalLedger('general_ledger', 'دفتر الأستاذ العام'),
  accountStatement('account_statement', 'كشف حساب'),
  profitLoss('profit_loss', 'الأرباح والخسائر'),
  cashFlowDetailed('cash_flow_detailed', 'التدفق النقدي التفصيلي');

  const ReportType(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum ReportPeriod {
  daily('daily', 'يومي'),
  weekly('weekly', 'أسبوعي'),
  monthly('monthly', 'شهري'),
  quarterly('quarterly', 'ربع سنوي'),
  yearly('yearly', 'سنوي'),
  custom('custom', 'فترة مخصصة');

  const ReportPeriod(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

enum ReportFormat {
  pdf('pdf', 'PDF'),
  excel('excel', 'Excel'),
  csv('csv', 'CSV'),
  html('html', 'HTML');

  const ReportFormat(this.value, this.arabicName);
  final String value;
  final String arabicName;
}

/// نموذج التقرير المالي
/// Financial Report Model
class FinancialReport {
  final String id;
  final ReportType type;
  final String title;
  final String? subtitle;
  final DateTime fromDate;
  final DateTime toDate;
  final DateTime generatedAt;
  final String? generatedBy;
  final Map<String, dynamic> parameters;
  final List<ReportSection> sections;
  final Map<String, dynamic> summary;
  final String? notes;

  FinancialReport({
    String? id,
    required this.type,
    required this.title,
    this.subtitle,
    required this.fromDate,
    required this.toDate,
    DateTime? generatedAt,
    this.generatedBy,
    this.parameters = const {},
    this.sections = const [],
    this.summary = const {},
    this.notes,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       generatedAt = generatedAt ?? DateTime.now();

  /// Create a copy with updated fields
  FinancialReport copyWith({
    String? id,
    ReportType? type,
    String? title,
    String? subtitle,
    DateTime? fromDate,
    DateTime? toDate,
    DateTime? generatedAt,
    String? generatedBy,
    Map<String, dynamic>? parameters,
    List<ReportSection>? sections,
    Map<String, dynamic>? summary,
    String? notes,
  }) {
    return FinancialReport(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      generatedAt: generatedAt ?? this.generatedAt,
      generatedBy: generatedBy ?? this.generatedBy,
      parameters: parameters ?? this.parameters,
      sections: sections ?? this.sections,
      summary: summary ?? this.summary,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'FinancialReport{type: ${type.arabicName}, title: $title}';
  }
}

/// نموذج قسم التقرير
/// Report Section Model
class ReportSection {
  final String id;
  final String title;
  final String? subtitle;
  final int order;
  final List<ReportLine> lines;
  final Map<String, dynamic> totals;
  final String? notes;

  ReportSection({
    String? id,
    required this.title,
    this.subtitle,
    required this.order,
    this.lines = const [],
    this.totals = const {},
    this.notes,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  /// Calculate section total
  double get totalAmount {
    return lines.fold(0.0, (sum, line) => sum + line.amount);
  }

  /// Create a copy with updated fields
  ReportSection copyWith({
    String? id,
    String? title,
    String? subtitle,
    int? order,
    List<ReportLine>? lines,
    Map<String, dynamic>? totals,
    String? notes,
  }) {
    return ReportSection(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      order: order ?? this.order,
      lines: lines ?? this.lines,
      totals: totals ?? this.totals,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'ReportSection{title: $title, lines: ${lines.length}}';
  }
}

/// نموذج سطر التقرير
/// Report Line Model
class ReportLine {
  final String id;
  final String accountCode;
  final String accountName;
  final double amount;
  final double? previousAmount;
  final double? percentage;
  final int level;
  final bool isTotal;
  final bool isBold;
  final String? notes;
  final Map<String, dynamic> details;

  ReportLine({
    String? id,
    required this.accountCode,
    required this.accountName,
    required this.amount,
    this.previousAmount,
    this.percentage,
    this.level = 0,
    this.isTotal = false,
    this.isBold = false,
    this.notes,
    this.details = const {},
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  /// Calculate variance from previous period
  double? get variance {
    if (previousAmount == null) return null;
    return amount - previousAmount!;
  }

  /// Calculate variance percentage
  double? get variancePercentage {
    if (previousAmount == null || previousAmount == 0) return null;
    return ((amount - previousAmount!) / previousAmount!.abs()) * 100;
  }

  /// Create a copy with updated fields
  ReportLine copyWith({
    String? id,
    String? accountCode,
    String? accountName,
    double? amount,
    double? previousAmount,
    double? percentage,
    int? level,
    bool? isTotal,
    bool? isBold,
    String? notes,
    Map<String, dynamic>? details,
  }) {
    return ReportLine(
      id: id ?? this.id,
      accountCode: accountCode ?? this.accountCode,
      accountName: accountName ?? this.accountName,
      amount: amount ?? this.amount,
      previousAmount: previousAmount ?? this.previousAmount,
      percentage: percentage ?? this.percentage,
      level: level ?? this.level,
      isTotal: isTotal ?? this.isTotal,
      isBold: isBold ?? this.isBold,
      notes: notes ?? this.notes,
      details: details ?? this.details,
    );
  }

  @override
  String toString() {
    return 'ReportLine{accountCode: $accountCode, accountName: $accountName, amount: $amount}';
  }
}

/// نموذج قائمة التدفق النقدي
/// Cash Flow Statement Model
class CashFlowStatement {
  final DateTime fromDate;
  final DateTime toDate;
  final CashFlowSection operatingActivities;
  final CashFlowSection investingActivities;
  final CashFlowSection financingActivities;
  final double beginningCash;
  final double endingCash;
  final double netCashFlow;

  CashFlowStatement({
    required this.fromDate,
    required this.toDate,
    required this.operatingActivities,
    required this.investingActivities,
    required this.financingActivities,
    required this.beginningCash,
    required this.endingCash,
  }) : netCashFlow = operatingActivities.total + 
                     investingActivities.total + 
                     financingActivities.total;

  /// Verify cash flow calculation
  bool get isBalanced {
    final calculatedEnding = beginningCash + netCashFlow;
    return (calculatedEnding - endingCash).abs() < 0.01;
  }

  @override
  String toString() {
    return 'CashFlowStatement{netCashFlow: $netCashFlow, isBalanced: $isBalanced}';
  }
}

/// نموذج قسم التدفق النقدي
/// Cash Flow Section Model
class CashFlowSection {
  final String title;
  final List<CashFlowItem> items;
  final double total;

  CashFlowSection({
    required this.title,
    required this.items,
  }) : total = items.fold(0.0, (sum, item) => sum + item.amount);

  @override
  String toString() {
    return 'CashFlowSection{title: $title, total: $total}';
  }
}

/// نموذج عنصر التدفق النقدي
/// Cash Flow Item Model
class CashFlowItem {
  final String description;
  final double amount;
  final String? accountCode;
  final String? notes;

  CashFlowItem({
    required this.description,
    required this.amount,
    this.accountCode,
    this.notes,
  });

  @override
  String toString() {
    return 'CashFlowItem{description: $description, amount: $amount}';
  }
}

/// نموذج ميزان المراجعة
/// Trial Balance Model
class TrialBalance {
  final DateTime asOfDate;
  final List<TrialBalanceLine> lines;
  final double totalDebits;
  final double totalCredits;

  TrialBalance({
    required this.asOfDate,
    required this.lines,
  }) : totalDebits = lines.fold(0.0, (sum, line) => sum + line.debitBalance),
       totalCredits = lines.fold(0.0, (sum, line) => sum + line.creditBalance);

  /// Check if trial balance is balanced
  bool get isBalanced {
    return (totalDebits - totalCredits).abs() < 0.01;
  }

  @override
  String toString() {
    return 'TrialBalance{totalDebits: $totalDebits, totalCredits: $totalCredits, isBalanced: $isBalanced}';
  }
}

/// نموذج سطر ميزان المراجعة
/// Trial Balance Line Model
class TrialBalanceLine {
  final String accountCode;
  final String accountName;
  final String accountType;
  final double debitBalance;
  final double creditBalance;
  final double netBalance;

  TrialBalanceLine({
    required this.accountCode,
    required this.accountName,
    required this.accountType,
    required this.debitBalance,
    required this.creditBalance,
  }) : netBalance = debitBalance - creditBalance;

  @override
  String toString() {
    return 'TrialBalanceLine{accountCode: $accountCode, netBalance: $netBalance}';
  }
}
