/// خدمة إدارة المخازن
/// Warehouse Management Service for Smart Ledger
library;

import '../database/warehouse_dao.dart';
import '../database/warehouse_location_dao.dart';
import '../database/stock_movement_dao.dart';
import '../database/stock_balance_dao.dart';
import '../models/warehouse.dart';
import '../models/stock_movement.dart';
import '../models/stock_balance.dart';
import '../utils/result.dart';

class WarehouseService {
  final WarehouseDao _warehouseDao = WarehouseDao();
  final WarehouseLocationDao _locationDao = WarehouseLocationDao();
  final StockMovementDao _movementDao = StockMovementDao();
  final StockBalanceDao _balanceDao = StockBalanceDao();

  // ==================== إدارة المخازن ====================

  /// الحصول على جميع المخازن
  Future<Result<List<Warehouse>>> getAllWarehouses() async {
    try {
      final warehouses = await _warehouseDao.getAllWarehouses();
      return Result.success(warehouses);
    } catch (e) {
      return Result.error('خطأ في جلب المخازن: ${e.toString()}');
    }
  }

  /// الحصول على المخازن النشطة
  Future<Result<List<Warehouse>>> getActiveWarehouses() async {
    try {
      final warehouses = await _warehouseDao.getActiveWarehouses();
      return Result.success(warehouses);
    } catch (e) {
      return Result.error('خطأ في جلب المخازن النشطة: ${e.toString()}');
    }
  }

  /// الحصول على مخزن بالمعرف
  Future<Result<Warehouse>> getWarehouseById(int id) async {
    try {
      final warehouse = await _warehouseDao.getWarehouseById(id);
      if (warehouse != null) {
        return Result.success(warehouse);
      } else {
        return Result.error('المخزن غير موجود');
      }
    } catch (e) {
      return Result.error('خطأ في جلب المخزن: ${e.toString()}');
    }
  }

  /// إضافة مخزن جديد
  Future<Result<int>> addWarehouse(Warehouse warehouse) async {
    try {
      // التحقق من عدم تكرار الكود
      final codeExists = await _warehouseDao.isWarehouseCodeExists(
        warehouse.code,
      );
      if (codeExists) {
        return Result.error('كود المخزن موجود مسبقاً');
      }

      final id = await _warehouseDao.insertWarehouse(warehouse);
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إضافة المخزن: ${e.toString()}');
    }
  }

  /// تحديث مخزن
  Future<Result<bool>> updateWarehouse(Warehouse warehouse) async {
    try {
      // التحقق من عدم تكرار الكود
      final codeExists = await _warehouseDao.isWarehouseCodeExists(
        warehouse.code,
        excludeId: warehouse.id,
      );
      if (codeExists) {
        return Result.error('كود المخزن موجود مسبقاً');
      }

      final rowsAffected = await _warehouseDao.updateWarehouse(warehouse);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث المخزن: ${e.toString()}');
    }
  }

  /// حذف مخزن
  Future<Result<bool>> deleteWarehouse(int id) async {
    try {
      // التحقق من وجود مخزون في المخزن
      final balances = await _balanceDao.getBalancesByWarehouse(id);
      if (balances.isNotEmpty) {
        return Result.error('لا يمكن حذف المخزن لوجود مخزون به');
      }

      final rowsAffected = await _warehouseDao.deleteWarehouse(id);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في حذف المخزن: ${e.toString()}');
    }
  }

  /// البحث في المخازن
  Future<Result<List<Warehouse>>> searchWarehouses(String searchTerm) async {
    try {
      final warehouses = await _warehouseDao.searchWarehouses(searchTerm);
      return Result.success(warehouses);
    } catch (e) {
      return Result.error('خطأ في البحث: ${e.toString()}');
    }
  }

  // ==================== إدارة المواقع ====================

  /// الحصول على مواقع مخزن
  Future<Result<List<WarehouseLocation>>> getWarehouseLocations(
    int warehouseId,
  ) async {
    try {
      final locations = await _locationDao.getLocationsByWarehouse(warehouseId);
      return Result.success(locations);
    } catch (e) {
      return Result.error('خطأ في جلب مواقع المخزن: ${e.toString()}');
    }
  }

  /// إضافة موقع جديد
  Future<Result<int>> addLocation(WarehouseLocation location) async {
    try {
      // التحقق من عدم تكرار الكود
      final codeExists = await _locationDao.isLocationCodeExists(
        location.code,
        location.warehouseId,
      );
      if (codeExists) {
        return Result.error('كود الموقع موجود مسبقاً في هذا المخزن');
      }

      final id = await _locationDao.insertLocation(location);
      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إضافة الموقع: ${e.toString()}');
    }
  }

  /// تحديث موقع
  Future<Result<bool>> updateLocation(WarehouseLocation location) async {
    try {
      // التحقق من عدم تكرار الكود
      final codeExists = await _locationDao.isLocationCodeExists(
        location.code,
        location.warehouseId,
        excludeId: location.id,
      );
      if (codeExists) {
        return Result.error('كود الموقع موجود مسبقاً في هذا المخزن');
      }

      final rowsAffected = await _locationDao.updateLocation(location);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث الموقع: ${e.toString()}');
    }
  }

  /// حذف موقع
  Future<Result<bool>> deleteLocation(int id) async {
    try {
      final rowsAffected = await _locationDao.deleteLocation(id);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في حذف الموقع: ${e.toString()}');
    }
  }

  // ==================== إدارة حركات المخزون ====================

  /// إضافة حركة مخزون
  Future<Result<int>> addStockMovement(StockMovement movement) async {
    try {
      final id = await _movementDao.insertMovement(movement);

      // تحديث الرصيد إذا كانت الحركة معتمدة
      if (movement.status == MovementStatus.approved ||
          movement.status == MovementStatus.completed) {
        await _balanceDao.updateBalanceAfterMovement(movement);
      }

      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في إضافة حركة المخزون: ${e.toString()}');
    }
  }

  /// اعتماد حركة مخزون
  Future<Result<bool>> approveMovement(int movementId, int approvedBy) async {
    try {
      final movement = await _movementDao.getMovementById(movementId);
      if (movement == null) {
        return Result.error('الحركة غير موجودة');
      }

      if (movement.status != MovementStatus.pending) {
        return Result.error('لا يمكن اعتماد هذه الحركة');
      }

      // اعتماد الحركة
      await _movementDao.approveMovement(movementId, approvedBy);

      // تحديث الرصيد
      await _balanceDao.updateBalanceAfterMovement(movement);

      return Result.success(true);
    } catch (e) {
      return Result.error('خطأ في اعتماد الحركة: ${e.toString()}');
    }
  }

  /// رفض حركة مخزون
  Future<Result<bool>> rejectMovement(int movementId, int rejectedBy) async {
    try {
      final rowsAffected = await _movementDao.rejectMovement(
        movementId,
        rejectedBy,
      );
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في رفض الحركة: ${e.toString()}');
    }
  }

  /// نقل مخزون بين المخازن
  Future<Result<int>> transferStock({
    required int itemId,
    required int fromWarehouseId,
    required int toWarehouseId,
    int? fromLocationId,
    int? toLocationId,
    required double quantity,
    required String notes,
    required int userId,
  }) async {
    try {
      // التحقق من وجود رصيد كافي
      final balance = await _balanceDao.getBalance(
        itemId,
        fromWarehouseId,
        locationId: fromLocationId,
      );

      if (balance == null || balance.quantity < quantity) {
        return Result.error('رصيد غير كافي للنقل');
      }

      // إنشاء رقم مستند
      final documentNumber = await _movementDao.getNextDocumentNumber('TR');

      // إنشاء حركة النقل
      final movement = StockMovement(
        documentNumber: documentNumber,
        type: MovementType.transfer,
        status: MovementStatus.approved,
        reason: MovementReason.transfer,
        itemId: itemId,
        warehouseId: fromWarehouseId,
        locationId: fromLocationId,
        toWarehouseId: toWarehouseId,
        toLocationId: toLocationId,
        quantity: quantity,
        unitCost: balance.averageCost,
        totalCost: quantity * balance.averageCost,
        notes: notes,
        userId: userId,
        approvedBy: userId,
        approvedAt: DateTime.now(),
      );

      final id = await _movementDao.insertMovement(movement);

      // تحديث الأرصدة
      await _balanceDao.updateBalanceAfterMovement(movement);

      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في نقل المخزون: ${e.toString()}');
    }
  }

  /// تسوية مخزون
  Future<Result<int>> adjustStock({
    required int itemId,
    required int warehouseId,
    int? locationId,
    required double newQuantity,
    required String reason,
    required int userId,
  }) async {
    try {
      // الحصول على الرصيد الحالي
      final balance = await _balanceDao.getBalance(
        itemId,
        warehouseId,
        locationId: locationId,
      );

      final currentQuantity = balance?.quantity ?? 0.0;
      final adjustmentQuantity = newQuantity - currentQuantity;

      if (adjustmentQuantity == 0) {
        return Result.error('لا توجد حاجة للتسوية');
      }

      // إنشاء رقم مستند
      final documentNumber = await _movementDao.getNextDocumentNumber('ADJ');

      // إنشاء حركة التسوية
      final movement = StockMovement(
        documentNumber: documentNumber,
        type: MovementType.adjustment,
        status: MovementStatus.approved,
        reason: MovementReason.adjustment,
        itemId: itemId,
        warehouseId: warehouseId,
        locationId: locationId,
        quantity: adjustmentQuantity.abs(),
        unitCost: balance?.averageCost ?? 0.0,
        totalCost: adjustmentQuantity.abs() * (balance?.averageCost ?? 0.0),
        notes: reason,
        userId: userId,
        approvedBy: userId,
        approvedAt: DateTime.now(),
      );

      final id = await _movementDao.insertMovement(movement);

      // تحديث الرصيد
      await _balanceDao.updateBalanceAfterMovement(movement);

      return Result.success(id);
    } catch (e) {
      return Result.error('خطأ في تسوية المخزون: ${e.toString()}');
    }
  }

  // ==================== التقارير والإحصائيات ====================

  /// الحصول على إحصائيات المخازن
  Future<Result<Map<String, dynamic>>> getWarehouseStatistics() async {
    try {
      final stats = await _warehouseDao.getWarehouseStatistics();
      return Result.success(stats);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات المخازن: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات المخزون
  Future<Result<Map<String, dynamic>>> getStockStatistics() async {
    try {
      final stats = await _balanceDao.getStockStatistics();
      return Result.success(stats);
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات المخزون: ${e.toString()}');
    }
  }

  /// الحصول على الأصناف منخفضة المخزون
  Future<Result<List<Map<String, dynamic>>>> getLowStockItems() async {
    try {
      final items = await _balanceDao.getLowStockItems();
      return Result.success(items);
    } catch (e) {
      return Result.error('خطأ في جلب الأصناف منخفضة المخزون: ${e.toString()}');
    }
  }

  /// الحصول على تقرير أعمار المخزون
  Future<Result<List<Map<String, dynamic>>>> getStockAgeReport() async {
    try {
      final report = await _balanceDao.getStockAgeReport();
      return Result.success(report);
    } catch (e) {
      return Result.error('خطأ في جلب تقرير أعمار المخزون: ${e.toString()}');
    }
  }

  /// الحصول على حركات المخزون مع فلترة
  Future<Result<List<StockMovement>>> getMovements({
    int? warehouseId,
    int? itemId,
    MovementType? type,
    MovementStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      List<StockMovement> movements;

      // إذا كان هناك فترة زمنية محددة
      if (startDate != null && endDate != null) {
        movements = await _movementDao.getMovementsByDateRange(
          startDate,
          endDate,
          itemId: itemId,
          warehouseId: warehouseId,
        );
      }
      // إذا كان هناك مخزن محدد
      else if (warehouseId != null) {
        movements = await _movementDao.getMovementsByWarehouse(warehouseId);
      }
      // إذا كان هناك صنف محدد
      else if (itemId != null) {
        movements = await _movementDao.getMovementsByItem(itemId);
      }
      // إذا كان هناك نوع محدد
      else if (type != null) {
        movements = await _movementDao.getMovementsByType(type);
      }
      // إذا كان هناك حالة محددة
      else if (status != null) {
        movements = await _movementDao.getMovementsByStatus(status);
      }
      // جلب جميع الحركات
      else {
        movements = await _movementDao.getAllMovements();
      }

      // فلترة إضافية حسب النوع والحالة إذا لم تكن مطبقة
      if (type != null && startDate != null) {
        movements = movements.where((m) => m.type == type).toList();
      }
      if (status != null && startDate != null) {
        movements = movements.where((m) => m.status == status).toList();
      }

      return Result.success(movements);
    } catch (e) {
      return Result.error('خطأ في جلب حركات المخزون: ${e.toString()}');
    }
  }

  /// الحصول على أرصدة المخزون مع فلترة
  Future<Result<List<StockBalance>>> getStockBalances({
    int? warehouseId,
    int? itemId,
    bool includeZeroBalances = false,
    bool lowStockOnly = false,
  }) async {
    try {
      List<StockBalance> balances;

      // إذا كان هناك مخزن محدد
      if (warehouseId != null) {
        balances = await _balanceDao.getBalancesByWarehouse(warehouseId);
      }
      // إذا كان هناك صنف محدد
      else if (itemId != null) {
        balances = await _balanceDao.getBalancesByItem(itemId);
      }
      // جلب جميع الأرصدة
      else {
        balances = await _balanceDao.getAllBalances();
      }

      // فلترة الأرصدة الصفرية
      if (!includeZeroBalances) {
        balances = balances.where((balance) => balance.quantity > 0).toList();
      }

      // فلترة المخزون المنخفض
      if (lowStockOnly) {
        // هنا نحتاج للحصول على معلومات الأصناف للتحقق من الحد الأدنى
        // سنتركها للتطبيق في الواجهة
      }

      return Result.success(balances);
    } catch (e) {
      return Result.error('خطأ في جلب أرصدة المخزون: ${e.toString()}');
    }
  }

  /// تحديث حركة مخزون
  Future<Result<bool>> updateStockMovement(StockMovement movement) async {
    try {
      final rowsAffected = await _movementDao.updateMovement(movement);
      return Result.success(rowsAffected > 0);
    } catch (e) {
      return Result.error('خطأ في تحديث حركة المخزون: ${e.toString()}');
    }
  }
}
