/// نموذج استهلاك الأصول الثابتة
/// Asset Depreciation Model for Smart Ledger
library;

class AssetDepreciation {
  final int? id;
  final int assetId;
  final int year;
  final int month;
  final DateTime depreciationDate;
  final double depreciationAmount;
  final double accumulatedDepreciation;
  final double bookValue;
  final String? notes;
  final int? journalEntryId; // القيد المحاسبي المرتبط
  final bool isPosted; // هل تم ترحيل القيد
  final DateTime createdAt;
  final DateTime updatedAt;

  const AssetDepreciation({
    this.id,
    required this.assetId,
    required this.year,
    required this.month,
    required this.depreciationDate,
    required this.depreciationAmount,
    required this.accumulatedDepreciation,
    required this.bookValue,
    this.notes,
    this.journalEntryId,
    this.isPosted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء نسخة من الكائن مع تعديل بعض الخصائص
  AssetDepreciation copyWith({
    int? id,
    int? assetId,
    int? year,
    int? month,
    DateTime? depreciationDate,
    double? depreciationAmount,
    double? accumulatedDepreciation,
    double? bookValue,
    String? notes,
    int? journalEntryId,
    bool? isPosted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AssetDepreciation(
      id: id ?? this.id,
      assetId: assetId ?? this.assetId,
      year: year ?? this.year,
      month: month ?? this.month,
      depreciationDate: depreciationDate ?? this.depreciationDate,
      depreciationAmount: depreciationAmount ?? this.depreciationAmount,
      accumulatedDepreciation:
          accumulatedDepreciation ?? this.accumulatedDepreciation,
      bookValue: bookValue ?? this.bookValue,
      notes: notes ?? this.notes,
      journalEntryId: journalEntryId ?? this.journalEntryId,
      isPosted: isPosted ?? this.isPosted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'asset_id': assetId,
      'year': year,
      'month': month,
      'depreciation_date': depreciationDate.toIso8601String(),
      'depreciation_amount': depreciationAmount,
      'accumulated_depreciation': accumulatedDepreciation,
      'book_value': bookValue,
      'notes': notes,
      'journal_entry_id': journalEntryId,
      'is_posted': isPosted ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء كائن من Map
  factory AssetDepreciation.fromMap(Map<String, dynamic> map) {
    return AssetDepreciation(
      id: map['id']?.toInt(),
      assetId: map['asset_id']?.toInt() ?? 0,
      year: map['year']?.toInt() ?? 0,
      month: map['month']?.toInt() ?? 0,
      depreciationDate: DateTime.parse(map['depreciation_date']),
      depreciationAmount: map['depreciation_amount']?.toDouble() ?? 0.0,
      accumulatedDepreciation:
          map['accumulated_depreciation']?.toDouble() ?? 0.0,
      bookValue: map['book_value']?.toDouble() ?? 0.0,
      notes: map['notes'],
      journalEntryId: map['journal_entry_id']?.toInt(),
      isPosted: (map['is_posted'] ?? 0) == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  @override
  String toString() {
    return 'AssetDepreciation(id: $id, assetId: $assetId, year: $year, month: $month, amount: $depreciationAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssetDepreciation &&
        other.id == id &&
        other.assetId == assetId &&
        other.year == year &&
        other.month == month;
  }

  @override
  int get hashCode =>
      id.hashCode ^ assetId.hashCode ^ year.hashCode ^ month.hashCode;
}

/// نموذج تقرير استهلاك الأصول
class AssetDepreciationReport {
  final int assetId;
  final String assetCode;
  final String assetName;
  final double purchasePrice;
  final double salvageValue;
  final double totalDepreciation;
  final double bookValue;
  final List<AssetDepreciation> depreciationSchedule;

  const AssetDepreciationReport({
    required this.assetId,
    required this.assetCode,
    required this.assetName,
    required this.purchasePrice,
    required this.salvageValue,
    required this.totalDepreciation,
    required this.bookValue,
    required this.depreciationSchedule,
  });

  /// إنشاء كائن من Map
  factory AssetDepreciationReport.fromMap(Map<String, dynamic> map) {
    return AssetDepreciationReport(
      assetId: map['asset_id']?.toInt() ?? 0,
      assetCode: map['asset_code'] ?? '',
      assetName: map['asset_name'] ?? '',
      purchasePrice: map['purchase_price']?.toDouble() ?? 0.0,
      salvageValue: map['salvage_value']?.toDouble() ?? 0.0,
      totalDepreciation: map['total_depreciation']?.toDouble() ?? 0.0,
      bookValue: map['book_value']?.toDouble() ?? 0.0,
      depreciationSchedule:
          (map['depreciation_schedule'] as List<dynamic>?)
              ?.map(
                (item) =>
                    AssetDepreciation.fromMap(item as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'asset_id': assetId,
      'asset_code': assetCode,
      'asset_name': assetName,
      'purchase_price': purchasePrice,
      'salvage_value': salvageValue,
      'total_depreciation': totalDepreciation,
      'book_value': bookValue,
      'depreciation_schedule': depreciationSchedule
          .map((item) => item.toMap())
          .toList(),
    };
  }
}

/// نموذج ملخص الأصول الثابتة
class FixedAssetsSummary {
  final int totalAssets;
  final double totalPurchaseValue;
  final double totalDepreciation;
  final double totalBookValue;
  final Map<String, int> assetsByCategory;
  final Map<String, double> valueByCategory;

  const FixedAssetsSummary({
    required this.totalAssets,
    required this.totalPurchaseValue,
    required this.totalDepreciation,
    required this.totalBookValue,
    required this.assetsByCategory,
    required this.valueByCategory,
  });

  /// إنشاء كائن من Map
  factory FixedAssetsSummary.fromMap(Map<String, dynamic> map) {
    return FixedAssetsSummary(
      totalAssets: map['total_assets']?.toInt() ?? 0,
      totalPurchaseValue: map['total_purchase_value']?.toDouble() ?? 0.0,
      totalDepreciation: map['total_depreciation']?.toDouble() ?? 0.0,
      totalBookValue: map['total_book_value']?.toDouble() ?? 0.0,
      assetsByCategory: Map<String, int>.from(map['assets_by_category'] ?? {}),
      valueByCategory: Map<String, double>.from(map['value_by_category'] ?? {}),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'total_assets': totalAssets,
      'total_purchase_value': totalPurchaseValue,
      'total_depreciation': totalDepreciation,
      'total_book_value': totalBookValue,
      'assets_by_category': assetsByCategory,
      'value_by_category': valueByCategory,
    };
  }
}
