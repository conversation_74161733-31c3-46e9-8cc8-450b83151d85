import 'employee.dart';


/// نموذج سجل الراتب
/// Payroll Record Model
class PayrollRecord {
  final int? id;
  final String employeeCode;
  final int payrollPeriodId;
  final DateTime payrollDate;
  final double basicSalary;
  final double totalAllowances;
  final double totalDeductions;
  final double grossSalary;
  final double netSalary;
  final double taxAmount;
  final double insuranceAmount;
  final int workingDays;
  final int actualWorkingDays;
  final double overtimeHours;
  final double overtimeAmount;
  final PayrollStatus status;
  final String? notes;
  final DateTime? paidDate;
  final String? paidBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  Employee? employee;
  PayrollPeriod? payrollPeriod;
  List<PayrollAllowanceDetail> allowanceDetails = [];
  List<PayrollDeductionDetail> deductionDetails = [];

  PayrollRecord({
    this.id,
    required this.employeeCode,
    required this.payrollPeriodId,
    required this.payrollDate,
    required this.basicSalary,
    required this.totalAllowances,
    required this.totalDeductions,
    required this.grossSalary,
    required this.netSalary,
    required this.taxAmount,
    required this.insuranceAmount,
    required this.workingDays,
    required this.actualWorkingDays,
    this.overtimeHours = 0.0,
    this.overtimeAmount = 0.0,
    this.status = PayrollStatus.draft,
    this.notes,
    this.paidDate,
    this.paidBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// نسبة الحضور
  double get attendancePercentage {
    if (workingDays == 0) return 0.0;
    return (actualWorkingDays / workingDays) * 100;
  }

  /// الراتب المحسوب حسب أيام الحضور
  double get proRatedSalary {
    if (workingDays == 0) return basicSalary;
    return (basicSalary / workingDays) * actualWorkingDays;
  }

  /// Factory constructor from database map
  factory PayrollRecord.fromMap(Map<String, dynamic> map) {
    return PayrollRecord(
      id: map['id'] as int?,
      employeeCode: map['employee_code'] as String,
      payrollPeriodId: map['payroll_period_id'] as int,
      payrollDate: DateTime.parse(map['payroll_date'] as String),
      basicSalary: (map['basic_salary'] as num).toDouble(),
      totalAllowances: (map['total_allowances'] as num).toDouble(),
      totalDeductions: (map['total_deductions'] as num).toDouble(),
      grossSalary: (map['gross_salary'] as num).toDouble(),
      netSalary: (map['net_salary'] as num).toDouble(),
      taxAmount: (map['tax_amount'] as num).toDouble(),
      insuranceAmount: (map['insurance_amount'] as num).toDouble(),
      workingDays: map['working_days'] as int,
      actualWorkingDays: map['actual_working_days'] as int,
      overtimeHours: (map['overtime_hours'] as num?)?.toDouble() ?? 0.0,
      overtimeAmount: (map['overtime_amount'] as num?)?.toDouble() ?? 0.0,
      status: PayrollStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => PayrollStatus.draft,
      ),
      notes: map['notes'] as String?,
      paidDate: map['paid_date'] != null 
          ? DateTime.parse(map['paid_date'] as String)
          : null,
      paidBy: map['paid_by'] as String?,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_code': employeeCode,
      'payroll_period_id': payrollPeriodId,
      'payroll_date': payrollDate.toIso8601String(),
      'basic_salary': basicSalary,
      'total_allowances': totalAllowances,
      'total_deductions': totalDeductions,
      'gross_salary': grossSalary,
      'net_salary': netSalary,
      'tax_amount': taxAmount,
      'insurance_amount': insuranceAmount,
      'working_days': workingDays,
      'actual_working_days': actualWorkingDays,
      'overtime_hours': overtimeHours,
      'overtime_amount': overtimeAmount,
      'status': status.name,
      'notes': notes,
      'paid_date': paidDate?.toIso8601String(),
      'paid_by': paidBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  PayrollRecord copyWith({
    int? id,
    String? employeeCode,
    int? payrollPeriodId,
    DateTime? payrollDate,
    double? basicSalary,
    double? totalAllowances,
    double? totalDeductions,
    double? grossSalary,
    double? netSalary,
    double? taxAmount,
    double? insuranceAmount,
    int? workingDays,
    int? actualWorkingDays,
    double? overtimeHours,
    double? overtimeAmount,
    PayrollStatus? status,
    String? notes,
    DateTime? paidDate,
    String? paidBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PayrollRecord(
      id: id ?? this.id,
      employeeCode: employeeCode ?? this.employeeCode,
      payrollPeriodId: payrollPeriodId ?? this.payrollPeriodId,
      payrollDate: payrollDate ?? this.payrollDate,
      basicSalary: basicSalary ?? this.basicSalary,
      totalAllowances: totalAllowances ?? this.totalAllowances,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      grossSalary: grossSalary ?? this.grossSalary,
      netSalary: netSalary ?? this.netSalary,
      taxAmount: taxAmount ?? this.taxAmount,
      insuranceAmount: insuranceAmount ?? this.insuranceAmount,
      workingDays: workingDays ?? this.workingDays,
      actualWorkingDays: actualWorkingDays ?? this.actualWorkingDays,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      overtimeAmount: overtimeAmount ?? this.overtimeAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      paidDate: paidDate ?? this.paidDate,
      paidBy: paidBy ?? this.paidBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج فترة الراتب
/// Payroll Period Model
class PayrollPeriod {
  final int? id;
  final String name;
  final DateTime startDate;
  final DateTime endDate;
  final int workingDays;
  final PayrollPeriodStatus status;
  final DateTime? processedDate;
  final String? processedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  // علاقات
  List<PayrollRecord> payrollRecords = [];

  PayrollPeriod({
    this.id,
    required this.name,
    required this.startDate,
    required this.endDate,
    required this.workingDays,
    this.status = PayrollPeriodStatus.open,
    this.processedDate,
    this.processedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// عدد الأيام في الفترة
  int get totalDays {
    return endDate.difference(startDate).inDays + 1;
  }

  /// إجمالي الرواتب في الفترة
  double get totalPayroll {
    return payrollRecords.fold(0.0, (sum, record) => sum + record.netSalary);
  }

  /// عدد الموظفين في الفترة
  int get employeeCount => payrollRecords.length;

  /// Factory constructor from database map
  factory PayrollPeriod.fromMap(Map<String, dynamic> map) {
    return PayrollPeriod(
      id: map['id'] as int?,
      name: map['name'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      workingDays: map['working_days'] as int,
      status: PayrollPeriodStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => PayrollPeriodStatus.open,
      ),
      processedDate: map['processed_date'] != null 
          ? DateTime.parse(map['processed_date'] as String)
          : null,
      processedBy: map['processed_by'] as String?,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'working_days': workingDays,
      'status': status.name,
      'processed_date': processedDate?.toIso8601String(),
      'processed_by': processedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  PayrollPeriod copyWith({
    int? id,
    String? name,
    DateTime? startDate,
    DateTime? endDate,
    int? workingDays,
    PayrollPeriodStatus? status,
    DateTime? processedDate,
    String? processedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PayrollPeriod(
      id: id ?? this.id,
      name: name ?? this.name,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      workingDays: workingDays ?? this.workingDays,
      status: status ?? this.status,
      processedDate: processedDate ?? this.processedDate,
      processedBy: processedBy ?? this.processedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// تفاصيل بدل في سجل الراتب
class PayrollAllowanceDetail {
  final int? id;
  final int payrollRecordId;
  final int allowanceId;
  final double amount;
  final String? notes;

  PayrollAllowanceDetail({
    this.id,
    required this.payrollRecordId,
    required this.allowanceId,
    required this.amount,
    this.notes,
  });

  factory PayrollAllowanceDetail.fromMap(Map<String, dynamic> map) {
    return PayrollAllowanceDetail(
      id: map['id'] as int?,
      payrollRecordId: map['payroll_record_id'] as int,
      allowanceId: map['allowance_id'] as int,
      amount: (map['amount'] as num).toDouble(),
      notes: map['notes'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'payroll_record_id': payrollRecordId,
      'allowance_id': allowanceId,
      'amount': amount,
      'notes': notes,
    };
  }
}

/// تفاصيل استقطاع في سجل الراتب
class PayrollDeductionDetail {
  final int? id;
  final int payrollRecordId;
  final int deductionId;
  final double amount;
  final String? notes;

  PayrollDeductionDetail({
    this.id,
    required this.payrollRecordId,
    required this.deductionId,
    required this.amount,
    this.notes,
  });

  factory PayrollDeductionDetail.fromMap(Map<String, dynamic> map) {
    return PayrollDeductionDetail(
      id: map['id'] as int?,
      payrollRecordId: map['payroll_record_id'] as int,
      deductionId: map['deduction_id'] as int,
      amount: (map['amount'] as num).toDouble(),
      notes: map['notes'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'payroll_record_id': payrollRecordId,
      'deduction_id': deductionId,
      'amount': amount,
      'notes': notes,
    };
  }
}

/// تعدادات حالة الراتب
enum PayrollStatus {
  draft('مسودة'),
  calculated('محسوب'),
  approved('معتمد'),
  paid('مدفوع'),
  cancelled('ملغي');

  const PayrollStatus(this.displayName);
  final String displayName;
}

/// تعدادات حالة فترة الراتب
enum PayrollPeriodStatus {
  open('مفتوحة'),
  processing('قيد المعالجة'),
  processed('معالجة'),
  closed('مغلقة');

  const PayrollPeriodStatus(this.displayName);
  final String displayName;
}
