# نظام الفواتير المتقدم - Smart Ledger

## نظرة عامة

تم تطوير نظام الفواتير المتقدم في Smart Ledger ليوفر حلولاً شاملة لإدارة الفواتير مع التكامل التلقائي مع النظام المحاسبي والمخزون والضرائب.

## الميزات الرئيسية

### 1. خدمة الفواتير المتقدمة (AdvancedInvoiceService)

#### الوظائف الأساسية:
- **إنشاء فاتورة مع التكامل الكامل**: `createInvoiceWithIntegration()`
- **إلغاء فاتورة مع العكس التلقائي**: `cancelInvoiceWithReversal()`
- **التحقق من صحة البيانات**: التحقق الشامل من بيانات الفاتورة
- **إدارة المعاملات**: ضمان سلامة البيانات عبر جميع العمليات

#### التكامل التلقائي:
1. **النظام المحاسبي**: إنشاء قيود يومية تلقائية
2. **إدارة المخزون**: تحديث حركات المخزون تلقائياً
3. **نظام الضرائب**: حساب الضرائب المتقدم
4. **إدارة العملاء/الموردين**: ربط تلقائي مع بيانات العملاء والموردين

### 2. نظام الضرائب المحسن (Enhanced TaxService)

#### طرق حساب الضرائب المتقدمة:
- **`calculateTaxForAmount()`**: حساب الضريبة لمبلغ محدد
- **`calculateVATAdvanced()`**: حساب ضريبة القيمة المضافة المتقدم
- **`calculateWithholdingTax()`**: حساب ضريبة الاستقطاع
- **`calculateCompoundTaxes()`**: حساب الضرائب المركبة
- **`calculateIncomeTaxWithholding()`**: حساب ضريبة الدخل التصاعدية

#### أنواع الضرائب المدعومة:
- ضريبة القيمة المضافة (VAT)
- ضريبة الاستقطاع (Withholding Tax)
- ضريبة الدخل (Income Tax)
- ضريبة الشركات (Corporate Tax)
- الضرائب المركبة (Compound Taxes)

### 3. خدمة PDF المتقدمة (Enhanced PdfService)

#### ميزات PDF المتقدمة:
- **`generateAdvancedInvoicePdf()`**: توليد PDF متقدم للفواتير
- **دعم اللغة العربية**: تخطيط RTL كامل
- **تخصيص التصميم**: شعار الشركة، العلامة المائية، التوقيعات
- **QR Code**: إضافة رموز QR للفواتير
- **تفاصيل شاملة**: جداول متقدمة مع تفاصيل الضرائب

#### خيارات التخصيص:
- إضافة شعار الشركة
- العلامة المائية المخصصة
- قسم التوقيعات
- رموز QR للتحقق
- تخطيط متجاوب

### 4. واجهة المستخدم المتقدمة (AdvancedInvoiceScreen)

#### مكونات الواجهة:
- **رأس الفاتورة**: معلومات أساسية مع اختيار التواريخ
- **قسم العميل/المورد**: اختيار ديناميكي حسب نوع الفاتورة
- **إدارة البنود**: إضافة/حذف/تعديل بنود الفاتورة
- **حساب الإجماليات**: حساب تلقائي للضرائب والخصومات
- **الملاحظات**: قسم للملاحظات الإضافية

#### الميزات التفاعلية:
- حساب تلقائي للإجماليات
- اختيار الأصناف من القائمة
- تحديث الأسعار تلقائياً
- معاينة الضرائب في الوقت الفعلي
- التحقق من صحة البيانات

## التدفق التقني

### 1. إنشاء فاتورة جديدة

```dart
// إنشاء فاتورة مع التكامل الكامل
final result = await advancedInvoiceService.createInvoiceWithIntegration(invoice);

if (result.isSuccess) {
  // تم إنشاء الفاتورة بنجاح مع:
  // - قيد يومي تلقائي
  // - تحديث المخزون
  // - حساب الضرائب
  // - ربط العميل/المورد
}
```

### 2. حساب الضرائب المتقدم

```dart
// حساب ضريبة القيمة المضافة
final vatResult = await taxService.calculateVATAdvanced(
  amount: 1000.0,
  vatRate: 15.0,
  isInclusive: false,
);

// حساب ضريبة الاستقطاع
final withholdingResult = await taxService.calculateWithholdingTax(
  amount: 5000.0,
  withholdingRate: 5.0,
  taxType: TaxType.withholdingTax,
);
```

### 3. توليد PDF متقدم

```dart
// توليد PDF مع خيارات متقدمة
final pdfResult = await pdfService.generateAdvancedInvoicePdf(
  invoice: invoice,
  includeQRCode: true,
  includeWatermark: true,
  watermarkText: 'نسخة أصلية',
  includeSignature: true,
  logoPath: 'assets/images/company_logo.png',
);
```

## هيكل قاعدة البيانات

### جداول الفواتير:
- **invoices**: الجدول الرئيسي للفواتير
- **invoice_lines**: بنود الفواتير
- **invoice_payments**: مدفوعات الفواتير

### التكامل مع الجداول الأخرى:
- **journal_entries**: القيود اليومية
- **stock_movements**: حركات المخزون
- **customers/suppliers**: العملاء والموردين
- **items**: الأصناف
- **taxes**: الضرائب

## الاختبارات

### اختبارات الوحدة:
- اختبار إنشاء الفواتير
- اختبار حساب الضرائب
- اختبار التكامل مع النظام المحاسبي
- اختبار توليد PDF

### اختبارات التكامل:
- اختبار التدفق الكامل للفاتورة
- اختبار العكس والإلغاء
- اختبار الأداء مع البيانات الكبيرة

## الأمان والتحقق

### التحقق من البيانات:
- التحقق من صحة أرقام الفواتير
- التحقق من وجود العملاء/الموردين
- التحقق من توفر المخزون
- التحقق من صحة المبالغ

### إدارة الأخطاء:
- معالجة شاملة للأخطاء
- رسائل خطأ واضحة باللغة العربية
- آلية العكس التلقائي عند الفشل
- تسجيل مفصل للعمليات

## الأداء والتحسين

### تحسينات الأداء:
- استخدام المعاملات لضمان سلامة البيانات
- فهرسة محسنة لجداول قاعدة البيانات
- تحميل البيانات بشكل تدريجي
- ذاكرة تخزين مؤقت للبيانات المتكررة

### قابلية التوسع:
- هيكل مرن يدعم أنواع فواتير جديدة
- نظام ضرائب قابل للتخصيص
- واجهات برمجة قابلة للتوسع
- دعم العملات المتعددة (جاهز للتطوير)

## الاستخدام والتطبيق

### للمطورين:
1. استيراد الخدمات المطلوبة
2. إنشاء كائن الفاتورة
3. استدعاء `createInvoiceWithIntegration()`
4. معالجة النتيجة

### للمستخدمين:
1. فتح شاشة الفاتورة المتقدمة
2. إدخال البيانات الأساسية
3. إضافة البنود
4. مراجعة الإجماليات
5. حفظ الفاتورة

## التطوير المستقبلي

### ميزات مخططة:
- دعم العملات المتعددة
- فواتير متكررة
- قوالب فواتير مخصصة
- تكامل مع أنظمة الدفع الإلكتروني
- تقارير تحليلية متقدمة

### تحسينات مقترحة:
- واجهة مستخدم محسنة
- دعم الملفات المرفقة
- نظام الموافقات
- تتبع تاريخ التعديلات
- تصدير بصيغ متعددة
