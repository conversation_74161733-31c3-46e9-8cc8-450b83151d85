/// Data Access Object لحركات المخزون
/// Stock Movement DAO for Smart Ledger
library;

import 'dart:developer' as developer;
import '../models/stock_movement.dart';
import 'database_helper.dart';

class StockMovementDao {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع حركات المخزون
  Future<List<StockMovement>> getAllMovements({int? limit, int? offset}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      orderBy: 'movement_date DESC, created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على حركات صنف معين
  Future<List<StockMovement>> getMovementsByItem(
    int itemId, {
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'item_id = ?',
      whereArgs: [itemId],
      orderBy: 'movement_date DESC, created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على حركات مخزن معين
  Future<List<StockMovement>> getMovementsByWarehouse(
    int warehouseId, {
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'warehouse_id = ? OR to_warehouse_id = ?',
      whereArgs: [warehouseId, warehouseId],
      orderBy: 'movement_date DESC, created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على حركة بالمعرف
  Future<StockMovement?> getMovementById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return StockMovement.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة حركة جديدة
  Future<int> insertMovement(StockMovement movement) async {
    final db = await _databaseHelper.database;
    return await db.insert('stock_movements', movement.toMap());
  }

  /// تحديث حركة
  Future<int> updateMovement(StockMovement movement) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'stock_movements',
      movement.toMap(),
      where: 'id = ?',
      whereArgs: [movement.id],
    );
  }

  /// حذف حركة
  Future<int> deleteMovement(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete('stock_movements', where: 'id = ?', whereArgs: [id]);
  }

  /// البحث في الحركات
  Future<List<StockMovement>> searchMovements(String searchTerm) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT sm.* 
      FROM stock_movements sm
      LEFT JOIN items i ON sm.item_id = i.id
      LEFT JOIN warehouses w ON sm.warehouse_id = w.id
      WHERE sm.document_number LIKE ? 
         OR sm.notes LIKE ?
         OR sm.reference_document LIKE ?
         OR i.name LIKE ?
         OR i.code LIKE ?
         OR w.name LIKE ?
      ORDER BY sm.movement_date DESC, sm.created_at DESC
    ''',
      [
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
        '%$searchTerm%',
      ],
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على الحركات حسب النوع
  Future<List<StockMovement>> getMovementsByType(
    MovementType type, {
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'type = ?',
      whereArgs: [type.name],
      orderBy: 'movement_date DESC, created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على الحركات حسب الحالة
  Future<List<StockMovement>> getMovementsByStatus(
    MovementStatus status, {
    int? limit,
    int? offset,
  }) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'movement_date DESC, created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على الحركات المعلقة
  Future<List<StockMovement>> getPendingMovements() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'status = ?',
      whereArgs: [MovementStatus.pending.name],
      orderBy: 'movement_date ASC, created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على الحركات في فترة زمنية
  Future<List<StockMovement>> getMovementsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    int? itemId,
    int? warehouseId,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = 'movement_date >= ? AND movement_date <= ?';
    List<dynamic> whereArgs = [
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ];

    if (itemId != null) {
      whereClause += ' AND item_id = ?';
      whereArgs.add(itemId);
    }

    if (warehouseId != null) {
      whereClause += ' AND (warehouse_id = ? OR to_warehouse_id = ?)';
      whereArgs.addAll([warehouseId, warehouseId]);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'movement_date DESC, created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على إحصائيات الحركات
  Future<Map<String, dynamic>> getMovementStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = '1=1';
    List<dynamic> whereArgs = [];

    if (startDate != null && endDate != null) {
      whereClause += ' AND movement_date >= ? AND movement_date <= ?';
      whereArgs.addAll([
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ]);
    }

    // إجمالي عدد الحركات
    final totalResult = await db.rawQuery(
      'SELECT COUNT(*) as total FROM stock_movements WHERE $whereClause',
      whereArgs,
    );
    final total = totalResult.first['total'] as int;

    // الحركات حسب النوع
    final typeStats = await db.rawQuery('''
      SELECT type, COUNT(*) as count, SUM(total_cost) as total_value
      FROM stock_movements 
      WHERE $whereClause
      GROUP BY type
      ORDER BY count DESC
    ''', whereArgs);

    // الحركات حسب الحالة
    final statusStats = await db.rawQuery('''
      SELECT status, COUNT(*) as count
      FROM stock_movements 
      WHERE $whereClause
      GROUP BY status
      ORDER BY count DESC
    ''', whereArgs);

    // إجمالي القيمة
    final valueResult = await db.rawQuery(
      'SELECT SUM(total_cost) as total_value FROM stock_movements WHERE $whereClause',
      whereArgs,
    );
    final totalValue =
        (valueResult.first['total_value'] as num?)?.toDouble() ?? 0.0;

    return {
      'total': total,
      'total_value': totalValue,
      'by_type': typeStats,
      'by_status': statusStats,
    };
  }

  /// اعتماد حركة
  Future<int> approveMovement(int id, int approvedBy) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'stock_movements',
      {
        'status': MovementStatus.approved.name,
        'approved_by': approvedBy,
        'approved_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// رفض حركة
  Future<int> rejectMovement(int id, int rejectedBy) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'stock_movements',
      {
        'status': MovementStatus.rejected.name,
        'approved_by': rejectedBy,
        'approved_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// إكمال حركة
  Future<int> completeMovement(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'stock_movements',
      {
        'status': MovementStatus.completed.name,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// إلغاء حركة
  Future<int> cancelMovement(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'stock_movements',
      {
        'status': MovementStatus.cancelled.name,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على آخر رقم مستند
  Future<String> getNextDocumentNumber(String prefix) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT document_number 
      FROM stock_movements 
      WHERE document_number LIKE ?
      ORDER BY document_number DESC 
      LIMIT 1
    ''',
      ['$prefix%'],
    );

    if (maps.isNotEmpty) {
      final lastNumber = maps.first['document_number'] as String;
      final numberPart = lastNumber.replaceFirst(prefix, '');
      final nextNumber = (int.tryParse(numberPart) ?? 0) + 1;
      return '$prefix${nextNumber.toString().padLeft(6, '0')}';
    } else {
      return '${prefix}000001';
    }
  }

  /// نسخ احتياطي للحركات
  Future<List<Map<String, dynamic>>> exportMovements({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = '1=1';
    List<dynamic> whereArgs = [];

    if (startDate != null && endDate != null) {
      whereClause += ' AND movement_date >= ? AND movement_date <= ?';
      whereArgs.addAll([
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ]);
    }

    return await db.query(
      'stock_movements',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'movement_date DESC',
    );
  }

  /// استيراد الحركات
  Future<int> importMovements(List<Map<String, dynamic>> movements) async {
    final db = await _databaseHelper.database;
    int importedCount = 0;

    await db.transaction((txn) async {
      for (final movementData in movements) {
        try {
          await txn.insert('stock_movements', movementData);
          importedCount++;
        } catch (e) {
          // تجاهل الأخطاء والمتابعة
          developer.log(
            'Error importing movement: $e',
            name: 'StockMovementDao',
          );
        }
      }
    });

    return importedCount;
  }

  /// الحصول على حركات الدفعة
  Future<List<StockMovement>> getMovementsByBatch(String batchNumber) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'batch_number = ?',
      whereArgs: [batchNumber],
      orderBy: 'movement_date DESC, created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }

  /// الحصول على حركات الرقم التسلسلي
  Future<List<StockMovement>> getMovementsBySerial(String serialNumber) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'stock_movements',
      where: 'serial_number = ?',
      whereArgs: [serialNumber],
      orderBy: 'movement_date DESC, created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return StockMovement.fromMap(maps[i]);
    });
  }
}
