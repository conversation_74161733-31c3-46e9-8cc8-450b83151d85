/// نموذج حركة المخزون
/// Stock Movement Model for Smart Ledger
library;

import 'warehouse.dart';
import 'item.dart';

enum MovementType {
  receipt, // استلام
  issue, // صرف
  transfer, // نقل
  adjustment, // تسوية
  return_, // مرتجع
  damage, // تالف
  loss, // فقدان
  found, // عثور
}

enum MovementStatus {
  pending, // معلق
  approved, // معتمد
  rejected, // مرفوض
  completed, // مكتمل
  cancelled, // ملغي
}

enum MovementReason {
  purchase, // شراء
  sale, // بيع
  production, // إنتاج
  consumption, // استهلاك
  transfer, // نقل
  adjustment, // تسوية
  return_, // مرتجع
  damage, // تلف
  theft, // سرقة
  expiry, // انتهاء صلاحية
  other, // أخرى
}

class StockMovement {
  final int? id;
  final String documentNumber;
  final MovementType type;
  final MovementStatus status;
  final MovementReason reason;
  final int itemId;
  final int warehouseId;
  final int? locationId;
  final int? toWarehouseId;
  final int? toLocationId;
  final double quantity;
  final double unitCost;
  final double totalCost;
  final String? batchNumber;
  final String? serialNumber;
  final DateTime? expiryDate;
  final String? notes;
  final String? referenceDocument;
  final int? userId;
  final int? approvedBy;
  final DateTime? approvedAt;
  final DateTime movementDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Navigation properties
  Item? item;
  Warehouse? warehouse;
  WarehouseLocation? location;
  Warehouse? toWarehouse;
  WarehouseLocation? toLocation;

  StockMovement({
    this.id,
    required this.documentNumber,
    required this.type,
    this.status = MovementStatus.pending,
    required this.reason,
    required this.itemId,
    required this.warehouseId,
    this.locationId,
    this.toWarehouseId,
    this.toLocationId,
    required this.quantity,
    this.unitCost = 0.0,
    this.totalCost = 0.0,
    this.batchNumber,
    this.serialNumber,
    this.expiryDate,
    this.notes,
    this.referenceDocument,
    this.userId,
    this.approvedBy,
    this.approvedAt,
    DateTime? movementDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.item,
    this.warehouse,
    this.location,
    this.toWarehouse,
    this.toLocation,
  }) : movementDate = movementDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Factory constructor from database map
  factory StockMovement.fromMap(Map<String, dynamic> map) {
    return StockMovement(
      id: map['id'] as int?,
      documentNumber: map['document_number'] as String,
      type: MovementType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MovementType.adjustment,
      ),
      status: MovementStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => MovementStatus.pending,
      ),
      reason: MovementReason.values.firstWhere(
        (e) => e.name == map['reason'],
        orElse: () => MovementReason.other,
      ),
      itemId: map['item_id'] as int,
      warehouseId: map['warehouse_id'] as int,
      locationId: map['location_id'] as int?,
      toWarehouseId: map['to_warehouse_id'] as int?,
      toLocationId: map['to_location_id'] as int?,
      quantity: (map['quantity'] as num).toDouble(),
      unitCost: (map['unit_cost'] as num?)?.toDouble() ?? 0.0,
      totalCost: (map['total_cost'] as num?)?.toDouble() ?? 0.0,
      batchNumber: map['batch_number'] as String?,
      serialNumber: map['serial_number'] as String?,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      notes: map['notes'] as String?,
      referenceDocument: map['reference_document'] as String?,
      userId: map['user_id'] as int?,
      approvedBy: map['approved_by'] as int?,
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      movementDate: DateTime.parse(map['movement_date'] as String),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'document_number': documentNumber,
      'type': type.name,
      'status': status.name,
      'reason': reason.name,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'to_warehouse_id': toWarehouseId,
      'to_location_id': toLocationId,
      'quantity': quantity,
      'unit_cost': unitCost,
      'total_cost': totalCost,
      'batch_number': batchNumber,
      'serial_number': serialNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      'notes': notes,
      'reference_document': referenceDocument,
      'user_id': userId,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'movement_date': movementDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Get movement type name in Arabic
  String get typeNameAr {
    switch (type) {
      case MovementType.receipt:
        return 'استلام';
      case MovementType.issue:
        return 'صرف';
      case MovementType.transfer:
        return 'نقل';
      case MovementType.adjustment:
        return 'تسوية';
      case MovementType.return_:
        return 'مرتجع';
      case MovementType.damage:
        return 'تالف';
      case MovementType.loss:
        return 'فقدان';
      case MovementType.found:
        return 'عثور';
    }
  }

  /// Get movement status name in Arabic
  String get statusNameAr {
    switch (status) {
      case MovementStatus.pending:
        return 'معلق';
      case MovementStatus.approved:
        return 'معتمد';
      case MovementStatus.rejected:
        return 'مرفوض';
      case MovementStatus.completed:
        return 'مكتمل';
      case MovementStatus.cancelled:
        return 'ملغي';
    }
  }

  /// Get movement reason name in Arabic
  String get reasonNameAr {
    switch (reason) {
      case MovementReason.purchase:
        return 'شراء';
      case MovementReason.sale:
        return 'بيع';
      case MovementReason.production:
        return 'إنتاج';
      case MovementReason.consumption:
        return 'استهلاك';
      case MovementReason.transfer:
        return 'نقل';
      case MovementReason.adjustment:
        return 'تسوية';
      case MovementReason.return_:
        return 'مرتجع';
      case MovementReason.damage:
        return 'تلف';
      case MovementReason.theft:
        return 'سرقة';
      case MovementReason.expiry:
        return 'انتهاء صلاحية';
      case MovementReason.other:
        return 'أخرى';
    }
  }

  /// Check if movement is inbound (increases stock)
  bool get isInbound {
    return [
      MovementType.receipt,
      MovementType.return_,
      MovementType.found,
    ].contains(type);
  }

  /// Check if movement is outbound (decreases stock)
  bool get isOutbound {
    return [
      MovementType.issue,
      MovementType.damage,
      MovementType.loss,
    ].contains(type);
  }

  /// Get signed quantity (positive for inbound, negative for outbound)
  double get signedQuantity {
    if (isInbound) return quantity;
    if (isOutbound) return -quantity;
    return 0; // For transfers and adjustments
  }

  /// Create a copy with updated fields
  StockMovement copyWith({
    int? id,
    String? documentNumber,
    MovementType? type,
    MovementStatus? status,
    MovementReason? reason,
    int? itemId,
    int? warehouseId,
    int? locationId,
    int? toWarehouseId,
    int? toLocationId,
    double? quantity,
    double? unitCost,
    double? totalCost,
    String? batchNumber,
    String? serialNumber,
    DateTime? expiryDate,
    String? notes,
    String? referenceDocument,
    int? userId,
    int? approvedBy,
    DateTime? approvedAt,
    DateTime? movementDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    Item? item,
    Warehouse? warehouse,
    WarehouseLocation? location,
    Warehouse? toWarehouse,
    WarehouseLocation? toLocation,
  }) {
    return StockMovement(
      id: id ?? this.id,
      documentNumber: documentNumber ?? this.documentNumber,
      type: type ?? this.type,
      status: status ?? this.status,
      reason: reason ?? this.reason,
      itemId: itemId ?? this.itemId,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      toWarehouseId: toWarehouseId ?? this.toWarehouseId,
      toLocationId: toLocationId ?? this.toLocationId,
      quantity: quantity ?? this.quantity,
      unitCost: unitCost ?? this.unitCost,
      totalCost: totalCost ?? this.totalCost,
      batchNumber: batchNumber ?? this.batchNumber,
      serialNumber: serialNumber ?? this.serialNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      notes: notes ?? this.notes,
      referenceDocument: referenceDocument ?? this.referenceDocument,
      userId: userId ?? this.userId,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      movementDate: movementDate ?? this.movementDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      item: item ?? this.item,
      warehouse: warehouse ?? this.warehouse,
      location: location ?? this.location,
      toWarehouse: toWarehouse ?? this.toWarehouse,
      toLocation: toLocation ?? this.toLocation,
    );
  }

  @override
  String toString() {
    return 'StockMovement{id: $id, type: $type, quantity: $quantity}';
  }
}
