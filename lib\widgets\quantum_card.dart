/// بطاقة كمية متقدمة لتطبيق Smart Ledger
/// Advanced Quantum Card for Smart Ledger Application
library;

import 'package:flutter/material.dart';

/// بطاقة كمية مع تأثيرات بصرية متقدمة
/// Quantum card with advanced visual effects
class QuantumCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final double borderRadius;
  final double elevation;
  final bool enableHoverEffect;
  final bool enableGlowEffect;
  final VoidCallback? onTap;

  const QuantumCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.backgroundColor,
    this.borderRadius = 16.0,
    this.elevation = 8.0,
    this.enableHoverEffect = true,
    this.enableGlowEffect = true,
    this.onTap,
  });

  @override
  State<QuantumCard> createState() => _QuantumCardState();
}

class _QuantumCardState extends State<QuantumCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _glowController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    if (widget.enableGlowEffect) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (!widget.enableHoverEffect) return;

    setState(() => _isHovered = isHovered);

    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor =
        widget.backgroundColor ?? theme.cardColor.withValues(alpha: 0.1);

    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _glowAnimation]),
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: MouseRegion(
              onEnter: (_) => _onHover(true),
              onExit: (_) => _onHover(false),
              child: GestureDetector(
                onTap: widget.onTap,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        backgroundColor,
                        backgroundColor.withValues(alpha: 0.8),
                      ],
                    ),
                    border: Border.all(
                      color: widget.enableGlowEffect
                          ? Colors.cyan.withValues(
                              alpha: _glowAnimation.value * 0.5,
                            )
                          : Colors.transparent,
                      width: 1.0,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: widget.elevation,
                        offset: const Offset(0, 4),
                      ),
                      if (widget.enableGlowEffect)
                        BoxShadow(
                          color: Colors.cyan.withValues(
                            alpha: _glowAnimation.value * 0.2,
                          ),
                          blurRadius: widget.elevation * 2,
                          offset: const Offset(0, 0),
                        ),
                      if (_isHovered)
                        BoxShadow(
                          color: Colors.blue.withValues(alpha: 0.3),
                          blurRadius: widget.elevation * 1.5,
                          offset: const Offset(0, 6),
                        ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    child: Container(
                      padding: widget.padding ?? const EdgeInsets.all(16),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// بطاقة كمية مبسطة للاستخدام السريع
/// Simple quantum card for quick usage
class SimpleQuantumCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;

  const SimpleQuantumCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return QuantumCard(
      padding: padding,
      margin: margin,
      onTap: onTap,
      enableHoverEffect: onTap != null,
      enableGlowEffect: false,
      child: child,
    );
  }
}
