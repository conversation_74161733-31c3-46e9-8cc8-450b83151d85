/// شاشة حركات المخزون المحسنة
/// Enhanced Stock Movements Screen
library;

import 'package:flutter/material.dart';
import '../../models/stock_movement.dart';
import '../../models/warehouse.dart';
import '../../models/item.dart';
import '../../services/warehouse_service.dart';
import '../../services/item_service.dart';
import '../../theme/app_theme.dart';
import 'add_stock_movement_screen.dart';
import 'stock_movement_details_screen.dart';

class EnhancedStockMovementsScreen extends StatefulWidget {
  final int? warehouseId;
  final int? itemId;

  const EnhancedStockMovementsScreen({
    super.key,
    this.warehouseId,
    this.itemId,
  });

  @override
  State<EnhancedStockMovementsScreen> createState() =>
      _EnhancedStockMovementsScreenState();
}

class _EnhancedStockMovementsScreenState
    extends State<EnhancedStockMovementsScreen>
    with TickerProviderStateMixin {
  final WarehouseService _warehouseService = WarehouseService();
  final ItemService _itemService = ItemService();
  final TextEditingController _searchController = TextEditingController();

  List<StockMovement> _allMovements = [];
  List<StockMovement> _filteredMovements = [];
  List<Warehouse> _warehouses = [];
  List<Item> _items = [];
  bool _isLoading = true;
  String _searchQuery = '';
  MovementType? _selectedType;
  MovementStatus? _selectedStatus;
  int? _selectedWarehouseId;
  int? _selectedItemId;
  DateTime? _startDate;
  DateTime? _endDate;

  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(length: 5, vsync: this);
    _selectedWarehouseId = widget.warehouseId;
    _selectedItemId = widget.itemId;
    _loadData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل البيانات بشكل متوازي
      final futures = await Future.wait([
        _warehouseService.getAllWarehouses(),
        _itemService.getAllItems(),
        _warehouseService.getMovements(),
      ]);

      if (mounted) {
        final warehousesResult = futures[0] as Result<List<Warehouse>>;
        final itemsResult = futures[1] as Result<List<Item>>;
        final movementsResult = futures[2] as Result<List<StockMovement>>;

        if (warehousesResult.isSuccess && warehousesResult.data != null) {
          _warehouses = warehousesResult.data!;
        }

        if (itemsResult.isSuccess && itemsResult.data != null) {
          _items = itemsResult.data!;
        }

        if (movementsResult.isSuccess && movementsResult.data != null) {
          _allMovements = movementsResult.data!;
          _applyFilters();
        } else {
          _showErrorSnackBar(
            movementsResult.error ?? 'خطأ في تحميل حركات المخزون',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل البيانات: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _applyFilters() {
    List<StockMovement> filtered = List.from(_allMovements);

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((movement) {
        final item = _getItemById(movement.itemId);
        final warehouse = _getWarehouseById(movement.warehouseId);

        return movement.referenceDocument?.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ==
                true ||
            movement.notes?.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ==
                true ||
            item?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ==
                true ||
            warehouse?.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ==
                true;
      }).toList();
    }

    // تطبيق فلاتر أخرى
    if (_selectedType != null) {
      filtered = filtered.where((m) => m.type == _selectedType).toList();
    }

    if (_selectedStatus != null) {
      filtered = filtered.where((m) => m.status == _selectedStatus).toList();
    }

    if (_selectedWarehouseId != null) {
      filtered = filtered
          .where((m) => m.warehouseId == _selectedWarehouseId)
          .toList();
    }

    if (_selectedItemId != null) {
      filtered = filtered.where((m) => m.itemId == _selectedItemId).toList();
    }

    if (_startDate != null) {
      filtered = filtered
          .where(
            (m) => m.movementDate.isAfter(
              _startDate!.subtract(const Duration(days: 1)),
            ),
          )
          .toList();
    }

    if (_endDate != null) {
      filtered = filtered
          .where(
            (m) =>
                m.movementDate.isBefore(_endDate!.add(const Duration(days: 1))),
          )
          .toList();
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    filtered.sort((a, b) => b.movementDate.compareTo(a.movementDate));

    setState(() {
      _filteredMovements = filtered;
    });
  }

  Item? _getItemById(int itemId) {
    try {
      return _items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  Warehouse? _getWarehouseById(int warehouseId) {
    try {
      return _warehouses.firstWhere((warehouse) => warehouse.id == warehouseId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'حركات المخزون',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: 'فلترة',
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.list)),
            Tab(text: 'وارد', icon: Icon(Icons.arrow_downward)),
            Tab(text: 'صادر', icon: Icon(Icons.arrow_upward)),
            Tab(text: 'تحويل', icon: Icon(Icons.swap_horiz)),
            Tab(text: 'تسوية', icon: Icon(Icons.balance)),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildSearchSection(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildMovementsList(_filteredMovements),
                  _buildMovementsList(
                    _filteredMovements
                        .where((m) => m.type == MovementType.receipt)
                        .toList(),
                  ),
                  _buildMovementsList(
                    _filteredMovements
                        .where((m) => m.type == MovementType.issue)
                        .toList(),
                  ),
                  _buildMovementsList(
                    _filteredMovements
                        .where((m) => m.type == MovementType.transfer)
                        .toList(),
                  ),
                  _buildMovementsList(
                    _filteredMovements
                        .where((m) => m.type == MovementType.adjustment)
                        .toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToAddMovement(),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('حركة جديدة'),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في حركات المخزون...',
          prefixIcon: Icon(Icons.search, color: AppTheme.primaryColor),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: Colors.grey[50],
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
            _applyFilters();
          });
        },
      ),
    );
  }

  Widget _buildMovementsList(List<StockMovement> movements) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (movements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد حركات مخزون',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة حركة مخزون جديدة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: movements.length,
        itemBuilder: (context, index) => _buildMovementCard(movements[index]),
      ),
    );
  }

  Widget _buildMovementCard(StockMovement movement) {
    final item = _getItemById(movement.itemId);
    final warehouse = _getWarehouseById(movement.warehouseId);
    final movementColor = _getMovementTypeColor(movement.type);
    final statusColor = _getMovementStatusColor(movement.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () => _navigateToMovementDetails(movement),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // أيقونة نوع الحركة
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: movementColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getMovementTypeIcon(movement.type),
                        color: movementColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // معلومات الحركة
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item?.name ?? 'صنف غير معروف',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            warehouse?.name ?? 'مستودع غير معروف',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            _formatDate(movement.movementDate),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // الكمية والحالة
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: statusColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _getMovementStatusText(movement.status),
                            style: TextStyle(
                              color: statusColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${movement.quantity.toStringAsFixed(0)} ${item?.unit ?? ''}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: movementColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                if (movement.referenceDocument != null ||
                    movement.notes != null) ...[
                  const SizedBox(height: 12),
                  const Divider(height: 1),
                  const SizedBox(height: 8),
                  if (movement.referenceDocument != null)
                    Text(
                      'المرجع: ${movement.referenceDocument}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  if (movement.notes != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      'ملاحظات: ${movement.notes}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _getMovementTypeText(movement.type),
                      style: TextStyle(
                        color: movementColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Row(
                      children: [
                        if (movement.status == MovementStatus.pending)
                          IconButton(
                            onPressed: () => _confirmMovement(movement),
                            icon: const Icon(Icons.check, size: 20),
                            tooltip: 'تأكيد',
                            color: Colors.green,
                          ),
                        IconButton(
                          onPressed: () => _showMovementOptions(movement),
                          icon: const Icon(Icons.more_vert, size: 20),
                          tooltip: 'خيارات',
                          color: Colors.grey[600],
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getMovementTypeColor(MovementType type) {
    switch (type) {
      case MovementType.receipt:
        return Colors.green;
      case MovementType.issue:
        return Colors.red;
      case MovementType.transfer:
        return Colors.blue;
      case MovementType.adjustment:
        return Colors.orange;
      case MovementType.return_:
        return Colors.purple;
      case MovementType.damage:
        return Colors.red[800]!;
      case MovementType.loss:
        return Colors.grey[800]!;
      case MovementType.found:
        return Colors.teal;
    }
  }

  IconData _getMovementTypeIcon(MovementType type) {
    switch (type) {
      case MovementType.receipt:
        return Icons.arrow_downward;
      case MovementType.issue:
        return Icons.arrow_upward;
      case MovementType.transfer:
        return Icons.swap_horiz;
      case MovementType.adjustment:
        return Icons.balance;
      case MovementType.return_:
        return Icons.keyboard_return;
      case MovementType.damage:
        return Icons.broken_image;
      case MovementType.loss:
        return Icons.remove_circle;
      case MovementType.found:
        return Icons.add_circle;
    }
  }

  String _getMovementTypeText(MovementType type) {
    switch (type) {
      case MovementType.receipt:
        return 'استلام';
      case MovementType.issue:
        return 'صرف';
      case MovementType.transfer:
        return 'نقل';
      case MovementType.adjustment:
        return 'تسوية';
      case MovementType.return_:
        return 'مرتجع';
      case MovementType.damage:
        return 'تالف';
      case MovementType.loss:
        return 'فقدان';
      case MovementType.found:
        return 'عثور';
    }
  }

  Color _getMovementStatusColor(MovementStatus status) {
    switch (status) {
      case MovementStatus.pending:
        return Colors.orange;
      case MovementStatus.approved:
        return Colors.blue;
      case MovementStatus.completed:
        return Colors.green;
      case MovementStatus.rejected:
        return Colors.red;
      case MovementStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getMovementStatusText(MovementStatus status) {
    switch (status) {
      case MovementStatus.pending:
        return 'معلق';
      case MovementStatus.approved:
        return 'معتمد';
      case MovementStatus.completed:
        return 'مكتمل';
      case MovementStatus.rejected:
        return 'مرفوض';
      case MovementStatus.cancelled:
        return 'ملغي';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('فلترة حركات المخزون'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // فلتر نوع الحركة
              DropdownButtonFormField<MovementType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الحركة',
                  border: OutlineInputBorder(),
                ),
                items: MovementType.values.map((type) {
                  return DropdownMenuItem<MovementType>(
                    value: type,
                    child: Text(_getMovementTypeText(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              // فلتر حالة الحركة
              DropdownButtonFormField<MovementStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'حالة الحركة',
                  border: OutlineInputBorder(),
                ),
                items: MovementStatus.values.map((status) {
                  return DropdownMenuItem<MovementStatus>(
                    value: status,
                    child: Text(_getMovementStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              // فلتر المستودع
              DropdownButtonFormField<int>(
                value: _selectedWarehouseId,
                decoration: const InputDecoration(
                  labelText: 'المستودع',
                  border: OutlineInputBorder(),
                ),
                items: _warehouses.map((warehouse) {
                  return DropdownMenuItem<int>(
                    value: warehouse.id,
                    child: Text(warehouse.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedWarehouseId = value;
                  });
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedType = null;
                _selectedStatus = null;
                _selectedWarehouseId = widget.warehouseId;
                _selectedItemId = widget.itemId;
                _startDate = null;
                _endDate = null;
                _applyFilters();
              });
              Navigator.pop(context);
            },
            child: const Text('إعادة تعيين'),
          ),
          ElevatedButton(
            onPressed: () {
              _applyFilters();
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _navigateToAddMovement() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddStockMovementScreen(
          warehouseId: _selectedWarehouseId,
          itemId: _selectedItemId,
        ),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }

  void _navigateToMovementDetails(StockMovement movement) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StockMovementDetailsScreen(movement: movement),
      ),
    );
  }

  void _navigateToEditMovement(StockMovement movement) async {
    // Check if movement can be edited (only pending movements can be edited)
    if (movement.status != MovementStatus.pending) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن تعديل الحركة بعد اعتمادها'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddStockMovementScreen(movement: movement),
      ),
    );

    if (result == true && mounted) {
      _loadData(); // Refresh the movements list
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث الحركة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _confirmMovement(StockMovement movement) async {
    try {
      // تحديث حالة الحركة إلى مكتملة
      final updatedMovement = StockMovement(
        id: movement.id,
        documentNumber: movement.documentNumber,
        type: movement.type,
        status: MovementStatus.completed,
        reason: movement.reason,
        itemId: movement.itemId,
        warehouseId: movement.warehouseId,
        locationId: movement.locationId,
        toWarehouseId: movement.toWarehouseId,
        toLocationId: movement.toLocationId,
        quantity: movement.quantity,
        unitCost: movement.unitCost,
        totalCost: movement.totalCost,
        batchNumber: movement.batchNumber,
        serialNumber: movement.serialNumber,
        expiryDate: movement.expiryDate,
        notes: movement.notes,
        referenceDocument: movement.referenceDocument,
        userId: movement.userId,
        approvedBy: movement.approvedBy,
        approvedAt: DateTime.now(),
        movementDate: movement.movementDate,
        createdAt: movement.createdAt,
        updatedAt: DateTime.now(),
      );

      final result = await _warehouseService.updateStockMovement(
        updatedMovement,
      );
      if (result.isSuccess) {
        _showSuccessSnackBar('تم تأكيد الحركة بنجاح');
        _loadData();
      } else {
        _showErrorSnackBar(result.error ?? 'خطأ في تأكيد الحركة');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تأكيد الحركة: $e');
    }
  }

  void _showMovementOptions(StockMovement movement) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'حركة رقم: ${movement.documentNumber}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            _buildOptionTile(
              icon: Icons.visibility,
              title: 'عرض التفاصيل',
              onTap: () {
                Navigator.pop(context);
                _navigateToMovementDetails(movement);
              },
            ),
            if (movement.status == MovementStatus.pending) ...[
              _buildOptionTile(
                icon: Icons.check,
                title: 'تأكيد الحركة',
                color: Colors.green,
                onTap: () {
                  Navigator.pop(context);
                  _confirmMovement(movement);
                },
              ),
              _buildOptionTile(
                icon: Icons.edit,
                title: 'تعديل',
                onTap: () {
                  Navigator.pop(context);
                  _navigateToEditMovement(movement);
                },
              ),
              _buildOptionTile(
                icon: Icons.cancel,
                title: 'إلغاء الحركة',
                color: Colors.red,
                onTap: () {
                  Navigator.pop(context);
                  _confirmCancelMovement(movement);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? AppTheme.primaryColor),
      title: Text(
        title,
        style: TextStyle(color: color, fontWeight: FontWeight.w500),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }

  void _confirmCancelMovement(StockMovement movement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('تأكيد الإلغاء'),
        content: Text(
          'هل أنت متأكد من إلغاء حركة المخزون رقم "${movement.documentNumber}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _cancelMovement(movement);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('إلغاء الحركة'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelMovement(StockMovement movement) async {
    try {
      // تحديث حالة الحركة إلى ملغية
      final updatedMovement = StockMovement(
        id: movement.id,
        documentNumber: movement.documentNumber,
        type: movement.type,
        status: MovementStatus.cancelled,
        reason: movement.reason,
        itemId: movement.itemId,
        warehouseId: movement.warehouseId,
        locationId: movement.locationId,
        toWarehouseId: movement.toWarehouseId,
        toLocationId: movement.toLocationId,
        quantity: movement.quantity,
        unitCost: movement.unitCost,
        totalCost: movement.totalCost,
        batchNumber: movement.batchNumber,
        serialNumber: movement.serialNumber,
        expiryDate: movement.expiryDate,
        notes: movement.notes,
        referenceDocument: movement.referenceDocument,
        userId: movement.userId,
        approvedBy: movement.approvedBy,
        approvedAt: movement.approvedAt,
        movementDate: movement.movementDate,
        createdAt: movement.createdAt,
        updatedAt: DateTime.now(),
      );

      final result = await _warehouseService.updateStockMovement(
        updatedMovement,
      );
      if (result.isSuccess) {
        _showSuccessSnackBar('تم إلغاء الحركة بنجاح');
        _loadData();
      } else {
        _showErrorSnackBar(result.error ?? 'خطأ في إلغاء الحركة');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في إلغاء الحركة: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
