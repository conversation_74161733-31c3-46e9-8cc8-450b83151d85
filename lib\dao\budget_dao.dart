/// طبقة الوصول للبيانات - الموازنات والتخطيط المالي
/// Budget Data Access Object for Smart Ledger
library;


import '../database/database_helper.dart';
import '../database/database_schema.dart';
import '../models/budget.dart';
import '../models/account.dart';
import '../utils/result.dart';

class BudgetDao {
  static final BudgetDao _instance = BudgetDao._internal();
  factory BudgetDao() => _instance;
  BudgetDao._internal();

  /// إنشاء موازنة جديدة
  Future<Result<Budget>> createBudget(Budget budget) async {
    try {
      final db = await DatabaseHelper().database;

      // التحقق من عدم تكرار الكود
      final existingBudget = await getBudgetByCode(budget.code);
      if (existingBudget.isSuccess) {
        return Result.error('كود الموازنة موجود مسبقاً');
      }

      // إدراج الموازنة
      final budgetId = await db.insert(
        DatabaseSchema.tableBudgets,
        budget.toMap()..remove('id'),
      );

      // إدراج بنود الموازنة
      for (BudgetLine line in budget.lines) {
        await db.insert(
          DatabaseSchema.tableBudgetLines,
          line.copyWith(budgetId: budgetId).toMap()..remove('id'),
        );
      }

      // جلب الموازنة المُنشأة مع البنود
      final createdBudget = await getBudgetById(budgetId);
      return createdBudget;
    } catch (e) {
      return Result.error('خطأ في إنشاء الموازنة: ${e.toString()}');
    }
  }

  /// تحديث موازنة
  Future<Result<Budget>> updateBudget(Budget budget) async {
    try {
      final db = await DatabaseHelper().database;

      if (budget.id == null) {
        return Result.error('معرف الموازنة مطلوب للتحديث');
      }

      // التحقق من وجود الموازنة
      final existingResult = await getBudgetById(budget.id!);
      if (!existingResult.isSuccess) {
        return Result.error('الموازنة غير موجودة');
      }

      // التحقق من إمكانية التعديل
      final existingBudget = existingResult.data!;
      if (!existingBudget.status.canEdit) {
        return Result.error('لا يمكن تعديل الموازنة في الحالة الحالية');
      }

      // تحديث الموازنة
      await db.update(
        DatabaseSchema.tableBudgets,
        budget.toMap(),
        where: 'id = ?',
        whereArgs: [budget.id],
      );

      // حذف البنود القديمة
      await db.delete(
        DatabaseSchema.tableBudgetLines,
        where: 'budget_id = ?',
        whereArgs: [budget.id],
      );

      // إدراج البنود الجديدة
      for (BudgetLine line in budget.lines) {
        await db.insert(
          DatabaseSchema.tableBudgetLines,
          line.copyWith(budgetId: budget.id!).toMap()..remove('id'),
        );
      }

      // جلب الموازنة المُحدثة
      final updatedBudget = await getBudgetById(budget.id!);
      return updatedBudget;
    } catch (e) {
      return Result.error('خطأ في تحديث الموازنة: ${e.toString()}');
    }
  }

  /// حذف موازنة
  Future<Result<void>> deleteBudget(int budgetId) async {
    try {
      final db = await DatabaseHelper().database;

      // التحقق من وجود الموازنة وإمكانية الحذف
      final budgetResult = await getBudgetById(budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error('الموازنة غير موجودة');
      }

      final budget = budgetResult.data!;
      if (!budget.status.canDelete) {
        return Result.error('لا يمكن حذف الموازنة في الحالة الحالية');
      }

      // حذف الموازنة (سيتم حذف البنود والمراجعات تلقائياً بسبب CASCADE)
      final deletedRows = await db.delete(
        DatabaseSchema.tableBudgets,
        where: 'id = ?',
        whereArgs: [budgetId],
      );

      if (deletedRows == 0) {
        return Result.error('فشل في حذف الموازنة');
      }

      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في حذف الموازنة: ${e.toString()}');
    }
  }

  /// جلب موازنة بالمعرف
  Future<Result<Budget>> getBudgetById(int id) async {
    try {
      final db = await DatabaseHelper().database;

      // جلب الموازنة
      final budgetMaps = await db.query(
        DatabaseSchema.tableBudgets,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (budgetMaps.isEmpty) {
        return Result.error('الموازنة غير موجودة');
      }

      final budget = Budget.fromMap(budgetMaps.first);

      // جلب بنود الموازنة مع بيانات الحسابات
      final linesMaps = await db.rawQuery(
        '''
        SELECT bl.*, a.code as account_code, a.name as account_name, 
               a.account_type, a.name_en as account_name_en
        FROM ${DatabaseSchema.tableBudgetLines} bl
        JOIN ${DatabaseSchema.tableAccounts} a ON bl.account_id = a.id
        WHERE bl.budget_id = ?
        ORDER BY a.code
      ''',
        [id],
      );

      for (var lineMap in linesMaps) {
        final line = BudgetLine.fromMap(lineMap);
        line.account = Account(
          id: line.accountId,
          code: lineMap['account_code'] as String,
          name: lineMap['account_name'] as String,
          nameEn: lineMap['account_name_en'] as String?,
          accountType: AccountType.fromString(
            lineMap['account_type'] as String,
          ),
        );
        budget.lines.add(line);
      }

      // جلب مراجعات الموازنة
      final revisionsMaps = await db.query(
        DatabaseSchema.tableBudgetRevisions,
        where: 'budget_id = ?',
        whereArgs: [id],
        orderBy: 'revision_number DESC',
      );

      for (var revisionMap in revisionsMaps) {
        budget.revisions.add(BudgetRevision.fromMap(revisionMap));
      }

      return Result.success(budget);
    } catch (e) {
      return Result.error('خطأ في جلب الموازنة: ${e.toString()}');
    }
  }

  /// جلب موازنة بالكود
  Future<Result<Budget>> getBudgetByCode(String code) async {
    try {
      final db = await DatabaseHelper().database;

      final budgetMaps = await db.query(
        DatabaseSchema.tableBudgets,
        where: 'code = ?',
        whereArgs: [code],
      );

      if (budgetMaps.isEmpty) {
        return Result.error('الموازنة غير موجودة');
      }

      return getBudgetById(budgetMaps.first['id'] as int);
    } catch (e) {
      return Result.error('خطأ في جلب الموازنة: ${e.toString()}');
    }
  }

  /// جلب جميع الموازنات
  Future<Result<List<Budget>>> getAllBudgets({
    BudgetType? type,
    BudgetStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await DatabaseHelper().database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (type != null) {
        whereClause += 'type = ?';
        whereArgs.add(type.value);
      }

      if (status != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'status = ?';
        whereArgs.add(status.value);
      }

      if (startDate != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'start_date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'end_date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      final budgetMaps = await db.query(
        DatabaseSchema.tableBudgets,
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'created_at DESC',
        limit: limit,
        offset: offset,
      );

      List<Budget> budgets = [];
      for (var budgetMap in budgetMaps) {
        final budgetResult = await getBudgetById(budgetMap['id'] as int);
        if (budgetResult.isSuccess) {
          budgets.add(budgetResult.data!);
        }
      }

      return Result.success(budgets);
    } catch (e) {
      return Result.error('خطأ في جلب الموازنات: ${e.toString()}');
    }
  }

  /// اعتماد موازنة
  Future<Result<Budget>> approveBudget(int budgetId, String approvedBy) async {
    try {
      final db = await DatabaseHelper().database;

      // التحقق من وجود الموازنة وإمكانية الاعتماد
      final budgetResult = await getBudgetById(budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error('الموازنة غير موجودة');
      }

      final budget = budgetResult.data!;
      if (!budget.status.canApprove) {
        return Result.error('لا يمكن اعتماد الموازنة في الحالة الحالية');
      }

      // تحديث حالة الموازنة
      await db.update(
        DatabaseSchema.tableBudgets,
        {
          'status': BudgetStatus.approved.value,
          'approved_by': approvedBy,
          'approved_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [budgetId],
      );

      // جلب الموازنة المُحدثة
      return getBudgetById(budgetId);
    } catch (e) {
      return Result.error('خطأ في اعتماد الموازنة: ${e.toString()}');
    }
  }

  /// تفعيل موازنة
  Future<Result<Budget>> activateBudget(int budgetId) async {
    try {
      final db = await DatabaseHelper().database;

      // التحقق من وجود الموازنة
      final budgetResult = await getBudgetById(budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error('الموازنة غير موجودة');
      }

      final budget = budgetResult.data!;
      if (budget.status != BudgetStatus.approved) {
        return Result.error('يجب اعتماد الموازنة أولاً قبل التفعيل');
      }

      // إلغاء تفعيل الموازنات الأخرى من نفس النوع والفترة
      await db.update(
        DatabaseSchema.tableBudgets,
        {
          'status': BudgetStatus.closed.value,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'type = ? AND status = ? AND id != ?',
        whereArgs: [budget.type.value, BudgetStatus.active.value, budgetId],
      );

      // تفعيل الموازنة الحالية
      await db.update(
        DatabaseSchema.tableBudgets,
        {
          'status': BudgetStatus.active.value,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [budgetId],
      );

      // جلب الموازنة المُحدثة
      return getBudgetById(budgetId);
    } catch (e) {
      return Result.error('خطأ في تفعيل الموازنة: ${e.toString()}');
    }
  }

  /// إضافة مراجعة للموازنة
  Future<Result<BudgetRevision>> addBudgetRevision(
    BudgetRevision revision,
  ) async {
    try {
      final db = await DatabaseHelper().database;

      // التحقق من وجود الموازنة
      final budgetResult = await getBudgetById(revision.budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error('الموازنة غير موجودة');
      }

      // حساب رقم المراجعة التالي
      final maxRevisionMaps = await db.rawQuery(
        '''
        SELECT MAX(revision_number) as max_revision
        FROM ${DatabaseSchema.tableBudgetRevisions}
        WHERE budget_id = ?
      ''',
        [revision.budgetId],
      );

      final maxRevision = maxRevisionMaps.first['max_revision'] as int? ?? 0;
      final newRevisionNumber = maxRevision + 1;

      // إدراج المراجعة
      final revisionId = await db.insert(
        DatabaseSchema.tableBudgetRevisions,
        revision.copyWith(revisionNumber: newRevisionNumber).toMap()
          ..remove('id'),
      );

      // جلب المراجعة المُنشأة
      final revisionMaps = await db.query(
        DatabaseSchema.tableBudgetRevisions,
        where: 'id = ?',
        whereArgs: [revisionId],
      );

      return Result.success(BudgetRevision.fromMap(revisionMaps.first));
    } catch (e) {
      return Result.error('خطأ في إضافة مراجعة الموازنة: ${e.toString()}');
    }
  }

  /// تحديث المبالغ الفعلية للموازنة
  Future<Result<void>> updateActualAmounts(
    int budgetId,
    Map<int, double> actualAmounts,
  ) async {
    try {
      final db = await DatabaseHelper().database;

      // التحقق من وجود الموازنة
      final budgetResult = await getBudgetById(budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error('الموازنة غير موجودة');
      }

      // تحديث المبالغ الفعلية
      for (var entry in actualAmounts.entries) {
        await db.update(
          DatabaseSchema.tableBudgetLines,
          {
            'actual_amount': entry.value,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'budget_id = ? AND account_id = ?',
          whereArgs: [budgetId, entry.key],
        );
      }

      return Result.success(null);
    } catch (e) {
      return Result.error('خطأ في تحديث المبالغ الفعلية: ${e.toString()}');
    }
  }

  /// حساب المبالغ الفعلية من القيود المحاسبية
  Future<Result<Map<int, double>>> calculateActualAmounts(
    int budgetId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await DatabaseHelper().database;

      // جلب بنود الموازنة
      final budgetResult = await getBudgetById(budgetId);
      if (!budgetResult.isSuccess) {
        return Result.error('الموازنة غير موجودة');
      }

      final budget = budgetResult.data!;
      Map<int, double> actualAmounts = {};

      // حساب المبالغ الفعلية لكل حساب
      for (var line in budget.lines) {
        final actualMaps = await db.rawQuery(
          '''
          SELECT
            COALESCE(SUM(jel.debit_amount), 0.0) - COALESCE(SUM(jel.credit_amount), 0.0) as net_amount
          FROM ${DatabaseSchema.tableJournalEntryLines} jel
          JOIN ${DatabaseSchema.tableJournalEntries} je ON jel.journal_entry_id = je.id
          WHERE jel.account_id = ?
            AND je.date >= ?
            AND je.date <= ?
            AND je.is_posted = 1
        ''',
          [
            line.accountId,
            startDate.toIso8601String(),
            endDate.toIso8601String(),
          ],
        );

        final netAmount = actualMaps.first['net_amount'] as double? ?? 0.0;

        // تعديل المبلغ حسب نوع الحساب (للحسابات الدائنة)
        if (line.account != null && !line.account!.accountType.isDebitNormal) {
          actualAmounts[line.accountId] = -netAmount;
        } else {
          actualAmounts[line.accountId] = netAmount;
        }
      }

      return Result.success(actualAmounts);
    } catch (e) {
      return Result.error('خطأ في حساب المبالغ الفعلية: ${e.toString()}');
    }
  }

  /// جلب الموازنة النشطة حسب النوع
  Future<Result<Budget?>> getActiveBudget(BudgetType type) async {
    try {
      final db = await DatabaseHelper().database;

      final budgetMaps = await db.query(
        DatabaseSchema.tableBudgets,
        where: 'type = ? AND status = ?',
        whereArgs: [type.value, BudgetStatus.active.value],
        limit: 1,
      );

      if (budgetMaps.isEmpty) {
        return Result.success(null);
      }

      final budgetResult = await getBudgetById(budgetMaps.first['id'] as int);
      return budgetResult.isSuccess
          ? Result.success(budgetResult.data)
          : Result.error(budgetResult.error!);
    } catch (e) {
      return Result.error('خطأ في جلب الموازنة النشطة: ${e.toString()}');
    }
  }

  /// جلب إحصائيات الموازنات
  Future<Result<Map<String, dynamic>>> getBudgetStatistics() async {
    try {
      final db = await DatabaseHelper().database;

      // إحصائيات عامة
      final totalMaps = await db.rawQuery('''
        SELECT
          COUNT(*) as total_budgets,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_budgets,
          COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_budgets,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_budgets
        FROM ${DatabaseSchema.tableBudgets}
      ''');

      // إحصائيات المبالغ
      final amountMaps = await db.rawQuery('''
        SELECT
          COALESCE(SUM(bl.budget_amount), 0.0) as total_budget_amount,
          COALESCE(SUM(bl.actual_amount), 0.0) as total_actual_amount
        FROM ${DatabaseSchema.tableBudgetLines} bl
        JOIN ${DatabaseSchema.tableBudgets} b ON bl.budget_id = b.id
        WHERE b.status = 'active'
      ''');

      final totalStats = totalMaps.first;
      final amountStats = amountMaps.first;

      final totalBudgetAmount = amountStats['total_budget_amount'] as double;
      final totalActualAmount = amountStats['total_actual_amount'] as double;
      final variance = totalActualAmount - totalBudgetAmount;
      final variancePercentage = totalBudgetAmount == 0
          ? 0.0
          : (variance / totalBudgetAmount) * 100;

      return Result.success({
        'total_budgets': totalStats['total_budgets'],
        'active_budgets': totalStats['active_budgets'],
        'draft_budgets': totalStats['draft_budgets'],
        'approved_budgets': totalStats['approved_budgets'],
        'total_budget_amount': totalBudgetAmount,
        'total_actual_amount': totalActualAmount,
        'total_variance': variance,
        'variance_percentage': variancePercentage,
      });
    } catch (e) {
      return Result.error('خطأ في جلب إحصائيات الموازنات: ${e.toString()}');
    }
  }
}
