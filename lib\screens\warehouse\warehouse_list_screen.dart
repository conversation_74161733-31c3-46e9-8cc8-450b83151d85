/// شاشة قائمة المخازن
/// Warehouse List Screen for Smart Ledger
library;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/warehouse.dart';
import '../../services/warehouse_service.dart';
import '../../providers/language_provider.dart';
import '../../widgets/quantum_card.dart';
import '../../widgets/holographic_app_bar.dart';
import '../../widgets/quantum_search_bar.dart';
import '../../widgets/quantum_loading.dart';
import '../../widgets/quantum_fab.dart';
import 'warehouse_form_screen.dart';
import 'warehouse_details_screen.dart';
import 'stock_movements_screen.dart';
import 'stock_balances_screen.dart';

class WarehouseListScreen extends StatefulWidget {
  const WarehouseListScreen({super.key});

  @override
  State<WarehouseListScreen> createState() => _WarehouseListScreenState();
}

class _WarehouseListScreenState extends State<WarehouseListScreen>
    with TickerProviderStateMixin {
  final WarehouseService _warehouseService = WarehouseService();
  final TextEditingController _searchController = TextEditingController();

  List<Warehouse> _warehouses = [];
  List<Warehouse> _filteredWarehouses = [];
  bool _isLoading = true;
  String _searchQuery = '';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadWarehouses();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  Future<void> _loadWarehouses() async {
    setState(() => _isLoading = true);

    final result = await _warehouseService.getAllWarehouses();

    if (result.isSuccess) {
      setState(() {
        _warehouses = result.data!;
        _filteredWarehouses = _warehouses;
        _isLoading = false;
      });
    } else {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _filterWarehouses(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredWarehouses = _warehouses;
      } else {
        _filteredWarehouses = _warehouses.where((warehouse) {
          return warehouse.name.toLowerCase().contains(query.toLowerCase()) ||
              warehouse.code.toLowerCase().contains(query.toLowerCase()) ||
              (warehouse.description?.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ??
                  false);
        }).toList();
      }
    });
  }

  void _navigateToWarehouseForm([Warehouse? warehouse]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WarehouseFormScreen(warehouse: warehouse),
      ),
    );

    if (result == true) {
      _loadWarehouses();
    }
  }

  void _navigateToWarehouseDetails(Warehouse warehouse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WarehouseDetailsScreen(warehouse: warehouse),
      ),
    );
  }

  void _navigateToStockMovements(Warehouse warehouse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StockMovementsScreen(warehouseId: warehouse.id),
      ),
    );
  }

  void _navigateToStockBalances(Warehouse warehouse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StockBalancesScreen(warehouseId: warehouse.id),
      ),
    );
  }

  void _navigateToAllStockMovements() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const StockMovementsScreen()),
    );
  }

  void _navigateToAllStockBalances() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const StockBalancesScreen()),
    );
  }

  Widget _buildWarehouseCard(Warehouse warehouse, int index) {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: QuantumCard(
            child: InkWell(
              onTap: () => _navigateToWarehouseDetails(warehouse),
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: _getWarehouseTypeColors(warehouse.type),
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: _getWarehouseTypeColors(
                                  warehouse.type,
                                )[0].withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Icon(
                            _getWarehouseTypeIcon(warehouse.type),
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                warehouse.name,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                warehouse.code,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(
                              warehouse.status,
                            ).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: _getStatusColor(warehouse.status),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            warehouse.status.arabicName,
                            style: TextStyle(
                              fontSize: 12,
                              color: _getStatusColor(warehouse.status),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        PopupMenuButton<String>(
                          icon: const Icon(
                            Icons.more_vert,
                            color: Colors.white,
                          ),
                          onSelected: (value) {
                            switch (value) {
                              case 'edit':
                                _navigateToWarehouseForm(warehouse);
                                break;
                              case 'details':
                                _navigateToWarehouseDetails(warehouse);
                                break;
                              case 'movements':
                                _navigateToStockMovements(warehouse);
                                break;
                              case 'balances':
                                _navigateToStockBalances(warehouse);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'details',
                              child: Row(
                                children: [
                                  const Icon(Icons.info_outline),
                                  const SizedBox(width: 8),
                                  Text(isArabic ? 'التفاصيل' : 'Details'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'movements',
                              child: Row(
                                children: [
                                  const Icon(Icons.swap_horiz),
                                  const SizedBox(width: 8),
                                  Text(
                                    isArabic
                                        ? 'حركات المخزون'
                                        : 'Stock Movements',
                                  ),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'balances',
                              child: Row(
                                children: [
                                  const Icon(Icons.inventory),
                                  const SizedBox(width: 8),
                                  Text(
                                    isArabic
                                        ? 'أرصدة المخزون'
                                        : 'Stock Balances',
                                  ),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  const Icon(Icons.edit_outlined),
                                  const SizedBox(width: 8),
                                  Text(isArabic ? 'تعديل' : 'Edit'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if (warehouse.description?.isNotEmpty == true) ...[
                      const SizedBox(height: 12),
                      Text(
                        warehouse.description!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        _buildInfoChip(
                          icon: Icons.category_outlined,
                          label: warehouse.type.arabicName,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        if (warehouse.isMainWarehouse)
                          _buildInfoChip(
                            icon: Icons.star,
                            label: isArabic ? 'رئيسي' : 'Main',
                            color: Colors.amber,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getWarehouseTypeColors(WarehouseType type) {
    switch (type) {
      case WarehouseType.main:
        return [Colors.blue, Colors.indigo];
      case WarehouseType.branch:
        return [Colors.green, Colors.teal];
      case WarehouseType.retail:
        return [Colors.orange, Colors.deepOrange];
      case WarehouseType.wholesale:
        return [Colors.purple, Colors.deepPurple];
      case WarehouseType.transit:
        return [Colors.cyan, Colors.blue];
      case WarehouseType.damaged:
        return [Colors.red, Colors.pink];
      case WarehouseType.quarantine:
        return [Colors.amber, Colors.orange];
    }
  }

  IconData _getWarehouseTypeIcon(WarehouseType type) {
    switch (type) {
      case WarehouseType.main:
        return Icons.business;
      case WarehouseType.branch:
        return Icons.store;
      case WarehouseType.retail:
        return Icons.shopping_cart;
      case WarehouseType.wholesale:
        return Icons.inventory;
      case WarehouseType.transit:
        return Icons.local_shipping;
      case WarehouseType.damaged:
        return Icons.warning;
      case WarehouseType.quarantine:
        return Icons.security;
    }
  }

  Color _getStatusColor(WarehouseStatus status) {
    switch (status) {
      case WarehouseStatus.active:
        return Colors.green;
      case WarehouseStatus.inactive:
        return Colors.grey;
      case WarehouseStatus.maintenance:
        return Colors.orange;
      case WarehouseStatus.closed:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Provider.of<LanguageProvider>(context).isArabic;

    return Scaffold(
      appBar: HolographicAppBar(
        title: isArabic ? 'إدارة المخازن' : 'Warehouse Management',
        subtitle: isArabic ? 'قائمة المخازن' : 'Warehouse List',
        actions: [
          IconButton(
            icon: const Icon(Icons.swap_horiz),
            tooltip: isArabic ? 'جميع حركات المخزون' : 'All Stock Movements',
            onPressed: () => _navigateToAllStockMovements(),
          ),
          IconButton(
            icon: const Icon(Icons.inventory),
            tooltip: isArabic ? 'جميع أرصدة المخزون' : 'All Stock Balances',
            onPressed: () => _navigateToAllStockBalances(),
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: QuantumSearchBar(
              controller: _searchController,
              hintText: isArabic
                  ? 'البحث في المخازن...'
                  : 'Search warehouses...',
              onChanged: _filterWarehouses,
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: QuantumLoading())
                : _filteredWarehouses.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.warehouse_outlined,
                          size: 64,
                          color: Colors.white.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isEmpty
                              ? (isArabic
                                    ? 'لا توجد مخازن'
                                    : 'No warehouses found')
                              : (isArabic
                                    ? 'لا توجد نتائج للبحث'
                                    : 'No search results'),
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredWarehouses.length,
                    itemBuilder: (context, index) {
                      return _buildWarehouseCard(
                        _filteredWarehouses[index],
                        index,
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: QuantumFAB(
        onPressed: () => _navigateToWarehouseForm(),
        icon: Icons.add,
        tooltip: isArabic ? 'إضافة مخزن' : 'Add Warehouse',
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}
