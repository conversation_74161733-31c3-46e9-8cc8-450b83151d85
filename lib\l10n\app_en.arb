{"@@locale": "en", "appTitle": "Smart Ledger", "home": "Home", "accounts": "Accounts", "entries": "Entries", "customers": "Customers", "suppliers": "Suppliers", "inventory": "Inventory", "invoices": "Invoices", "reports": "Reports", "settings": "Settings", "add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "search": "Search", "filter": "Filter", "date": "Date", "amount": "Amount", "description": "Description", "balance": "Balance", "debit": "Debit", "credit": "Credit", "total": "Total", "accountName": "Account Name", "accountCode": "Account Code", "accountType": "Account Type", "parentAccount": "Parent Account", "journalEntry": "Journal Entry", "reference": "Reference", "customerName": "Customer Name", "supplierName": "Supplier Name", "phone": "Phone", "email": "Email", "address": "Address", "itemName": "Item Name", "quantity": "Quantity", "price": "Price", "unit": "Unit", "invoiceNumber": "Invoice Number", "salesInvoice": "Sales Invoice", "purchaseInvoice": "Purchase Invoice", "trialBalance": "Trial Balance", "balanceSheet": "Balance Sheet", "incomeStatement": "Income Statement", "accountStatement": "Account Statement", "backup": "Backup", "restore": "Rest<PERSON>", "companyInfo": "Company Info", "currency": "<PERSON><PERSON><PERSON><PERSON>", "fiscalYear": "Fiscal Year"}